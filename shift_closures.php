<?php
/**
 * ملف تقفيل الورديات - مدمج مع نظام الصلاحيات
 * 
 * الصلاحيات المطبقة:
 * - view: عرض الورديات (مطلوبة للوصول للصفحة)
 * - add_shift: إضافة وردية جديدة
 * - edit_shift: تعديل الوردية
 * - delete_shift: حذف الوردية
 * - confirm_shift: تأكيد الوردية
 * - comprehensive_report: عرض التقارير
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('shift_closures', 'view');

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;

if ($encrypted_store_id) {
    $store_id = decrypt($encrypted_store_id, $key);

    $stmt = $conn->prepare("SELECT sc.*, a.username FROM shift_closures sc 
                            JOIN accounts a ON sc.account_id = a.account_id 
                            WHERE sc.store_id = ? ORDER BY sc.shift_date DESC");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
} else {
    echo "Store ID is missing or invalid.";
    exit();
}
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}

// Fetch shift cards logic
$shiftCards = []; 
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقفيل الورديات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        /* تحسينات خاصة بصلاحيات تقفيل الورديات */
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
            align-items: center;
        }

        .action-buttons .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            min-width: 40px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-buttons .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .action-buttons .edit-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .action-buttons .edit-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea080);
        }

        .device-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 60px;
            transition: all 0.3s ease;
        }

        .device-status.green {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .device-status.red {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }

        .device-status:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        /* تحسين مظهر البطاقات */
        .card .actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }

        .card .actions button {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .card .actions .edit-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .card .actions .delete-btn {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }

        .card .actions button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* رسائل عدم وجود صلاحيات */
        .no-permission-message {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #6c757d;
        }

        [data-theme="dark"] .no-permission-message {
            background: linear-gradient(135deg, #2d3748, #4a5568);
            border-left-color: #718096;
        }

        .no-permission-message i {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .no-permission-message h3 {
            color: var(--text-color);
            margin-bottom: 10px;
        }

        .no-permission-message p {
            color: var(--text-secondary);
            margin: 0;
        }

        /* تحسين الأزرار المعطلة */
        .btn-disabled {
            opacity: 0.6;
            cursor: not-allowed !important;
            pointer-events: none;
        }

        /* تحسين مظهر الجدول */
        .table td:last-child {
            text-align: center;
            vertical-align: middle;
        }

        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
                gap: 8px;
            }

            .action-buttons .action-btn {
                width: 100%;
                min-width: auto;
            }
        }
        .page-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 3000 !important;
        }
    </style>
</head>
<body>
<?php include 'sidebar.php'; ?>
<div class="container">
    <h2>تقفيل الورديات</h2>
    
    <?php 
    // فحص إذا كان المستخدم لديه أي صلاحيات للعمليات
    $hasAnyOperationPermission = hasPermission('shift_closures', 'add_shift') || 
                                 hasPermission('shift_closures', 'edit_shift') || 
                                 hasPermission('shift_closures', 'delete_shift') || 
                                 hasPermission('shift_closures', 'confirm_shift') || 
                                 hasPermission('shift_closures', 'comprehensive_report');
    ?>
    
    <?php if (!$hasAnyOperationPermission): ?>
        <div class="no-permission-message">
            <i class="fas fa-eye"></i>
            <h3>وضع العرض فقط</h3>
            <p>يمكنك عرض بيانات الورديات فقط. ليس لديك صلاحيات لإجراء عمليات أخرى.</p>
        </div>
    <?php endif; ?>

    <div class="button-container">
        <?php if (hasPermission('shift_closures', 'add_shift')): ?>
            <button class="add-btn" onclick="openAddShiftModal()">
                <i class="fas fa-plus"></i> إضافة وردية
            </button>
        <?php endif; ?>
        
        <?php if (hasPermission('shift_closures', 'comprehensive_report')): ?>
            <button class="action-btn" onclick="showReport()">
                <i class="fas fa-file-alt"></i> عرض التقرير
            </button>
        <?php endif; ?>
    </div>
    <div class="filter-container">
        <div class="filter-item">
            <label for="start_date">من:</label>
            <input type="date" id="start_date" value="<?php echo date('Y-m-01'); ?>" onchange="fetchShifts()">
        </div>
        <div class="filter-item">
            <label for="end_date">إلى:</label>
            <input type="date" id="end_date" value="<?php echo date('Y-m-t'); ?>" onchange="fetchShifts()">
        </div>
        <div class="filter-item">
            <label for="search_query">بحث:</label>
            <input type="text" id="search_query" placeholder="ابحث عن اسم أو ملاحظة..." oninput="filterShifts()">
        </div>
        <div class="filter-item">
            <label for="status_filter">الحالة:</label>
            <select id="status_filter" onchange="filterShifts()">
                <option value="">الكل</option>
                <option value="Active">مؤكدة</option>
                <option value="Inactive">غير نشطة</option>
            </select>
        </div>
        <div class="filter-item">
            <label for="shift_type_filter">نوع الوردية:</label>
            <select id="shift_type_filter" onchange="filterShifts()">
                <option value="">الكل</option>
                <option value="morning">صباحية</option>
                <option value="night">مسائية</option>
            </select>
        </div>
    </div>
    <table class="table">
        <thead>
            <tr>
                <th>التاريخ</th>
                <th>الشخص</th>
                <th>نوع الوردية</th>
                <th>الحالة</th>
                <th>النقدي</th>
                <th>الملاحظات</th>
                <th>المشتريات</th>
                <th>تاريخ التسجيل</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody id="shift_table_body">
            <tr><td colspan="9">جاري التحميل...</td></tr>
        </tbody>
    </table>
    <div class="card-container" id="shiftCardsContainer">
        <?php
        if (!empty($shiftCards)) {
            foreach($shiftCards as $card) {
                echo "<div class='card'>".$card['content']."</div>";
            }
        }
        ?>
    </div>
</div>

<!-- نافذة التعديل (المودال) -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeEditModal()">&times;</span>
        <h2>تعديل تفاصيل الوردية</h2>
        <form method="POST" action="edit_shift_closure.php" enctype="multipart/form-data">
            <input type="hidden" name="closure_id" id="edit_closure_id">
            <div class="form-group">
                <label for="edit_shift_type" class="form-label">نوع الوردية:</label>
                <select name="shift_type" id="edit_shift_type" class="input-field" required>
                    <option value="morning">صباحية</option>
                    <option value="night">مسائية</option>
                </select>
            </div>
            <div class="form-group">
                <label for="edit_shift_amount" class="form-label">النقدي:</label>
                <input type="number" step="0.01" name="shift_amount" id="edit_shift_amount" class="input-field" placeholder="أدخل المبلغ الجديد" required>
            </div>
            <div class="form-group">
                <label for="edit_notes" class="form-label">الملاحظات:</label>
                <textarea name="notes" id="edit_notes" class="input-field" placeholder="أدخل الملاحظات" rows="4"></textarea>
            </div>
            <div class="form-group">
                <label for="edit_purchases" class="form-label">المشتريات:</label>
                <input type="number" step="0.01" name="purchases" id="edit_purchases" class="input-field" placeholder="أدخل المشتريات" required>
            </div>
            <button type="submit" class="action-btn">حفظ التعديلات</button>
        </form>
    </div>
</div>

<!-- نافذة إضافة وردية جديدة -->
<div id="addShiftModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeAddShiftModal()">&times;</span>
        <h2>إضافة وردية جديدة</h2>
        <form method="POST" action="add_shift_closure.php" enctype="multipart/form-data">
            <input type="hidden" name="store_id" value="<?php echo $store_id; ?>">
            <input type="hidden" name="account_id" value="<?php echo $_SESSION['account_id']; ?>">
            <div class="form-group">
                <label for="new_shift_date" class="form-label">تاريخ الوردية:</label>
                <input type="date" name="shift_date" id="new_shift_date" class="input-field" required>
            </div>
            <div class="form-group">
                <label for="new_shift_type" class="form-label">نوع الوردية:</label>
                <select name="shift_type" id="new_shift_type" class="input-field" required>
                    <option value="morning">صباحية</option>
                    <option value="night">مسائية</option>
                </select>
            </div>
            <div class="form-group">
                <label for="new_shift_amount" class="form-label">النقدي:</label>
                <input type="number" step="0.01" name="shift_amount" id="new_shift_amount" class="input-field" placeholder="أدخل المبلغ" required>
            </div>
            <div class="form-group">
                <label for="new_purchases" class="form-label">المشتريات:</label>
                <input type="number" step="0.01" name="purchases" id="new_purchases" class="input-field" placeholder="أدخل المشتريات" required>
            </div>
            <div class="form-group">
                <label for="new_notes" class="form-label">الملاحظات:</label>
                <textarea name="notes" id="new_notes" class="input-field" placeholder="أدخل الملاحظات" rows="4"></textarea>
            </div>
            <button type="submit" class="action-btn">إضافة الوردية</button>
        </form>
    </div>
</div>

<script>
    // متغيرات الصلاحيات
    const permissions = {
        view: <?php echo hasPermission('shift_closures', 'view') ? 'true' : 'false'; ?>,
        add_shift: <?php echo hasPermission('shift_closures', 'add_shift') ? 'true' : 'false'; ?>,
        edit_shift: <?php echo hasPermission('shift_closures', 'edit_shift') ? 'true' : 'false'; ?>,
        delete_shift: <?php echo hasPermission('shift_closures', 'delete_shift') ? 'true' : 'false'; ?>,
        confirm_shift: <?php echo hasPermission('shift_closures', 'confirm_shift') ? 'true' : 'false'; ?>,
        comprehensive_report: <?php echo hasPermission('shift_closures', 'comprehensive_report') ? 'true' : 'false'; ?>
    };

    let allShifts = []; // Store all shift data for client-side filtering

    function fetchShifts() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        fetch(`filter_shift_closures.php?store_id=<?php echo urlencode($encrypted_store_id); ?>&start_date=${startDate}&end_date=${endDate}`)
            .then(response => response.json())
            .then(data => {
                allShifts = data; // Store the fetched data
                filterShifts(); // Filter and render the data
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء جلب البيانات.'
                });
            });
    }

    function filterShifts() {
        const sd = document.getElementById('start_date').value;
        const ed = document.getElementById('end_date').value;
        const search = document.getElementById('search_query').value.toLowerCase();
        const statusFilter = document.getElementById('status_filter').value; // "" or "Active" or "Inactive"
        const typeFilter = document.getElementById('shift_type_filter').value; // "" or "morning" or "night"

        const start = sd ? new Date(sd) : null;
        const end = ed ? new Date(ed) : null;

        const filtered = allShifts.filter(item => {
            // 1) Date filtering
            const itemDate = new Date(item.shift_date);
            if (start && itemDate < start) return false;
            if (end && itemDate > end) return false;

            // 2) Search filtering (username or notes)
            const inSearch =
                item.username.toLowerCase().includes(search) ||
                (item.notes && item.notes.toLowerCase().includes(search));
            if (!inSearch) return false;

            // 3) Status filtering
            if (statusFilter && item.status !== statusFilter) return false;

            // 4) Shift type filtering
            if (typeFilter && item.shift_type !== typeFilter) return false;

            return true;
        });

        renderShifts(filtered); // Render the filtered data
    }

    function showReport() {
        if (!permissions.comprehensive_report) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لعرض التقارير'
            });
            return;
        }

        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        fetch(`fetch_report_data.php?store_id=<?php echo $encrypted_store_id; ?>&start_date=${startDate}&end_date=${endDate}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديد الوضع الحالي (فاتح أم مظلم)
                    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
                    
                    // ألوان متكيفة مع الوضع
                    const colors = {
                        background: isDarkMode ? '#21262d' : '#f8f9fa',
                        text: isDarkMode ? '#e6edf3' : '#333333',
                        title: isDarkMode ? '#58a6ff' : '#3498db',
                        totalBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#e8f5e8',
                        totalBorder: isDarkMode ? '#28a745' : '#4CAF50',
                        totalText: isDarkMode ? '#4caf50' : '#2e7d32',
                        cardBorder: isDarkMode ? '#30363d' : 'transparent',
                        shadow: isDarkMode ? '0 4px 15px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
                        confirmedBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#d4edda',
                        confirmedBorder: isDarkMode ? '#28a745' : '#155724',
                        confirmedText: isDarkMode ? '#4caf50' : '#155724',
                        pendingBg: isDarkMode ? 'linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%)' : '#fff3cd',
                        pendingBorder: isDarkMode ? '#ffc107' : '#856404',
                        pendingText: isDarkMode ? '#f7cc47' : '#856404',
                        morningBg: isDarkMode ? 'linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%)' : '#e3f2fd',
                        morningBorder: isDarkMode ? '#3498db' : '#1565c0',
                        morningText: isDarkMode ? '#58a6ff' : '#1565c0',
                        nightBg: isDarkMode ? 'linear-gradient(135deg, rgba(155, 89, 182, 0.2) 0%, rgba(155, 89, 182, 0.1) 100%)' : '#f3e5f5',
                        nightBorder: isDarkMode ? '#9b59b6' : '#7b1fa2',
                        nightText: isDarkMode ? '#bc8dbf' : '#7b1fa2'
                    };

                    Swal.fire({
                        title: '<i class="fas fa-cash-register" style="color: #28a745;"></i> تقرير تقفيل الورديات الشامل',
                        html: `
                            <div style="text-align: right; padding: 25px; font-family: 'Cairo', sans-serif;">
                                <div style="text-align: center; margin-bottom: 25px;">
                                    <h3 style="color: ${colors.title}; margin-bottom: 8px; font-weight: 700;">
                                        <i class="fas fa-chart-line" style="margin-left: 10px;"></i>
                                        ملخص الورديات
                                    </h3>
                                    <p style="color: ${colors.text}; opacity: 0.8; margin: 0; font-size: 16px;">
                                        <?php echo htmlspecialchars($store_name); ?>
                                    </p>
                                    <p style="color: ${colors.text}; opacity: 0.6; margin: 5px 0 0 0; font-size: 14px;">
                                        من ${startDate} إلى ${endDate}
                                    </p>
                                    <div style="width: 60px; height: 3px; background: ${colors.title}; margin: 15px auto; border-radius: 2px;"></div>
                                </div>
                                
                                <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                                    <div class="shift-report-card" style="background: ${colors.background}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.cardBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                                        <div style="display: flex; align-items: center; justify-content: space-between;">
                                            <div>
                                                <div style="color: ${colors.text}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي عدد الورديات</div>
                                                <div style="color: ${colors.text}; font-size: 24px; font-weight: 700;">${data.total_shifts} وردية</div>
                                            </div>
                                            <div class="report-icon" style="background: linear-gradient(135deg, #3498db, #2980b9); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);">
                                                <i class="fas fa-list" style="color: white; font-size: 20px;"></i>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="shift-report-card" style="background: ${colors.morningBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.morningBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                                        <div style="display: flex; align-items: center; justify-content: space-between;">
                                            <div>
                                                <div style="color: ${colors.morningText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">الورديات الصباحية</div>
                                                <div style="color: ${colors.morningText}; font-size: 20px; font-weight: 700;">${data.morning_shifts} وردية</div>
                                                <div style="color: ${colors.morningText}; font-size: 16px; margin-top: 5px;">النقدي: ${data.morning_amount.toFixed(2)} جنيه</div>
                                            </div>
                                            <div class="report-icon" style="background: linear-gradient(135deg, #3498db, #2980b9); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);">
                                                <i class="fas fa-sun" style="color: white; font-size: 20px;"></i>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="shift-report-card" style="background: ${colors.nightBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.nightBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                                        <div style="display: flex; align-items: center; justify-content: space-between;">
                                            <div>
                                                <div style="color: ${colors.nightText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">الورديات المسائية</div>
                                                <div style="color: ${colors.nightText}; font-size: 20px; font-weight: 700;">${data.night_shifts} وردية</div>
                                                <div style="color: ${colors.nightText}; font-size: 16px; margin-top: 5px;">النقدي: ${data.night_amount.toFixed(2)} جنيه</div>
                                            </div>
                                            <div class="report-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);">
                                                <i class="fas fa-moon" style="color: white; font-size: 20px;"></i>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="shift-report-card" style="background: ${colors.confirmedBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.confirmedBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                                        <div style="display: flex; align-items: center; justify-content: space-between;">
                                            <div>
                                                <div style="color: ${colors.confirmedText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">الورديات المؤكدة</div>
                                                <div style="color: ${colors.confirmedText}; font-size: 20px; font-weight: 700;">${data.confirmed_shifts} وردية</div>
                                            </div>
                                            <div class="report-icon" style="background: linear-gradient(135deg, #28a745, #20c997); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                                                <i class="fas fa-check-circle" style="color: white; font-size: 20px;"></i>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="shift-report-card" style="background: ${colors.pendingBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.pendingBorder}; box-shadow: ${colors.shadow}; transition: all 0.3s ease;">
                                        <div style="display: flex; align-items: center; justify-content: space-between;">
                                            <div>
                                                <div style="color: ${colors.pendingText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">الورديات المعلقة</div>
                                                <div style="color: ${colors.pendingText}; font-size: 20px; font-weight: 700;">${data.pending_shifts} وردية</div>
                                            </div>
                                            <div class="report-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);">
                                                <i class="fas fa-clock" style="color: white; font-size: 20px;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                                    <div style="background: ${colors.totalBg}; padding: 25px; border-radius: 15px; border: 2px solid ${colors.totalBorder}; text-align: center; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, transparent 70%); pointer-events: none;"></div>
                                        <div style="position: relative; z-index: 1;">
                                            <div style="color: ${colors.totalText}; font-size: 16px; margin-bottom: 10px; font-weight: 600;">
                                                <i class="fas fa-money-bill-wave" style="margin-left: 8px;"></i>
                                                إجمالي النقدي
                                            </div>
                                            <div style="color: ${colors.totalText}; font-size: 32px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                ${data.total_amount.toFixed(2)} جنيه
                                            </div>
                                            <div style="width: 80px; height: 2px; background: ${colors.totalBorder}; margin: 15px auto; border-radius: 1px;"></div>
                                            <div style="color: ${colors.totalText}; font-size: 12px; opacity: 0.8;">
                                                مجموع النقدي في جميع الورديات
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div style="background: ${colors.background}; padding: 25px; border-radius: 15px; border: 2px solid ${colors.title}; text-align: center; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(52, 152, 219, 0.1) 0%, transparent 70%); pointer-events: none;"></div>
                                        <div style="position: relative; z-index: 1;">
                                            <div style="color: ${colors.title}; font-size: 16px; margin-bottom: 10px; font-weight: 600;">
                                                <i class="fas fa-shopping-cart" style="margin-left: 8px;"></i>
                                                إجمالي المشتريات
                                            </div>
                                            <div style="color: ${colors.title}; font-size: 32px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                ${data.total_purchases.toFixed(2)} جنيه
                                            </div>
                                            <div style="width: 80px; height: 2px; background: ${colors.title}; margin: 15px auto; border-radius: 1px;"></div>
                                            <div style="color: ${colors.title}; font-size: 12px; opacity: 0.8;">
                                                مجموع المشتريات في جميع الورديات
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="info-section" style="margin-top: 20px; padding: 15px; background: ${isDarkMode ? 'rgba(88, 166, 255, 0.1)' : 'rgba(52, 152, 219, 0.1)'}; border-radius: 10px; border: 1px dashed ${colors.title}; animation: fadeInUp 0.5s ease-out;">
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                        <i class="fas fa-info-circle" style="color: ${colors.title}; font-size: 16px;"></i>
                                        <span style="color: ${colors.title}; font-weight: 600; font-size: 14px;">معلومات إضافية</span>
                                    </div>
                                    <div style="color: ${colors.text}; font-size: 13px; line-height: 1.5; opacity: 0.9;">
                                        • يتم تحديث هذا التقرير تلقائياً عند إضافة أو تعديل الورديات<br>
                                        • جميع المبالغ محسوبة بالجنيه المصري<br>
                                        • التقرير يشمل الورديات في الفترة المحددة فقط<br>
                                        • يمكن تغيير الفترة الزمنية من خلال مرشحات التاريخ أعلى الصفحة
                                    </div>
                                </div>
                            </div>
                        `,
                        icon: null,
                        confirmButtonText: 'إغلاق',
                        confirmButtonColor: '#28a745',
                        width: '750px',
                        customClass: {
                            popup: isDarkMode ? 'swal2-dark' : '',
                            title: isDarkMode ? 'swal2-title-dark' : '',
                            content: isDarkMode ? 'swal2-content-dark' : ''
                        },
                        background: isDarkMode ? '#0d1117' : '#ffffff',
                        color: isDarkMode ? '#e6edf3' : '#333333',
                        showClass: {
                            popup: 'animate__animated animate__fadeInUp animate__faster'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutDown animate__faster'
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.error || 'حدث خطأ أثناء جلب بيانات التقرير.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم.'
                });
            });
    }

    function adjustViewMode() {
        const cardContainer = document.getElementById("shiftCardsContainer");
        const table = document.querySelector(".table");
        if (window.innerWidth <= 768) {
            cardContainer.style.display = "flex";
            table.style.display = "none";
        } else {
            cardContainer.style.display = "none";
            table.style.display = "table";
        }
    }

    function renderShifts(shifts) {
        const tableBody = document.getElementById('shift_table_body');
        const cardContainer = document.getElementById('shiftCardsContainer');
        tableBody.innerHTML = '';
        cardContainer.innerHTML = '';

        if (shifts.length > 0) {
            shifts.forEach(row => {
                // Format dates and times to Arabic locale
                const formattedDate = new Date(row.shift_date).toLocaleDateString('ar-EG');
                const formattedCreatedAt = new Date(row.created_at).toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                });

                // Determine the status class based on the status
                const statusClass = row.status === 'Active' ? 'device-status green' : 'device-status red';
                const statusText = row.status === 'Active' ? 'مؤكدة' : 'معلقة';

                // إنشاء أزرار العمليات حسب الصلاحيات
                let actionButtons = '';
                if (permissions.edit_shift) {
                    actionButtons += `<button class="action-btn edit-btn" onclick='openEditModal(${JSON.stringify(row)})' title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>`;
                }
                if (permissions.delete_shift) {
                    actionButtons += `<button class="action-btn" onclick='deleteShift(${row.closure_id})' title="حذف">
                        <i class="fas fa-trash-alt"></i>
                    </button>`;
                }

                // تحديد إمكانية النقر على الحالة
                const statusClickHandler = permissions.confirm_shift ? `onclick="handleStatusClick(${row.closure_id}, '${row.status}')"` : '';
                const statusCursor = permissions.confirm_shift ? 'cursor: pointer;' : 'cursor: default;';

                // Render table rows with Arabic dates and times
                tableBody.innerHTML += `
                    <tr>
                        <td>${formattedDate}</td>
                        <td>${row.username}</td>
                        <td>${row.shift_type === 'morning' ? 'صباحية' : 'مسائية'}</td>
                        <td>
                            <span class="${statusClass}" ${statusClickHandler} style="${statusCursor}" title="${permissions.confirm_shift ? 'انقر للتأكيد' : ''}">
                                ${statusText}
                            </span>
                        </td>
                        <td>${row.shift_amount}</td>
                        <td>${row.notes || ''}</td>
                        <td>${row.purchases !== null ? row.purchases : ''}</td>
                        <td>${formattedCreatedAt}</td>
                        <td>
                            <div class='action-buttons'>
                                ${actionButtons}
                            </div>
                        </td>
                    </tr>
                `;

                // إنشاء أزرار العمليات للبطاقات حسب الصلاحيات
                let cardActionButtons = '';
                if (permissions.edit_shift) {
                    cardActionButtons += `<button class="edit-btn" onclick='openEditModal(${JSON.stringify(row)})' title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>`;
                }
                if (permissions.delete_shift) {
                    cardActionButtons += `<button class="delete-btn" onclick='deleteShift(${row.closure_id})' title="حذف">
                        <i class="fas fa-trash-alt"></i>
                    </button>`;
                }

                // تحديد عرض الحالة للبطاقات
                const cardStatusDisplay = permissions.confirm_shift ? 
                    `<span class="${statusClass}" onclick="handleStatusClick(${row.closure_id}, '${row.status}')" style="cursor: pointer;" title="ان��ر للتأكيد">${statusText}</span>` :
                    `<span class="${statusClass}" style="cursor: default;">${statusText}</span>`;

                // Render cards for smaller screens with Arabic dates and times
                cardContainer.innerHTML += `
                    <div class="card">
                        <h3>${formattedDate} - ${row.username}</h3>
                        <p><strong>نوع الوردية:</strong> ${row.shift_type === 'morning' ? 'صباحية' : 'مسائية'}</p>
                        <p><strong>الحالة:</strong> ${cardStatusDisplay}</p>
                        <p><strong>النقدي:</strong> ${row.shift_amount}</p>
                        <p><strong>المشتريات:</strong> ${row.purchases !== null ? row.purchases : ''}</p>
                        <p><strong>الملاحظات:</strong> ${row.notes || 'لا توجد ملاحظات'}</p>
                        <p><strong>تاريخ التسجيل:</strong> ${formattedCreatedAt}</p>
                        ${cardActionButtons ? `<div class="actions">${cardActionButtons}</div>` : ''}
                    </div>
                `;
            });

            // Show table for larger screens and cards for smaller screens
            if (window.innerWidth <= 768) {
                cardContainer.style.display = 'flex';
                tableBody.parentElement.style.display = 'none';
            } else {
                cardContainer.style.display = 'none';
                tableBody.parentElement.style.display = 'table';
            }
        } else {
            tableBody.innerHTML = '<tr><td colspan="9">لا توجد بيانات حالياً</td></tr>';
            cardContainer.innerHTML = '<p>لا توجد بيانات حالياً</p>';
            tableBody.parentElement.style.display = 'none';
            cardContainer.style.display = (window.innerWidth <= 768 ? 'flex' : 'block');
        }

        adjustViewMode(); // Ensure the correct view mode is applied after rendering
    }

    // Adjust view on window resize
    window.addEventListener('resize', adjustViewMode);

    function handleStatusClick(closureId, currentStatus) {
        if (!permissions.confirm_shift) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لتأكيد الورديات'
            });
            return;
        }

        if (currentStatus === 'Inactive') {
            Swal.fire({
                title: 'تأكيد الحالة',
                text: 'هل تريد تأكيد هذه الوردية إلى "مؤكدة"؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#d33',
                confirmButtonText: 'نعم، تأكيد',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    confirmShiftStatus(closureId);
                }
            });
        }
    }

    function confirmShiftStatus(closureId) {
        fetch('confirm_shift_status.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `closure_id=${closureId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم التأكيد!',
                    text: 'تم تأكيد حالة الوردية بنجاح.',
                    showConfirmButton: false,
                    timer: 2000
                });
                fetchShifts(); // Refresh the data
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.error || 'حدث خطأ أثناء تأكيد حالة الوردية.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء الاتصال بالخادم.'
            });
        });
    }

    document.addEventListener('DOMContentLoaded', () => {
        fetchShifts();
        adjustViewMode();
    });

    function openEditModal(row) {
        if (!permissions.edit_shift) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لتعديل الورديات'
            });
            return;
        }

        document.getElementById('edit_closure_id').value = row.closure_id;
        document.getElementById('edit_shift_type').value = row.shift_type === 'morning' ? 'morning' : 'night';
        document.getElementById('edit_shift_amount').value = row.shift_amount;
        document.getElementById('edit_notes').value = row.notes || '';
        document.getElementById('edit_purchases').value = row.purchases;
        document.getElementById('editModal').classList.add('active');
    }

    function closeEditModal() {
        document.getElementById('editModal').classList.remove('active');
    }

    function openAddShiftModal() {
        if (!permissions.add_shift) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لإضافة ورديات جديدة'
            });
            return;
        }

        document.getElementById('addShiftModal').classList.add('active');
    }

    function closeAddShiftModal() {
        document.getElementById('addShiftModal').classList.remove('active');
    }

    // Close modals when clicking outside
    window.onclick = function(event) {
        if (event.target.classList && event.target.classList.contains('modal')) {
            event.target.classList.remove('active');
        }
    }

    document.querySelector('form[action="edit_shift_closure.php"]').addEventListener('submit', function(event) {
        event.preventDefault();
        
        if (!permissions.edit_shift) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لتعديل الورديات'
            });
            return;
        }

        const formData = new FormData(this);

        fetch('edit_shift_closure.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'نجاح',
                    text: data.message,
                    showConfirmButton: false,
                    timer: 3000
                });
                closeEditModal();
                setTimeout(() => location.reload(), 3000); // Reload after 3 seconds
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.error || 'حدث خطأ أثناء تعديل تفاصيل الوردية.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء الاتصال بالخادم.'
            });
        });
    });

    function deleteShift(closureId) {
        if (!permissions.delete_shift) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لحذف الورديات'
            });
            return;
        }

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'لن تتمكن من التراجع عن هذا الإجراء!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('delete_shift_closure.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `closure_id=${closureId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الحذف!',
                            text: 'تم حذف الوردية بنجاح.',
                            showConfirmButton: false,
                            timer: 2000
                        });
                        setTimeout(() => location.reload(), 2000); // Reload after 2 seconds
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.error || 'حدث خطأ أثناء حذف الوردية.'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء الاتصال بالخادم.'
                    });
                });
            }
        });
    }

    function submitAddShiftForm(event) {
        event.preventDefault();
        
        if (!permissions.add_shift) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لإضافة ورديات جديدة'
            });
            return;
        }

        const formData = new FormData(event.target);

        fetch('add_shift_closure.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'نجاح',
                    text: data.message,
                    showConfirmButton: false,
                    timer: 2000
                });
                closeAddShiftModal();
                setTimeout(() => location.reload(), 2000); // Reload after 2 seconds
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.error || 'حدث خطأ أثناء إضافة الوردية.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء الاتصال بالخادم.'
            });
        });
    }

    document.querySelector('form[action="add_shift_closure.php"]').addEventListener('submit', submitAddShiftForm);

    document.addEventListener("DOMContentLoaded", function() {
        if(window.innerWidth <= 600) {
            var container = document.getElementById("shiftCardsContainer");
            if(container.children.length === 0) {
                container.style.display = "flex";
            }
        }
    });
</script>
<?php include 'notifications.php'; ?>

</body>
</html>

<?php
$conn->close();

?>
