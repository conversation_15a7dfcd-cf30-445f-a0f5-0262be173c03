<?php
/**
 * ملف منع التخزين المؤقت (Cache Buster)
 * يتم تضمينه في بداية أي ملف PHP لمنع الكاش
 * 
 * الاستخدام: include 'no_cache.php';
 */

// منع التخزين المؤقت بجميع الطرق الممكنة
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0, private');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
header('ETag: "' . md5(microtime() . rand()) . '"');

// منع الكاش للمتصفحات المختلفة
header('X-Accel-Expires: 0');
header('Surrogate-Control: no-store');
header('Vary: *');

// إضافة timestamp للجلسة لضمان التحديث
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['_cache_buster'] = microtime(true);

/**
 * دالة لإضافة timestamp للروابط
 */
function addCacheBuster($url) {
    $separator = strpos($url, '?') !== false ? '&' : '?';
    return $url . $separator . '_t=' . microtime(true) . '&_r=' . rand(1000, 9999);
}

/**
 * دالة لإنشاء meta tags منع الكاش
 */
function getCachePreventionMetaTags() {
    $timestamp = microtime(true);
    return '
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Last-Modified" content="' . gmdate('D, d M Y H:i:s') . ' GMT">
    <meta name="cache-buster" content="' . $timestamp . '">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
    ';
}

/**
 * دالة لإنشاء JavaScript منع الكاش
 */
function getCachePreventionJS() {
    $timestamp = microtime(true);
    return "
    <script>
    // منع التخزين المؤقت - تم التحميل في: " . date('Y-m-d H:i:s') . "
    (function() {
        'use strict';
        
        // إضافة timestamp فريد لكل صفحة
        window.CACHE_BUSTER = '" . $timestamp . "';
        
        // منع التخزين المؤقت لجميع طلبات fetch
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                // إضافة timestamp للرابط
                if (typeof args[0] === 'string') {
                    const separator = args[0].includes('?') ? '&' : '?';
                    args[0] = args[0] + separator + '_t=' + Date.now() + '&_cb=' + Math.random();
                }
                
                // إضافة headers لمنع الكاش
                if (args[1]) {
                    args[1].cache = 'no-cache';
                    args[1].headers = {
                        ...args[1].headers,
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-Cache-Buster': Date.now()
                    };
                } else {
                    args[1] = {
                        cache: 'no-cache',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-Cache-Buster': Date.now()
                        }
                    };
                }
                return originalFetch.apply(this, args);
            };
        }
        
        // منع التخزين المؤقت لـ XMLHttpRequest
        if (window.XMLHttpRequest) {
            const originalOpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url, ...rest) {
                const separator = url.includes('?') ? '&' : '?';
                const newUrl = url + separator + '_t=' + Date.now() + '&_cb=' + Math.random();
                return originalOpen.call(this, method, newUrl, ...rest);
            };
        }
        
        // منع التخزين المؤقت لـ jQuery إذا كان متاحاً
        if (typeof jQuery !== 'undefined') {
            jQuery.ajaxSetup({
                cache: false,
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                beforeSend: function(xhr, settings) {
                    const separator = settings.url.includes('?') ? '&' : '?';
                    settings.url = settings.url + separator + '_t=' + Date.now() + '&_cb=' + Math.random();
                }
            });
        }
        
        // إضافة timestamp لجميع الروابط والنماذج
        function addTimestampToElements() {
            // الروابط
            const links = document.querySelectorAll('a[href]');
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && !href.includes('javascript:') && !href.includes('#') && !href.includes('_t=')) {
                    const separator = href.includes('?') ? '&' : '?';
                    link.setAttribute('href', href + separator + '_t=' + Date.now());
                }
            });
            
            // النماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                // إضافة حقل مخفي للـ timestamp
                let timestampInput = form.querySelector('input[name=\"_timestamp\"]');
                if (!timestampInput) {
                    timestampInput = document.createElement('input');
                    timestampInput.type = 'hidden';
                    timestampInput.name = '_timestamp';
                    form.appendChild(timestampInput);
                }
                timestampInput.value = Date.now();
                
                // إضافة cache buster للـ action
                const action = form.getAttribute('action');
                if (action && !action.includes('_t=')) {
                    const separator = action.includes('?') ? '&' : '?';
                    form.setAttribute('action', action + separator + '_t=' + Date.now());
                }
            });
        }
        
        // تشغيل عند تحميل الصفحة
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', addTimestampToElements);
        } else {
            addTimestampToElements();
        }
        
        // مراقبة التغييرات في DOM وإضافة timestamp للعناصر الجديدة
        if (window.MutationObserver) {
            const observer = new MutationObserver(function(mutations) {
                let shouldUpdate = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldUpdate = true;
                    }
                });
                if (shouldUpdate) {
                    setTimeout(addTimestampToElements, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
        
        // منع الكاش للصور
        const images = document.querySelectorAll('img[src]');
        images.forEach(img => {
            const src = img.getAttribute('src');
            if (src && !src.includes('_t=')) {
                const separator = src.includes('?') ? '&' : '?';
                img.setAttribute('src', src + separator + '_t=' + Date.now());
            }
        });
        
        // تحديث العنوان لضمان عدم الكاش
        const originalTitle = document.title;
        setInterval(function() {
            const now = new Date();
            document.title = originalTitle + ' - ' + now.toLocaleTimeString('ar-EG');
        }, 5000);
        
        // مسح الكاش المحلي
        if ('caches' in window) {
            caches.keys().then(function(names) {
                names.forEach(function(name) {
                    caches.delete(name);
                });
            });
        }
        
        // مسح localStorage و sessionStorage للصفحة الحالية
        try {
            const currentPage = window.location.pathname;
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (key && key.includes(currentPage)) {
                    localStorage.removeItem(key);
                }
            }
        } catch (e) {
            // تجاهل الأخطاء
        }
        
        console.log('🚫 Cache Prevention System Loaded - ' + new Date().toISOString());
        console.log('📄 Page Cache Buster ID: ' + window.CACHE_BUSTER);
        
    })();
    </script>
    ";
}

/**
 * دالة لإنشاء CSS منع الكاش
 */
function getCachePreventionCSS() {
    return "
    <style>
    /* منع التخزين المؤقت للخلفيات والصور */
    body::before {
        content: '';
        display: none;
        background-image: url('data:image/svg+xml;base64," . base64_encode('<svg xmlns="http://www.w3.org/2000/svg"><text>' . microtime(true) . '</text></svg>') . "');
    }
    
    /* تأثير بصري للتحديثات */
    .cache-updated {
        animation: cacheUpdateFlash 0.5s ease-in-out;
    }
    
    @keyframes cacheUpdateFlash {
        0% { background-color: transparent; }
        50% { background-color: rgba(40, 167, 69, 0.2); }
        100% { background-color: transparent; }
    }
    
    /* مؤشر تحديث الصفحة */
    .page-refresh-indicator {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        z-index: 10000;
        display: none;
    }
    </style>
    ";
}

// تسجيل وقت تحميل الملف
if (!defined('NO_CACHE_LOADED')) {
    define('NO_CACHE_LOADED', microtime(true));
    
    // إضافة معلومات debug في حالة التطوير
    if (isset($_GET['debug_cache']) || (defined('DEBUG_MODE') && DEBUG_MODE)) {
        error_log("No Cache System Loaded at: " . date('Y-m-d H:i:s') . " for " . $_SERVER['REQUEST_URI']);
    }
}
?>
