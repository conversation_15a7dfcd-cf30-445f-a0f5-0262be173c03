<?php

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('inventory', 'access');

$key = getenv('ENCRYPTION_KEY');

// Fetch stores
$stores_sql = "SELECT * FROM stores";
$stores_result = $conn->query($stores_sql);
$stores = [];
if ($stores_result->num_rows > 0) {
    while($store = $stores_result->fetch_assoc()) {
        $stores[] = $store;
    }
}

// معالجة طلبات POST مع فحص الصلاحيات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['store_id']) && isset($_POST['inventory_date'])) {
        // فحص صلاحية إضافة جرد جديد
        if (!hasPermission('inventory', 'add_inventory')) {
            echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لإضافة جرد جديد']);
            exit();
        }

        $store_id = $_POST['store_id'];
        $inventory_date = $_POST['inventory_date'];

        $stmt = $conn->prepare("INSERT INTO monthly_inventory (store_id, inventory_date) VALUES (?, ?)");
        $stmt->bind_param("is", $store_id, $inventory_date);
        $stmt->execute();
        $inventory_id = $stmt->insert_id;
        $stmt->close();

        // Fetch store name
        $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
        $stmt->bind_param("i", $store_id);
        $stmt->execute();
        $stmt->bind_result($store_name);
        $stmt->fetch();
        $stmt->close();

        // Create JSON file
        $folder_path = __DIR__ . '/inventory';
        if (!is_dir($folder_path)) {
            mkdir($folder_path, 0755, true);
        }

        header("Location: inventory_details.php?inventory_id=" . urlencode(encrypt($inventory_id, $key)));
        exit();
    }
}

$encrypted_store_id = $_GET['store_id'] ?? $_SESSION['store_id'] ?? null;
$store_name = '';
$where_clause = '';
$store_id = null;

if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $where_clause = "WHERE monthly_inventory.store_id = ?";
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}

// Update SQL query to include the WHERE clause if a store ID is provided
$sql = "SELECT monthly_inventory.*, stores.name AS store_name 
        FROM monthly_inventory 
        JOIN stores ON monthly_inventory.store_id = stores.store_id 
        $where_clause
        ORDER BY monthly_inventory.created_at DESC";

$stmt = $conn->prepare($sql);

if ($where_clause) {
    $stmt->bind_param("i", $store_id);
}

$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $store_name ? "جرد فرع " . htmlspecialchars($store_name) : "إدارة الجرد"; ?></title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
</head>
<body>
    <?php include 'sidebar.php'; ?>
<div class="container">
    <!-- Header Section with Stats -->
    <div class="page-header">
        <div class="header-content">
            <div class="title-section">
                <h2><i class="fas fa-warehouse"></i> إدارة الجرد</h2>
                <?php if ($store_name): ?>
                    <p class="subtitle">
                        <i class="fas fa-store"></i> 
                        جرد فرع: <strong><?php echo htmlspecialchars($store_name); ?></strong>
                    </p>
                <?php else: ?>
                    <p class="subtitle">إدارة وتتبع جرد المخازن والفروع</p>
                <?php endif; ?>
            </div>
            
            <!-- زر إضافة جرد جديد مع فحص الصلاحيات -->
            <?php if (hasPermission('inventory', 'add_inventory')): ?>
                <button class="add-btn modern-btn" id="addInventoryBtn" title="إضافة جرد جديد">
                    <i class="fas fa-plus"></i>
                    <span>جرد جديد</span>
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-container">
        <?php
        // إحصائيات الجرد للفرع المحدد أو جميع الفروع
        $stats_sql = "SELECT 
            COUNT(*) as total_inventories,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_inventories,
            SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as completed_inventories
            FROM monthly_inventory";
        
        if ($where_clause) {
            $stats_sql .= " WHERE store_id = ?";
            $stats_stmt = $conn->prepare($stats_sql);
            $stats_stmt->bind_param("i", $store_id);
            $stats_stmt->execute();
            $stats_result = $stats_stmt->get_result();
            $stats = $stats_result->fetch_assoc();
            $stats_stmt->close();
        } else {
            $stats_result = $conn->query($stats_sql);
            $stats = $stats_result->fetch_assoc();
        }
        ?>
        
        <div class="stat-card total">
            <div class="stat-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats['total_inventories']; ?></h3>
                <p>إجمالي الجرد</p>
            </div>
        </div>
        
        <div class="stat-card pending">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats['pending_inventories']; ?></h3>
                <p>قيد التنفيذ</p>
            </div>
        </div>
        
        <div class="stat-card completed">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats['completed_inventories']; ?></h3>
                <p>مكتمل</p>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="controls-section">
        <div class="date-search-container">
            <div class="date-range-box">
                <i class="fas fa-calendar-alt"></i>
                <input type="date" id="dateFrom" class="date-input" title="من تاريخ">
                <span class="date-separator">إلى</span>
                <input type="date" id="dateTo" class="date-input" title="إلى تاريخ">
                <button type="button" id="clearDates" class="clear-dates-btn" title="مسح التواريخ">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <div class="filters-container">
            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="pending">قيد التنفيذ</option>
                <option value="confirmed">مكتمل</option>
            </select>
            
            <div class="export-dropdown">
                <button type="button" id="exportBtn" class="export-btn" title="تصدير البيانات">
                    <i class="fas fa-download"></i>
                    <span>تصدير</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="export-menu" id="exportMenu">
                    <a href="#" onclick="exportData('excel')" class="export-option">
                        <i class="fas fa-file-excel"></i>
                        <span>Excel</span>
                    </a>
                    <a href="#" onclick="exportData('pdf')" class="export-option">
                        <i class="fas fa-file-pdf"></i>
                        <span>PDF</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Section -->
    <div class="table-section">
        <div class="table-header">
            <h3><i class="fas fa-list"></i> قائمة الجرد</h3>
            <div class="table-actions">
                <button type="button" id="refreshBtn" class="refresh-btn" title="تحديث البيانات">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
        <table id="inventoryTable" class="modern-table">
            <thead>
                <tr>
                    <th class="sortable" data-column="inventory_id">
                        <span>رقم الجرد</span>
                        <i class="fas fa-sort sort-icon"></i>
                    </th>
                    <th class="sortable" data-column="store_name">
                        <span>الفرع</span>
                        <i class="fas fa-sort sort-icon"></i>
                    </th>
                    <th class="sortable" data-column="inventory_date">
                        <span>تاريخ الجرد</span>
                        <i class="fas fa-sort sort-icon"></i>
                    </th>
                    <th class="sortable" data-column="created_at">
                        <span>تاريخ الإنشاء</span>
                        <i class="fas fa-sort sort-icon"></i>
                    </th>
                    <th class="sortable" data-column="status">
                        <span>الحالة</span>
                        <i class="fas fa-sort sort-icon"></i>
                    </th>
                    <?php if (hasPermission('inventory', 'open_inventory_details')): ?>
                        <th class="actions-col">عرض التفاصيل</th>
                    <?php endif; ?>
                    <?php if (hasPermission('inventory', 'open_inventory')): ?>
                        <th class="actions-col">فتح الجرد</th>
                    <?php endif; ?>
                    <?php if (hasPermission('inventory', 'delete_inventory')): ?>
                        <th class="actions-col">الإجراءات</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody id="inventoryTableBody">
                <?php
                if ($result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $encrypted_inventory_id = urlencode(encrypt($row['inventory_id'], $key));
                        $status_text = $row['status'] === 'pending' ? 'قيد التنفيذ' : 'مكتمل';
                        $status_class = $row['status'] === 'pending' ? 'status-pending' : 'status-completed';
                        
                        echo "<tr class='inventory-row' data-store='{$row['store_name']}' data-status='{$row['status']}' data-date='{$row['inventory_date']}' data-search='{$row['inventory_id']} {$row['store_name']} {$row['inventory_date']}'>
                                <td class='id-cell'>
                                    <span class='id-badge'>{$row['inventory_id']}</span>
                                </td>
                                <td class='store-cell'>
                                    <div class='store-info'>
                                        <i class='fas fa-store'></i>
                                        <span>{$row['store_name']}</span>
                                    </div>
                                </td>
                                <td class='date-cell'>{$row['inventory_date']}</td>
                                <td class='date-cell'>{$row['created_at']}</td>
                                <td class='status-cell'>
                                    <span class='status-badge {$status_class}'>
                                        <i class='fas fa-" . ($row['status'] === 'pending' ? 'clock' : 'check-circle') . "'></i>
                                        {$status_text}
                                    </span>
                                </td>";
                        
                        // عرض التفاصيل
                        if (hasPermission('inventory', 'open_inventory_details')) {
                            echo "<td><a href='inventory_summary.php?inventory_id={$encrypted_inventory_id}' class='action-btn' title='عرض تفاصيل الجرد'>
                                    <i class='fas fa-eye'></i>
                                  </a></td>";
                        }
                        
                        // فتح الجرد
                        if (hasPermission('inventory', 'open_inventory')) {
                            echo "<td><a href='inventory_details.php?inventory_id={$encrypted_inventory_id}' class='action-btn' title='فتح الجرد'>
                                    <i class='fas fa-folder-open'></i>
                                  </a></td>";
                        }
                        
                        // الإجراءات (حذف)
                        if (hasPermission('inventory', 'delete_inventory')) {
                            echo "<td>";
                            if ($row['status'] === 'pending') {
                                echo "<div class='action-buttons'>
                                        <button class='delete-btn' onclick='deleteInventory(\"{$encrypted_inventory_id}\", \"{$row['store_name']}\")' title='حذف الجرد (متاح فقط للجرد قيد التنفيذ)'>
                                            <i class='fas fa-trash-alt'></i>
                                        </button>
                                      </div>";
                            } else {
                                echo "<span class='no-action' title='لا يمكن حذف الجرد المكتمل'>-</span>";
                            }
                            echo "</td>";
                        }
                        
                        echo "</tr>";
                    }
                } else {
                    $colspan = 5; // العدد الأساسي للأعمدة
                    if (hasPermission('inventory', 'open_inventory_details')) $colspan++;
                    if (hasPermission('inventory', 'open_inventory')) $colspan++;
                    if (hasPermission('inventory', 'delete_inventory')) $colspan++;
                    
                    $no_data_message = $store_name 
                        ? "لا توجد سجلات جرد لفرع " . htmlspecialchars($store_name) . ". ابدأ بإضافة جرد جديد لهذا الفرع."
                        : "لم يتم إنشاء أي جرد بعد. ابدأ بإضافة جرد جديد.";
                    
                    echo "<tr class='no-data-row'>
                            <td colspan='{$colspan}'>
                                <div class='no-data-message'>
                                    <i class='fas fa-clipboard-list'></i>
                                    <h3>لا توجد سجلات جرد</h3>
                                    <p>{$no_data_message}</p>
                                </div>
                            </td>
                          </tr>";
                }
                ?>
            </tbody>
        </table>
        
        <!-- No Results Message (Hidden by default) -->
        <div id="noResultsMessage" class="no-results-message" style="display: none;">
            <div class="no-results-content">
                <i class="fas fa-search"></i>
                <h3>لا توجد نتائج</h3>
                <p>لم يتم العثور على أي جرد يطابق معايير البحث.</p>
                <button type="button" id="clearFilters" class="clear-filters-btn">
                    <i class="fas fa-times"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة جرد جديد -->
<?php if (hasPermission('inventory', 'add_inventory')): ?>
<div id="addInventoryModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>إضافة جرد جديد</h2>
        <form method="POST" action="">
            <label for="current_store">الفرع الحالي:</label>
            <div class="current-store-display">
                <i class="fas fa-store"></i>
                <span class="store-name-text">
                    <?php 
                    if ($store_name) {
                        echo htmlspecialchars($store_name);
                    } else {
                        echo "لم يتم تحديد فرع";
                    }
                    ?>
                </span>
            </div>
            
            <!-- حقل مخفي لإرسال معرف الفرع -->
            <input type="hidden" name="store_id" value="<?php echo $store_id ? $store_id : ''; ?>" required>
            
            <label for="inventory_date">تاريخ الجرد:</label>
            <input type="date" name="inventory_date" class="input-field" required>
            
            <button type="submit" class="add-btn" <?php echo !$store_id ? 'disabled' : ''; ?>>
                <?php echo $store_id ? 'بدء الجرد' : 'يجب تحديد فرع أولاً'; ?>
            </button>
        </form>
    </div>
</div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    // متغيرات الصلاحيات
    const permissions = {
        add_inventory: <?php echo hasPermission('inventory', 'add_inventory') ? 'true' : 'false'; ?>,
        open_inventory: <?php echo hasPermission('inventory', 'open_inventory') ? 'true' : 'false'; ?>,
        open_inventory_details: <?php echo hasPermission('inventory', 'open_inventory_details') ? 'true' : 'false'; ?>,
        delete_inventory: <?php echo hasPermission('inventory', 'delete_inventory') ? 'true' : 'false'; ?>
    };

    // متغيرات البحث والتصفية
    let currentSort = { column: null, direction: 'asc' };
    let allRows = [];

    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        initializeTable();
        setupEventListeners();
        setupKeyboardShortcuts();
    });

    function initializeTable() {
        const tableBody = document.getElementById('inventoryTableBody');
        allRows = Array.from(tableBody.querySelectorAll('.inventory-row'));
    }

    function setupEventListeners() {
        // البحث بالتاريخ
        const dateFromInput = document.getElementById('dateFrom');
        const dateToInput = document.getElementById('dateTo');
        const clearDates = document.getElementById('clearDates');
        
        dateFromInput.addEventListener('change', performDateSearch);
        dateToInput.addEventListener('change', performDateSearch);
        clearDates.addEventListener('click', clearDateFilters);
        
        // إظهار/إخفاء زر مسح التواريخ
        dateFromInput.addEventListener('input', toggleClearDatesBtn);
        dateToInput.addEventListener('input', toggleClearDatesBtn);

        // التصفية
        document.getElementById('statusFilter').addEventListener('change', performDateSearch);

        // الترتيب
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => sortTable(header.dataset.column));
        });

        // أزرار الإجراءات
        document.getElementById('refreshBtn').addEventListener('click', refreshData);
        document.getElementById('exportBtn').addEventListener('click', toggleExportMenu);
        document.getElementById('clearFilters')?.addEventListener('click', clearDateFilters);
        
        // إغلاق قائمة التصدير عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.export-dropdown')) {
                document.getElementById('exportMenu').classList.remove('show');
            }
        });

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(updateStats, 30000);
    }

    function setupKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + D للتركيز على تاريخ البداية
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                document.getElementById('dateFrom').focus();
            }
            
            // Ctrl/Cmd + N لإضافة جرد جديد
            if ((e.ctrlKey || e.metaKey) && e.key === 'n' && permissions.add_inventory) {
                e.preventDefault();
                document.getElementById('addInventoryBtn')?.click();
            }
            
            // Ctrl/Cmd + R لمسح الفلاتر
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                clearDateFilters();
            }
            
            // F5 للتحديث
            if (e.key === 'F5') {
                e.preventDefault();
                refreshData();
            }
        });
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function performDateSearch() {
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;
        const statusFilter = document.getElementById('statusFilter').value;

        let visibleCount = 0;

        allRows.forEach(row => {
            const rowDate = row.dataset.date; // تاريخ الجرد
            const statusData = row.dataset.status;

            // فحص التاريخ
            let matchesDate = true;
            if (dateFrom && rowDate < dateFrom) matchesDate = false;
            if (dateTo && rowDate > dateTo) matchesDate = false;

            // فحص الحالة
            const matchesStatus = !statusFilter || statusData === statusFilter;

            const isVisible = matchesDate && matchesStatus;
            
            row.style.display = isVisible ? '' : 'none';
            if (isVisible) visibleCount++;
        });

        // إظهار/إخفاء رسالة عدم وجود نتائج
        toggleNoResultsMessage(visibleCount === 0 && allRows.length > 0);
        
        // إظهار/إخفاء زر مسح التواريخ
        toggleClearDatesBtn();
    }

    function clearDateFilters() {
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('clearDates').classList.remove('show');
        performDateSearch();
    }

    function toggleClearDatesBtn() {
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;
        const clearBtn = document.getElementById('clearDates');
        
        if (dateFrom || dateTo) {
            clearBtn.classList.add('show');
        } else {
            clearBtn.classList.remove('show');
        }
    }

    function toggleNoResultsMessage(show) {
        const noResultsMessage = document.getElementById('noResultsMessage');
        const table = document.getElementById('inventoryTable');
        
        if (show) {
            table.style.display = 'none';
            noResultsMessage.style.display = 'block';
        } else {
            table.style.display = '';
            noResultsMessage.style.display = 'none';
        }
    }

    function sortTable(column) {
        const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';
        currentSort = { column, direction };

        // ��حديث أيقونات الترتيب
        document.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
        });

        const currentHeader = document.querySelector(`[data-column="${column}"] .sort-icon`);
        currentHeader.className = `fas fa-sort-${direction === 'asc' ? 'up' : 'down'} sort-icon active`;

        // ترتيب الصفوف
        allRows.sort((a, b) => {
            let aValue, bValue;

            switch (column) {
                case 'inventory_id':
                    aValue = parseInt(a.querySelector('.id-badge').textContent);
                    bValue = parseInt(b.querySelector('.id-badge').textContent);
                    break;
                case 'store_name':
                    aValue = a.dataset.store;
                    bValue = b.dataset.store;
                    break;
                case 'inventory_date':
                case 'created_at':
                    aValue = new Date(a.querySelector('.date-cell').textContent);
                    bValue = new Date(b.querySelector('.date-cell').textContent);
                    break;
                case 'status':
                    aValue = a.dataset.status;
                    bValue = b.dataset.status;
                    break;
                default:
                    return 0;
            }

            if (aValue < bValue) return direction === 'asc' ? -1 : 1;
            if (aValue > bValue) return direction === 'asc' ? 1 : -1;
            return 0;
        });

        // إعادة ترتيب الصفوف في DOM
        const tableBody = document.getElementById('inventoryTableBody');
        allRows.forEach(row => tableBody.appendChild(row));
    }

    function refreshData() {
        const refreshBtn = document.getElementById('refreshBtn');
        const icon = refreshBtn.querySelector('i');
        
        icon.classList.add('fa-spin');
        
        setTimeout(() => {
            location.reload();
        }, 500);
    }

    function updateStats() {
        // بناء رابط الإحصائيات
        let statsUrl = 'get_inventory_stats.php';
        
        // إضافة معرف الفرع إذا كان محدداً
        const urlParams = new URLSearchParams(window.location.search);
        const storeId = urlParams.get('store_id');
        if (storeId) {
            statsUrl += '?store_id=' + encodeURIComponent(storeId);
        }
        
        // تحديث الإحصائيات عبر AJAX
        fetch(statsUrl)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.querySelector('.stat-card.total h3').textContent = data.total;
                    document.querySelector('.stat-card.pending h3').textContent = data.pending;
                    document.querySelector('.stat-card.completed h3').textContent = data.completed;
                }
            })
            .catch(error => console.error('Error updating stats:', error));
    }

    function toggleExportMenu() {
        const exportMenu = document.getElementById('exportMenu');
        exportMenu.classList.toggle('show');
    }

    function exportData(type) {
        // إغلاق القائمة
        document.getElementById('exportMenu').classList.remove('show');
        
        const exportBtn = document.getElementById('exportBtn');
        const originalText = exportBtn.innerHTML;
        
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
        exportBtn.disabled = true;

        if (type === 'pdf') {
            // تصدير الصفحة الحالية كـ PDF
            exportCurrentPageToPDF();
        } else {
            // تصدير Excel من الخادم
            exportFromServer(type);
        }

        function exportCurrentPageToPDF() {
            try {
                // إخفاء العناصر غير المرغوب فيها مؤقتاً
                const elementsToHide = [
                    '.sidebar',
                    '.export-dropdown', 
                    '.controls-section .filters-container',
                    '.table-actions',
                    '.refresh-btn'
                ];
                
                elementsToHide.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.classList.add('pdf-export-hide'));
                });

                // تحديد اسم الملف
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const urlParams = new URLSearchParams(window.location.search);
                const storeId = urlParams.get('store_id');
                let filename = `inventory_report_${timestamp}.pdf`;
                
                if (storeId) {
                    filename = `inventory_store_${storeId}_${timestamp}.pdf`;
                }

                // استخدام html2canvas لتحويل الصفحة لصورة
                html2canvas(document.body, {
                    scale: 1,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    width: window.innerWidth,
                    height: document.body.scrollHeight
                }).then(canvas => {
                    // إنشاء PDF
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');
                    
                    const imgWidth = 210; // عرض A4 بالمليمتر
                    const pageHeight = 295; // ارتفاع A4 بالمليمتر
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    let heightLeft = imgHeight;
                    let position = 0;

                    // إضافة الصورة للصفحة الأولى
                    pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;

                    // إضافة صفحات إضافية إذا لزم الأمر
                    while (heightLeft >= 0) {
                        position = heightLeft - imgHeight;
                        pdf.addPage();
                        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
                        heightLeft -= pageHeight;
                    }

                    // تحميل الملف
                    pdf.save(filename);

                    // إظهار رسالة النجاح
                    Swal.fire({
                        icon: 'success',
                        title: 'تم التصدير بنجاح!',
                        text: 'تم تحميل ملف PDF',
                        timer: 3000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });

                }).catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في التصدير',
                        text: 'حدث خطأ أثناء تصدير PDF',
                        confirmButtonText: 'حسناً'
                    });
                }).finally(() => {
                    // إعادة إظهار العناصر المخفية
                    elementsToHide.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => el.classList.remove('pdf-export-hide'));
                    });
                    
                    exportBtn.innerHTML = originalText;
                    exportBtn.disabled = false;
                });

            } catch (error) {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في التصدير',
                    text: 'حدث خطأ أثناء تصدير PDF',
                    confirmButtonText: 'حسناً'
                });
                
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;
            }
        }

        function exportFromServer(type) {
            // بناء رابط التصدير
            let exportUrl = 'export_inventory.php?type=' + type;
            
            // إضافة معرف الفرع إذا كان محدداً
            const urlParams = new URLSearchParams(window.location.search);
            const storeId = urlParams.get('store_id');
            if (storeId) {
                exportUrl += '&store_id=' + encodeURIComponent(storeId);
            }

            // تصدير الملف من الخادم
            fetch(exportUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل في تصدير البيانات');
                    }
                    return response.blob();
                })
                .then(blob => {
                    // إنشاء رابط تحميل مؤقت
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    
                    // تحديد اسم الملف
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                    let filename = `inventory_report_${timestamp}.xls`;
                    
                    if (storeId) {
                        filename = `inventory_store_${storeId}_${timestamp}.xls`;
                    }
                    
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    
                    // تنظيف الرابط المؤقت
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    // إظهار رسالة النجاح
                    Swal.fire({
                        icon: 'success',
                        title: 'تم التصدير بنجاح!',
                        text: 'تم تحميل ملف Excel',
                        timer: 3000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في التصدير',
                        text: 'حدث خطأ أثناء تصدير البيانات',
                        confirmButtonText: 'حسناً'
                    });
                })
                .finally(() => {
                    exportBtn.innerHTML = originalText;
                    exportBtn.disabled = false;
                });
        }
    }

    // إدارة نافذة إضافة الجرد
    <?php if (hasPermission('inventory', 'add_inventory')): ?>
    var addInventoryModal = document.getElementById("addInventoryModal");
    var addInventoryBtn = document.getElementById("addInventoryBtn");
    var addInventorySpan = document.getElementsByClassName("close")[0];

    addInventoryBtn.onclick = function() {
        addInventoryModal.classList.add("active");
    }

    addInventorySpan.onclick = function() {
        addInventoryModal.classList.remove("active");
    }

    window.onclick = function(event) {
        if (event.target.classList.contains("modal")) {
            event.target.classList.remove("active");
        }
    }
    <?php endif; ?>

    // دالة حذف الجرد
    function deleteInventory(inventoryId, storeName) {
        if (!permissions.delete_inventory) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لحذف الجرد'
            });
            return;
        }

        Swal.fire({
            title: 'تأكيد حذف الجرد',
            html: `
                <div style="text-align: right; line-height: 1.6;">
                    <p><strong>هل أنت متأكد من حذف جرد فرع "${storeName}"؟</strong></p>
                    <div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; border-right: 4px solid #ffc107;">
                        <p style="margin: 0; color: #856404; font-size: 14px;">
                            <i class="fas fa-exclamation-triangle"></i> 
                            <strong>شروط الحذف:</strong>
                        </p>
                        <ul style="margin: 5px 0 0 20px; color: #856404; font-size: 13px;">
                            <li>الجرد يجب أن يكون في حالة "قيد التنفيذ"</li>
                            <li>لا توجد أصناف مرتبطة بالجرد</li>
                        </ul>
                    </div>
                    <p style="color: #dc3545; font-weight: bold;">هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash-alt"></i> نعم، احذف الجرد',
            cancelButtonText: '<i class="fas fa-times"></i> إلغاء',
            width: '500px'
        }).then((result) => {
            if (result.isConfirmed) {
                // إرسال طلب الحذف للملف المنفصل
                fetch('delete_inventory.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `inventory_id=${inventoryId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم حذف الجرد بنجاح!',
                            text: data.message,
                            confirmButtonText: 'حسناً',
                            confirmButtonColor: '#28a745',
                            timer: 3000,
                            timerProgressBar: true
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'فشل في حذف الجرد',
                            html: `
                                <div style="text-align: right;">
                                    <p>${data.message}</p>
                                    <div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin-top: 10px; border-right: 4px solid #dc3545;">
                                        <p style="margin: 0; color: #721c24; font-size: 13px;">
                                            <i class="fas fa-info-circle"></i> 
                                            تأكد من أن الجرد في حالة "قيد التنفيذ" ولا يحتوي على أصناف
                                        </p>
                                    </div>
                                </div>
                            `,
                            confirmButtonText: 'حسناً',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء حذف الجرد'
                    });
                });
            }
        });
    }

    // فحص الصلاحيات عند النقر على الروابط
    document.addEventListener('DOMContentLoaded', function() {
        // فحص روابط عرض التفاصيل
        document.querySelectorAll('a[href*="inventory_summary.php"]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (!permissions.open_inventory_details) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'غير مسموح',
                        text: 'ليس لديك صلاحية لعرض تفاصيل الجرد'
                    });
                }
            });
        });

        // فحص روابط فتح الجرد
        document.querySelectorAll('a[href*="inventory_details.php"]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (!permissions.open_inventory) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'غير مسموح',
                        text: 'ليس لديك صلاحية لفتح الجرد'
                    });
                }
            });
        });
    });
</script>

<style>
    /* تحسينات شاملة للتصميم مع دعم الوضع المظلم */
    * {
        font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* متغيرات إضافية للوضع المظلم */
    :root {
        --text-secondary: #6c757d;
        --border-color: #dee2e6;
        --success-bg: #d1ecf1;
        --error-bg: #f8d7da;
        --hover-color: rgba(0, 0, 0, 0.05);
    }

    [data-theme="dark"] {
        --text-secondary: #8b949e;
        --border-color: #30363d;
        --success-bg: rgba(40, 167, 69, 0.1);
        --error-bg: rgba(220, 53, 69, 0.1);
        --hover-color: rgba(255, 255, 255, 0.05);
    }

    .page-header {
        background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .page-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        box-shadow: 0 8px 32px rgba(30, 58, 138, 0.4);
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .title-section h2 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 12px;
        color: white;
    }

    .subtitle {
        margin: 0;
        opacity: 0.9;
        font-size: 16px;
    }

    .modern-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 10px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .modern-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    
    /* بطاقات الإحصائيات */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: var(--color-secondary);
        padding: 25px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        gap: 20px;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--color-primary), #5c6bc0);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .stat-card {
        background: var(--color-secondary);
        border-color: var(--border-color);
    }

    [data-theme="dark"] .stat-card:hover {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .stat-card.total::before { background: linear-gradient(90deg, #3498db, #2980b9); }
    .stat-card.pending::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
    .stat-card.completed::before { background: linear-gradient(90deg, #27ae60, #2ecc71); }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
    }

    .stat-card.total .stat-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
    .stat-card.pending .stat-icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
    .stat-card.completed .stat-icon { background: linear-gradient(135deg, #27ae60, #2ecc71); }

    .stat-content h3 {
        margin: 0 0 5px 0;
        font-size: 32px;
        font-weight: 700;
        color: var(--color-fg);
    }

    .stat-content p {
        margin: 0;
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* قسم التحكم */
    .controls-section {
        background: var(--color-secondary);
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
        border: 1px solid var(--border-color);
    }

    .date-search-container {
        flex: 1;
        min-width: 350px;
    }

    .date-range-box {
        position: relative;
        display: flex;
        align-items: center;
        background: var(--color-primary);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 8px 15px;
        gap: 10px;
        transition: all 0.3s ease;
    }

    .date-range-box:focus-within {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
    }

    .date-range-box i {
        color: var(--text-secondary);
        font-size: 16px;
    }

    .date-input {
        border: none;
        background: transparent;
        color: var(--color-fg);
        font-size: 14px;
        font-family: 'Cairo', sans-serif;
        font-weight: 500;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        min-width: 140px;
    }

    .date-input:focus {
        outline: none;
        background: var(--hover-color);
    }

    .date-separator {
        color: var(--text-secondary);
        font-weight: 600;
        font-size: 14px;
        padding: 0 5px;
    }

    .clear-dates-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: all 0.3s ease;
        display: none;
    }

    .clear-dates-btn:hover {
        background: var(--hover-color);
        color: var(--color-fg);
    }

    .clear-dates-btn.show {
        display: block;
    }

    [data-theme="dark"] .date-range-box {
        background: var(--color-secondary);
        border-color: var(--border-color);
    }

    [data-theme="dark"] .date-range-box:focus-within {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.2);
    }

    [data-theme="dark"] .date-input {
        color: var(--color-fg);
    }

    [data-theme="dark"] .date-input:focus {
        background: var(--hover-color);
    }

    [data-theme="dark"] .clear-dates-btn:hover {
        background: var(--hover-color);
        color: var(--color-fg);
    }

    .filters-container {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-select {
        padding: 10px 15px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--color-primary);
        color: var(--color-fg);
        font-size: 14px;
        min-width: 150px;
        transition: all 0.3s ease;
    }

    .filter-select:focus {
        border-color: var(--color-primary);
        outline: none;
    }

    [data-theme="dark"] .filter-select {
        background: var(--color-secondary);
        color: var(--color-fg);
        border-color: var(--border-color);
    }

    [data-theme="dark"] .filter-select:focus {
        border-color: var(--color-primary);
    }

    .export-btn, .refresh-btn {
        background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .export-btn:hover, .refresh-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
    }

    /* قائمة الت��دير المنسدلة */
    .export-dropdown {
        position: relative;
        display: inline-block;
    }

    .export-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: var(--color-secondary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        min-width: 150px;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        margin-top: 5px;
    }

    .export-menu.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .export-option {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        color: var(--color-fg);
        text-decoration: none;
        transition: all 0.3s ease;
        border-bottom: 1px solid var(--border-color);
    }

    .export-option:last-child {
        border-bottom: none;
    }

    .export-option:hover {
        background: var(--hover-color);
        color: var(--color-primary);
        text-decoration: none;
    }

    .export-option i {
        font-size: 16px;
        width: 20px;
        text-align: center;
    }

    .export-option span {
        font-weight: 500;
    }

    [data-theme="dark"] .export-menu {
        background: var(--color-secondary);
        border-color: var(--border-color);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    [data-theme="dark"] .export-option {
        color: var(--color-fg);
        border-color: var(--border-color);
    }

    [data-theme="dark"] .export-option:hover {
        background: var(--hover-color);
        color: var(--color-primary);
    }

    /* قسم الجدول */
    .table-section {
        background: var(--color-secondary);
        border-radius: 15px;
        overflow: hidden;
        border: 1px solid var(--border-color);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    [data-theme="dark"] .table-section {
        background: var(--color-secondary);
        border-color: var(--border-color);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }

    .table-header {
        background: var(--color-primary);
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid var(--border-color);
    }

    .table-header h3 {
        margin: 0;
        color: white;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .table-actions {
        display: flex;
        gap: 10px;
    }

    .refresh-btn {
        padding: 8px 12px;
        font-size: 14px;
    }

    /* الجدول المحسن */
    .modern-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--color-secondary);
    }

    .modern-table th {
        background: var(--color-primary);
        color: white;
        padding: 15px 20px;
        text-align: right;
        font-weight: 600;
        border-bottom: 2px solid var(--border-color);
        position: relative;
        user-select: none;
    }

    .modern-table th.sortable {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .modern-table th.sortable:hover {
        background: var(--hover-color);
    }

    .sort-icon {
        margin-right: 8px;
        opacity: 0.6;
        transition: all 0.3s ease;
    }

    .sort-icon.active {
        opacity: 1;
        color: var(--color-primary);
    }

    .modern-table td {
        padding: 15px 20px;
        border-bottom: 1px solid var(--border-color);
        color: var(--color-fg);
        vertical-align: middle;
    }

    .inventory-row {
        transition: all 0.3s ease;
    }

    .inventory-row:hover {
        background: var(--hover-color);
        transform: scale(1.01);
    }

    [data-theme="dark"] .inventory-row:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .id-badge {
        background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
    }

    .store-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .store-info i {
        color: var(--color-primary);
        font-size: 16px;
    }

    /* أنماط حالة الجرد المحسنة */
    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        min-width: 100px;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .status-badge i {
        font-size: 14px;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border: 1px solid #ffeaa7;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
    }

    .status-completed {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border: 1px solid #c3e6cb;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }

    [data-theme="dark"] .status-pending {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%);
        color: #ffc107;
        border-color: rgba(255, 193, 7, 0.3);
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    }

    [data-theme="dark"] .status-completed {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%);
        color: #28a745;
        border-color: rgba(40, 167, 69, 0.3);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    /* رسائل عدم وجود بيانات */
    .no-data-message, .no-results-content {
        text-align: center;
        padding: 60px 20px;
        color: var(--text-secondary);
    }

    .no-data-message i, .no-results-content i {
        font-size: 64px;
        margin-bottom: 20px;
        opacity: 0.5;
        color: var(--color-primary);
    }

    .no-data-message h3, .no-results-content h3 {
        font-size: 24px;
        margin-bottom: 10px;
        color: var(--color-fg);
    }

    .no-data-message p, .no-results-content p {
        font-size: 16px;
        line-height: 1.6;
        max-width: 400px;
        margin: 0 auto 20px auto;
    }

    .no-results-message {
        background: var(--color-secondary);
        border-radius: 15px;
        margin: 20px 0;
        border: 1px solid var(--border-color);
    }

    .clear-filters-btn {
        background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .clear-filters-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
    }

    /* أنماط أزرار الإجراءات */
    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
    }

    .delete-btn {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border: 2px solid #dc3545;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }

    .delete-btn:hover {
        background: linear-gradient(135deg, #c82333, #a71e2a);
        border-color: #c82333;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
    }

    .delete-btn i {
        color: white;
        font-weight: bold;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* نمط للعناصر غير المتاحة */
    .no-action {
        color: #6c757d;
        font-style: italic;
        font-size: 14px;
        display: inline-block;
        padding: 8px 12px;
    }

    [data-theme="dark"] .no-action {
        color: #adb5bd;
    }

    /* تحسينات للوضع المظلم */
    [data-theme="dark"] .delete-btn {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border-color: #dc3545;
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.4);
    }

    [data-theme="dark"] .delete-btn:hover {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border-color: #e74c3c;
        box-shadow: 0 4px 8px rgba(231, 76, 60, 0.5);
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        background-color: var(--color-primary);
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        background-color: var(--color-primary-dark);
        transform: translateY(-1px);
        text-decoration: none;
        color: white;
    }

    .action-btn i {
        font-size: 16px;
    }

    /* تحسين تصميم النموذج */
    .modal-content form label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: var(--color-fg);
        font-size: 14px;
    }

    .modal-content form .input-field {
        margin-bottom: 15px;
        background: var(--color-primary);
        color: var(--color-fg);
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 12px 15px;
        font-family: 'Cairo', sans-serif;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .modal-content form .input-field:focus {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
        outline: none;
    }

    [data-theme="dark"] .modal-content form .input-field {
        background: var(--color-secondary);
        border-color: var(--border-color);
    }

    [data-theme="dark"] .modal-content form .input-field:focus {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.2);
    }

    /* تصميم مربع عرض الفرع الحالي */
    .current-store-display {
        background: linear-gradient(135deg, var(--color-primary) 0%, #5c6bc0 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
        border: 2px solid transparent;
        box-shadow: 0 4px 15px rgba(88, 166, 255, 0.2);
        transition: all 0.3s ease;
    }

    .current-store-display i {
        font-size: 20px;
        color: rgba(255, 255, 255, 0.9);
    }

    .store-name-text {
        font-size: 16px;
        font-weight: 600;
        flex: 1;
    }

    [data-theme="dark"] .current-store-display {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        border-color: var(--border-color);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    /* تصميم الزر المعطل */
    .add-btn:disabled {
        background: #6c757d !important;
        cursor: not-allowed;
        opacity: 0.6;
        transform: none !important;
        box-shadow: none !important;
    }

    .add-btn:disabled:hover {
        background: #6c757d !important;
        transform: none !important;
        box-shadow: none !important;
    }

    /* تحسينات الاستجابة للشاشات المختلفة */
    @media (max-width: 1200px) {
        .stats-container {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .controls-section {
            flex-direction: column;
            align-items: stretch;
        }
        
        .date-search-container {
            min-width: auto;
        }
        
        .filters-container {
            justify-content: center;
        }
    }

    @media (max-width: 768px) {
        .page-header {
            padding: 20px;
        }
        
        .header-content {
            flex-direction: column;
            text-align: center;
        }
        
        .title-section h2 {
            font-size: 24px;
        }
        
        .stats-container {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-card {
            padding: 20px;
            flex-direction: column;
            text-align: center;
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
        
        .stat-content h3 {
            font-size: 24px;
        }
        
        .controls-section {
            padding: 20px;
        }
        
        .date-search-container {
            min-width: auto;
            width: 100%;
        }
        
        .date-range-box {
            flex-direction: column;
            gap: 15px;
            padding: 15px;
        }
        
        .date-input {
            min-width: auto;
            width: 100%;
            text-align: center;
        }
        
        .date-separator {
            order: 2;
            font-size: 16px;
            font-weight: 700;
        }
        
        .clear-dates-btn {
            position: absolute;
            top: 10px;
            left: 10px;
        }
        
        .filters-container {
            flex-direction: column;
            width: 100%;
        }
        
        .filter-select {
            min-width: auto;
            width: 100%;
        }
        
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .modern-table th,
        .modern-table td {
            padding: 10px 15px;
            font-size: 14px;
        }
        
        .action-buttons {
            flex-direction: column;
            gap: 5px;
        }
        
        .action-btn, .delete-btn {
            padding: 8px 12px;
            font-size: 12px;
        }
        
        .status-badge {
            padding: 6px 12px;
            font-size: 11px;
            min-width: 80px;
        }
        
        .id-badge {
            padding: 4px 8px;
            font-size: 11px;
        }
        
        .store-info {
            flex-direction: column;
            gap: 5px;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .container {
            padding: 10px;
        }
        
        .page-header {
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .title-section h2 {
            font-size: 20px;
        }
        
        .subtitle {
            font-size: 14px;
        }
        
        .stats-container {
            grid-template-columns: 1fr;
            gap: 10px;
        }
        
        .stat-card {
            padding: 15px;
        }
        
        .controls-section {
            padding: 15px;
        }
        
        .search-box input {
            padding: 10px 40px 10px 12px;
            font-size: 14px;
        }
        
        .modern-table th,
        .modern-table td {
            padding: 8px 10px;
            font-size: 12px;
        }
        
        .table-header {
            padding: 15px 20px;
        }
        
        .table-header h3 {
            font-size: 16px;
        }
    }

    /* تحسينات إضافية للوضع المظلم */
    [data-theme="dark"] .controls-section {
        background: var(--color-secondary);
        border-color: var(--border-color);
    }

    [data-theme="dark"] .clear-btn:hover {
        background: var(--hover-color);
        color: var(--color-fg);
    }

    [data-theme="dark"] .no-results-message {
        background: var(--color-secondary);
        border-color: var(--border-color);
    }

    [data-theme="dark"] .no-data-message i,
    [data-theme="dark"] .no-results-content i {
        color: var(--color-primary);
    }

    [data-theme="dark"] .no-data-message h3,
    [data-theme="dark"] .no-results-content h3 {
        color: var(--color-fg);
    }

    [data-theme="dark"] .no-data-message p,
    [data-theme="dark"] .no-results-content p {
        color: var(--text-secondary);
    }

    /* تأثيرات حركية إضافية */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    .stat-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }

    .modern-btn:active {
        animation: pulse 0.3s ease-in-out;
    }

    /* تحسين إمكانية الوصول */
    .sortable:focus,
    .modern-btn:focus,
    .export-btn:focus,
    .refresh-btn:focus {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
    }

    /* أنماط خاصة لتصدير PDF */
    @media print {
        .sidebar,
        .export-dropdown,
        .controls-section .filters-container,
        .table-actions {
            display: none !important;
        }
        
        .container {
            margin: 0 !important;
            padding: 0 !important;
            max-width: none !important;
        }
        
        .page-header {
            background: #4472C4 !important;
            color: white !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .modern-table th {
            background: #4472C4 !important;
            color: white !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .status-pending {
            background: #FFF2CC !important;
            color: #7F6000 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .status-completed {
            background: #D5E8D4 !important;
            color: #0F5132 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
    }

    /* كلاس مساعد لإخفاء العناصر أثناء تصدير PDF */
    .pdf-export-hide {
        display: none !important;
    }

    /* تحسين مؤشرات التحميل */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .loading-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .loading-spinner-large {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: var(--color-primary);
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* تحسين النصائح والتلميحات */
    .tooltip {
        position: relative;
        cursor: help;
    }

    .tooltip::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .tooltip:hover::after {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(-5px);
    }
</style>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
