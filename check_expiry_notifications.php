<?php
include 'db_connection.php';

// Fetch items that are expiring within 1 to 3 days
$query = "SELECT item_expiry.expiry_id, items.name, item_expiry.expiry_date, item_expiry.quantity, item_expiry.store_id, DATEDIFF(item_expiry.expiry_date, CURDATE()) AS days_left 
          FROM item_expiry 
          JOIN items ON item_expiry.item_id = items.item_id 
          WHERE item_expiry.expiry_date BETWEEN CURDATE() + INTERVAL 1 DAY AND CURDATE() + INTERVAL 3 DAY";
$result = $conn->query($query);

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $message = "الصنف '{$row['name']}' بكمية {$row['quantity']} سينتهي في تاريخ {$row['expiry_date']} (فاضل {$row['days_left']} يوم).";
        $store_id = $row['store_id'];
        
        // Check if a notification for this expiry_id already exists
        $check_query = "SELECT COUNT(*) FROM notifications WHERE expiry_id = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $row['expiry_id']);
        $check_stmt->execute();
        $check_stmt->bind_result($count);
        $check_stmt->fetch();
        $check_stmt->close();

        if ($count == 0) {
            // Insert notification into notifications table
            $stmt = $conn->prepare("INSERT INTO notifications (message, expiry_id, store_id) VALUES (?, ?, ?)");
            $stmt->bind_param("sii", $message, $row['expiry_id'], $store_id);
            $stmt->execute();
            $stmt->close();
        }
    }
}

// Fetch items that have expired today
$expired_query = "SELECT item_expiry.expiry_id, items.name, item_expiry.expiry_date, item_expiry.quantity, item_expiry.store_id 
                  FROM item_expiry 
                  JOIN items ON item_expiry.item_id = items.item_id 
                  WHERE item_expiry.expiry_date = CURDATE()";
$expired_result = $conn->query($expired_query);

if ($expired_result->num_rows > 0) {
    while ($row = $expired_result->fetch_assoc()) {
        $message = "الصنف '{$row['name']}' بكمية {$row['quantity']} انتهت صلاحيته في تاريخ {$row['expiry_date']}.";
        $store_id = $row['store_id'];
        
        // Insert notification into notifications table
        $stmt = $conn->prepare("INSERT INTO notifications (message, expiry_id, store_id) VALUES (?, ?, ?)");
        $stmt->bind_param("sii", $message, $row['expiry_id'], $store_id);
        $stmt->execute();
        $stmt->close();

        // Delete expired item from item_expiry table
        $delete_stmt = $conn->prepare("DELETE FROM item_expiry WHERE expiry_id = ?");
        $delete_stmt->bind_param("i", $row['expiry_id']);
        $delete_stmt->execute();
        $delete_stmt->close();
    }
}

// Fetch items that have expired
$expired_query = "SELECT item_expiry.expiry_id, items.name, item_expiry.expiry_date, item_expiry.quantity, item_expiry.store_id 
                  FROM item_expiry 
                  JOIN items ON item_expiry.item_id = items.item_id 
                  WHERE item_expiry.expiry_date < CURDATE()";
$expired_result = $conn->query($expired_query);

if ($expired_result->num_rows > 0) {
    while ($row = $expired_result->fetch_assoc()) {
        $message = "الصنف '{$row['name']}' بكمية {$row['quantity']} انتهت صلاحيته في تاريخ {$row['expiry_date']}.";
        $store_id = $row['store_id'];
        
        // Insert notification into notifications table
        $stmt = $conn->prepare("INSERT INTO notifications (message, expiry_id, store_id) VALUES (?, ?, ?)");
        $stmt->bind_param("sii", $message, $row['expiry_id'], $store_id);
        $stmt->execute();
        $stmt->close();

        // Delete expired item from item_expiry table
        $delete_stmt = $conn->prepare("DELETE FROM item_expiry WHERE expiry_id = ?");
        $delete_stmt->bind_param("i", $row['expiry_id']);
        $delete_stmt->execute();
        $delete_stmt->close();
    }
}
?>
