<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
$store_id = decrypt($encrypted_store_id, $key);

// Get recent notifications
$notifications_query = "SELECT id, message, created_at FROM notifications WHERE store_id = ? ORDER BY created_at DESC LIMIT 10";
$notifications_stmt = $conn->prepare($notifications_query);
$notifications_stmt->bind_param("i", $store_id);
$notifications_stmt->execute();
$notifications_result = $notifications_stmt->get_result();

if ($notifications_result->num_rows > 0) {
    while ($row = $notifications_result->fetch_assoc()) {
        echo '<div class="notification-item">';
        echo '<p class="notification-message">' . htmlspecialchars($row['message']) . '</p>';
        echo '<small class="notification-time">' . htmlspecialchars($row['created_at']) . '</small>';
        echo '</div>';
    }
} else {
    echo '<p>لا توجد إشعارات سابقة.</p>';
}

$notifications_stmt->close();
$conn->close();
?>
