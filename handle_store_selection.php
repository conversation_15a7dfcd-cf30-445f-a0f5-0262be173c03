<?php
session_start();

header('Content-Type: application/json'); // Set the response content type to JSON

// Check if the request method is POST and the Content-Type is application/json
if ($_SERVER['REQUEST_METHOD'] === 'POST' && strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
    // Decode the JSON payload
    $input = json_decode(file_get_contents('php://input'), true);

    if (isset($input['selected_store_id'])) {
        $_SESSION['store_id'] = $input['selected_store_id']; // Save the encrypted store ID in the session
        echo json_encode(['success' => true]);
        exit();
    }
}

echo json_encode(['success' => false, 'error' => 'Invalid request.']);
exit();
?>
