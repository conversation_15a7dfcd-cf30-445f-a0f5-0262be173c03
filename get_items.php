<?php
include 'db_connection.php';
include 'encryption_functions.php';

session_start();
$key = getenv('ENCRYPTION_KEY');
$category_id = decrypt($_GET['category_id'], $key);

// Get account_id from session if available
$account_id = isset($_SESSION['account_id']) ? decrypt($_SESSION['account_id'], $key) : null;

if ($account_id) {
    $sql = "SELECT i.*, COUNT(img.img_id) as image_count,
            (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) as is_favorite
            FROM items i 
            LEFT JOIN item_images img ON i.item_id = img.item_id 
            WHERE i.category_id = ? 
            GROUP BY i.item_id 
            ORDER BY (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) DESC, i.item_id DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iii", $account_id, $category_id, $account_id);
} else {
    $sql = "SELECT i.*, COUNT(img.img_id) as image_count, 0 as is_favorite
            FROM items i 
            LEFT JOIN item_images img ON i.item_id = img.item_id 
            WHERE i.category_id = ? 
            GROUP BY i.item_id 
            ORDER BY i.item_id DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $category_id);
}

$stmt->execute();
$result = $stmt->get_result();
$items = [];
while ($row = $result->fetch_assoc()) {
    $items[] = $row;
}
$stmt->close();

echo json_encode(['items' => $items]);
?>
