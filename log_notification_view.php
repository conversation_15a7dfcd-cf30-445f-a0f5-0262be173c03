<?php
include 'db_connection.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $notification_id = $data['notification_id'];
    $account_id = $data['account_id'];

    // Check if the notification view already exists
    $check_query = "SELECT COUNT(*) FROM notification_reads WHERE notification_id = ? AND account_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $notification_id, $account_id);
    $check_stmt->execute();
    $check_stmt->bind_result($count);
    $check_stmt->fetch();
    $check_stmt->close();

    if ($count == 0) {
        // Insert the notification view into the notification_reads table
        $stmt = $conn->prepare("INSERT INTO notification_reads (notification_id, account_id) VALUES (?, ?)");
        $stmt->bind_param("ii", $notification_id, $account_id);
        if ($stmt->execute()) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تسجيل مشاهدة الإشعار.']);
        }
        $stmt->close();
    } else {
        echo json_encode(['success' => true, 'message' => 'تم تسجيل مشاهدة الإشعار مسبقاً.']);
    }
}

$conn->close();
?>
