<?php
/**
 * تعديل تحويل رصيد - مع فحص الصلاحيات
 * الصلاحية المطلوبة: edit_transfer في وحدة balance_transfers
 */

include 'db_connection.php';
require_once 'security.php';

header('Content-Type: application/json');

// فحص صلاحية تعديل تحويل رصيد
if (!hasPermission('balance_transfers', 'edit_transfer')) {
    echo json_encode(['success' => false, 'error' => 'ليس لديك صلاحية لتعديل تحويلات الرصيد']);
    exit();
}

// Debugging: Log received input data
error_log("Received data: " . json_encode($_POST));

// Retrieve and validate input
$balance_id = isset($_POST['balance_id']) ? intval($_POST['balance_id']) : null;
$provider = isset($_POST['provider']) ? trim($_POST['provider']) : null;
$cost = isset($_POST['cost']) ? floatval($_POST['cost']) : null;
$sale_price = isset($_POST['sale_price']) ? floatval($_POST['sale_price']) : null;
$value = isset($_POST['value']) ? floatval($_POST['value']) : null;

if (!$balance_id || !$provider || !$cost || !$sale_price || !$value) {
    echo json_encode(['success' => false, 'error' => 'جميع الحقول مطلوبة.']);
    exit();
}

// Fetch all valid providers dynamically from the ENUM column
$result = $conn->query("SHOW COLUMNS FROM balance_transfers LIKE 'provider'");
$row = $result->fetch_assoc();
$enum_values = str_replace(["enum(", ")", "'"], "", $row['Type']);
$valid_providers = explode(",", $enum_values);

// Ensure valid provider
if (!in_array($provider, $valid_providers)) {
    echo json_encode(['success' => false, 'error' => 'مزود غير صالح.']);
    exit();
}

// Fetch the current balance transfer details for comparison
$current_stmt = $conn->prepare("SELECT provider, cost, sale_price, value FROM balance_transfers WHERE balance_id = ?");
$current_stmt->bind_param("i", $balance_id);
$current_stmt->execute();
$current_stmt->bind_result($current_provider, $current_cost, $current_sale_price, $current_value);
$current_stmt->fetch();
$current_stmt->close();

// Update the balance transfer
$stmt = $conn->prepare("UPDATE balance_transfers SET provider = ?, cost = ?, sale_price = ?, value = ? WHERE balance_id = ?");
$stmt->bind_param("sdddi", $provider, $cost, $sale_price, $value, $balance_id);

if ($stmt->execute()) {
    // Log the balance transfer edit action
    include 'encryption_functions.php';
    $key = getenv('ENCRYPTION_KEY');
    $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

    // Prepare a description of what was changed
    $changes = [];
    if ($provider !== $current_provider) $changes[] = "المزود من $current_provider إلى $provider";
    if ($cost != $current_cost) $changes[] = "التكلفة من $current_cost إلى $cost";
    if ($sale_price != $current_sale_price) $changes[] = "سعر البيع من $current_sale_price إلى $sale_price";
    if ($value != $current_value) $changes[] = "القيمة من $current_value إلى $value";

    $description = "تم تعديل تحويل الرصيد رقم $balance_id: " . implode(", ", $changes);

    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'update', 'balance_transfers', ?, ?)";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $balance_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    echo json_encode(['success' => true, 'message' => 'تم تعديل تحويل الرصيد بنجاح.']);
} else {
    echo json_encode(['success' => false, 'error' => 'فشل في تعديل تحويل الرصيد.']);
}

$stmt->close();
$conn->close();
?>
