<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php'; // Include the common encryption functions

if (!isset($_SESSION['account_id'])) {
    header('Location: index.php');
    exit();
}

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');
$encrypted_account_id = $_SESSION['account_id'];
$account_id = decrypt($encrypted_account_id, $key);

if (!$account_id) {
    header('Location: index.php');
    exit();
}

$sql = "SELECT status, role FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$stmt->bind_result($status, $role);
$stmt->fetch();
$stmt->close();

if ($status !== 'active' || $role !== 'admin') {
    header('Location: index.php');
    exit();
}
?>
