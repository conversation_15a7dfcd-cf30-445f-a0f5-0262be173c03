<?php
include 'db_connection.php';
include 'encryption_functions.php';

session_start();

// Get the JSON data from the request
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

if (!$data || !isset($data['token'])) {
    echo json_encode(['success' => false, 'message' => 'No token provided']);
    exit;
}

$token = $data['token'];

// Get store_id and account_id from session
$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';

if (!$encrypted_store_id || !$encrypted_account_id) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$store_id = decrypt($encrypted_store_id, $key);
$account_id = decrypt($encrypted_account_id, $key);

// Check if token already exists for this account
$check_query = "SELECT id FROM fcm_tokens WHERE account_id = ? AND token = ?";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bind_param("is", $account_id, $token);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows > 0) {
    // Token already exists, update last_updated
    $update_query = "UPDATE fcm_tokens SET last_updated = NOW() WHERE account_id = ? AND token = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("is", $account_id, $token);
    
    if ($update_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Token updated']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update token']);
    }
    $update_stmt->close();
} else {
    // Token doesn't exist, insert new record
    $insert_query = "INSERT INTO fcm_tokens (account_id, store_id, token, created_at, last_updated) VALUES (?, ?, ?, NOW(), NOW())";
    $insert_stmt = $conn->prepare($insert_query);
    $insert_stmt->bind_param("iis", $account_id, $store_id, $token);
    
    if ($insert_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Token saved']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save token']);
    }
    $insert_stmt->close();
}

$check_stmt->close();
$conn->close();
?>
