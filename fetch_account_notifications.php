<?php
include 'db_connection.php';
include 'encryption_functions.php';

session_start();

try {
    $key = getenv('ENCRYPTION_KEY');
    
    // Handle store_id
    $encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
    if (empty($encrypted_store_id)) {
        // Try to get from GET parameter
        $encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';
    }
    
    if (!empty($encrypted_store_id)) {
        $store_id = decrypt($encrypted_store_id, $key);
    } else {
        $store_id = isset($_GET['store_id']) ? (int)$_GET['store_id'] : 1; // Default fallback
    }
    
    // Handle account_id
    $encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';
    if (!empty($encrypted_account_id)) {
        $account_id = decrypt($encrypted_account_id, $key);
    } else {
        $account_id = isset($_GET['account_id']) ? (int)$_GET['account_id'] : 1; // Default fallback
    }
    
} catch (Exception $e) {
    // Handle decryption errors
    error_log("Decryption error in fetch_account_notifications.php: " . $e->getMessage());
    
    // Set default values or get from GET parameters
    $store_id = isset($_GET['store_id']) ? (int)$_GET['store_id'] : 1;
    $account_id = isset($_GET['account_id']) ? (int)$_GET['account_id'] : 1;
}

// Get limit parameter (default 50, max 100)
$limit = isset($_GET['limit']) ? min((int)$_GET['limit'], 100) : 50;

// Get time filter parameter (default 1 month)
$time_filter = isset($_GET['time_filter']) ? $_GET['time_filter'] : '1 MONTH';
$allowed_filters = ['1 WEEK', '1 MONTH', '3 MONTH', '6 MONTH', '1 YEAR'];
if (!in_array($time_filter, $allowed_filters)) {
    $time_filter = '1 MONTH';
}

// Combined query to fetch notifications with unread status in one query
$query = "SELECT 
            n.id, 
            n.message, 
            n.created_at, 
            n.status,
            CASE 
                WHEN nr.id IS NULL THEN 'unread'
                ELSE 'read'
            END as read_status,
            (SELECT COUNT(*) 
             FROM notifications n2 
             LEFT JOIN notification_reads nr2 ON n2.id = nr2.notification_id AND nr2.account_id = ?
             WHERE n2.store_id = ? AND nr2.id IS NULL AND n2.created_at >= DATE_SUB(NOW(), INTERVAL $time_filter)
            ) as unread_count,
            (SELECT COUNT(*) FROM notifications WHERE store_id = ?) as total_in_store
          FROM notifications n
          LEFT JOIN notification_reads nr ON n.id = nr.notification_id AND nr.account_id = ?
          WHERE n.store_id = ? AND n.created_at >= DATE_SUB(NOW(), INTERVAL $time_filter)
          ORDER BY n.created_at DESC
          LIMIT ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("iiiiii", $account_id, $store_id, $store_id, $account_id, $store_id, $limit);
$stmt->execute();
$result = $stmt->get_result();

// Get unread count and total from first row (all rows will have same value)
$unreadCount = 0;
$totalCount = 0;
$notifications_data = [];

while ($row = $result->fetch_assoc()) {
    if ($unreadCount == 0) {
        $unreadCount = $row['unread_count'];
        $totalCount = $row['total_in_store'];
    }
    $notifications_data[] = $row;
}

// Debug information
$debug_info = [
    'store_id' => $store_id,
    'account_id' => $account_id,
    'limit' => $limit,
    'time_filter' => $time_filter,
    'total_found' => count($notifications_data),
    'unread_count' => $unreadCount,
    'total_in_store' => $totalCount
];

$notifications = '<div class="notification-container">';
$notificationData = [];

if (count($notifications_data) > 0) {
    foreach ($notifications_data as $row) {
        // Determine if it's a Firebase notification by checking message content
        $isFirebase = (strpos($row['message'], '[Firebase]') === 0);
        $iconClass = $isFirebase ? 'fas fa-cloud' : 'fas fa-bell';
        $iconColor = $isFirebase ? '#667eea' : '#3B82F6';
        $typeLabel = $isFirebase ? 'Firebase' : 'نظام';
        
        // Use the read_status from the query
        $isUnread = ($row['read_status'] === 'unread');

        $notifications .= '<div class="notification-item ' . ($isUnread ? 'unread' : 'read') . '" id="notification-' . $row['id'] . '">';

        // Header with icon and type
        $notifications .= '<div class="notification-header" style="display: flex; align-items: center; margin-bottom: 8px;">';
        $notifications .= '<i class="' . $iconClass . '" style="color: ' . $iconColor . '; margin-left: 8px; font-size: 14px;"></i>';
        $notifications .= '<span class="notification-type" style="font-size: 11px; color: #888; font-weight: bold;">' . $typeLabel . '</span>';
        if ($isUnread) {
            $notifications .= '<span style="background: #e74c3c; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px; margin-right: 8px;">جديد</span>';
        }
        $notifications .= '<span class="notification-time" style="margin-right: auto; font-size: 11px; color: #999;">' . htmlspecialchars($row['created_at']) . '</span>';
        $notifications .= '</div>';

        // Message (clean Firebase prefix for display)
        $displayMessage = $row['message'];
        if ($isFirebase) {
            $displayMessage = str_replace('[Firebase] ', '', $displayMessage);
        }
        $notifications .= '<p class="notification-message" style="margin: 0; color: #666; line-height: 1.4;">' . htmlspecialchars($displayMessage) . '</p>';

        $notifications .= '</div>';
        $notificationData[] = [
            'id' => $row['id'],
            'read_status' => $row['read_status']
        ];
    }
} else {
    $notifications .= '<div class="notification-item" style="text-align: center; color: #999; padding: 20px;">';
    $notifications .= '<i class="fas fa-bell-slash" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>';
    $notifications .= '<div>لا توجد إشعارات في الفترة المحددة</div>';
    $notifications .= '<div style="font-size: 12px; margin-top: 10px;">';
    $notifications .= 'المتجر: ' . $store_id . ' | الفترة: ' . $time_filter . ' | المجموع في المتجر: ' . $totalCount;
    $notifications .= '</div>';
    $notifications .= '</div>';
}
$notifications .= '</div>';

// Add debug info if requested
if (isset($_GET['debug']) && $_GET['debug'] == '1') {
    $notifications .= '<div style="background: #f8f9fa; padding: 10px; margin-top: 10px; border-radius: 5px; font-size: 12px;">';
    $notifications .= '<strong>معلومات التشخيص:</strong><br>';
    $notifications .= 'معرف المتجر: ' . $debug_info['store_id'] . '<br>';
    $notifications .= 'معرف الحساب: ' . $debug_info['account_id'] . '<br>';
    $notifications .= 'الحد الأقصى: ' . $debug_info['limit'] . '<br>';
    $notifications .= 'فترة البحث: ' . $debug_info['time_filter'] . '<br>';
    $notifications .= 'النتائج الموجودة: ' . $debug_info['total_found'] . '<br>';
    $notifications .= 'غير المقروءة: ' . $debug_info['unread_count'] . '<br>';
    $notifications .= 'المجموع في المتجر: ' . $debug_info['total_in_store'] . '<br>';
    $notifications .= '</div>';
}

header('Content-Type: application/json');
echo json_encode([
    'notifications' => $notifications, 
    'notificationData' => $notificationData, 
    'unreadCount' => $unreadCount,
    'debug' => $debug_info
]);

$stmt->close();
$conn->close();
?>
