<?php
/**
 * Mark All Notifications as Read
 *
 * This file marks all notifications as read for a user
 */

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(0);

// Include database connection
require_once 'db_connection.php';
require_once 'encryption_functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    $key = getenv('ENCRYPTION_KEY');

    // Get store_id from URL parameter or POST data
    $encrypted_store_id = '';
    $store_id = null;

    if (isset($_GET['store_id'])) {
        $encrypted_store_id = $_GET['store_id'];
    } elseif (isset($_POST['store_id'])) {
        $encrypted_store_id = $_POST['store_id'];
    }

    if (!empty($encrypted_store_id)) {
        $store_id = decrypt($encrypted_store_id, $key);
    }

    // Get current user ID from encrypted account_id in session
    $user_id = null;
    if (isset($_SESSION['account_id']) && !empty($_SESSION['account_id'])) {
        $user_id = decrypt($_SESSION['account_id'], $key);
    }

    // Validate user session
    if (!$user_id) {
        echo json_encode([
            'success' => false,
            'message' => 'User not authenticated'
        ]);
        exit;
    }

    // Get all unread notifications
    $notifications_query = "
        SELECT id
        FROM notifications
        WHERE status = 'notread'
    ";

    $params = [];

    // Add store filter if store_id is provided
    if ($store_id) {
        $notifications_query .= " AND (store_id = ? OR store_id IS NULL)";
        $params[] = $store_id;
    }

    if (count($params) > 0) {
        $notifications_stmt = $conn->prepare($notifications_query);
        $notifications_stmt->bind_param(str_repeat('i', count($params)), ...$params);
    } else {
        $notifications_stmt = $conn->prepare($notifications_query);
    }
    $notifications_stmt->execute();
    $notifications_result = $notifications_stmt->get_result();

    $notification_ids = [];
    while ($row = $notifications_result->fetch_assoc()) {
        $notification_ids[] = $row['id'];
    }

    if (empty($notification_ids)) {
        echo json_encode([
            'success' => true,
            'message' => 'No unread notifications found',
            'marked_count' => 0
        ]);
        exit;
    }

    // Mark all notifications as read by updating status
    $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
    $update_query = "UPDATE notifications SET status = 'read' WHERE id IN ($placeholders)";

    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param(str_repeat('i', count($notification_ids)), ...$notification_ids);

    if ($update_stmt->execute()) {
        $marked_count = $update_stmt->affected_rows;

        // Also insert records in notification_reads for tracking
        foreach ($notification_ids as $notification_id) {
            $insert_query = "INSERT IGNORE INTO notification_reads (notification_id, account_id, viewed_at) VALUES (?, ?, NOW())";
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bind_param("ii", $notification_id, $user_id);
            $insert_stmt->execute();
            $insert_stmt->close();
        }

        echo json_encode([
            'success' => true,
            'message' => "تم تحديد $marked_count إشعار كمقروء",
            'marked_count' => $marked_count
        ]);
        exit;
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to mark notifications as read'
        ]);
        exit;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
    exit;
}

?>
