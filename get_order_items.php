<?php
include 'db_connection.php';

header('Content-Type: application/json');

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

// Read POST JSON payload
$input = json_decode(file_get_contents('php://input'), true);
if (!isset($input['sale_ids']) || !is_array($input['sale_ids']) || empty($input['sale_ids'])) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid sale_ids']);
    exit();
}

// Sanitize sale_ids as integers
$sale_ids = array_map('intval', $input['sale_ids']);
$inClause = implode(',', $sale_ids);

// Fetch order items for the provided sale_ids
$sql = "SELECT s.sale_id, i.name, s.quantity, s.price, s.total_amount, COALESCE(s.collected, 0) AS collected, s.status
        FROM sales s
        JOIN items i ON s.item_id = i.item_id
        WHERE s.sale_id IN ($inClause)";
$result = $conn->query($sql);

// دالة لتنسيق الأرقام بشكل ذكي
function formatNumber($number) {
    $num = floatval($number);
    if ($num == intval($num)) {
        return intval($num);
    }
    return rtrim(rtrim(number_format($num, 2, '.', ''), '0'), '.');
}

$order_items = [];
while ($row = $result->fetch_assoc()) {
    // تنسيق الكمية بشكل ذكي - عرض الخانات العشرية فقط عند الحاجة
    $quantity = floatval($row['quantity']);
    if ($quantity == intval($quantity)) {
        // إذا كانت الكمية عدد صحيح، اعرضها بدون خانات عشرية
        $row['quantity'] = intval($quantity);
    } else {
        // إذا كانت الكمية عشرية، اعرضها بـ 3 خانات عشرية مع إزالة الأصفار الزائدة
        $row['quantity'] = rtrim(rtrim(number_format($quantity, 3, '.', ''), '0'), '.');
    }

    // تنسيق السعر والإجمالي والتكلفة والمكسب
    $row['price'] = formatNumber($row['price']);
    $row['total_amount'] = formatNumber($row['total_amount']);

    // تنسيق التكلفة والمكسب إذا كانا موجودين
    if (isset($row['cost'])) {
        $row['cost'] = formatNumber($row['cost']);
    }
    if (isset($row['profit'])) {
        $row['profit'] = formatNumber($row['profit']);
    }

    $order_items[] = $row;
}

$conn->close();
echo json_encode($order_items);
?>
