<?php
// تضمين ملف منع التخزين المؤقت
include 'no_cache.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getCachePreventionMetaTags(); ?>
    <title>مسح الكاش وتحديث Service Worker - <?php echo date('H:i:s'); ?></title>
    <style>
        body { 
            font-family: 'Cairo', Arial, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            color: #333;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .warning { 
            background: #fff3cd; 
            border: 2px solid #ffc107; 
            color: #856404; 
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success { 
            background: #d4edda; 
            border: 2px solid #28a745; 
            color: #155724; 
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .button { 
            background: linear-gradient(45deg, #dc3545, #c82333); 
            color: white; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 25px; 
            cursor: pointer; 
            margin: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220,53,69,0.4);
        }
        .button.success { 
            background: linear-gradient(45deg, #28a745, #20c997); 
        }
        .button.success:hover { 
            box-shadow: 0 5px 15px rgba(40,167,69,0.4);
        }
        .log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>🗑️ مسح الكاش وتحديث Service Worker</h1>
    
    <div class="warning">
        <h3>⚠️ تحذير مهم!</h3>
        <p>هذه الأداة ستقوم بـ:</p>
        <ul style="text-align: right;">
            <li>مسح جميع الكاش الموجود</li>
            <li>إلغاء تسجيل Service Worker القديم</li>
            <li>تسجيل Service Worker جديد بسياسة عدم التخزين المؤقت</li>
            <li>إعادة تحميل الصفحة</li>
        </ul>
    </div>

    <button class="button" onclick="clearEverything()">🚀 مسح الكاش وتحديث Service Worker</button>

    <div class="log" id="log">
        جاري التحضير...
    </div>

    <div id="result" style="display: none;"></div>

</div>

<script>
let logArea = document.getElementById('log');

function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString('ar-EG');
    const colors = {
        info: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000',
        success: '#00ff88'
    };
    
    logArea.innerHTML += `<div style="color: ${colors[type]};">[${timestamp}] ${message}</div>`;
    logArea.scrollTop = logArea.scrollHeight;
}

async function clearEverything() {
    try {
        addLog('🚀 بدء عملية المسح الشامل...', 'info');
        
        // 1. مسح جميع الكاش
        addLog('🗑️ مسح جميع الكاش...', 'info');
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            addLog(`تم العثور على ${cacheNames.length} كاش للحذف`, 'info');
            
            for (const cacheName of cacheNames) {
                addLog(`حذف كاش: ${cacheName}`, 'info');
                await caches.delete(cacheName);
            }
            addLog('✅ تم حذف جميع الكاش', 'success');
        }
        
        // 2. إلغاء تسجيل جميع Service Workers
        addLog('❌ إلغاء تسجيل Service Workers...', 'info');
        if ('serviceWorker' in navigator) {
            const registrations = await navigator.serviceWorker.getRegistrations();
            addLog(`تم العثور على ${registrations.length} Service Worker للإلغاء`, 'info');
            
            for (const registration of registrations) {
                addLog(`إلغاء تسجيل: ${registration.scope}`, 'info');
                await registration.unregister();
            }
            addLog('✅ تم إلغاء تسجيل جميع Service Workers', 'success');
        }
        
        // 3. مسح localStorage و sessionStorage
        addLog('🧹 مسح التخزين المحلي...', 'info');
        try {
            localStorage.clear();
            sessionStorage.clear();
            addLog('✅ تم مسح التخزين المحلي', 'success');
        } catch (e) {
            addLog('⚠️ لا يمكن مسح التخزين المحلي', 'warning');
        }
        
        // 4. تسجيل Service Worker الجديد
        addLog('📝 تسجيل Service Worker الجديد...', 'info');
        if ('serviceWorker' in navigator) {
            const registration = await navigator.serviceWorker.register('/sw.js', {
                updateViaCache: 'none'
            });
            addLog('✅ تم تسجيل Service Worker الجديد بنجاح', 'success');
            addLog(`Scope: ${registration.scope}`, 'info');
        }
        
        // 5. إظهار النتيجة
        const resultDiv = document.getElementById('result');
        resultDiv.className = 'success';
        resultDiv.innerHTML = `
            <h3>🎉 تم بنجاح!</h3>
            <p>تم مسح جميع الكاش وتحديث Service Worker</p>
            <p>Service Worker الجديد يعمل بسياسة عدم التخزين المؤقت الكاملة</p>
            <button class="button success" onclick="location.reload()">🔄 إعادة تحميل الصفحة</button>
            <button class="button success" onclick="window.open('accounts.php', '_blank')">👥 اختبار الحسابات</button>
        `;
        resultDiv.style.display = 'block';
        
        addLog('🎉 تم الانتهاء من جميع العمليات بنجاح!', 'success');
        addLog('💡 يمكنك الآن اختبار النظام - لن يتم تخزين أي ملفات مؤقتاً', 'info');
        
    } catch (error) {
        addLog(`❌ خطأ: ${error.message}`, 'error');
        console.error('Clear everything error:', error);
        
        const resultDiv = document.getElementById('result');
        resultDiv.className = 'warning';
        resultDiv.innerHTML = `
            <h3>❌ حدث خطأ</h3>
            <p>فشل في إكمال العملية: ${error.message}</p>
            <button class="button" onclick="location.reload()">🔄 إعادة المحاولة</button>
        `;
        resultDiv.style.display = 'block';
    }
}

// رسالة ترحيب
document.addEventListener('DOMContentLoaded', () => {
    addLog('مرحباً بك في أداة مسح الكاش الشاملة', 'info');
    addLog('هذه الأداة ستمسح جميع الكاش وتحديث Service Worker', 'info');
    addLog('Service Worker الجديد لن يخزن أي ملفات مؤقتاً', 'warning');
    addLog('اضغط الزر أعلاه للبدء', 'info');
});

console.log('🗑️ Cache Clear Tool Loaded');
</script>

</body>
</html>
