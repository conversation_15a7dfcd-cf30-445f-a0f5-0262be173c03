<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$response = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $account_id = $data['account_id'] ?? null;
    $theme = $data['theme'] ?? null;

    if (!$account_id || !$theme) {
        $response['success'] = false;
        $response['message'] = 'Invalid account ID or theme.';
        echo json_encode($response);
        exit();
    }

    $stmt = $conn->prepare("UPDATE accounts SET theme = ? WHERE account_id = ?");
    $stmt->bind_param("si", $theme, $account_id);

    if ($stmt->execute()) {
        $response['success'] = true;
        $response['message'] = 'Theme updated successfully.';
    } else {
        $response['success'] = false;
        $response['message'] = 'Error updating theme: ' . $stmt->error;
    }
    $stmt->close();
} else {
    $response['success'] = false;
    $response['message'] = 'Invalid request method.';
}

header('Content-Type: application/json');
echo json_encode($response);
exit();
