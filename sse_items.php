<?php
include 'db_connection.php';

include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');

if (isset($_GET['category_id'])) {
    $encrypted_category_id = $_GET['category_id'];
    $category_id = decrypt($encrypted_category_id, $key);

    $sql = "SELECT * FROM items WHERE category_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $items = [];

    while ($row = $result->fetch_assoc()) {
        $items[] = $row;
    }

    $stmt->close();
    echo "data: " . json_encode(['items' => $items]) . "\n\n";
    flush();
}

$conn->close();
?>
