<?php
/**
 * ملف تعديل المصاريف - مدمج مع نظام الصلاحيات
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// معالجة طلبات POST مع فحص الصلاحيات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // فحص صلاحية تعديل المصاريف
    if (!hasPermission('expenses', 'edit_expense')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لتعديل المصاريف']);
        exit();
    }
    $expense_id = $_POST['expense_id'];
    $expense_name = $_POST['expense_name'];
    $expense_type = $_POST['expense_type'];
    $amount = $_POST['amount'];

    // Fetch the current expense details for comparison
    $fetch_stmt = $conn->prepare("SELECT expense_name, expense_type, amount FROM expenses WHERE expense_id = ?");
    $fetch_stmt->bind_param("i", $expense_id);
    $fetch_stmt->execute();
    $fetch_stmt->bind_result($current_expense_name, $current_expense_type, $current_amount);
    $fetch_stmt->fetch();
    $fetch_stmt->close();

    $stmt = $conn->prepare("UPDATE expenses SET expense_name = ?, expense_type = ?, amount = ? WHERE expense_id = ?");
    $stmt->bind_param("ssdi", $expense_name, $expense_type, $amount, $expense_id);

    if ($stmt->execute()) {
        // Log the expense edit action
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        // Prepare a description of what was changed
        $changes = [];
        if ($expense_name !== $current_expense_name) $changes[] = "اسم المصروف من $current_expense_name إلى $expense_name";
        if ($expense_type !== $current_expense_type) $changes[] = "نوع المصروف من $current_expense_type إلى $expense_type";
        if ($amount != $current_amount) $changes[] = "المبلغ من $current_amount إلى $amount";

        $description = "تم تعديل المصروف رقم $expense_id: " . implode(", ", $changes);

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'update', 'expenses', ?, ?)";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $expense_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تعديل المصروف.']);
    }

    $stmt->close();
}

$conn->close();
?>
