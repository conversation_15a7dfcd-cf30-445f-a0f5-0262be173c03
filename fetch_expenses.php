<?php
include 'db_connection.php';

$inventory_id = isset($_GET['inventory_id']) ? intval($_GET['inventory_id']) : 0;

if ($inventory_id <= 0) {
    echo json_encode([]);
    exit;
}

$sql = "SELECT expense_name, expense_type, amount, expense_date 
        FROM inventory_expenses 
        WHERE inventory_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$result = $stmt->get_result();

$expenses = [];
while ($row = $result->fetch_assoc()) {
    $expenses[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($expenses);
?>
