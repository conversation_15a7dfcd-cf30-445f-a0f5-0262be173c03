<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = $_GET['store_id'] ?? null;

if (!$encrypted_store_id) {
    echo json_encode(['success' => false, 'error' => 'Store ID is required']);
    exit();
}

$store_id = decrypt($encrypted_store_id, $key);
if (!$store_id) {
    echo json_encode(['success' => false, 'error' => 'Invalid Store ID']);
    exit();
}

$stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
$store = $result->fetch_assoc();

if ($store) {
    echo json_encode(['success' => true, 'name' => $store['name']]);
} else {
    echo json_encode(['success' => false, 'error' => 'Store not found']);
}

$stmt->close();
$conn->close();
?>
