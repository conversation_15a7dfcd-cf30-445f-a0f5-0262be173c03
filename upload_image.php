<?php
$type = $_POST['type'] ?? null;

if (!$type || !in_array($type, ['purchase', 'sale'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid or missing invoice type']);
    exit();
}

if (!empty($_FILES['image'])) {
    $uploadDir = __DIR__ . '/uploads/' . ($type === 'sale' ? 'wholesale_invoice_images/' : 'invoice_images/');
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $ext = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION) ?: 'png';
    $uniqueName = bin2hex(random_bytes(16)) . ".$ext";
    $imagePath = $uploadDir . $uniqueName;

    if (move_uploaded_file($_FILES['image']['tmp_name'], $imagePath)) {
        echo json_encode(['path' => 'uploads/' . ($type === 'sale' ? 'wholesale_invoice_images/' : 'invoice_images/') . $uniqueName]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to upload image']);
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'No image provided']);
}
?>
