<?php
/**
 * security.php
 * Centralised authentication & authorisation middleware.
 * 
 * Usage example in any protected script:
 *     $requiredRole = 'admin'; // or [ 'admin', 'dealer' ]
 *     require_once 'security.php';
 */


// Prevent direct double-inclusion side-effects
if (defined('SECURITY_MIDDLEWARE_LOADED')) {
    return;
}
define('SECURITY_MIDDLEWARE_LOADED', true);

// Start PHP session if not already started
if (session_status() === PHP_SESSION_NONE) {
    // Security-oriented cookie flags handled in index.php / session_manager
    session_start();
}

// Required dependencies
require_once __DIR__ . '/db_connection.php';
require_once __DIR__ . '/encryption_functions.php';
require_once __DIR__ . '/session_manager.php';

// Determine environment (production or local) – consistent with index.php
$isProduction = ($_SERVER['HTTP_HOST'] === 'elwaled.shop');
$key          = getenv('ENCRYPTION_KEY');

$sessionManager = new SessionManager($conn, $key, $isProduction);

// 1) Validate existing session
$authenticated = $sessionManager->validateSession();

// 2) If session invalid, attempt automatic login via Remember-Me cookie
if (!$authenticated) {
    $roleFromToken = $sessionManager->validateRememberMeToken();
    $authenticated = (bool)$roleFromToken; // true if token produced a valid session
}

// 3) If still not authenticated, redirect to login
if (!$authenticated) {
    header('Location: index.php');
    exit();
}

// 4) Authorisation check (role-based)
if (isset($requiredRole) && !empty($requiredRole)) {
    $currentRole = $_SESSION['role'] ?? null;

    // Allow string or array
    if (is_array($requiredRole)) {
        if (!in_array($currentRole, $requiredRole, true)) {
            header('Location: unauthorized.php');
exit();
        }
    } else { // scalar
        if ($currentRole !== $requiredRole) {
            header('Location: unauthorized.php');
            exit();
        }
    }
}

// ---- Permissions system integration ----
require_once __DIR__ . '/permissions_system.php';

global $permissions_system, $role;
try {
    $permissions_system = new PermissionsSystem($conn, $key);
} catch (Exception $e) {
    // During initial setup the tables may not exist yet
    $permissions_system = null;
}

function checkPagePermission($module_name, $permission = 'view', $redirect_on_fail = true)
{
    global $permissions_system;

    if (!$permissions_system) {
        return true; // permissions system not initialised yet
    }

    if (!$permissions_system->hasPermission($module_name, $permission)) {
        if ($redirect_on_fail) {
            header('Location: unauthorized.php');
            exit();
        }
        return false;
    }
    return true;
}

function requireAdmin()
{
    global $permissions_system;
    $role = $_SESSION['role'] ?? null;

    if (!$permissions_system) {
        if ($role !== 'admin') {
            header('Location: unauthorized.php');
            exit();
        }
        return;
    }

    if (!$permissions_system->isAdmin()) {
        header('Location: unauthorized.php');
        exit();
    }
}

function hasAnyPermission($module_name, $permissions = ['view'])
{
    global $permissions_system;

    if (!$permissions_system) {
        return true;
    }

    foreach ($permissions as $permission) {
        if ($permissions_system->hasPermission($module_name, $permission)) {
            return true;
        }
    }
    return false;
}

function getCurrentUserPermissions()
{
    global $permissions_system;
    if (!$permissions_system) {
        return [];
    }
    return $permissions_system->getUserPermissions();
}

/**
 * دالة مساعدة للتحقق من صلاحية واحدة
 * Wrapper function for checking a single permission
 */
function hasPermission($module_name, $permission = 'view')
{
    global $permissions_system;
    
    if (!$permissions_system) {
        return true; // permissions system not initialised yet
    }
    
    return $permissions_system->hasPermission($module_name, $permission);
}

/**
 * دالة مساعدة للتحقق من صلاحيات الوحدات في السايد بار والاختصارات
 * Helper function for checking module permissions in sidebar and shortcuts
 */
function hasModulePermission($module_name, $permission = 'view') {
    global $permissions_system, $conn;
    
    // التحقق من وجود نظام الصلاحيات
    if (!$permissions_system) {
        // إذا لم يكن نظام الصلاحيات متاحاً، السماح للمديرين فقط
        return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }
    
    // التحقق من أن الوحدة مفعلة في قاعدة البيانات
    if ($conn) {
        $stmt = $conn->prepare("SELECT is_active FROM modules WHERE module_name = ?");
        $stmt->bind_param("s", $module_name);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if (!$row['is_active']) {
                $stmt->close();
                return false; // الوحدة معطلة
            }
        } else {
            $stmt->close();
            return false; // الوحدة غير موجودة
        }
        $stmt->close();
    }
    
    // التحقق من صلاحية الوصول فقط - هذا هو المطلوب للسايد بار والاختصارات
    return hasPermission($module_name, 'access');
}

// 5) Optional: refresh activity timestamp (useful لإنهاء الجلسة بعد فترة خمول)
$_SESSION['last_activity'] = time();
?>

