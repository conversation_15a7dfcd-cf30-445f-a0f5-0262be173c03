<?php
/**
 * ملف حذف المصاريف - مدمج مع نظام الصلاحيات
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// معالجة طلبات POST مع فحص الصلاحيات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // فحص صلاحية حذف المصاريف
    if (!hasPermission('expenses', 'delete_expense')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لحذف المصاريف']);
        exit();
    }
    $data = json_decode(file_get_contents('php://input'), true);
    $expense_id = $data['expense_id'];

    // Fetch the expense details before deletion
    $stmt = $conn->prepare("SELECT expense_name, amount FROM expenses WHERE expense_id = ?");
    $stmt->bind_param("i", $expense_id);
    $stmt->execute();
    $stmt->bind_result($expense_name, $amount);
    $stmt->fetch();
    $stmt->close();

    // Delete the expense
    $stmt = $conn->prepare("DELETE FROM expenses WHERE expense_id = ?");
    $stmt->bind_param("i", $expense_id);
    $success = $stmt->execute();
    $stmt->close();

    if ($success) {
        // Log the expense deletion action
        $key = getenv('ENCRYPTION_KEY');
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'delete', 'expenses', ?, ?)";
        $description = "تم حذف المصروف $expense_name بقيمة $amount";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $expense_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حذف المصروف.']);
    }
}

$conn->close();
?>
