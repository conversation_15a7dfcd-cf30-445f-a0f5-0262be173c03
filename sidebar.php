<!-- تطبيق الثيم فوراً قبل تحميل أي شيء آخر -->
<script src="js/instant_theme.js"></script>

<link rel="stylesheet" href="web_css/sidebar.css">

<?php

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

// تضمين الحماية الموحّدة (تشمل session و PermissionsSystem)
require_once __DIR__ . '/security.php';

// Get current file name for active link highlighting
$current_file = basename($_SERVER['PHP_SELF']);

// Get store_id from URL parameter or from local variable (for items.php)
$encrypted_store_id = isset($encrypted_store_id) ? $encrypted_store_id : (isset($_GET['store_id']) ? $_GET['store_id'] : '');
$store_id = null;
$store_name = 'غير محدد';

if (!empty($encrypted_store_id)) {
    require_once 'encryption_functions.php';
    $key = getenv('ENCRYPTION_KEY');
    $store_id = decrypt($encrypted_store_id, $key);

    if ($store_id) {
        // Get store name
        if (!isset($conn)) {
            require_once 'db_connection.php';
        }
        $store_query = "SELECT name FROM stores WHERE store_id = ?";
        $store_stmt = $conn->prepare($store_query);
        $store_stmt->bind_param("i", $store_id);
        $store_stmt->execute();
        $store_result = $store_stmt->get_result();
        if ($store_row = $store_result->fetch_assoc()) {
            $store_name = $store_row['name'];
        }
        $store_stmt->close();
    }
}

// Check if current user ID is 7 for sales access
$show_sales_link = false;
if (isset($_SESSION['account_id'])) {
    $key = getenv('ENCRYPTION_KEY');
    $current_user_id = decrypt($_SESSION['account_id'], $key);
    if ($current_user_id == 7) {
        $show_sales_link = true;
    }
}

// Get unread notifications count for sidebar
if (!isset($unreadCount)) {
    $unreadCount = 0;
    if (isset($_SESSION['account_id'])) {
        $key = getenv('ENCRYPTION_KEY');
        $user_id = decrypt($_SESSION['account_id'], $key);

        // Count unread notifications
        $unread_query = "
            SELECT COUNT(*) as unread_count
            FROM notifications n
            WHERE n.status = 'notread'
        ";

        $unread_params = [];

        if ($store_id) {
            $unread_query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
            $unread_params[] = $store_id;
        }

        if (!isset($conn)) {
            require_once 'db_connection.php';
        }

        $unread_stmt = $conn->prepare($unread_query);
        if (count($unread_params) > 0) {
            $unread_stmt->bind_param(str_repeat('i', count($unread_params)), ...$unread_params);
        }
        $unread_stmt->execute();
        $unread_result = $unread_stmt->get_result();
        $unread_row = $unread_result->fetch_assoc();
        $unreadCount = $unread_row['unread_count'] ?? 0;
        $unread_stmt->close();
    }
}

?>

<?php include_once 'firebase_scripts.php'; ?>

<script>
    // تعريف متغير معرف الفرع المشفر ليكون متاحًا للاستخدام في JavaScript
    var encrypted_store_id = "<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>";

    document.addEventListener("DOMContentLoaded", () => {
        const loader = document.getElementById('page-loader');

        // Function to hide the loader
        const hideLoader = () => {
            loader.style.opacity = '0';
            setTimeout(() => {
                loader.style.visibility = 'hidden';
                loader.style.display = 'none';
            }, 500); // Match the transition duration
        };

        // Show the loader initially
        if (loader) {
            loader.style.display = 'flex';
            loader.style.visibility = 'visible';
            loader.style.opacity = '1';
        }

        // Create particles animation
        createParticles();

        // Hide the loader when the page is fully loaded
        window.addEventListener('load', hideLoader);

        // Fallback to hide loader after a few seconds in case `load` event fails
        setTimeout(hideLoader, 3000);

        const sidebar = document.getElementById("sidebar");
        const menuBtn = document.querySelector('.menu-icon-container'); // Updated selector
        function toggleSidebar() {
            if (sidebar.classList.contains("open")) {
                sidebar.classList.remove("open");
                menuBtn.classList.remove("open");
            } else {
                sidebar.classList.add("open");
                menuBtn.classList.add("open");
            }
        }
        menuBtn.addEventListener("click", toggleSidebar);

        // Always show accounts submenu
        const accountsMenuItem = document.querySelector('#accounts-menu');
        if (accountsMenuItem) {
            const submenu = accountsMenuItem.querySelector('ul');
            if (submenu) {
                submenu.style.maxHeight = '500px';
                submenu.style.overflow = 'visible';
                submenu.style.display = 'block';
                submenu.style.opacity = '1';
            }
        }

        // إدارة قائمة التصنيفات
        const categoriesToggle = document.querySelector('.submenu-toggle');
        const categoriesSubmenu = document.querySelector('.categories-submenu');
        let categoriesLoaded = false;

        if (categoriesToggle && categoriesSubmenu) {
            // إضافة تأثير عند تحويم الماوس
            categoriesToggle.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(63, 81, 181, 0.1)';
            });

            categoriesToggle.addEventListener('mouseleave', function() {
                if (!this.classList.contains('open')) {
                    this.style.backgroundColor = '';
                }
            });

            // تبديل حالة القائمة عند النقر
            categoriesToggle.addEventListener('click', function(e) {
                // منع انتشار الحدث
                e.stopPropagation();

                // تبديل حالة القائمة
                this.classList.toggle('open');
                categoriesSubmenu.classList.toggle('open');

                // تغيير لون الخلفية عند الفتح
                if (this.classList.contains('open')) {
                    this.style.backgroundColor = 'rgba(63, 81, 181, 0.2)';
                } else {
                    this.style.backgroundColor = '';
                }

                // جلب التصنيفات إذا لم يتم تحميلها بعد
                if (!categoriesLoaded) {
                    loadCategories();
                }
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!categoriesToggle.contains(e.target) && !categoriesSubmenu.contains(e.target)) {
                    categoriesToggle.classList.remove('open');
                    categoriesSubmenu.classList.remove('open');
                    categoriesToggle.style.backgroundColor = '';
                }
            });

            // دالة لتحميل التصنيفات من الخادم
            function loadCategories() {
                // الحصول على معرف الفرع من عنوان URL أو من معرف التصنيف
                const urlParams = new URLSearchParams(window.location.search);
                let storeId = urlParams.get('store_id');

                // في صفحة items.php، قد لا يكون هناك معرف فرع في URL، ولكن يمكننا استخدام معرف الفرع المستخرج من التصنيف
                if (!storeId && typeof encrypted_store_id !== 'undefined') {
                    storeId = encrypted_store_id;
                }

                if (!storeId) {
                    categoriesSubmenu.innerHTML = '<li><span>لا يوجد فرع محدد</span></li>';
                    categoriesLoaded = true;
                    return;
                }

                // جلب التصنيفات من الخادم
                fetch(`get_sidebar_categories.php?store_id=${storeId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        categoriesSubmenu.innerHTML = '';

                        if (data.success && data.categories && data.categories.length > 0) {
                            // إضافة التصنيفات إلى القائمة
                            data.categories.forEach(category => {
                                const categoryItem = document.createElement('li');
                                categoryItem.innerHTML = `
                                    <a href="items.php?category_id=${category.encrypted_id}">
                                        <i class="fas fa-tag" style="margin-left: 8px;"></i>
                                        ${category.name}
                                    </a>
                                `;
                                categoriesSubmenu.appendChild(categoryItem);
                            });
                        } else {
                            categoriesSubmenu.innerHTML = '<li><span>لا توجد تصنيفات</span></li>';
                        }

                        categoriesLoaded = true;
                    })
                    .catch(error => {
                        console.error('Error loading categories:', error);
                        categoriesSubmenu.innerHTML = '<li><span>حدث خطأ أثناء تحميل التصنيفات</span></li>';
                        categoriesLoaded = true;
                    });
            }
        }

        // عند النقر خارج الشريط الجانبي والأيقونة يتم إغلاق الشريط وإعادة الأيقونة لحالتها الافتراضية
        document.addEventListener("click", function(event) {
            if (!sidebar.contains(event.target) && !menuBtn.contains(event.target)) {
                sidebar.classList.remove("open");
                menuBtn.classList.remove("open");
            }
        });

        const themeToggleLink = document.getElementById("theme-toggle-link");
        const themeToggleContainer = document.querySelector(".theme-toggle-container");

        // Update link appearance based on the current theme
        const updateThemeLink = () => {
            const currentTheme = document.documentElement.getAttribute("data-theme") || "light";
            themeToggleLink.setAttribute("data-theme", currentTheme);
        };

        // Toggle theme logic with fast animation
        themeToggleLink.addEventListener("click", (event) => {
            event.preventDefault();
            const currentTheme = document.documentElement.getAttribute("data-theme") || "light";
            const newTheme = currentTheme === "dark" ? "light" : "dark";

            // Add animation class to the toggle button
            themeToggleContainer.classList.add("animate");

            // Apply theme immediately for instant response
            document.documentElement.setAttribute("data-theme", newTheme);
            localStorage.setItem("theme", newTheme);
            updateThemeLink();

            // Remove the animation class after the animation duration
            setTimeout(() => {
                themeToggleContainer.classList.remove("animate");
            }, 200); // Reduced duration for faster response
        });

        // Theme is now applied by instant_theme.js and updated by theme.js
        updateThemeLink();

        // Handle store status indicator and stores dropdown
        const storeStatus = document.getElementById('store-status');
        const storesDropdown = document.getElementById('stores-dropdown');

        if (storeStatus) {
            // Keep store status always visible
            storeStatus.style.display = 'flex';
            storeStatus.style.opacity = '1';

            // No need for scroll event listener as position:fixed in CSS already keeps it fixed
            // Just set the initial positions once
            storeStatus.style.top = '140px';
            storeStatus.style.right = '15px';

            // Initialize dropdown position
            storesDropdown.style.top = '180px';
            storesDropdown.style.right = '15px';



            // Toggle dropdown when clicking on store status
            storeStatus.addEventListener('click', function(e) {
                // Toggle dropdown visibility with direct style manipulation
                if (storesDropdown.style.display === 'block') {
                    storesDropdown.style.display = 'none';
                    storesDropdown.classList.remove('show');
                } else {
                    storesDropdown.style.display = 'block';
                    storesDropdown.classList.add('show');

                    // Ensure dropdown is positioned correctly
                    storesDropdown.style.top = '180px';
                    storesDropdown.style.right = '15px';

                    // Load stores list
                    loadStoresList();
                }

                // Add an enhanced animation when clicked
                const branchIcon = storeStatus.querySelector('.branch-icon');
                if (branchIcon) {
                    // Remove the class first to ensure animation can be triggered again
                    branchIcon.classList.remove('clicked');

                    // Force a reflow to ensure the animation restarts
                    void branchIcon.offsetWidth;

                    // Add the class to trigger the animation
                    branchIcon.classList.add('clicked');

                    // Remove the class after animation completes
                    setTimeout(() => {
                        branchIcon.classList.remove('clicked');
                    }, 600); // Match the duration in CSS (0.6s = 600ms)
                }

                // Prevent event from bubbling up
                e.stopPropagation();
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!storeStatus.contains(e.target) && !storesDropdown.contains(e.target) && storesDropdown.style.display === 'block') {
                    storesDropdown.style.display = 'none';
                    storesDropdown.classList.remove('show');
                }
            });



            // Function to load stores list
            function loadStoresList() {
                // Get current store ID from URL
                const urlParams = new URLSearchParams(window.location.search);
                const currentStoreId = urlParams.get('store_id');

                // Fetch stores list from API
                fetch(`get_stores_list.php${currentStoreId ? `?current_store_id=${currentStoreId}` : ''}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Clear dropdown content first
                        storesDropdown.innerHTML = '';

                        if (data.success && data.stores && data.stores.length > 0) {
                            // Add stores to dropdown
                            data.stores.forEach(store => {
                                const storeItem = document.createElement('div');
                                storeItem.className = `store-item${store.current ? ' current' : ''}`;
                                storeItem.innerHTML = `
                                    <i class="fas fa-store"></i>
                                    <span>${store.name}</span>
                                    ${store.current ? '<i class="fas fa-check" style="margin-right: auto;"></i>' : ''}
                                `;

                                // Add click event to switch store
                                if (!store.current) {
                                    storeItem.addEventListener('click', function() {
                                        switchStore(store.id, store.name);
                                    });
                                }

                                storesDropdown.appendChild(storeItem);
                            });

                            // If only one store, show message
                            if (data.stores.length === 1) {
                                const messageItem = document.createElement('div');
                                messageItem.className = 'store-item message';
                                messageItem.innerHTML = '<span>لا توجد فروع أخرى متاحة</span>';
                                storesDropdown.appendChild(messageItem);
                            }
                        } else {
                            storesDropdown.innerHTML = '<div class="store-item message"><span>لا توجد فروع متاحة</span></div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading stores:', error);
                        storesDropdown.innerHTML = '<div class="store-item message"><span>حدث خطأ أثناء تحميل الفروع</span></div>';
                    });
            }

            // Function to switch to a different store
            function switchStore(storeId, storeName) {
                // Get current URL and replace store_id parameter
                const currentUrl = new URL(window.location.href);
                const currentPath = currentUrl.pathname;
                const currentParams = new URLSearchParams(window.location.search);

                // Update store_id parameter
                currentParams.set('store_id', storeId);

                // Redirect to the same page with new store_id
                window.location.href = `${currentPath}?${currentParams.toString()}`;
            }
        }

        // Handle logout confirmation
        const logoutLink = document.querySelector('.logout a');
        if (logoutLink) {
            logoutLink.addEventListener('click', (event) => {
                event.preventDefault(); // Prevent default link behavior
                Swal.fire({
                    title: 'هل أنت متأكد؟',
                    text: "سيتم تسجيل خروجك من الحساب!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، تسجيل الخروج',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = logoutLink.href; // Redirect to logout URL
                    }
                });
            });
        }
    });

    // Function to show system logs message
    function showSystemLogsMessage(event) {
        event.preventDefault(); // Prevent default link behavior
        
        Swal.fire({
            title: 'ميزة مغلقة 🔒',
            text: 'هذه الميزة مغلقة لأنها تحت التطوير 🚧👨‍💻',
            icon: 'info',
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'حسناً',
            customClass: {
                popup: 'rtl-popup'
            }
        });
    }

    // Function to show permissions management message
    function showPermissionsMessage(event) {
        event.preventDefault(); // Prevent default link behavior
        
        Swal.fire({
            title: 'ميزة مغلقة 🔒',
            text: 'هذه الميزة مغلقة لأنها تحت التطوير 🚧👨‍💻',
            icon: 'info',
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'حسناً',
            customClass: {
                popup: 'rtl-popup'
            }
        });
    }

    // Function to create floating particles
    function createParticles() {
        const particlesContainer = document.getElementById('particles-container');
        if (!particlesContainer) return;

        const particleCount = 15; // Number of particles

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random starting position
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            particle.style.animationDuration = (6 + Math.random() * 4) + 's';
            
            // Random size
            const size = 3 + Math.random() * 3;
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            
            particlesContainer.appendChild(particle);
        }
    }
</script>

<!-- Page Loader -->
<div id="page-loader">
    <!-- Particles Background -->
    <div class="particles-container" id="particles-container">
        <!-- Particles will be generated by JavaScript -->
    </div>
    
    <!-- Brand Logo Container -->
    <div class="loader-brand">
        <div class="brand-name">Elwaled Market</div>
        
        <!-- Simple Animated Dots Loader -->
        <div class="dots-loader">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
    </div>
    
    <div class="loading-text">جاري التحميل...</div>
    <div class="loading-subtitle">يرجى الانتظار قليلاً</div>
</div>

<div class="header-container">
    <!-- New Header Title -->
    <div class="header-title" style="width: 100%; text-align: center; font-size: 2rem; font-weight: bold; color: var(--color-primary);">
        Elwaled Market
    </div>
    <!-- Icons Row -->
    <div class="icons-container" style="display: flex; justify-content: space-between; align-items: center; width: 100%; margin-top: 10px;">
        <!-- Back Arrow Icon -->
        <div class="back-arrow-container" onclick="window.history.back();" style="cursor:pointer;display:flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background-color:var(--color-bg);margin-left:10px;">
            <i class="fas fa-arrow-right" style="font-size:22px;color:var(--color-primary);"></i>
        </div>
        <!-- Sidebar Toggle Icon -->
        <div class="menu-icon-container" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </div>
        <!-- Notification Bell Icon -->
        <div class="notification-icon-container" onclick="showNotifications()">
            <i class="fas fa-bell"></i>
            <?php if ($unreadCount > 0): ?>
                <span class="notification-count"><?= $unreadCount ?></span>
            <?php else: ?>
                <span class="notification-count" style="display: none;">0</span>
            <?php endif; ?>
        </div>
        <!-- Calculator Icon -->
        <div class="calculator-icon-container" onclick="toggleCalculator()">
            <i class="fas fa-calculator"></i>
        </div>
        <!-- Theme Toggle Button -->
        <div class="theme-toggle-container">
            <a href="#" id="theme-toggle-link" class="theme-toggle-link">
                <i class="fas fa-adjust"></i>
            </a>
        </div>
        <!-- Account Icon -->
        <div class="account-icon">
            <a href="profile.php?store_id=<?= urlencode($encrypted_store_id); ?>">
                <i class="fas fa-user-circle"></i>
            </a>
        </div>
    </div>
</div>

<!-- Store Status Indicator -->
<?php if (isset($store_name) && !empty($store_name)): ?>
<div id="store-status">
    <i class="fas fa-store branch-icon"></i>
    <span class="status-text">الفرع: <?php echo htmlspecialchars($store_name); ?></span>
    <i class="fas fa-chevron-down" style="font-size: 12px; margin-right: 5px;"></i>
</div>

<!-- Stores Dropdown Menu - Moved outside to avoid nesting issues -->
<div id="stores-dropdown">
    <!-- Will be populated with JavaScript -->
</div>


<?php endif; ?>

<div id="sidebar" class="styled-sidebar">
    <!-- Store name display -->
    <div class="store-name-container">
        <h3 class="store-name"><i class="fas fa-store" style="margin-left: 8px;"></i> الفرع: <?php echo htmlspecialchars($store_name); ?></h3>
    </div>
    <ul id="navLinks">
        <!-- الرئيسية - لوحة التحكم -->
        <?php if (hasModulePermission('dashboard')): ?>
        <li><a href="stores.php" class="<?php echo $current_file === 'stores.php' ? 'active' : ''; ?>"><i class="fas fa-home"></i> لوحة التحكم</a></li>
        <?php endif; ?>

        <!-- اختصارات الفرع - أساسي لجميع المستخدمين -->
        <li>
            <a href="store_shortcuts.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?= $current_file === 'store_shortcuts.php' ? 'active' : ''; ?>">
                <i class="fas fa-cogs"></i> ادارة الفرع
            </a>
        </li>

        <!-- التقارير -->
        <?php if (hasModulePermission('reports')): ?>
        <li><a href="item_reports.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'item_reports.php' ? 'active' : ''; ?>"><i class="fas fa-chart-line"></i> التقارير</a></li>
        <?php endif; ?>

        <!-- المبيعات - يظهر فقط للحساب رقم 7 -->
        <?php if ($show_sales_link): ?>
        <li><a href="sales.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'sales.php' ? 'active' : ''; ?>"><i class="fas fa-cash-register"></i> المبيعات</a></li>
        <?php endif; ?>

        <!-- التصنيفات -->
        <?php if (hasModulePermission('categories')): ?>
        <li class="has-submenu">
            <div class="menu-item-container">
                <a href="categories.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'categories.php' ? 'active' : ''; ?>"><i class="fas fa-th-list"></i> التصنيفات</a>
                <span class="submenu-toggle"><i class="fas fa-chevron-down"></i></span>
            </div>
            <ul class="categories-submenu" style="max-height: 0; overflow: hidden;">
                <!-- سيتم ملء هذه القائمة بالتصنيفات عبر JavaScript -->
                <li class="loading-item"><span><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</span></li>
            </ul>
            <?php if ($current_file === 'items.php' && isset($category_name) && !empty($category_name)): ?>
            <ul style="max-height: 500px; overflow: visible; display: block; opacity: 1; margin-top: 5px; padding-right: 15px;">
                <li><a href="items.php?category_id=<?php echo urlencode($encrypted_category_id); ?>" class="active subcategory-link">
                    <i class="fas fa-tag" style="margin-left: 8px;"></i>
                    <?php echo htmlspecialchars($category_name); ?>
                </a></li>
            </ul>
            <?php endif; ?>
        </li>
        <?php endif; ?>

        <!-- الأصناف -->
        <?php if (hasModulePermission('items')): ?>
        <li><a href="item_store.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'item_store.php' ? 'active' : ''; ?>"><i class="fas fa-boxes"></i> الأصناف</a></li>
        <?php endif; ?>

        <!-- فواتير الشراء -->
        <?php if (hasModulePermission('purchase_invoices')): ?>
        <li><a href="purchase_invoices.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'purchase_invoices.php' ? 'active' : ''; ?>"><i class="fas fa-file-invoice-dollar"></i> فواتير الشراء</a></li>
        <?php endif; ?>

        <!-- فواتير البيع بالجملة -->
        <?php if (hasModulePermission('wholesale_invoices')): ?>
        <li><a href="wholesale_invoices.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'wholesale_invoices.php' ? 'active' : ''; ?>"><i class="fas fa-shopping-cart"></i> فواتير البيع بالجملة</a></li>
        <?php endif; ?>

        <!-- الجرد -->
        <?php if (hasModulePermission('inventory')): ?>
        <li><a href="inventory.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'inventory.php' ? 'active' : ''; ?>"><i class="fas fa-warehouse"></i> الجرد</a></li>
        <?php endif; ?>

        <!-- الحسابات -->
        <?php if (hasModulePermission('accounts')): ?>
        <li id="accounts-menu">
            <span><i class="fas fa-user-cog"></i> الحسابات</span>
            <ul>
                <li><a href="accounts.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'accounts.php' ? 'active' : ''; ?>">إدارة</a></li>
                <li><a href="requested_accounts.php" class="<?php echo $current_file === 'requested_accounts.php' ? 'active' : ''; ?>">طلبات</a></li>
                <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
                <li><a href="manage_permissions.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'manage_permissions.php' ? 'active' : ''; ?>">إدارة الصلاحيات</a></li>
                <?php endif; ?>
            </ul>
        </li>
        <?php endif; ?>

        <!-- نقل الأصناف -->
        <?php if (hasModulePermission('transfer_items', 'access')): ?>
        <li><a href="transfer_items.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'transfer_items.php' ? 'active' : ''; ?>"><i class="fas fa-exchange-alt"></i> نقل الأصناف</a></li>
        <?php endif; ?>

        <!-- صلاحيات الأصناف -->
        <?php if (hasModulePermission('expired_items', 'access')): ?>
        <li><a href="expired_items.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'expired_items.php' ? 'active' : ''; ?>"><i class="fas fa-hourglass-end"></i> صلاحيات الأصناف</a></li>
        <?php endif; ?>

        <!-- المصاريف -->
        <?php if (hasModulePermission('expenses')): ?>
        <li><a href="expenses.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'expenses.php' ? 'active' : ''; ?>"><i class="fas fa-money-bill-wave"></i> المصاريف</a></li>
        <?php endif; ?>
        
        <!-- تقفيل الورديات -->
        <?php if (hasModulePermission('shift_closures')): ?>
        <li>
            <a href="shift_closures.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'shift_closures.php' ? 'active' : ''; ?>">
                <i class="fas fa-cash-register"></i> تقفيل الورديات
            </a>
        </li>
        <?php endif; ?>

        <!-- تحويلات الرصيد -->
        <?php if (hasModulePermission('balance_transfers')): ?>
        <li>
            <a href="balance_transfers.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'balance_transfers.php' ? 'active' : ''; ?>">
                <i class="fas fa-wallet"></i> تحويلات الرصيد
            </a>
        </li>
        <?php endif; ?>

        <!-- شرح النظام - متاح للجميع -->
        

        <!-- الإشعارات -->
        <?php if (hasModulePermission('notifications', 'access')): ?>
        <li>
            <a href="notifications_page.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'notifications_page.php' ? 'active' : ''; ?>">
                <i class="fas fa-bell"></i> الإشعارات
                <?php if ($unreadCount > 0): ?>
                    <span class="notification-badge"><?= $unreadCount ?></span>
                <?php else: ?>
                    <span class="notification-badge" style="display: none;">0</span>
                <?php endif; ?>
            </a>
        </li>
        <?php endif; ?>

        <!-- إرسال الإشعارات -->
        <?php if (hasModulePermission('send_notifications', 'access')): ?>
        <li>
            <a href="send_notifications.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'send_notifications.php' ? 'active' : ''; ?>">
                <i class="fas fa-paper-plane"></i> إرسال الإشعارات
            </a>
        </li>
        <?php endif; ?>

        <!-- سجلات النظام - للمديرين فقط -->
        <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
        <li>
            <a href="#" onclick="showSystemLogsMessage(event)" class="<?php echo $current_file === 'system_logs.php' ? 'active' : ''; ?>">
                <i class="fas fa-clipboard-list"></i> سجلات النظام
            </a>
        </li>
        <?php endif; ?>
        <li>
            <a href="project_summary.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'project_summary.php' ? 'active' : ''; ?>">
                <i class="fas fa-info-circle"></i> شرح النظام
            </a>
        </li>

        <!-- تسجيل الخروج - متاح للجميع -->
        <li class="logout"><a href="logout.php" class="<?php echo $current_file === 'logout.php' ? 'active' : ''; ?>"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
    </ul>
</div> <!-- End of #sidebar -->

<!-- الآلة الحاسبة -->
<div id="calculator" class="draggable" style="display:none;">
  <div id="calcHeader">
      🧮 آلة حاسبة
      <span id="calcCloseBtn" onclick="closeCalculator()" style="cursor:pointer; float:left; margin-left:10px; font-size:18px; color: rgba(255,255,255,0.8); transition: all 0.2s ease;">✖</span>
  </div>
  <input type="text" id="calcDisplay" oninput="liveCompute()" placeholder="0">
  <div id="calcResult">0</div>
  <div id="calcButtons">
    <button onclick="calcInput('7')">7</button>
    <button onclick="calcInput('8')">8</button>
    <button onclick="calcInput('9')">9</button>
    <button onclick="calcOperation('/')">÷</button>
    <button onclick="calcInput('4')">4</button>
    <button onclick="calcInput('5')">5</button>
    <button onclick="calcInput('6')">6</button>
    <button onclick="calcOperation('*')">×</button>
    <button onclick="calcInput('1')">1</button>
    <button onclick="calcInput('2')">2</button>
    <button onclick="calcInput('3')">3</button>
    <button onclick="calcOperation('-')">-</button>
    <button onclick="calcInput('0')">0</button>
    <button onclick="calcInput('.')">.</button>
    <button onclick="calcCompute()">=</button>
    <button onclick="calcOperation('+')">+</button>
    <button onclick="calcUndo()">تراجع</button>
    <button onclick="calcClear()" style="grid-column: span 4; background: #b71c1c;">مسح</button>
  </div>
  <div style="padding: 8px; text-align: center; font-size: 11px; color: var(--calc-text); opacity: 0.7; font-family: 'Cairo', sans-serif;">
    💡 استخدم لوحة المفاتيح للكتابة السريعة
  </div>
</div>

<!-- Updated global page footer with new font and heart icon -->
<div class="page-footer footer-text" style="position: fixed; bottom: 0; left: 0; right: 0; background-color: var(--color-secondary); color: var(--color-primary); text-align: center; padding: 10px 0; border-top: 2px solid var(--color-primary); box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);">
    Developed with <i class="fas fa-heart heart-icon"></i> by Eyad Waled
</div>

<script>
// Force show notification counters for testing
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sidebar loaded, checking notification counters...');

    // Find all notification counters
    const counters = document.querySelectorAll('.notification-count');
    const badges = document.querySelectorAll('.notification-badge');

    console.log('Found counters:', counters.length);
    console.log('Found badges:', badges.length);

    // Force show counters
    counters.forEach((counter, index) => {
        console.log(`Counter ${index}:`, counter);
        counter.style.display = 'flex';
        counter.style.visibility = 'visible';
        counter.style.opacity = '1';
    });

    // Add enhanced notification bell animations
    const notificationContainer = document.querySelector('.notification-icon-container');
    if (notificationContainer) {
        // Add click animation
        notificationContainer.addEventListener('click', function() {
            this.classList.add('new-notification');
            setTimeout(() => {
                this.classList.remove('new-notification');
            }, 1000);
        });

        // Trigger alert animation when new notifications arrive
        window.triggerNotificationAlert = function() {
            notificationContainer.classList.add('new-notification');
            setTimeout(() => {
                notificationContainer.classList.remove('new-notification');
            }, 1000);
        };

        // Enhanced pulse for dark mode
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        if (isDarkMode) {
            notificationContainer.classList.add('dark-mode-enhanced');
        }

        // Listen for theme changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                    if (isDark) {
                        notificationContainer.classList.add('dark-mode-enhanced');
                    } else {
                        notificationContainer.classList.remove('dark-mode-enhanced');
                    }
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
    }

    badges.forEach((badge, index) => {
        console.log(`Badge ${index}:`, badge);
        badge.style.display = 'inline-block';
        badge.style.visibility = 'visible';
        badge.style.opacity = '1';
    });
});

// وظائف الآلة الحاسبة
let calcCurrent = '';

function calcInput(val) {
  calcCurrent += val;
  document.getElementById('calcDisplay').value = calcCurrent;
  liveCompute();
  
  // Add button click effect
  addButtonEffect(event);
}

// إضافة تأثير للأزرار
function addButtonEffect(event) {
  if (event && event.target) {
    const button = event.target;
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
      button.style.transform = '';
    }, 100);
  }
}

function calcOperation(op) {
  if(calcCurrent === '') return;
  calcCurrent += op;
  document.getElementById('calcDisplay').value = calcCurrent;
  liveCompute();
  addButtonEffect(event);
}

function calcCompute() {
  try {
    const result = eval(calcCurrent);
    document.getElementById('calcDisplay').value = result;
    calcCurrent = result.toString();
    liveCompute();
    
    // Add success animation
    const calcResult = document.getElementById('calcResult');
    calcResult.style.transform = 'scale(1.1)';
    calcResult.style.background = '#28a745';
    calcResult.style.color = 'white';
    setTimeout(() => {
      calcResult.style.transform = '';
      calcResult.style.background = '';
      calcResult.style.color = '';
    }, 500);
    
  } catch(e) {
    document.getElementById('calcDisplay').value = "خطأ في العملية";
    calcCurrent = '';
    document.getElementById('calcResult').innerText = '';
    
    // Add error animation
    const calcDisplay = document.getElementById('calcDisplay');
    calcDisplay.style.background = '#dc3545';
    calcDisplay.style.color = 'white';
    setTimeout(() => {
      calcDisplay.style.background = '';
      calcDisplay.style.color = '';
    }, 1000);
  }
  addButtonEffect(event);
}

function calcClear() {
  calcCurrent = '';
  document.getElementById('calcDisplay').value = '';
  document.getElementById('calcResult').innerText = '0';
  
  // Add clear animation
  const calc = document.getElementById('calculator');
  calc.style.transform = 'scale(0.98)';
  setTimeout(() => {
    calc.style.transform = '';
  }, 150);
  
  addButtonEffect(event);
}

function liveCompute() {
  calcCurrent = document.getElementById('calcDisplay').value;
  try {
      const result = eval(calcCurrent);
      document.getElementById('calcResult').innerText = result !== undefined ? result : '0';
  } catch (e) {
      const lastValidResult = eval(calcCurrent.slice(0, -1)) || '0';
      document.getElementById('calcResult').innerText = lastValidResult;
  }
}

function calcUndo() {
  calcCurrent = calcCurrent.slice(0, -1);
  document.getElementById('calcDisplay').value = calcCurrent;
  liveCompute();
  addButtonEffect(event);
}

function toggleCalculator() {
  var calc = document.getElementById('calculator');
  const icon = document.querySelector('.calculator-icon-container');
  
  if (calc.style.display === "none" || calc.style.display === "") {
    calc.style.display = "block";
    calc.classList.remove('hide');
    calc.classList.add('show');
    
    // استعادة موقع الآلة الحاسبة من localStorage
    const savedPosition = localStorage.getItem('calculatorPosition');
    if (savedPosition) {
      const position = JSON.parse(savedPosition);
      calc.style.top = position.top + 'px';
      calc.style.left = position.left + 'px';
      calc.style.right = 'auto';
    }
    
    // تفعيل تأثير الأيقونة وجعلها active
    if (icon) {
      icon.style.transform = 'scale(1.2) rotate(360deg)';
      icon.classList.add('active'); // تفعيل التأثير البصري للأيقونة
      setTimeout(() => {
        icon.style.transform = '';
      }, 300);
    }
  } else {
    closeCalculator();
  }
}

function closeCalculator() {
  var calc = document.getElementById('calculator');
  const icon = document.querySelector('.calculator-icon-container');
  
  // حفظ موقع الآلة الحاسبة في localStorage قبل الإغلاق
  const position = {
    top: calc.offsetTop,
    left: calc.offsetLeft
  };
  localStorage.setItem('calculatorPosition', JSON.stringify(position));
  
  calc.classList.remove('show');
  calc.classList.add('hide');
  
  // إزالة الكلاس active من الأيقونة
  if (icon) {
    icon.classList.remove('active');
  }
  
  setTimeout(() => {
    calc.style.display = "none";
    calc.classList.remove('hide');
  }, 300);
}

// تفعيل خاصية السحب للآلة الحاسبة ودعم لوحة المفاتيح
document.addEventListener('DOMContentLoaded', function() {
    dragElement(document.getElementById("calculator"));
    
    // التحقق من حالة الآلة الحاسبة المحفوظة وتحديث الأيقونة
    const calc = document.getElementById('calculator');
    const icon = document.querySelector('.calculator-icon-container');
    
    // إذا كانت الآلة الحاسبة مفتوحة، تأكد من أن الأيقونة active
    if (calc && calc.style.display === 'block') {
        if (icon) {
            icon.classList.add('active');
        }
        
        // استعادة الموقع المحفوظ عند تحميل الصفحة
        const savedPosition = localStorage.getItem('calculatorPosition');
        if (savedPosition) {
            const position = JSON.parse(savedPosition);
            calc.style.top = position.top + 'px';
            calc.style.left = position.left + 'px';
            calc.style.right = 'auto';
        }
    }
    
    // دعم لوحة المفاتيح للآلة الحاسبة
    document.addEventListener('keydown', function(e) {
        const calc = document.getElementById('calculator');
        if (calc.style.display === 'block') {
            switch(e.key) {
                case '0': case '1': case '2': case '3': case '4':
                case '5': case '6': case '7': case '8': case '9':
                case '.':
                    calcInput(e.key);
                    e.preventDefault();
                    break;
                case '+': case '-': case '*': case '/':
                    calcOperation(e.key);
                    e.preventDefault();
                    break;
                case 'Enter':
                case '=':
                    calcCompute();
                    e.preventDefault();
                    break;
                case 'Escape':
                    closeCalculator();
                    e.preventDefault();
                    break;
                case 'Backspace':
                    calcUndo();
                    e.preventDefault();
                    break;
                case 'Delete':
                case 'c':
                case 'C':
                    calcClear();
                    e.preventDefault();
                    break;
            }
        }
    });

    function dragElement(elm) {
        if (!elm) return;
        
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        const header = document.getElementById("calcHeader");
        if (header) {
          header.onmousedown = dragMouseDown;
          header.addEventListener("touchstart", dragTouchStart, {passive: false});
        } else {
          elm.onmousedown = dragMouseDown;
          elm.addEventListener("touchstart", dragTouchStart, {passive: false});
        }

        function dragMouseDown(e) {
          e = e || window.event;
          e.preventDefault();
          pos3 = e.clientX;
          pos4 = e.clientY;
          document.onmouseup = closeDragElement;
          document.onmousemove = elementDrag;
        }

        function elementDrag(e) {
          e = e || window.event;
          e.preventDefault();
          pos1 = pos3 - e.clientX;
          pos2 = pos4 - e.clientY;
          pos3 = e.clientX;
          pos4 = e.clientY;
          elm.style.top = (elm.offsetTop - pos2) + "px";
          elm.style.left = (elm.offsetLeft - pos1) + "px";
          elm.style.right = "auto";
        }

        function closeDragElement() {
          document.onmouseup = null;
          document.onmousemove = null;
          
          // حفظ الموقع الجديد في localStorage عند انتهاء السحب
          const position = {
            top: elm.offsetTop,
            left: elm.offsetLeft
          };
          localStorage.setItem('calculatorPosition', JSON.stringify(position));
        }
        
        function dragTouchStart(e) {
          e.preventDefault();
          pos3 = e.touches[0].clientX;
          pos4 = e.touches[0].clientY;
          document.addEventListener("touchmove", elementTouchDrag, {passive: false});
          document.addEventListener("touchend", closeTouchDrag, {passive: false});
        }
        
        function elementTouchDrag(e) {
          e.preventDefault();
          pos1 = pos3 - e.touches[0].clientX;
          pos2 = pos4 - e.touches[0].clientY;
          pos3 = e.touches[0].clientX;
          pos4 = e.touches[0].clientY;
          elm.style.top = (elm.offsetTop - pos2) + "px";
          elm.style.left = (elm.offsetLeft - pos1) + "px";
          elm.style.right = "auto";
        }
        
        function closeTouchDrag() {
          document.removeEventListener("touchmove", elementTouchDrag);
          document.removeEventListener("touchend", closeTouchDrag);
          
          // حفظ الموقع الجديد في localStorage عند انتهاء السحب باللمس
          const position = {
            top: elm.offsetTop,
            left: elm.offsetLeft
          };
          localStorage.setItem('calculatorPosition', JSON.stringify(position));
        }
    }

    // إضافة مستمع حدث لتشغيل دالة closeCalculator عند حدث touchend على زر الإغلاق
    var closeBtn = document.getElementById('calcCloseBtn');
    if (closeBtn) {
        closeBtn.addEventListener('touchend', function(e) {
            e.preventDefault();
            closeCalculator();
        });
    }
});
</script>
