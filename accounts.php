<?php
/**
 * ملف إدارة الحسابات - مدمج مع نظام الصلاحيات المتقدم
 * 
 * الصلاحيات المطبقة:
 * - view: عرض قائمة الحسابات (مطلوبة للوصول للصفحة)
 * - add_account: إضافة حساب جديد
 * - edit_account: تعديل بيانات الحساب
 * - delete_account: حذف الحساب
 * - change_account_status: تغيير حالة الحساب (تفعيل/إيقاف)
 * - manage_account_permissions: إدارة صلاحيات الحسابات (الوصول لصفحة إدارة الصلاحيات)
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('accounts', 'view');

$key = getenv('ENCRYPTION_KEY');

// Receive the encrypted store ID
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_account_id'])) {
    // فحص صلاحية حذف الحساب
    if (!hasPermission('accounts', 'delete_account')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لحذف الحسابات']);
        exit();
    }

    $delete_account_id = $_POST['delete_account_id'];

    // Fetch the account name for logging
    $stmt = $conn->prepare("SELECT name FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $delete_account_id);
    $stmt->execute();
    $stmt->bind_result($account_name);
    $stmt->fetch();
    $stmt->close();

    // Delete the account
    $stmt = $conn->prepare("DELETE FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $delete_account_id);
    if ($stmt->execute()) {
        // Log the account deletion action
        $logged_in_account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'delete', 'accounts', ?, ?)";
        $description = "تم حذف الحساب $account_name";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $logged_in_account_id, $delete_account_id, $description);
        $log_stmt->execute();
        $log_stmt->close();
    }
    $stmt->close();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_account_id'])) {
    // فحص صلاحية تغيير حالة الحساب
    if (!hasPermission('accounts', 'change_account_status')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لتغيير حالة الحسابات']);
        exit();
    }

    $toggle_account_id = $_POST['toggle_account_id'];
    $current_status = $_POST['current_status'];
    $new_status = $current_status === 'active' ? 'inactive' : 'active';

    // Update the account status
    $stmt = $conn->prepare("UPDATE accounts SET status = ? WHERE account_id = ?");
    $stmt->bind_param("si", $new_status, $toggle_account_id);
    if ($stmt->execute()) {
        // Log the account status change action
        $logged_in_account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        // Translate status to Arabic
        $new_status_ar = $new_status === 'active' ? 'نشط' : 'متوقف';

        // Fetch the account name for logging
        $stmt_account = $conn->prepare("SELECT name FROM accounts WHERE account_id = ?");
        $stmt_account->bind_param("i", $toggle_account_id);
        $stmt_account->execute();
        $stmt_account->bind_result($account_name);
        $stmt_account->fetch();
        $stmt_account->close();

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'update', 'accounts', ?, ?)";
        $description = "تم تغيير حالة الحساب $account_name إلى $new_status_ar";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $logged_in_account_id, $toggle_account_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true, 'new_status' => $new_status]);
    }
    $stmt->close();
    exit();
}

$sql = "SELECT accounts.*, stores.name AS store_name FROM accounts 
        LEFT JOIN stores ON accounts.store_id = stores.store_id 
        WHERE accounts.status != 'requested'";
$result = $conn->query($sql);

$stores_sql = "SELECT * FROM stores";
$stores_result = $conn->query($stores_sql);
$stores = [];
if ($stores_result->num_rows > 0) {
    while($store = $stores_result->fetch_assoc()) {
        $stores[] = $store;
    }
}
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحسابات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <!-- Ensure Font Awesome is loaded -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <!-- Include SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <style>
        /* أنماط خاصة بصلاحيات إدارة الحسابات */
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
            align-items: center;
        }
        
        .status-display {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }
        
        .status-display:not(.inactive) {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-display.inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .permissions-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-right: 4px solid #2196f3;
        }
        
        .permissions-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .permissions-info ul {
            margin: 0;
            padding-right: 20px;
            line-height: 1.6;
        }
        
        .permissions-info li {
            margin-bottom: 5px;
            color: #424242;
        }
        
        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
                gap: 3px;
            }
            
            .action-btn {
                padding: 8px;
                font-size: 12px;
            }
        }
        
        /* تحسين الوضع المظلم */
        [data-theme="dark"] .permissions-info {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%);
            border-color: var(--color-primary);
            border-right-color: var(--color-primary);
        }
        
        [data-theme="dark"] .permissions-info h4 {
            color: var(--color-primary);
        }
        
        [data-theme="dark"] .permissions-info li {
            color: var(--color-fg);
        }
        
        [data-theme="dark"] .status-display:not(.inactive) {
            background-color: rgba(40, 167, 69, 0.2);
            color: #4caf50;
            border-color: rgba(40, 167, 69, 0.3);
        }
        
        [data-theme="dark"] .status-display.inactive {
            background-color: rgba(220, 53, 69, 0.2);
            color: #f44336;
            border-color: rgba(220, 53, 69, 0.3);
        }
    </style>

</head>
<body>
    <?php include 'sidebar.php'; ?>
<div class="container">
    <h2>إدارة الحسابات</h2>
    
    <!-- أزرار العمليات مع فحص الصلاحيات -->
    <div style="display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap;">
        <?php if (hasPermission('accounts', 'manage_account_permissions')): ?>
            <button class="add-btn" id="managePermissionsBtn" style="background: #28a745; display: inline-flex; align-items: center; gap: 5px;" title="إدارة صلاحيات المستخدمين">
                <i class="fas fa-shield-alt"></i> إدارة الصلاحيات
            </button>
        <?php endif; ?>
        
        <?php if (hasPermission('accounts', 'add_account')): ?>
            <button class="add-btn" id="addAccountBtn" title="إضافة حساب جديد">
                <i class="fas fa-plus"></i> إضافة حساب
            </button>
        <?php endif; ?>
    </div>

    <!-- معلومات الصلاحيات المطبقة -->
    <div class="permissions-info">
        <h4><i class="fas fa-user-shield"></i> صلاحياتك في وحدة إدارة الحسابات</h4>
        
        <?php 
        $user_permissions_count = 0;
        if (hasPermission('accounts', 'add_account')) $user_permissions_count++;
        if (hasPermission('accounts', 'edit_account')) $user_permissions_count++;
        if (hasPermission('accounts', 'delete_account')) $user_permissions_count++;
        if (hasPermission('accounts', 'change_account_status')) $user_permissions_count++;
        if (hasPermission('accounts', 'manage_account_permissions')) $user_permissions_count++;
        ?>
        
        <div style="background: rgba(40, 167, 69, 0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px; border-right: 3px solid #28a745;">
            <strong style="color: #28a745;">
                <i class="fas fa-check-circle"></i> 
                لديك <?php echo $user_permissions_count + 1; ?> صلاحية من أصل 6 صلاحيات متاحة
            </strong>
        </div>
        <ul>
            <li style="color: #28a745;"><i class="fas fa-check"></i> <strong>عرض الحسابات:</strong> يمكنك عرض قائمة جميع الحسابات في النظام</li>
            
            <?php if (hasPermission('accounts', 'add_account')): ?>
                <li style="color: #28a745;"><i class="fas fa-check"></i> <strong>إضافة حساب:</strong> يمكنك إضافة حسابات جديدة للنظام</li>
            <?php else: ?>
                <li style="color: #dc3545;"><i class="fas fa-times"></i> <strong>إضافة حساب:</strong> غير متاح - تحتاج لصلاحية إضافية</li>
            <?php endif; ?>
            
            <?php if (hasPermission('accounts', 'edit_account')): ?>
                <li style="color: #28a745;"><i class="fas fa-check"></i> <strong>تعديل الحساب:</strong> يمكنك تعديل بيانات الحسابات الموجودة</li>
            <?php else: ?>
                <li style="color: #dc3545;"><i class="fas fa-times"></i> <strong>تعديل الحساب:</strong> غير متاح - تحتاج لصلاحية إضافية</li>
            <?php endif; ?>
            
            <?php if (hasPermission('accounts', 'delete_account')): ?>
                <li style="color: #28a745;"><i class="fas fa-check"></i> <strong>حذف الحساب:</strong> يمكنك حذف الحسابات من النظام</li>
            <?php else: ?>
                <li style="color: #dc3545;"><i class="fas fa-times"></i> <strong>حذف الحساب:</strong> غير متاح - تحتاج لصلاحية إضافية</li>
            <?php endif; ?>
            
            <?php if (hasPermission('accounts', 'change_account_status')): ?>
                <li style="color: #28a745;"><i class="fas fa-check"></i> <strong>تغيير حالة الحساب:</strong> يمكنك تفعيل أو إيقاف الحسابات</li>
            <?php else: ?>
                <li style="color: #dc3545;"><i class="fas fa-times"></i> <strong>تغيير حالة الحساب:</strong> غير متاح - تحتاج لصلاحية إضافية</li>
            <?php endif; ?>
            
            <?php if (hasPermission('accounts', 'manage_account_permissions')): ?>
                <li style="color: #28a745;"><i class="fas fa-check"></i> <strong>إدارة الصلاحيات:</strong> يمكنك الوصول لنظام إدارة صلاحيات المستخدمين</li>
            <?php else: ?>
                <li style="color: #dc3545;"><i class="fas fa-times"></i> <strong>إدارة الصلاحيات:</strong> غير متاح - تحتاج لصلاحية إضافية</li>
            <?php endif; ?>
        </ul>
        
        <?php if ($user_permissions_count < 5): ?>
            <div style="background: rgba(255, 193, 7, 0.1); padding: 10px; border-radius: 5px; margin-top: 15px; border-right: 3px solid #ffc107;">
                <small style="color: #856404;">
                    <i class="fas fa-info-circle"></i> 
                    للحصول على صلاحيات إضافية، يرجى التواصل مع مدير النظام
                </small>
            </div>
        <?php endif; ?>
    </div>

    <!-- Search and Filter Section using flex-wrap for responsiveness -->
    <div class="search-filter-container" style="display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;">
        <input type="text" id="searchInput" class="input-field" style="flex: 1 1 200px;" placeholder="بحث عن الحسابات..." onkeyup="filterAccounts()">
        <select id="branchFilter" class="input-field" style="flex: 1 1 150px;" onchange="filterAccounts()">
            <option value="">كل الفروع</option>
            <?php
            foreach ($stores as $store) {
                echo "<option value='{$store['name']}'>{$store['name']}</option>";
            }
            ?>
        </select>
        <select id="statusFilter" class="input-field" style="flex: 1 1 150px;" onchange="filterAccounts()">
            <option value="">كل الحالات</option>
            <option value="نشط">نشط</option>
            <option value="متوقف">متوقف</option>
        </select>
    </div>
    
    <!-- Wrap the table in a responsive container -->
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>اسم المستخدم</th>
                    <th>الدور</th>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>الفرع</th>
                    <th>الحالة</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody id="accountsTable">
                <?php
                if ($result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $encrypted_account_id = encrypt($row['account_id'], $key);
                        $toggle_class = $row['status'] === 'active' ? '' : 'inactive';
                        $toggle_text = $row['status'] === 'active' ? 'نشط' : 'متوقف';
                        $branch = $row['role'] === 'admin' ? 'مدير' : ($row['store_name'] ?? 'غير محدد');

                        echo "<tr>
                                <td>{$row['username']}</td>
                                <td>{$row['role']}</td>
                                <td>{$row['name']}</td>
                                <td>{$row['phone']}</td>
                                <td>{$branch}</td>
                                <td>";
                        
                        // زر تغيير الحالة مع فحص الصلاحية
                        if (hasPermission('accounts', 'change_account_status')) {
                            echo "<button class='toggle-btn $toggle_class' type='button' onclick='toggleAccountStatus(" . htmlspecialchars($row['account_id']) . ", \"" . htmlspecialchars($row['status']) . "\")' title='تغيير حالة الحساب'>$toggle_text</button>";
                        } else {
                            echo "<span class='status-display $toggle_class'>$toggle_text</span>";
                        }
                        
                        echo "</td>
                                <td>
                                    <div class='action-buttons'>";
                        
                        // زر التعديل مع فحص الصلاحية
                        if (hasPermission('accounts', 'edit_account')) {
                            echo "<button class='action-btn' type='button' onclick='editAccount(\"" . htmlspecialchars($encrypted_account_id) . "\")' title='تعديل الحساب'>
                                    <i class='fas fa-edit'></i>
                                  </button>";
                        }
                        
                        // زر الحذف مع فحص الصلاحية
                        if (hasPermission('accounts', 'delete_account')) {
                            echo "<button class='action-btn' type='button' onclick='deleteAccount(" . htmlspecialchars($row['account_id']) . ")' title='حذف الحساب'>
                                    <i class='fas fa-trash-alt'></i>
                                  </button>";
                        }
                        
                        echo "</div>
                                </td>
                              </tr>";
                    }
                } else {
                    echo "<tr><td colspan='7'>لا توجد حسابات حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>
</div>

<div id="editAccountModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>تعديل تفاصيل الحساب</h2>
        <form method="POST" action="edit_account.php">
            <input type="hidden" name="edit_account_id" id="edit_account_id">
            <input type="text" name="username" id="edit_username" class="input-field" placeholder="اسم المستخدم" required>
            <input type="password" name="password" id="edit_password" class="input-field" placeholder="كلمة المرور" required>
            <select name="role" id="edit_role" class="input-field" required onchange="toggleStoreField(this.value, 'edit')">
                <option value="admin">مدير</option>
                <option value="purchaser">مشتري</option>
                <option value="user">مستخدم</option>
                <option value="dealer">تاجر</option>
            </select>
            <input type="text" name="name" id="edit_name" class="input-field" placeholder="الاسم">
            <input type="text" name="phone" id="edit_phone" class="input-field" placeholder="الهاتف">
            <select name="store_id" id="edit_store_id" class="input-field" style="display: none;">
                <?php
                foreach ($stores as $store) {
                    echo "<option value='{$store['store_id']}'>{$store['name']}</option>";
                }
                ?>
            </select>
            <label>
                <input type="checkbox" name="status" id="edit_status"> تفعيل الحساب
            </label>
            <button type="submit" class="add-btn">تعديل الحساب</button>
        </form>
    </div>
</div>

<div id="addAccountModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>إضافة حساب جديد</h2>
        <form method="POST" action="add_account.php">
            <input type="text" name="username" class="input-field" placeholder="اسم المستخدم" required>
            <input type="password" name="password" class="input-field" placeholder="كلمة المرور" required>
            <select name="role" class="input-field" required onchange="toggleStoreField(this.value, 'add')">
                <option value="admin">مدير</option>
                <option value="purchaser">مشتري</option>
                <option value="user">مستخدم</option>
                <option value="dealer">تاجر</option>
            </select>
            <input type="text" name="name" class="input-field" placeholder="الاسم">
            <input type="text" name="phone" class="input-field" placeholder="الهاتف">
            <select name="store_id" id="add_store_id" class="input-field" style="display: none;">
                <?php
                foreach ($stores as $store) {
                    echo "<option value='{$store['store_id']}'>{$store['name']}</option>";
                }
                ?>
            </select>
            <label>
                <input type="checkbox" name="status"> تفعيل الحساب
            </label>
            <button type="submit" class="add-btn">إضافة الحساب</button>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // متغيرات الصلاحيات
    const permissions = {
        add_account: <?php echo hasPermission('accounts', 'add_account') ? 'true' : 'false'; ?>,
        edit_account: <?php echo hasPermission('accounts', 'edit_account') ? 'true' : 'false'; ?>,
        delete_account: <?php echo hasPermission('accounts', 'delete_account') ? 'true' : 'false'; ?>,
        change_account_status: <?php echo hasPermission('accounts', 'change_account_status') ? 'true' : 'false'; ?>,
        manage_account_permissions: <?php echo hasPermission('accounts', 'manage_account_permissions') ? 'true' : 'false'; ?>
    };

    var editAccountModal = document.getElementById("editAccountModal");
    var addAccountModal = document.getElementById("addAccountModal");
    var addAccountBtn = document.getElementById("addAccountBtn");
    var managePermissionsBtn = document.getElementById("managePermissionsBtn");
    var editAccountSpan = document.getElementsByClassName("close")[0];
    var addAccountSpan = document.getElementsByClassName("close")[1];

    // زر إدارة الصلاحيات
    if (managePermissionsBtn) {
        managePermissionsBtn.onclick = function() {
            if (!permissions.manage_account_permissions) {
                showPermissionAlert('manage_account_permissions', 'إدارة صلاحيات الحسابات');
                return;
            }
            
            // عرض رسالة أن الميزة تحت التطوير
            Swal.fire({
                icon: 'info',
                title: 'ميزة تحت التطوير',
                html: `
                    <div style="text-align: right; line-height: 1.8;">
                        <p><strong>هذه الميزة قيد التطوير حالياً</strong></p>
                        <p>سيتم إضافة نظام إدارة الصلاحيات المتقدم قريباً والذي سيتيح لك:</p>
                        <ul style="text-align: right; margin: 15px 0;">
                            <li>إدارة صلاحيات المستخدمين بشكل مفصل</li>
                            <li>تخصيص الوصول للوحدات المختلفة</li>
                            <li>إدارة صلاحيات الفروع</li>
                            <li>تحديد نوع الوصول (إداري/كاشير)</li>
                            <li>تسجيل تغييرات الصلاحيات</li>
                        </ul>
                        <p style="color: #666; font-size: 14px;">يرجى المتابعة مع المطور للحصول على آخر التحديثات</p>
                    </div>
                `,
                confirmButtonText: 'حسناً',
                confirmButtonColor: '#3085d6',
                width: '500px'
            });
        }
    }

    // زر إضافة حساب
    if (addAccountBtn) {
        addAccountBtn.onclick = function() {
            if (!permissions.add_account) {
                showPermissionAlert('add_account', 'إضافة حسابات جديدة');
                return;
            }
            addAccountModal.classList.add("active");
        }
    }

    editAccountSpan.onclick = function() {
        editAccountModal.classList.remove("active");
    }

    addAccountSpan.onclick = function() {
        addAccountModal.classList.remove("active");
    }

    window.onclick = function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove("active");
        }
    }

    // دالة عرض رسائل تنبيه الصلاحيات المفصلة
    function showPermissionAlert(permissionType, actionName) {
        let message = '';
        let availablePermissions = [];
        
        // تحديد الصلاحيات المتاحة للمستخدم
        if (permissions.add_account) availablePermissions.push('إضافة حسابات جديدة');
        if (permissions.edit_account) availablePermissions.push('تعديل بيانات الحسابات');
        if (permissions.delete_account) availablePermissions.push('حذف الحسابات');
        if (permissions.change_account_status) availablePermissions.push('تغيير حالة الحسابات');
        if (permissions.manage_account_permissions) availablePermissions.push('إدارة صلاحيات المستخدمين');
        
        if (availablePermissions.length > 0) {
            message = `
                <div style="text-align: right; line-height: 1.8;">
                    <p><strong>ليس لديك صلاحية لـ: ${actionName}</strong></p>
                    <p>الصلاحيات المتاحة لك في هذه الوحدة:</p>
                    <ul style="text-align: right; margin: 15px 0; color: #28a745;">
                        ${availablePermissions.map(perm => `<li>${perm}</li>`).join('')}
                    </ul>
                    <p style="color: #666; font-size: 14px;">للحصول على صلاحيات إضافية، يرجى التواصل مع المدير</p>
                </div>
            `;
        } else {
            message = `
                <div style="text-align: right; line-height: 1.8;">
                    <p><strong>ليس لديك صلاحية لـ: ${actionName}</strong></p>
                    <p style="color: #dc3545;">لا توجد لديك أي صلاحيات في وحدة إدارة الحسابات</p>
                    <p style="color: #666; font-size: 14px;">يرجى التواصل مع المدير لمنحك الصلاحيات المطلوبة</p>
                </div>
            `;
        }
        
        Swal.fire({
            icon: 'warning',
            title: 'صلاحية مطلوبة',
            html: message,
            confirmButtonText: 'حسناً',
            confirmButtonColor: '#3085d6',
            width: '450px'
        });
    }

    function editAccount(encryptedAccountId) {
        // فحص الصلاحية أولاً
        if (!permissions.edit_account) {
            showPermissionAlert('edit_account', 'تعديل الحسابات');
            return;
        }

        // Fetch data for the selected account using AJAX
        fetch(`get_account.php?account_id=${encodeURIComponent(encryptedAccountId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('edit_account_id').value = encryptedAccountId;
                    document.getElementById('edit_username').value = data.account.username;
                    document.getElementById('edit_password').value = data.account.password;
                    document.getElementById('edit_role').value = data.account.role;
                    document.getElementById('edit_name').value = data.account.name;
                    document.getElementById('edit_phone').value = data.account.phone;
                    document.getElementById('edit_status').checked = data.account.status === 'active';
                    document.getElementById('edit_store_id').value = data.account.store_id;

                    toggleStoreField(data.account.role, 'edit');

                    // Instead of using style.display, show modal with active class
                    editAccountModal.classList.add("active");
                } else {
                    Swal.fire('خطأ!', 'فشل في جلب تفاصيل الحساب.', 'error');
                }
            })
            .catch(error => {
                console.error("Error fetching account:", error);
                Swal.fire('خطأ!', 'حدث خطأ أثناء جلب تفاصيل الحساب.', 'error');
            });
    }

    document.querySelector('#editAccountModal form').addEventListener('submit', function (event) {
        event.preventDefault();
        const formData = new FormData(this);

        fetch('edit_account.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: data.message,
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    location.reload(); // Reload the page to reflect changes
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: data.message,
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: 'حدث خطأ أثناء تعديل الحساب.',
                showConfirmButton: false,
                timer: 3000
            });
        });
    });

    function deleteAccount(accountId) {
        // فحص الصلاحية أولاً
        if (!permissions.delete_account) {
            showPermissionAlert('delete_account', 'حذف الحسابات');
            return;
        }

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذف!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const form = new FormData();
                form.append('delete_account_id', accountId);

                fetch('delete_account.php', {
                    method: 'POST',
                    body: form
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم الحذف!', 'تم حذف الحساب بنجاح.', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ!', data.message || 'حدث خطأ أثناء حذف الحساب.', 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء حذف الحساب.', 'error');
                });
            }
        });
    }

    function toggleAccountStatus(accountId, currentStatus) {
        // فحص الصلاحية أولاً
        if (!permissions.change_account_status) {
            showPermissionAlert('change_account_status', 'تغيير حالة الحسابات');
            return;
        }

        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: `سيتم تغيير حالة الحساب إلى ${newStatus === 'active' ? 'نشط' : 'متوقف'}.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، تغيير',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const form = new FormData();
                form.append('toggle_account_id', accountId);
                form.append('current_status', currentStatus);

                fetch('', {
                    method: 'POST',
                    body: form
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم التغيير!', 'تم تغيير حالة الحساب بنجاح.', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ!', data.message || 'حدث خطأ أثناء تغيير حالة الحساب.', 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء تغيير حالة الحساب.', 'error');
                });
            }
        });
    }

    function toggleStoreField(role, prefix) {
        const storeField = document.getElementById(prefix + '_store_id');
        if (role === 'purchaser' || role === 'user' || role === 'dealer') {
            storeField.style.display = 'block';
            storeField.setAttribute('required', 'required');
        } else {
            storeField.style.display = 'none';
            storeField.removeAttribute('required');
        }
    }

    function showPopupMessage(message, type) {
        Swal.fire({
            icon: type,
            title: message,
            showConfirmButton: false,
            timer: 3000
        });
    }

    function filterAccounts() {
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        const branchFilter = document.getElementById('branchFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        const tableRows = document.querySelectorAll('#accountsTable tr');

        tableRows.forEach(row => {
            const username = row.cells[0].textContent.toLowerCase();
            const name = row.cells[2].textContent.toLowerCase();
            const phone = row.cells[3].textContent.toLowerCase();
            const branch = row.cells[4].textContent.trim();
            const status = row.cells[5].textContent.trim();

            const matchesSearch = username.includes(searchInput) || name.includes(searchInput) || phone.includes(searchInput);
            const matchesBranch = branchFilter === '' || branch === branchFilter;
            const matchesStatus = statusFilter === '' || status === statusFilter;

            row.style.display = matchesSearch && matchesBranch && matchesStatus ? '' : 'none';
        });
    }

    <?php if (isset($_SESSION['message'])): ?>
        showPopupMessage('<?php echo $_SESSION['message']; ?>', '<?php echo $_SESSION['message_type']; ?>');
        <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
    <?php endif; ?>
</script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
