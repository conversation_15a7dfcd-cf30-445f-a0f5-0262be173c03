<?php
require_once 'security.php';
require_once 'db_connection.php';
require_once 'encryption_functions.php';

// التحقق من صلاحية عرض استعلامات الأصناف
if (!hasPermission('reports', 'view_queries')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لعرض استعلامات الأصناف']);
    exit();
}

$key = getenv('ENCRYPTION_KEY');

if (!isset($_GET['store_id']) || !isset($_GET['query'])) {
    echo json_encode(['success' => false, 'message' => 'معاملات مفقودة']);
    exit();
}

$encrypted_store_id = $_GET['store_id'];
$query_type = $_GET['query'];
$store_id = decrypt($encrypted_store_id, $key);

if ($store_id === false) {
    echo json_encode(['success' => false, 'message' => 'معرف الفرع غير صحيح']);
    exit();
}

header('Content-Type: application/json; charset=utf-8');

try {
    switch ($query_type) {
        case 'most_profitable':
            // الأصناف الأكثر ربحية
            $sql = "SELECT items.name, 
                           (items.price - items.cost) * items.quantity as profit
                    FROM items 
                    JOIN categories ON items.category_id = categories.category_id 
                    WHERE categories.store_id = ? 
                    AND items.quantity > 0
                    ORDER BY profit DESC 
                    LIMIT 10";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $store_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $items = [];
            while ($row = $result->fetch_assoc()) {
                $items[] = $row;
            }
            $stmt->close();
            
            echo json_encode(['success' => true, 'items' => $items]);
            break;
            
        case 'low_stock':
            // الأصناف منخفضة المخزون (أقل من 10 قطع)
            $sql = "SELECT items.name, items.quantity
                    FROM items 
                    JOIN categories ON items.category_id = categories.category_id 
                    WHERE categories.store_id = ? 
                    AND items.quantity <= 10
                    AND items.quantity > 0
                    ORDER BY items.quantity ASC 
                    LIMIT 10";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $store_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $items = [];
            while ($row = $result->fetch_assoc()) {
                $items[] = $row;
            }
            $stmt->close();
            
            echo json_encode(['success' => true, 'items' => $items]);
            break;
            
        case 'quick_stats':
            // إحصائيات سريعة
            $stats = [];
            
            // إجمالي الأصناف
            $sql = "SELECT COUNT(*) as total_items
                    FROM items 
                    JOIN categories ON items.category_id = categories.category_id 
                    WHERE categories.store_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $store_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $stats['total_items'] = $row['total_items'];
            $stmt->close();
            
            // الأصناف التي نفدت
            $sql = "SELECT COUNT(*) as out_of_stock
                    FROM items 
                    JOIN categories ON items.category_id = categories.category_id 
                    WHERE categories.store_id = ? 
                    AND items.quantity = 0";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $store_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $stats['out_of_stock'] = $row['out_of_stock'];
            $stmt->close();
            
            // متوسط الربح
            $sql = "SELECT AVG(items.price - items.cost) as avg_profit
                    FROM items 
                    JOIN categories ON items.category_id = categories.category_id 
                    WHERE categories.store_id = ? 
                    AND items.quantity > 0";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $store_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $stats['avg_profit'] = $row['avg_profit'] ?? 0;
            $stmt->close();
            
            echo json_encode(['success' => true, 'stats' => $stats]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'نوع استعلام غير صحيح']);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}

$conn->close();
?>