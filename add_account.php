<?php
include 'db_connection.php';
include 'encryption_functions.php';

session_start();

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['username'])) {
    $username = $_POST['username'];
    $password = password_hash($_POST['password'], PASSWORD_BCRYPT);
    $role = $_POST['role'];
    $name = $_POST['name'];
    $phone = $_POST['phone'];
    $status = isset($_POST['status']) ? 'active' : 'inactive';
    $store_id = $_POST['store_id']; // Assuming store_id is passed in the form

    $stmt = $conn->prepare("INSERT INTO accounts (username, password, role, name, phone, status, store_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssssi", $username, $password, $role, $name, $phone, $status, $store_id);
    if ($stmt->execute()) {
        // Log the account creation action
        $created_account_id = $stmt->insert_id; // Get the ID of the newly created account
        $logged_in_account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
                    VALUES (?, 'add', 'accounts', ?)";
        $description = "تم إضافة حساب جديد باسم $name";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("is", $logged_in_account_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo "<script>alert('تم إضافة الحساب بنجاح.');</script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء إضافة الحساب: " . $stmt->error . "');</script>";
    }
    $stmt->close();
}

header("Location: accounts.php");
exit();
?>
