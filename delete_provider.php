<?php
include 'db_connection.php';
include 'auth_check.php';

header('Content-Type: application/json');

// Retrieve and validate input
$data = json_decode(file_get_contents('php://input'), true);
$provider_name = isset($data['provider_name']) ? trim($data['provider_name']) : null;

if (!$provider_name) {
    echo json_encode(['success' => false, 'error' => 'اسم المزود مطلوب.']);
    exit();
}

// Ensure 'Unknown' cannot be deleted
if ($provider_name === 'Unknown') {
    echo json_encode(['success' => false, 'error' => 'لا يمكن حذف المزود الافتراضي.']);
    exit();
}

// Check if the provider is in use in the balance_transfers table
$stmt = $conn->prepare("SELECT COUNT(*) FROM balance_transfers WHERE provider = ?");
$stmt->bind_param("s", $provider_name);
$stmt->execute();
$stmt->bind_result($count);
$stmt->fetch();
$stmt->close();

if ($count > 0) {
    echo json_encode(['success' => false, 'error' => 'لا يمكن حذف المزود لأنه مستخدم في تحويلات الرصيد.']);
    exit();
}

// Fetch the current ENUM values for the provider column
$result = $conn->query("SHOW COLUMNS FROM balance_transfers LIKE 'provider'");
$row = $result->fetch_assoc();
$enum_values = str_replace(["enum(", ")", "'"], "", $row['Type']);
$enum_array = explode(",", $enum_values);

// Check if the provider exists
if (!in_array($provider_name, $enum_array)) {
    echo json_encode(['success' => false, 'error' => 'المزود غير موجود.']);
    exit();
}

// Remove the provider from the ENUM values
$enum_array = array_diff($enum_array, [$provider_name]);
$new_enum_values = "'" . implode("','", $enum_array) . "'";
$alter_query = "ALTER TABLE balance_transfers MODIFY COLUMN provider ENUM($new_enum_values) NOT NULL";

if ($conn->query($alter_query)) {
    // Log the provider deletion action
    session_start();
    include 'encryption_functions.php';
    $key = getenv('ENCRYPTION_KEY');
    $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
                VALUES (?, 'delete', 'balance_transfers', ?)";
    $description = "تم حذف المزود $provider_name";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("is", $account_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    echo json_encode(['success' => true, 'message' => 'تم حذف المزود بنجاح.']);
} else {
    echo json_encode(['success' => false, 'error' => 'فشل في تحديث خيارات المزود.']);
}

$conn->close();
?>
