<?php
// Set headers to prevent caching and allow CORS if needed
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: application/json; charset=utf-8');

// Allow CORS for local development (adjust as needed for production)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: HEAD, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// For HEAD requests, just return success status
if ($_SERVER['REQUEST_METHOD'] === 'HEAD') {
    http_response_code(200);
    exit();
}

// For GET requests, return a simple JSON response
$response = array(
    'status' => 'ok',
    'timestamp' => time(),
    'message' => 'Server is reachable'
);

http_response_code(200);
echo json_encode($response);
exit();