<?php
/**
 * ملف إضافة المصاريف - مدمج مع نظام الصلاحيات
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// معالجة طلبات POST مع فحص الصلاحيات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // فحص صلاحية إضافة المصاريف
    if (!hasPermission('expenses', 'add_expense')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لإضافة المصاريف']);
        exit();
    }
    $store_id = decrypt($_POST['store_id'], $key);
    $expense_name = $_POST['expense_name'];
    $expense_type = $_POST['expense_type'];
    $amount = $_POST['amount'];

    $stmt = $conn->prepare("INSERT INTO expenses (expense_name, expense_type, amount, store_id) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssdi", $expense_name, $expense_type, $amount, $store_id);

    if ($stmt->execute()) {
        $expense_id = $stmt->insert_id; // Get the ID of the newly created expense

        // Log the expense addition action
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'add', 'expenses', ?, ?)";
        $description = "تم إضافة مصروف جديد باسم $expense_name بقيمة $amount";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $expense_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة المصروف.']);
    }

    $stmt->close();
}

$conn->close();
?>
