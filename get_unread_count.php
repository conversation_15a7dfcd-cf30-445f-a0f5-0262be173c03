<?php
/**
 * Get Unread Notifications Count
 *
 * This file returns the count of unread notifications for the current user
 */

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

// Include database connection
require_once 'db_connection.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get store_id from URL parameter if provided
    $store_id = null;
    if (isset($_GET['store_id']) && !empty($_GET['store_id'])) {
        // Decrypt store_id if provided
        require_once 'encryption_functions.php';
        $key = getenv('ENCRYPTION_KEY');
        $store_id = decrypt($_GET['store_id'], $key);

        if (!$store_id) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid store ID'
            ]);
            exit;
        }
    }

    // Start session to get user info
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Get current user ID from encrypted account_id in session
    $user_id = null;
    if (isset($_SESSION['account_id']) && !empty($_SESSION['account_id'])) {
        $key = getenv('ENCRYPTION_KEY');
        if ($key) {
            $user_id = decrypt($_SESSION['account_id'], $key);
        }
    }

    // Note: We don't require user_id for counting notifications
    // as notifications are global and not user-specific in this implementation

    // Build query to count unread notifications
    $query = "
        SELECT COUNT(*) as unread_count
        FROM notifications n
        WHERE n.status = 'notread'
    ";

    $params = [];

    // Add store filter if store_id is provided
    if ($store_id) {
        $query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
        $params[] = $store_id;
    }

    // Prepare and execute query
    $stmt = $conn->prepare($query);

    if (count($params) > 0) {
        $stmt->bind_param(str_repeat('i', count($params)), ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    $unread_count = $row['unread_count'] ?? 0;

    echo json_encode([
        'success' => true,
        'unread_count' => (int)$unread_count,
        'count' => (int)$unread_count, // For backward compatibility
        'message' => 'Unread count retrieved successfully'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

// Close database connection
if (isset($stmt)) {
    $stmt->close();
}
$conn->close();
?>
