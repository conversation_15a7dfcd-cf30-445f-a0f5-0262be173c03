<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$store_id = decrypt($_GET['store_id'], $key);

$response = ['success' => false];

try {
    $stmt = $conn->prepare("SELECT status, COUNT(*) AS count, SUM(total_amount) AS total 
                            FROM purchase_invoices 
                            WHERE store_id = ? 
                            GROUP BY status");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();

    $response['success'] = true;
    $response['total_invoices'] = 0;
    $response['total_price'] = 0;
    $response['confirmed_count'] = 0;
    $response['confirmed_total'] = 0;
    $response['pending_count'] = 0;
    $response['pending_total'] = 0;

    while ($row = $result->fetch_assoc()) {
        $response['total_invoices'] += $row['count'];
        $response['total_price'] += $row['total'];
        if (strtolower($row['status']) === 'confirmed') {
            $response['confirmed_count'] = $row['count'];
            $response['confirmed_total'] = $row['total'];
        } elseif (strtolower($row['status']) === 'pending') {
            $response['pending_count'] = $row['count'];
            $response['pending_total'] = $row['total'];
        }
    }

    // تنسيق الأرقام للعرض
    $response['total_price'] = number_format($response['total_price'], 2);
    $response['confirmed_total'] = number_format($response['confirmed_total'], 2);
    $response['pending_total'] = number_format($response['pending_total'], 2);
} catch (Exception $e) {
    $response['message'] = 'حدث خطأ أثناء جلب التقرير: ' . $e->getMessage();
}

echo json_encode($response);
$conn->close();
?>
