<?php
include 'db_connection.php';

$inventory_id = isset($_GET['inventory_id']) ? intval($_GET['inventory_id']) : 0;

if ($inventory_id <= 0) {
    echo json_encode([]);
    exit;
}

$sql = "SELECT inventory_invoice_id, account_name, total_amount, created_at 
        FROM inventory_purchase_invoices 
        WHERE inventory_id = ? 
        ORDER BY created_at DESC"; // Sort by creation date in descending order
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$result = $stmt->get_result();

$invoices = [];
while ($row = $result->fetch_assoc()) {
    $invoices[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($invoices);
?>
