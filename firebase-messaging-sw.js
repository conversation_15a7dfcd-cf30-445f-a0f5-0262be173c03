// Firebase Cloud Messaging Service Worker
importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js');

// Firebase configuration
firebase.initializeApp({
  apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
  authDomain: "macm-84114.firebaseapp.com",
  projectId: "macm-84114",
  storageBucket: "macm-84114.firebasestorage.app",
  messagingSenderId: "860043675105",
  appId: "1:860043675105:web:72586005d5bd035ff8bea0"
});

const messaging = firebase.messaging();

// Default notification icon
let customNotificationIcon = '/uploads/img/logo2.png';

// Listen for messages from main thread to update icon
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SET_NOTIFICATION_ICON') {
    customNotificationIcon = event.data.icon;
    console.log('[firebase-messaging-sw.js] Updated notification icon to:', customNotificationIcon);
  }
});

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);

  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: customNotificationIcon,
    badge: customNotificationIcon,
    data: payload.data,
    tag: 'notification-' + (payload.data?.notification_id || Date.now()),
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'عرض',
        icon: customNotificationIcon
      },
      {
        action: 'close',
        title: 'إغلاق'
      }
    ],
    vibrate: [200, 100, 200],
    sound: '/sounds/notification.mp3'
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', event => {
  console.log('[firebase-messaging-sw.js] Notification click Received.', event.action);

  event.notification.close();

  if (event.action === 'close') {
    // Just close the notification
    return;
  }

  // For 'view' action or default click
  event.waitUntil(
    clients.matchAll({
      type: "window"
    })
    .then(clientList => {
      // Check if the site is already open
      for (const client of clientList) {
        if (client.url.includes(self.location.hostname) && 'focus' in client) {
          // Focus the existing window and navigate to index page
          client.focus();
          return client.navigate('/index.php');
        }
      }

      // Open new window if no existing window found
      if (clients.openWindow) {
        return clients.openWindow('/index.php');
      }
    })
  );
});

// Handle notification action buttons
self.addEventListener('notificationaction', event => {
  console.log('[firebase-messaging-sw.js] Notification action received:', event.action);

  event.notification.close();

  if (event.action === 'view') {
    event.waitUntil(
      clients.openWindow('/index.php')
    );
  }
  // 'close' action doesn't need any additional handling
});
