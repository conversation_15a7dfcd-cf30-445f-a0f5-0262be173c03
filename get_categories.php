<?php
include 'db_connection.php';

include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

$response = [];

try {
    if (isset($_GET['store_id'])) {
        $encrypted_store_id = $_GET['store_id'];
        $store_id = decrypt($encrypted_store_id, $key);

        if ($store_id === false) {
            throw new Exception("Failed to decrypt store ID.");
        }

        $stmt = $conn->prepare("SELECT name, img_path FROM categories WHERE store_id = ?");
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }

        $stmt->bind_param("i", $store_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();

        while ($row = $result->fetch_assoc()) {
            $response[] = $row;
        }
    }
} catch (Exception $e) {
    error_log($e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => "حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقًا."]);
    exit();
}

echo json_encode($response);
?>
