<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإشعارات في الخلفية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding-top: 50px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
        
        .icon-large {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .countdown {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header text-center">
                        <h2><i class="fas fa-bell"></i> اختبار الإشعارات في الخلفية</h2>
                        <p class="mb-0">اختبر إرسال واستقبال الإشعارات عندما يكون الموقع مغلقًا</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- حالة النظام -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="status-card text-center">
                                    <i class="fas fa-shield-alt icon-large text-primary"></i>
                                    <h5>إذن الإشعارات</h5>
                                    <p id="permissionStatus">جاري التحقق...</p>
                                    <button id="requestPermissionBtn" class="btn btn-primary btn-sm d-none">طلب الإذن</button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="status-card text-center">
                                    <i class="fas fa-cogs icon-large text-success"></i>
                                    <h5>Service Worker</h5>
                                    <p id="serviceWorkerStatus">جاري التحقق...</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="status-card text-center">
                                    <i class="fas fa-key icon-large text-info"></i>
                                    <h5>رمز FCM</h5>
                                    <p id="fcmTokenStatus">جاري التحقق...</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- اختبار الإشعارات -->
                        <div class="test-section">
                            <h4><i class="fas fa-vial"></i> اختبار الإشعارات</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <button id="testForegroundBtn" class="btn btn-success w-100 mb-3">
                                        <i class="fas fa-desktop"></i> اختبار إشعار في المقدمة
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button id="testBackgroundBtn" class="btn btn-warning w-100 mb-3">
                                        <i class="fas fa-moon"></i> اختبار إشعار في الخلفية
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- اختبار الإشعارات المؤجلة -->
                        <div class="test-section">
                            <h4><i class="fas fa-clock"></i> اختبار الإشعارات المؤجلة</h4>
                            <p>سيتم إرسال إشعار بعد 10 ثوانٍ. أغلق هذه النافذة لاختبار الإشعارات في الخلفية.</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <button id="scheduleNotificationBtn" class="btn btn-primary w-100">
                                        <i class="fas fa-hourglass-start"></i> جدولة إشعار (10 ثوانٍ)
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <div id="countdownDisplay" class="text-center countdown d-none">
                                        <span id="countdownText">10</span> ثانية متبقية
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تعليمات الاختبار -->
                        <div class="test-section">
                            <h4><i class="fas fa-info-circle"></i> تعليمات الاختبار</h4>
                            <ol>
                                <li><strong>اختبار الإشعارات في المقدمة:</strong> انقر على "اختبار إشعار في المقدمة" وستظهر رسالة في الموقع</li>
                                <li><strong>اختبار الإشعارات في الخلفية:</strong> انقر على "اختبار إشعار في الخلفية" ثم أغلق النافذة أو انتقل لعلامة تبويب أخرى</li>
                                <li><strong>اختبار الإشعارات المؤجلة:</strong> انقر على "جدولة إشعار" ثم أغلق النافذة فورًا. ستصلك رسالة بعد 10 ثوانٍ</li>
                                <li><strong>النقر على الإشعار:</strong> عندما تصلك رسالة في الخلفية، انقر عليها لفتح الموقع</li>
                            </ol>
                        </div>
                        
                        <!-- سجل الأحداث -->
                        <div class="test-section">
                            <h4><i class="fas fa-list"></i> سجل الأحداث</h4>
                            <div id="eventLog" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <p class="text-muted">سيتم عرض الأحداث هنا...</p>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <!-- Firebase Messaging -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>
    <!-- Firebase Initialization -->
    <script src="/js/firebase-init.js"></script>
    
    <!-- Notification Sound -->
    <audio id="notificationSound" src="/sounds/notification.mp3" preload="auto"></audio>

    <script>
        let countdownInterval;
        
        $(document).ready(function() {
            // Check system status
            checkPermissionStatus();
            checkServiceWorkerStatus();
            checkFCMTokenStatus();
            
            // Event handlers
            $('#requestPermissionBtn').on('click', requestPermission);
            $('#testForegroundBtn').on('click', testForegroundNotification);
            $('#testBackgroundBtn').on('click', testBackgroundNotification);
            $('#scheduleNotificationBtn').on('click', scheduleNotification);
            
            // Log initial event
            logEvent('صفحة الاختبار تم تحميلها');
        });
        
        function checkPermissionStatus() {
            if (!('Notification' in window)) {
                $('#permissionStatus').html('<span class="text-danger">غير مدعوم</span>');
                return;
            }
            
            const permission = Notification.permission;
            if (permission === 'granted') {
                $('#permissionStatus').html('<span class="text-success">مُفعل</span>');
                $('#requestPermissionBtn').addClass('d-none');
            } else if (permission === 'denied') {
                $('#permissionStatus').html('<span class="text-danger">مرفوض</span>');
                $('#requestPermissionBtn').removeClass('d-none');
            } else {
                $('#permissionStatus').html('<span class="text-warning">غير محدد</span>');
                $('#requestPermissionBtn').removeClass('d-none');
            }
        }
        
        function checkServiceWorkerStatus() {
            if (!('serviceWorker' in navigator)) {
                $('#serviceWorkerStatus').html('<span class="text-danger">غير مدعوم</span>');
                return;
            }
            
            navigator.serviceWorker.getRegistrations().then(registrations => {
                if (registrations.length > 0) {
                    $('#serviceWorkerStatus').html('<span class="text-success">مُفعل</span>');
                } else {
                    $('#serviceWorkerStatus').html('<span class="text-warning">غير مسجل</span>');
                }
            });
        }
        
        function checkFCMTokenStatus() {
            // This will be updated by firebase-init.js
            setTimeout(() => {
                if (window.fcmToken) {
                    $('#fcmTokenStatus').html('<span class="text-success">متوفر</span>');
                } else {
                    $('#fcmTokenStatus').html('<span class="text-warning">غير متوفر</span>');
                }
            }, 2000);
        }
        
        function requestPermission() {
            Notification.requestPermission().then(permission => {
                checkPermissionStatus();
                logEvent('تم طلب إذن الإشعارات: ' + permission);
                
                if (permission === 'granted') {
                    location.reload();
                }
            });
        }
        
        function testForegroundNotification() {
            logEvent('إرسال إشعار تجريبي في المقدمة...');
            
            $.ajax({
                url: 'send_test_notification.php',
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        logEvent('تم إرسال الإشعار بنجاح');
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الإرسال',
                            text: 'تم إرسال الإشعار بنجاح',
                            timer: 3000
                        });
                    } else {
                        logEvent('فشل إرسال الإشعار: ' + response.message);
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: response.message
                        });
                    }
                },
                error: function() {
                    logEvent('خطأ في الاتصال بالسيرفر');
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ في الاتصال'
                    });
                }
            });
        }
        
        function testBackgroundNotification() {
            logEvent('إرسال إشعار تجريبي في الخلفية...');
            
            Swal.fire({
                icon: 'info',
                title: 'اختبار الإشعارات في الخلفية',
                text: 'سيتم إرسال إشعار الآن. أغلق هذه النافذة أو انتقل لعلامة تبويب أخرى لاختبار الإشعارات في الخلفية.',
                showCancelButton: true,
                confirmButtonText: 'إرسال الإشعار',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    testForegroundNotification();
                    
                    setTimeout(() => {
                        Swal.fire({
                            icon: 'info',
                            title: 'تم الإرسال',
                            text: 'تم إرسال الإشعار. أغلق هذه النافذة الآن لاختبار الإشعارات في الخلفية.',
                            timer: 5000
                        });
                    }, 1000);
                }
            });
        }
        
        function scheduleNotification() {
            logEvent('جدولة إشعار مؤجل لمدة 10 ثوانٍ...');
            
            $('#scheduleNotificationBtn').prop('disabled', true);
            $('#countdownDisplay').removeClass('d-none');
            
            let countdown = 10;
            countdownInterval = setInterval(() => {
                countdown--;
                $('#countdownText').text(countdown);
                
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    $('#countdownDisplay').addClass('d-none');
                    $('#scheduleNotificationBtn').prop('disabled', false);
                    
                    // Send the notification
                    testForegroundNotification();
                    logEvent('تم إرسال الإشعار المؤجل');
                }
            }, 1000);
            
            Swal.fire({
                icon: 'info',
                title: 'تم جدولة الإشعار',
                text: 'سيتم إرسال إشعار بعد 10 ثوانٍ. أغلق هذه النافذة الآن لاختبار الإشعارات في الخلفية.',
                timer: 3000
            });
        }
        
        function logEvent(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `<div class="mb-2"><strong>${timestamp}:</strong> ${message}</div>`;
            $('#eventLog').prepend(logEntry);
        }
        
        // Override the updateUnreadCount function to log events
        window.updateUnreadCount = function() {
            logEvent('تم استقبال إشعار جديد');
        };
    </script>
</body>
</html>
