<?php
/**
 * ملف حذف الجرد - مدمج مع نظام الصلاحيات
 * 
 * الشروط المطلوبة للحذف:
 * 1. المستخدم لديه صلاحية delete_inventory
 * 2. الجرد في حالة "قيد التنفيذ" (pending)
 * 3. لا توجد أصناف مرتبطة بالجرد
 */

header('Content-Type: application/json; charset=utf-8');

// تنظيف أي output سابق
if (ob_get_level()) {
    ob_clean();
}

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('inventory', 'access');

$key = getenv('ENCRYPTION_KEY');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

// فحص صلاحية حذف الجرد
if (!hasPermission('inventory', 'delete_inventory')) {
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لحذف الجرد']);
    exit();
}

// التحقق من وجود معرف الجرد
if (!isset($_POST['inventory_id']) || empty($_POST['inventory_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الجرد مطلوب']);
    exit();
}

try {
    $encrypted_inventory_id = $_POST['inventory_id'];
    $inventory_id = decrypt($encrypted_inventory_id, $key);
    
    if (!$inventory_id || !is_numeric($inventory_id)) {
        echo json_encode(['success' => false, 'message' => 'معرف الجرد غير صحيح']);
        exit();
    }
    
    // جلب بيانات الجرد للتحقق من الحالة
    $stmt = $conn->prepare("SELECT inventory_id, status, store_id FROM monthly_inventory WHERE inventory_id = ?");
    $stmt->bind_param("i", $inventory_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $stmt->close();
        echo json_encode(['success' => false, 'message' => 'الجرد غير موجود']);
        exit();
    }
    
    $inventory = $result->fetch_assoc();
    $stmt->close();
    
    // التحقق من أن الجرد في حالة "قيد التنفيذ"
    if ($inventory['status'] !== 'pending') {
        echo json_encode([
            'success' => false, 
            'message' => 'لا يمكن حذف الجرد المكتم��. يمكن حذف الجرد فقط إذا كان في حالة "قيد التنفيذ"'
        ]);
        exit();
    }
    
    // التحقق من عدم وجود أصناف مرتبطة بالجرد
    $stmt = $conn->prepare("SELECT COUNT(*) as items_count FROM monthly_inventory_items WHERE inventory_id = ?");
    $stmt->bind_param("i", $inventory_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $items_data = $result->fetch_assoc();
    $stmt->close();
    
    if ($items_data['items_count'] > 0) {
        echo json_encode([
            'success' => false, 
            'message' => 'لا يمكن حذف الجرد لأنه يحتوي على أصناف مرتبطة به. يجب حذف الأصناف أولاً أو إنهاء الجرد'
        ]);
        exit();
    }
    
    // بدء المعاملة
    $conn->begin_transaction();
    
    try {
        // حذف البيانات المرتبطة بالجرد (إن وجدت)
        
        // حذف المصروفات المرتبطة بالجرد
        $stmt = $conn->prepare("DELETE FROM inventory_expenses WHERE inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $stmt->close();
        
        // حذف إقفال الورديات المرتبطة بالجرد
        $stmt = $conn->prepare("DELETE FROM inventory_shift_closures WHERE inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $stmt->close();
        
        // حذف تحويلات الرصيد المرتبطة بالجرد
        $stmt = $conn->prepare("DELETE FROM inventory_balance_transfers WHERE inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $stmt->close();
        
        // حذف فواتير الشراء المرتبطة بالجرد
        $stmt = $conn->prepare("SELECT inventory_invoice_id FROM inventory_purchase_invoices WHERE inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $invoice_ids = [];
        while ($row = $result->fetch_assoc()) {
            $invoice_ids[] = $row['inventory_invoice_id'];
        }
        $stmt->close();
        
        // حذف أصناف فواتير الشراء
        if (!empty($invoice_ids)) {
            $placeholders = str_repeat('?,', count($invoice_ids) - 1) . '?';
            $stmt = $conn->prepare("DELETE FROM inventory_invoice_items WHERE inventory_invoice_id IN ($placeholders)");
            $stmt->bind_param(str_repeat('i', count($invoice_ids)), ...$invoice_ids);
            $stmt->execute();
            $stmt->close();
        }
        
        // حذف فواتير الشراء
        $stmt = $conn->prepare("DELETE FROM inventory_purchase_invoices WHERE inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $stmt->close();
        
        // حذف الجرد نفسه
        $stmt = $conn->prepare("DELETE FROM monthly_inventory WHERE inventory_id = ?");
        $stmt->bind_param("i", $inventory_id);
        $stmt->execute();
        $stmt->close();
        
        // حذف ملف JSON إن وجد
        $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
        $stmt->bind_param("i", $inventory['store_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($store = $result->fetch_assoc()) {
            $json_file_path = __DIR__ . "/inventory/{$store['name']}_{$inventory_id}.json";
            if (file_exists($json_file_path)) {
                unlink($json_file_path);
            }
        }
        $stmt->close();
        
        // تأكيد المعاملة
        $conn->commit();
        
        // تسجيل العملية في سجل النظام
        if (isset($_SESSION['account_id'])) {
            $user_id = decrypt($_SESSION['account_id'], $key);
            $log_stmt = $conn->prepare("INSERT INTO system_logs (account_id, action_type, table_name, description) VALUES (?, 'delete', 'monthly_inventory', ?)");
            $description = "تم حذف الجرد رقم {$inventory_id}";
            $log_stmt->bind_param("is", $user_id, $description);
            $log_stmt->execute();
            $log_stmt->close();
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم حذف الجرد بنجاح'
        ]);
        
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة الخطأ
        $conn->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("خطأ في حذف الجرد: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ أثناء حذف الجرد. يرجى المحاولة مرة أخرى'
    ]);
}

$conn->close();
?>