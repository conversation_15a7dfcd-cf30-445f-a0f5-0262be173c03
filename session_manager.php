<?php
/**
 * مدير الجلسات المحسن
 * Enhanced Session Manager
 */

class SessionManager {
    private $conn;
    private $key;
    private $isProduction;
    
    public function __construct($database_connection, $encryption_key, $is_production = false) {
        $this->conn = $database_connection;
        $this->key = $encryption_key;
        $this->isProduction = $is_production;
        
        // Set consistent timezone for PHP and MySQL
        date_default_timezone_set('UTC');
        try {
            $this->conn->query("SET time_zone = '+00:00'");
        } catch (Exception $e) {
            error_log("Failed to set MySQL timezone: " . $e->getMessage());
        }
    }
    
    /**
     * تحقق من صحة الجلسة الحالية
     * Validate current session
     */
    public function validateSession() {
        if (empty($_SESSION['account_id'])) {
            return false;
        }
        
        try {
            // Decrypt account ID
            $account_id = decrypt($_SESSION['account_id'], $this->key);
            
            // Check if account still exists and is active
            $stmt = $this->conn->prepare("SELECT status, role, access_type FROM accounts WHERE account_id = ?");
            $stmt->bind_param("i", $account_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                // Account doesn't exist
                $this->clearSession();
                return false;
            }
            
            $user_data = $result->fetch_assoc();
            $stmt->close();
            
            if ($user_data['status'] !== 'active') {
                // Account is not active
                $this->clearSession();
                return false;
            }
            
            // Update session role and access type if they changed
            if ($_SESSION['role'] !== $user_data['role']) {
                $_SESSION['role'] = $user_data['role'];
                error_log("Session role updated for account: " . $account_id . " to: " . $user_data['role']);
            }
            
            $current_access_type = $user_data['access_type'] ?? 'cashier_system';
            if (!isset($_SESSION['access_type']) || $_SESSION['access_type'] !== $current_access_type) {
                $_SESSION['access_type'] = $current_access_type;
                error_log("Session access_type updated for account: " . $account_id . " to: " . $current_access_type);
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Session validation error: " . $e->getMessage());
            $this->clearSession();
            return false;
        }
    }
    
    /**
     * مسح الجلسة والكوكيز
     * Clear session and cookies
     */
    public function clearSession() {
        // Clear session
        session_unset();
        session_destroy();
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_me'])) {
            if ($this->isProduction) {
                setcookie('remember_me', '', time() - 3600, '/', '.elwaled.shop', true, true);
            } else {
                setcookie('remember_me', '', time() - 3600, '/', '', false, true);
            }
        }
        
        // Start fresh session
        session_start();
    }
    
    /**
     * تحقق من توكن "تذكرني"
     * Validate remember me token
     */
    public function validateRememberMeToken() {
        if (empty($_COOKIE['remember_me'])) {
            return false;
        }
        
        $raw_token = $_COOKIE['remember_me'];
        
        try {
            // Get all tokens without time filtering - we'll check expiry in PHP
            $stmt = $this->conn->prepare("SELECT account_id, token_hash, expires_at FROM user_tokens");
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                if (password_verify($raw_token, $row['token_hash'])) {
                    // Token found, check if still valid
                    $token_expiry = new DateTime($row['expires_at']);
                    $current_time = new DateTime();
                    
                    if ($current_time < $token_expiry) {
                        $account_id = $row['account_id'];
                        
                        // Get user data
                        $user_stmt = $this->conn->prepare("SELECT username, role, store_id, name, status, access_type FROM accounts WHERE account_id = ? AND status = 'active'");
                        $user_stmt->bind_param("i", $account_id);
                        $user_stmt->execute();
                        $user_result = $user_stmt->get_result();
                        
                        if ($user_result->num_rows > 0) {
                            $user = $user_result->fetch_assoc();
                            $user_stmt->close();
                            $stmt->close();
                            
                            // Set session variables
                            $_SESSION['account_id'] = encrypt($account_id, $this->key);
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['role'] = $user['role'];
                            $_SESSION['store_id'] = encrypt($user['store_id'], $this->key);
                            $_SESSION['access_type'] = $user['access_type'] ?? 'cashier_system';
                            $_SESSION['remember_me_login'] = true;
                            $_SESSION['login_time'] = time();
                            
                            // Log automatic login
                            $this->logAutoLogin($account_id, $user['name']);
                            // Regenerate token to extend expiry (sliding window)
                            $this->createRememberMeToken($account_id, $user['username']);
                            
                            return $user['role'];
                        }
                        $user_stmt->close();
                    }
                    // Close main statement before breaking
                    $stmt->close();
                    break;
                }
            }
            
            // Close statement if not already closed
            if (isset($stmt)) {
                $stmt->close();
            }
            
        } catch (Exception $e) {
            error_log("Remember me validation error: " . $e->getMessage());
        }
        
        // Clean up expired tokens
        $this->cleanupExpiredTokens();
        
        // If we reach here, token is invalid
        $this->clearInvalidRememberMeToken($raw_token);
        return false;
    }
    
    /**
     * تنظيف التوكنات المنتهية الصلاحية
     * Clean up expired tokens
     */
    private function cleanupExpiredTokens() {
        try {
            $current_time = new DateTime();
            $current_time_str = $current_time->format('Y-m-d H:i:s');
            
            $cleanup_stmt = $this->conn->prepare("DELETE FROM user_tokens WHERE expires_at < ?");
            $cleanup_stmt->bind_param("s", $current_time_str);
            $cleanup_stmt->execute();
            $deleted_count = $cleanup_stmt->affected_rows;
            $cleanup_stmt->close();
            
            if ($deleted_count > 0) {
                error_log("Cleaned up $deleted_count expired tokens");
            }
        } catch (Exception $e) {
            error_log("Token cleanup error: " . $e->getMessage());
        }
    }
    
    /**
     * مسح توكن "تذكرني" غير الصالح
     * Clear invalid remember me token
     */
    private function clearInvalidRememberMeToken($raw_token) {
        // Clear cookie
        if ($this->isProduction) {
            setcookie('remember_me', '', time() - 3600, '/', '.elwaled.shop', true, true);
        } else {
            setcookie('remember_me', '', time() - 3600, '/', '', false, true);
        }
        
        // Clean up expired tokens
        $this->cleanupExpiredTokens();
        
        error_log("Invalid remember me token cleared");
    }
    
    /**
     * تسجيل الدخول التلقائي
     * Log automatic login
     */
    private function logAutoLogin($account_id, $user_name) {
        try {
            $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) VALUES (?, 'auto_login', 'accounts', ?)";
            $description = "تم تسجيل الدخول التلقائي للمستخدم " . $user_name . " عبر تذكرني";
            $log_stmt = $this->conn->prepare($log_sql);
            $log_stmt->bind_param("is", $account_id, $description);
            $log_stmt->execute();
            $log_stmt->close();
        } catch (Exception $e) {
            error_log("Auto login logging error: " . $e->getMessage());
        }
    }
    
    /**
     * إنشاء توكن "تذكرني" جديد
     * Create new remember me token
     */
    public function createRememberMeToken($account_id, $username) {
        try {
            // Clean up old tokens for this user
            $cleanup_stmt = $this->conn->prepare("DELETE FROM user_tokens WHERE account_id = ?");
            $cleanup_stmt->bind_param("i", $account_id);
            $cleanup_stmt->execute();
            $cleanup_stmt->close();
            
            // Generate new token
            $raw_token = bin2hex(random_bytes(32));
            $token_hash = password_hash($raw_token, PASSWORD_DEFAULT);
            
            // Set expiration (30 days)
            $expires = new DateTime('+30 days');
            $expires_str = $expires->format('Y-m-d H:i:s');
            
            // Store in database
            $insert_stmt = $this->conn->prepare("INSERT INTO user_tokens (account_id, token_hash, expires_at) VALUES (?, ?, ?)");
            $insert_stmt->bind_param("iss", $account_id, $token_hash, $expires_str);
            $insert_stmt->execute();
            $insert_stmt->close();
            
            // Set cookie
            $expireTime = $expires->getTimestamp();
            
            if ($this->isProduction) {
                setcookie("remember_me", $raw_token, [
                    'expires' => $expireTime,
                    'path' => '/',
                    'domain' => '.elwaled.shop',
                    'secure' => true,
                    'httponly' => true,
                    'samesite' => 'Strict'
                ]);
            } else {
                setcookie("remember_me", $raw_token, [
                    'expires' => $expireTime,
                    'path' => '/',
                    'domain' => '',
                    'secure' => false,
                    'httponly' => true,
                    'samesite' => 'Strict'
                ]);
            }
            
            error_log("Remember me token created for user: " . $username . " (ID: " . $account_id . ")");
            return true;
            
        } catch (Exception $e) {
            error_log("Error creating remember me token: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * مسح جميع توكنات المستخدم
     * Clear all user tokens
     */
    public function clearUserTokens($account_id) {
        try {
            $cleanup_stmt = $this->conn->prepare("DELETE FROM user_tokens WHERE account_id = ?");
            $cleanup_stmt->bind_param("i", $account_id);
            $cleanup_stmt->execute();
            $cleanup_stmt->close();
            
            // Clear cookie
            if ($this->isProduction) {
                setcookie('remember_me', '', time() - 3600, '/', '.elwaled.shop', true, true);
            } else {
                setcookie('remember_me', '', time() - 3600, '/', '', false, true);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Error clearing user tokens: " . $e->getMessage());
            return false;
        }
    }
}
?>