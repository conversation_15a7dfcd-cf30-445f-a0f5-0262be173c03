<?php
// منع التخزين المؤقت لضمان الحصول على أحدث ثيم
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/plain; charset=utf-8');

session_start();

// التحقق من وجود معرف الحساب في الجلسة
if (!isset($_SESSION['account_id'])) {
    error_log("getAccountTheme.php: No account_id in session");
    http_response_code(401);
    exit('unauthorized');
}

include 'db_connection.php';
include 'encryption_functions.php';

// التحقق من مفتاح التشفير
$key = getenv('ENCRYPTION_KEY');
if (empty($key)) {
    error_log("getAccountTheme.php: Encryption key not found");
    http_response_code(500);
    exit('error');
}

try {
    $encrypted_account_id = $_SESSION['account_id'];
    $accountId = decrypt($encrypted_account_id, $key);

    if (!$accountId || !is_numeric($accountId)) {
        error_log("getAccountTheme.php: Invalid account ID after decryption");
        http_response_code(400);
        exit('invalid_account');
    }

    // جلب الثيم من قاعدة البيانات
    $stmt = $conn->prepare("SELECT theme FROM accounts WHERE account_id = ? AND status = 'active'");
    $stmt->bind_param("i", $accountId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        $theme = $row['theme'];
        // التأكد من أن الثيم صالح
        if (in_array(strtolower($theme), ['light', 'dark'])) {
            echo strtolower($theme);
        } else {
            // إذا كان الثيم غير صالح، استخدم الافتراضي
            echo 'light';
        }
    } else {
        error_log("getAccountTheme.php: Account not found or inactive - ID: " . $accountId);
        echo 'light'; // ثيم افتراضي
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("getAccountTheme.php: Error - " . $e->getMessage());
    http_response_code(500);
    echo 'light'; // ثيم افتراضي في حالة الخطأ
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
