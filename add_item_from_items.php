<?php

include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['item_type'])) {
    $item_type = $_POST['item_type'];
    
    // تحديد الفرع والتصنيف
    $store_id = null;
    $category_id = null;
    
    if (isset($_POST['category_id'])) {
        // إضافة من صفحة items.php (تصنيف محدد)
        $encrypted_category_id = $_POST['category_id'];
        $category_id = decrypt($encrypted_category_id, $key);
        
        if ($category_id === false) {
            echo json_encode(['success' => false, 'message' => 'خطأ في فك التشفير.']);
            exit();
        }
        
        // Fetch store ID using category ID
        $store_sql = "SELECT store_id FROM categories WHERE category_id = ?";
        $store_stmt = $conn->prepare($store_sql);
        $store_stmt->bind_param("i", $category_id);
        $store_stmt->execute();
        $store_result = $store_stmt->get_result();
        $store_id = $store_result->fetch_assoc()['store_id'];
        $store_stmt->close();
        
    } elseif (isset($_POST['store_id'])) {
        // إضافة من صفحة item_store.php (فرع محدد)
        $encrypted_store_id = $_POST['store_id'];
        $store_id = decrypt($encrypted_store_id, $key);
        
        if ($store_id === false) {
            echo json_encode(['success' => false, 'message' => 'خطأ في فك التشفير.']);
            exit();
        }
        
        // الحصول على التصنيف من البيانات المرسلة
        if (isset($_POST['category_id'])) {
            $encrypted_category_id = $_POST['category_id'];
            $category_id = decrypt($encrypted_category_id, $key);
            
            if ($category_id === false) {
                echo json_encode(['success' => false, 'message' => 'خطأ في فك التشفير للتصنيف.']);
                exit();
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'التصنيف مطلوب.']);
            exit();
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'معرف الفرع أو التصنيف مفقود.']);
        exit();
    }

    $pieces_per_box = 0;

    if ($item_type == 'piece' || $item_type == 'other') {
        $item_name = $_POST['item_name_piece'];
        $cost = $_POST['cost_piece'];
        $price = $_POST['price1_piece'];
        $quantity = $_POST['quantity_piece'];
    } elseif ($item_type == 'box') {
        $item_name = $_POST['item_name_box'];
        $cost = $_POST['cost_box'];
        $price = $_POST['price1_box'];
        $quantity = $_POST['quantity_box'];
        $pieces_per_box = $_POST['pieces_per_box'];
    } elseif ($item_type == 'fridge') {
        $item_name = $_POST['item_name_fridge'];
        $cost = $_POST['cost_fridge'];
        $price = $_POST['price1_fridge'];
        $quantity = $_POST['quantity_fridge'];
    } elseif ($item_type == 'service') {
    $item_name = $_POST['item_name_service'];
    $cost = $_POST['cost_service'];
    $price = $_POST['price_service'];
    $quantity = $_POST['quantity_service'];
    $service_provider = $_POST['service_provider'];
    $service_type = $_POST['service_type'];
    
    // تحديد قيمة is_custom_priced بناءً على الـ checkbox المرسل من النموذج
    $is_custom_priced = isset($_POST['is_custom_priced']) ? 1 : 0;
    
    if ($service_type === 'cash_withdraw' || $service_type === 'cash_deposit') {
    $commission_threshold = $_POST['commission_threshold'] ?? 1000.00;
    $commission_fixed = $_POST['commission_fixed'] ?? 10.00;
    $commission_per_thousand = $_POST['commission_per_thousand'] ?? 15.00;
    } else {
    $commission_threshold = 0.00;
    $commission_fixed = 0.00;
    $commission_per_thousand = 0.00;
    }
    }

    $barcode = isset($_POST['barcode']) ? $_POST['barcode'] : '';

    if (isset($_POST['auto_generate_barcode'])) {
        // Fetch the highest barcode in the table and increment it by 1
        $barcode_sql = "SELECT MAX(CAST(barcode AS UNSIGNED)) AS max_barcode FROM items";
        $barcode_result = $conn->query($barcode_sql);
        $max_barcode = $barcode_result->fetch_assoc()['max_barcode'];
        $barcode = $max_barcode ? max($max_barcode + 1, 10000) : 10000; // Start from 10000 if no barcodes exist or max barcode is less than 10000
    } else {
        // Check if the manually entered barcode is unique in the current store only
        $barcode_check_sql = "SELECT COUNT(*) AS count FROM items i 
                             JOIN categories c ON i.category_id = c.category_id 
                             WHERE i.barcode = ? AND c.store_id = ?";
        $barcode_check_stmt = $conn->prepare($barcode_check_sql);
        $barcode_check_stmt->bind_param("si", $barcode, $store_id);
        $barcode_check_stmt->execute();
        $barcode_check_result = $barcode_check_stmt->get_result();
        $barcode_count = $barcode_check_result->fetch_assoc()['count'];
        $barcode_check_stmt->close();

        if ($barcode_count > 0) {
            echo json_encode(['success' => false, 'message' => 'الباركود المدخل مكرر في هذا الفرع.']);
            exit();
        }
    }

    if ($item_type == 'service') {
        $sql = "INSERT INTO items (name, cost, price, quantity, category_id, barcode, type, pieces_per_box, store_id, service_provider, service_type, commission_threshold, commission_fixed, commission_per_thousand, is_custom_priced) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sdddissiissdddi", $item_name, $cost, $price, $quantity, $category_id, $barcode, $item_type, $pieces_per_box, $store_id, $service_provider, $service_type, $commission_threshold, $commission_fixed, $commission_per_thousand, $is_custom_priced);
    } else {
        // للأصناف غير الخدمات، is_custom_priced يكون دائماً FALSE
        $is_custom_priced = 0;
        $sql = "INSERT INTO items (name, cost, price, quantity, category_id, barcode, type, pieces_per_box, store_id, is_custom_priced) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sdddissiii", $item_name, $cost, $price, $quantity, $category_id, $barcode, $item_type, $pieces_per_box, $store_id, $is_custom_priced);
    }

    if ($stmt->execute()) {
        $item_id = $stmt->insert_id;

        // Handle image uploads
        if (isset($_FILES['item_images']) && !empty($_FILES['item_images']['name'][0])) {
            $upload_dir = 'uploads/items/';
            
            // Create directory if it doesn't exist
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $uploaded_images = [];
            $total_files = count($_FILES['item_images']['name']);

            for ($i = 0; $i < $total_files; $i++) {
                if ($_FILES['item_images']['error'][$i] == UPLOAD_ERR_OK) {
                    $file_tmp = $_FILES['item_images']['tmp_name'][$i];
                    $file_name = $_FILES['item_images']['name'][$i];
                    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                    
                    // Check if file is an image
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                    if (in_array($file_ext, $allowed_extensions)) {
                        // Generate unique filename and check for duplicates
                        $counter = 0;
                        do {
                            $new_filename = uniqid() . ($counter > 0 ? '_' . $counter : '') . '.' . $file_ext;
                            $file_path = $upload_dir . $new_filename;
                            $counter++;
                            
                            // Check if file path already exists in database
                            $check_path_sql = "SELECT COUNT(*) as count FROM item_images WHERE img_path = ?";
                            $check_path_stmt = $conn->prepare($check_path_sql);
                            $check_path_stmt->bind_param("s", $file_path);
                            $check_path_stmt->execute();
                            $check_result = $check_path_stmt->get_result();
                            $path_exists_in_db = $check_result->fetch_assoc()['count'] > 0;
                            $check_path_stmt->close();
                            
                        } while (file_exists($file_path) || $path_exists_in_db);
                        
                        if (move_uploaded_file($file_tmp, $file_path)) {
                            // Save image path to database
                            $img_sql = "INSERT INTO item_images (item_id, img_path) VALUES (?, ?)";
                            $img_stmt = $conn->prepare($img_sql);
                            $img_stmt->bind_param("is", $item_id, $file_path);
                            $img_stmt->execute();
                            $img_stmt->close();
                            
                            $uploaded_images[] = $file_path;
                        }
                    }
                }
            }
        }

        // Log the item addition action
        session_start();
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'add', 'items', ?, ?)";
        $description = "تم إضافة صنف جديد باسم $item_name بقيمة $cost وسعر بيع $price";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $item_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        // Add the opening balance transaction
        $transaction_sql = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'opening_balance', 0, ?)";
        $transaction_stmt = $conn->prepare($transaction_sql);
        $transaction_stmt->bind_param("ii", $item_id, $quantity);
        $transaction_stmt->execute();
        $transaction_stmt->close();

        $added_stores_count = 0;
        
        // الحصول على اسم التصنيف الحالي
        $current_category_name = null;
        $current_category_sql = "SELECT name FROM categories WHERE category_id = ?";
        $current_category_stmt = $conn->prepare($current_category_sql);
        $current_category_stmt->bind_param("i", $category_id);
        $current_category_stmt->execute();
        $current_category_result = $current_category_stmt->get_result();

        if ($current_category_result->num_rows > 0) {
            $current_category_name = $current_category_result->fetch_assoc()['name'];
        }
        $current_category_stmt->close();

        // التحقق من الإضافة في فروع أخرى
        if (isset($_POST['add_to_other_stores']) && $_POST['add_to_other_stores'] === 'true' && isset($_POST['selected_stores'])) {
            $selected_stores = json_decode($_POST['selected_stores'], true);
            
            if ($selected_stores && is_array($selected_stores)) {
                foreach ($selected_stores as $store_data) {
                    // التحقق من نوع البيانات المرسلة
                    if (is_array($store_data) && isset($store_data['store_id']) && isset($store_data['category_id'])) {
                        $encrypted_other_store_id = $store_data['store_id'];
                        $encrypted_other_category_id = $store_data['category_id'];
                    } else {
                        // للتوافق مع النسخة القديمة
                        $encrypted_other_store_id = $store_data;
                        $encrypted_other_category_id = null;
                    }
                    
                    $other_store_id = decrypt($encrypted_other_store_id, $key);
                    
                    if ($other_store_id === false) {
                        continue; // تخطي الفروع التي لا يمكن فك تشفيرها
                    }
                    
                    if ($encrypted_other_category_id) {
                        // استخدام التصنيف المحدد مسبقاً
                        $other_category_id = decrypt($encrypted_other_category_id, $key);
                    } else {
                        // البحث عن التصنيف بنفس الاسم في الفرع الآخر
                        $category_sql = "SELECT category_id FROM categories WHERE store_id = ? AND name = ?";
                        $category_stmt = $conn->prepare($category_sql);
                        $category_stmt->bind_param("is", $other_store_id, $current_category_name);
                        $category_stmt->execute();
                        $category_result = $category_stmt->get_result();
                        
                        if ($category_result->num_rows > 0) {
                            $other_category_id = $category_result->fetch_assoc()['category_id'];
                        } else {
                            $other_category_id = null;
                        }
                        $category_stmt->close();
                    }
                    
                    // المتابعة فقط إذا وجد التصنيف المطابق
                    if ($other_category_id) {
                        
                        // التحقق من عدم وجود صنف بنفس الباركود في هذا الفرع
                        $barcode_check_sql = "SELECT COUNT(*) as count FROM items i 
                                            JOIN categories c ON i.category_id = c.category_id 
                                            WHERE i.barcode = ? AND c.store_id = ?";
                        $barcode_check_stmt = $conn->prepare($barcode_check_sql);
                        $barcode_check_stmt->bind_param("si", $barcode, $other_store_id);
                        $barcode_check_stmt->execute();
                        $barcode_check_result = $barcode_check_stmt->get_result();
                        $barcode_exists = $barcode_check_result->fetch_assoc()['count'] > 0;
                        $barcode_check_stmt->close();
                        
                        if (!$barcode_exists) {
                            // إضافة الصنف في الفرع الآخر
                            if ($item_type == 'service') {
                                $other_item_sql = "INSERT INTO items (name, cost, price, quantity, category_id, barcode, type, pieces_per_box, store_id, service_provider, service_type, commission_threshold, commission_fixed, commission_per_thousand, is_custom_priced) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                                $other_item_stmt = $conn->prepare($other_item_sql);
                                $other_item_stmt->bind_param("sdddissiissdddi", $item_name, $cost, $price, $quantity, $other_category_id, $barcode, $item_type, $pieces_per_box, $other_store_id, $service_provider, $service_type, $commission_threshold, $commission_fixed, $commission_per_thousand, $is_custom_priced);
                            } else {
                                $other_item_sql = "INSERT INTO items (name, cost, price, quantity, category_id, barcode, type, pieces_per_box, store_id, is_custom_priced) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                                $other_item_stmt = $conn->prepare($other_item_sql);
                                $other_item_stmt->bind_param("sdddissiii", $item_name, $cost, $price, $quantity, $other_category_id, $barcode, $item_type, $pieces_per_box, $other_store_id, $is_custom_priced);
                            }
                            
                            if ($other_item_stmt->execute()) {
                                $other_item_id = $other_item_stmt->insert_id;
                                
                                // إضافة معاملة الرصيد الافتتاحي للصنف في الفرع الآخر
                                $other_transaction_sql = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'opening_balance', 0, ?)";
                                $other_transaction_stmt = $conn->prepare($other_transaction_sql);
                                $other_transaction_stmt->bind_param("ii", $other_item_id, $quantity);
                                $other_transaction_stmt->execute();
                                $other_transaction_stmt->close();
                                
                                // نسخ الصور إلى الص��ف الجديد في الفرع الآخر
                                if (!empty($uploaded_images)) {
                                    foreach ($uploaded_images as $image_path) {
                                        $copy_img_sql = "INSERT INTO item_images (item_id, img_path) VALUES (?, ?)";
                                        $copy_img_stmt = $conn->prepare($copy_img_sql);
                                        $copy_img_stmt->bind_param("is", $other_item_id, $image_path);
                                        $copy_img_stmt->execute();
                                        $copy_img_stmt->close();
                                    }
                                }
                                
                                $added_stores_count++;
                            }
                            $other_item_stmt->close();
                        }
                    }
                }
            }
        }

        $message = 'تمت الإضافة بنجاح';
        if ($added_stores_count > 0) {
            $message .= " وتم إضافة الصنف في $added_stores_count فرع إضافي";
        }
        
        echo json_encode([
            'success' => true, 
            'message' => $message,
            'added_stores_count' => $added_stores_count
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في الإضافة: ' . htmlspecialchars($stmt->error)]);
    }
    $stmt->close();
    exit();
}

echo json_encode(['success' => false, 'message' => 'Invalid request']);
exit();
?>