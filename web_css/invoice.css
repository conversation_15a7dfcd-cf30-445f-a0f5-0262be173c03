/* Set font to Cairo */
body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
}

/* Responsive Layout Control */
.main-content {
    display: block;
}

.responsive-layout {
    display: none;
}

/* Large screens - show responsive layout */
@media (min-width: 769px) {
    .main-content {
        display: none;
    }
    
    .responsive-layout {
        display: flex !important;
        gap: 20px;
        padding: 20px;
        max-width: 1200px;
        margin: 20px auto;
    }
    
    .responsive-section {
        flex: 1;
        background: var(--white);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        height: 600px;
        overflow-y: auto;
    }
}

/* الألوان المحسنة للثيم الداكن */
:root {
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --success-color: #28a745;
    --white: #ffffff;
    --light-bg: #f0f2f5;
    --border-color: #dee2e6;
    
    /* ألوان محسنة للثيم الداكن */
    --dark-bg: #0f1419;
    --dark-surface: #1a2332;
    --dark-surface-light: #242b3d;
    --dark-text: #e1e8f0;
    --dark-text-secondary: #b8c5d1;
    --dark-border: #2d3748;
    --blue-soft: #6ba3d6;
}

/* Update container styles to match style_web.css */
.container {
    display: flex;
    gap: 30px;
    padding: 30px;
    max-width: 1200px;
    margin: 30px auto;
    text-align: center;
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.categories,
.items,
.selected-items {
    flex: 1;
    border: none;
    padding: 20px;
    border-radius: 15px;
    background-color: #ffffff;
    color: #333;
    height: 800px; /* Increased height for the sections */
    overflow-y: auto; /* Enable vertical scrolling within the sections */
    scrollbar-width: thin; /* For modern browsers */
    scrollbar-color: #3f51b5 #f5f7fa; /* Custom scrollbar colors */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative; /* Ensure the section is the positioning context */
    display: flex;
    flex-direction: column; /* Stack children vertically */
}

.categories {
    flex: 0.4; /* Reduced width for the categories section */
}

.categories h3 {
    font-size: 1.5rem; /* Adjusted font size for the title */
    margin-bottom: 15px;
    color: #3B82F6;
    text-align: center;
}

.search-bar {
    width: 80%; /* Reduced width for the search bar */
    height: 35px; /* Maintain reduced height */
    font-size: 14px; /* Adjust font size for better alignment */
    margin: 0 auto 15px; /* Center the search bar and add spacing */
}

.categories ul {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center; /* Center the category cards */
    padding: 0;
}

.categories ul li {
    flex: 0 1 calc(45% - 10px); /* Adjust width to fit within the reduced section */
    background-color: #3b82f6;
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 0.9rem; /* Adjusted font size for better fit */
    text-align: center;
}

.categories ul li:hover {
    background-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.items {
    flex: 1.4; /* Increased width for the items section */
}

.items .search-bar {
    width: 80%; /* Reduced width for the search bar in the items section */
    height: 35px; /* Maintain reduced height */
    font-size: 14px; /* Adjust font size for better alignment */
    margin: 0 auto 15px; /* Center the search bar and add spacing */
}

.selected-items {
    flex: 2.5; /* Increased width for the selected-items section */
    display: flex;
    flex-direction: column;
    height: 800px; /* Ensure the section has a fixed height */
}

.table-wrapper {
    flex: 1; /* Allow the table to take up available space */
    overflow-y: auto; /* Enable scrolling for the table */
}

.selected-items table {
    flex: 1; /* Allow the table to grow and take available space */
    overflow-y: auto; /* Enable scrolling for the table if needed */
}

.categories::-webkit-scrollbar,
.items::-webkit-scrollbar,
.selected-items::-webkit-scrollbar {
    width: 8px; /* Width of the scrollbar */
}

.categories::-webkit-scrollbar-thumb,
.items::-webkit-scrollbar-thumb,
.selected-items::-webkit-scrollbar-thumb {
    background-color: #3f51b5; /* Scrollbar thumb color */
    border-radius: 10px; /* Rounded scrollbar thumb */
}

.categories::-webkit-scrollbar-track,
.items::-webkit-scrollbar-track,
.selected-items::-webkit-scrollbar-track {
    background-color: #f5f7fa; /* Scrollbar track color */
}

/* Update section headers */
.categories h3,
.items h3,
.selected-items h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: #3B82F6;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

.categories ul,
.items ul {
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.items ul li {
    background-color: #3b82f6;
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 500;
}

.items ul li:hover {
    background-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Update input fields */
.input-field,
.search-bar {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 20px;
}

.search-bar::placeholder {
    font-size: 14px;
    color: #9ca3af;
}

.input-field:focus,
.search-bar:focus {
    border-color: #3f51b5;
    outline: none;
    box-shadow: 0 0 5px rgba(63, 81, 181, 0.4);
}

/* Update table styles */
.items table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 30px;
    border-radius: 12px;
    overflow: hidden;
    background-color: #f9f9f9;
}

.items th {
    background-color: #3f51b5;
    color: #ffffff;
    font-size: 16px;
    padding: 15px;
    text-transform: uppercase;
    font-weight: bold;
}

.items td {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.items tr {
    transition: all 0.3s ease;
}

.items tr:hover {
    background-color: #e3f2fd;
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 2px solid #3f51b5;
    border-radius: 8px;
    padding: 8px;
    font-size: 1rem;
    color: #333;
    font-weight: 600;
    background-color: #ffffff;
}

/* Update buttons to match style_web.css */
.add-btn,
.quantity-btn,
.action-btn {
    background-color: #3f51b5;
    color: #fff;
    border: none;
    padding: 12px 25px;
    font-size: 16px;
    border-radius: 50px;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.2s ease;
}

.add-btn:hover,
.quantity-btn:hover,
.action-btn:hover {
    background-color: #303f9f;
    transform: translateY(-2px);
}

.total-display,
.remaining-display {
    font-size: 1.8rem;
    font-weight: 700;
    color: #60a5fa;
    margin: 20px 0;
    padding: 15px;
    background-color: #374151;
    border-radius: 10px;
    text-align: right;
}

.total-display {
    position: sticky; /* Stick the total bar within the selected-items section */
    bottom: 0; /* Align it to the bottom of the section */
    z-index: 10; /* Ensure it appears above other content */
    background-color: #374151; /* Ensure the background color matches */
    padding: 15px; /* Maintain padding for the total bar */
    border-radius: 10px; /* Keep the rounded corners */
    text-align: right; /* Align text to the right */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1); /* Add a shadow for better visibility */
    display: flex; /* Use flexbox for alignment */
    justify-content: space-between; /* Space between total and button */
    align-items: center; /* Center items vertically */
}

.total-display .add-btn {
    margin: 0; /* Remove default margin */
    padding: 10px 20px; /* Adjust padding for the button */
    font-size: 14px; /* Adjust font size */
}

.image-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10px;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    text-align: center;
}

.image-upload-label {
    display: inline-block;
    background-color: #3B82F6;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s ease;
    margin-bottom: 10px;
}

.image-upload-label:hover {
    background-color: #2563EB;
}

.image-preview {
    display: flex; /* Use flexbox for side-by-side layout */
    flex-wrap: wrap; /* Allow wrapping if there are too many images */
    gap: 15px; /* Add spacing between images */
    justify-content: center; /* Center the images horizontally */
    margin-top: 20px;
}

.image-preview div {
    position: relative;
    width: 100px; /* Set a fixed width for each image container */
    height: 100px; /* Set a fixed height for each image container */
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensure the image fits within the container */
}

.remove-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border-radius: 50%;
    padding: 5px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-icon:hover {
    background: #dc2626;
}

/* Enhanced Dark Theme */
[data-theme="dark"] {
    --color-bg: #0d1117; /* Darker and more elegant */
    --color-fg: #e6edf3; /* Softer text color for better readability */
    --color-primary: #1f6feb; /* Brighter blue for better contrast */
    --color-secondary: #161b22; /* Smooth dark gray for backgrounds */
    --color-hover: rgba(31, 111, 235, 0.15); /* Subtle hover effect */
    --color-header-text: #ffffff;
    --color-button-text: #ffffff;
}

body {
    background-color: var(--color-bg);
    color: var(--color-fg);
}

.container {
    background-color: var(--color-secondary);
    color: var(--color-fg);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5); /* Add subtle shadow for depth */
}

.categories,
.items,
.selected-items {
    background-color: var(--color-secondary);
    color: var(--color-fg);
    scrollbar-color: var(--color-primary) var(--color-secondary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4); /* Add shadow for better separation */
}

.categories::-webkit-scrollbar-thumb,
.items::-webkit-scrollbar-thumb,
.selected-items::-webkit-scrollbar-thumb {
    background-color: var(--color-primary);
}

.categories h3,
.items h3,
.selected-items h3 {
    color: var(--color-primary);
    border-bottom: 2px solid var(--color-primary); /* Add a border for emphasis */
}

.items table {
    background-color: var(--color-secondary);
    border-radius: 8px; /* Add rounded corners */
    overflow: hidden;
}

.items th {
    background-color: var(--color-primary);
    color: var(--color-header-text);
    text-transform: uppercase;
    font-size: 14px;
    padding: 10px;
}

.items td {
    color: var(--color-fg);
    padding: 10px;
    border-bottom: 1px solid #2c313a; /* Subtle border for separation */
}

.items tr:hover {
    background-color: var(--color-hover);
    transition: background-color 0.3s ease; /* Smooth hover transition */
}

.quantity-input {
    border-color: var(--color-primary);
    color: var(--color-fg);
    background-color: var(--color-secondary);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5); /* Add depth to input */
}

.add-btn,
.quantity-btn,
.action-btn {
    background-color: var(--color-primary);
    color: var(--color-button-text);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4); /* Add shadow for buttons */
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.add-btn:hover,
.quantity-btn:hover,
.action-btn:hover {
    background-color: #3a8bff; /* Slightly brighter hover effect */
    transform: translateY(-2px);
}

.total-display {
    background-color: var(--color-secondary);
    color: var(--color-primary);
    border-top: 2px solid var(--color-primary); /* Add a border for separation */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.4); /* Add shadow for better visibility */
}

.image-upload-container {
    background-color: var(--color-secondary);
    border: 1px dashed var(--color-primary); /* Dashed border for better visibility */
    color: var(--color-fg);
}

.image-upload-label {
    background-color: var(--color-primary);
    color: var(--color-button-text);
    transition: background-color 0.3s ease;
}

.image-upload-label:hover {
    background-color: #3a8bff; /* Brighter hover effect */
}

.image-preview div {
    border: 1px solid var(--color-primary); /* Add border for better visibility */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4); /* Add shadow for depth */
}

.image-preview img {
    border-radius: 5px; /* Add rounded corners */
}

.remove-icon {
    background: rgba(255, 69, 58, 0.9); /* Brighter red for better visibility */
    color: var(--color-header-text);
}

.remove-icon:hover {
    background: rgba(255, 69, 58, 1); /* Slightly brighter on hover */
}

/* دعم الوضع الداكن */
.dark-mode body {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

.dark-mode .container {
    background-color: var(--dark-surface);
    color: var(--dark-text);
}

.dark-mode .categories,
.dark-mode .items,
.dark-mode .selected-items {
    background-color: var(--dark-surface-light);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

.dark-mode .categories h3,
.dark-mode .items h3,
.dark-mode .selected-items h3 {
    color: var(--blue-soft);
}

.dark-mode .search-bar input {
    background-color: var(--dark-surface);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

.dark-mode .search-bar input:focus {
    border-color: var(--blue-soft);
    box-shadow: 0 0 0 3px rgba(107, 163, 214, 0.1);
}

.dark-mode .category-btn {
    background-color: var(--dark-surface);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

.dark-mode .category-btn:hover,
.dark-mode .category-btn.active {
    background-color: var(--blue-soft);
    color: white;
}

.dark-mode .item-card {
    background-color: var(--dark-surface);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

.dark-mode .item-card:hover {
    border-color: var(--blue-soft);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-mode .add-btn,
.dark-mode .quantity-btn,
.dark-mode .action-btn {
    background-color: var(--blue-soft);
    color: white;
}

.dark-mode .add-btn:hover,
.dark-mode .quantity-btn:hover,
.dark-mode .action-btn:hover {
    background-color: #5a91c4;
}

.dark-mode .selected-item {
    background-color: var(--dark-surface);
    border-color: var(--dark-border);
}

.dark-mode .total-display {
    background-color: var(--dark-surface-light);
    color: var(--blue-soft);
    border-color: var(--blue-soft);
}
