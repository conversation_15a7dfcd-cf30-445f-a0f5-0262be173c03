/* Enhanced Page Header with Integrated Statistics */
.page-header-with-stats {
    margin-bottom: 30px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 25px 30px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 2px solid rgba(63, 81, 181, 0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 1;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, var(--color-primary) 0%, #58a6ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.header-text h2 {
    margin: 0 0 8px 0;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--color-primary);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.store-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    color: var(--color-fg);
    opacity: 0.8;
}

.store-info i {
    color: var(--color-primary);
    font-size: 1rem;
}

/* Enhanced Add Button */
.enhanced-add-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 100%);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(63, 81, 181, 0.3);
    position: relative;
    overflow: hidden;
}

.enhanced-add-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.enhanced-add-btn:hover::before {
    left: 100%;
}

.enhanced-add-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(63, 81, 181, 0.4);
}

.enhanced-add-btn:active {
    transform: translateY(-1px) scale(1.02);
}

.enhanced-add-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.enhanced-add-btn:hover i {
    transform: rotate(90deg);
}

/* Enhanced Statistics Dashboard */
.stats-dashboard {
    margin: 0;
    padding: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 100%);
    z-index: 1;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border-color: var(--color-primary);
}

.stat-card.primary::before {
    background: linear-gradient(90deg, #3f51b5 0%, #5c6bc0 100%);
}

.stat-card.success::before {
    background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

.stat-card.warning::before {
    background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%);
}

.stat-card.info::before {
    background: linear-gradient(90deg, #2196f3 0%, #42a5f5 100%);
}

.stat-card.secondary::before {
    background: linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%);
}

.stat-card.danger::before {
    background: linear-gradient(90deg, #f44336 0%, #ef5350 100%);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%);
    box-shadow: 0 4px 15px rgba(63, 81, 181, 0.3);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.stat-card.secondary .stat-icon {
    background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
}

.stat-card.danger .stat-icon {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.stat-content {
    flex: 1;
    overflow: hidden;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: 5px;
    line-height: 1;
    word-wrap: break-word;
    word-break: break-all;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-fg);
    margin-bottom: 5px;
}

.stat-sublabel {
    font-size: 0.9rem;
    color: var(--color-fg);
    opacity: 0.7;
}

.stat-progress {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.stat-card.success .progress-bar {
    background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

.stat-card.secondary .progress-bar {
    background: linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%);
}

/* Stats Details Section */
.stats-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.detail-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(63, 81, 181, 0.1);
    position: relative;
    overflow: hidden;
}

.detail-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 100%);
    z-index: 1;
}

.detail-section h4 {
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--color-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.type-distribution {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.type-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 0;
}

.type-label {
    font-weight: 600;
    color: var(--color-fg);
    min-width: 80px;
}

.type-count {
    font-weight: 700;
    color: var(--color-primary);
    min-width: 40px;
    text-align: center;
    background: rgba(63, 81, 181, 0.1);
    padding: 4px 8px;
    border-radius: 8px;
}

.type-bar {
    flex: 1;
    height: 8px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.type-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.quick-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: rgba(63, 81, 181, 0.05);
    border-radius: 10px;
    border-left: 4px solid var(--color-primary);
}

.info-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.text-danger {
    color: #f44336 !important;
}

.text-muted {
    color: #9e9e9e !important;
}

.text-primary {
    color: var(--color-primary) !important;
}

/* Dark Mode Support for Stats Dashboard */
[data-theme="dark"] .page-header {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%);
    border-color: rgba(88, 166, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .page-header::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .header-icon {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="dark"] .header-text h2 {
    color: #58a6ff;
}

[data-theme="dark"] .store-info {
    color: #c9d1d9;
}

[data-theme="dark"] .store-info i {
    color: #58a6ff;
}

[data-theme="dark"] .enhanced-add-btn {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.3);
}

[data-theme="dark"] .enhanced-add-btn:hover {
    box-shadow: 0 10px 30px rgba(88, 166, 255, 0.4);
}

[data-theme="dark"] .stat-card {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stat-card:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    border-color: #58a6ff;
}

[data-theme="dark"] .stat-card::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] .stat-card.primary::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] .stat-card.success::before {
    background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

[data-theme="dark"] .stat-card.warning::before {
    background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%);
}

[data-theme="dark"] .stat-card.info::before {
    background: linear-gradient(90deg, #2196f3 0%, #42a5f5 100%);
}

[data-theme="dark"] .stat-card.secondary::before {
    background: linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%);
}

[data-theme="dark"] .stat-card.danger::before {
    background: linear-gradient(90deg, #f44336 0%, #ef5350 100%);
}

[data-theme="dark"] .stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
    box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
}

[data-theme="dark"] .stat-number {
    color: #58a6ff;
}

[data-theme="dark"] .stat-label {
    color: #c9d1d9;
}

[data-theme="dark"] .stat-sublabel {
    color: #8a9ba8;
}

[data-theme="dark"] .stat-progress {
    background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .progress-bar {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] .stat-card.success .progress-bar {
    background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

[data-theme="dark"] .stat-card.secondary .progress-bar {
    background: linear-gradient(90deg, #9c27b0 0%, #ba68c8 100%);
}

[data-theme="dark"] .detail-section {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%);
    border-color: rgba(88, 166, 255, 0.2);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .detail-section::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] .detail-section h4 {
    color: #58a6ff;
}

[data-theme="dark"] .type-label {
    color: #c9d1d9;
}

[data-theme="dark"] .type-count {
    color: #58a6ff;
    background: rgba(88, 166, 255, 0.15);
}

[data-theme="dark"] .type-bar {
    background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .type-progress {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] .info-item {
    background: rgba(88, 166, 255, 0.08);
    border-left-color: #58a6ff;
}

/* Responsive Design for Stats Dashboard */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-icon {
        font-size: 2.5rem;
    }

    .header-text h2 {
        font-size: 1.8rem;
    }

    .store-info {
        justify-content: center;
        font-size: 1rem;
    }

    .enhanced-add-btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 20px;
        gap: 15px;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 1rem;
    }

    .stats-details {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 20px;
    }

    .detail-section {
        padding: 20px;
    }

    .detail-section h4 {
        font-size: 1.1rem;
    }

    .type-item {
        gap: 10px;
        padding: 10px 0;
    }

    .type-label {
        min-width: 60px;
        font-size: 0.9rem;
    }

    .type-count {
        min-width: 30px;
        font-size: 0.9rem;
    }

    .info-item {
        padding: 10px 12px;
        gap: 10px;
    }

    .info-item i {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .page-header-with-stats {
        margin-bottom: 20px;
    }

    .page-header {
        padding: 15px;
        margin-bottom: 15px;
    }

    .header-icon {
        font-size: 2rem;
    }

    .header-text h2 {
        font-size: 1.5rem;
    }

    .store-info {
        font-size: 0.9rem;
    }

    .enhanced-add-btn {
        padding: 10px 18px;
        font-size: 0.85rem;
        gap: 8px;
    }

    .enhanced-add-btn span {
        display: none;
    }

    .enhanced-add-btn i {
        font-size: 1.2rem;
    }

    .stats-dashboard {
        margin: 15px 0;
    }

    .stat-card {
        padding: 15px;
        gap: 12px;
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    .stat-sublabel {
        font-size: 0.8rem;
    }

    .detail-section {
        padding: 15px;
    }

    .detail-section h4 {
        font-size: 1rem;
        margin-bottom: 15px;
    }

    .type-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .type-bar {
        width: 100%;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: right;
    }
}

/* Enhanced Search Container */
.search-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto 30px auto;
}

.search-container .search-bar {
    width: 100%;
    padding: 18px 60px 18px 28px;
    border: 2px solid rgba(63, 81, 181, 0.2);
    border-radius: 25px;
    font-size: 1.1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    outline: none;
    color: var(--color-fg);
}

.search-container .search-bar:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(63, 81, 181, 0.15), 0 12px 35px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.search-container .search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-primary);
    font-size: 1.2rem;
    pointer-events: none;
    transition: all 0.3s ease;
}

.search-container:focus-within .search-icon {
    color: var(--color-primary);
    transform: translateY(-50%) scale(1.1);
}

/* Dark Mode Support for Search Container */
[data-theme="dark"] .search-container .search-bar {
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 100%);
    border-color: rgba(88, 166, 255, 0.3);
    color: #c9d1d9;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .search-container .search-bar:focus {
    border-color: #58a6ff;
    box-shadow: 0 0 0 4px rgba(88, 166, 255, 0.2), 0 12px 35px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .search-container .search-bar::placeholder {
    color: #8a9ba8;
}

[data-theme="dark"] .search-container .search-icon {
    color: #58a6ff;
}

/* Responsive Design for Search Container */
@media (max-width: 768px) {
    .search-container {
        max-width: 100%;
        margin-bottom: 25px;
    }

    .search-container .search-bar {
        padding: 15px 50px 15px 20px;
        font-size: 1rem;
    }

    .search-container .search-icon {
        right: 15px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .search-container .search-bar {
        padding: 12px 45px 12px 18px;
        font-size: 0.9rem;
    }

    .search-container .search-icon {
        right: 12px;
        font-size: 1rem;
    }
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 3500;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(26, 35, 50, 0.95) 100%);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    animation: fadeIn 0.4s ease-out;
    overflow-y: auto;
    padding: 20px 0;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    margin: auto;
    padding: 0;
    border-radius: 20px;
    width: 95%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(63, 81, 181, 0.1);
    position: relative;
    animation: slideIn 0.3s ease-out;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    border-radius: 20px 20px 0 0;
    z-index: 1;
}

/* Modal Header */
.modal-content h2 {
    margin: 0;
    padding: 25px 30px 20px 30px;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-primary);
    text-align: center;
    border-bottom: 2px solid rgba(63, 81, 181, 0.1);
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
    position: relative;
    z-index: 2;
}

.modal-content h2::before {
    content: '⚙️';
    margin-left: 10px;
    font-size: 1.5rem;
}

/* Close Button */
.close {
    position: absolute;
    top: 15px;
    left: 20px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid transparent;
}

.close:hover,
.close:focus {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    transform: scale(1.1);
}

/* Form Styling */
.modal-content form {
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.modal-content label {
    font-weight: 600;
    color: var(--color-fg);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.modal-content label::before {
    content: '▶';
    color: var(--color-primary);
    font-size: 0.8rem;
}

.input-field {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid rgba(63, 81, 181, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    transition: all 0.3s ease;
    outline: none;
    color: var(--color-fg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.input-field:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(63, 81, 181, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    background: #ffffff;
}

.input-field:disabled {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    cursor: not-allowed;
}

/* Custom Select Field Styling */
.select-wrapper {
    position: relative;
    width: 100%;
}

select.input-field {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding-right: 40px !important; /* Make space for the arrow */
}

.select-wrapper::after {
    content: '▼';
    font-size: 1.2rem;
    color: var(--color-primary);
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    transition: all 0.3s ease;
}

.select-wrapper:hover::after {
    transform: translateY(-50%) scale(1.1);
    color: #303f9f;
}

/* File Input Styling */
input[type="file"].input-field {
    padding: 12px 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed rgba(63, 81, 181, 0.3);
    cursor: pointer;
    position: relative;
}

input[type="file"].input-field:hover {
    border-color: var(--color-primary);
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

input[type="file"].input-field::file-selector-button {
    background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    margin-left: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

input[type="file"].input-field::file-selector-button:hover {
    background: linear-gradient(135deg, #303f9f 0%, #1976d2 100%);
    transform: scale(1.05);
}

/* Small Text */
small {
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 5px;
    display: block;
    font-style: italic;
}

/* Item Fields Container */
.item-fields {
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.03) 0%, rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid rgba(63, 81, 181, 0.1);
    border-radius: 15px;
    padding: 25px;
    margin: 15px 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.item-fields::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 100%);
    z-index: 1;
}

.item-fields:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Checkbox Styling */
input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--color-primary);
    cursor: pointer;
    margin-left: 8px;
}

/* Submit Button */
.add-btn {
    background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 100%);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(63, 81, 181, 0.3);
    margin-top: 20px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.add-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.add-btn:hover::before {
    left: 100%;
}

.add-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(63, 81, 181, 0.4);
}

.add-btn:active {
    transform: translateY(-1px) scale(1);
}

/* Error Message */
.error-message {
    color: #dc3545;
    font-size: 0.9rem;
    margin-top: 5px;
    padding: 8px 12px;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 8px;
    display: none;
}

/* Dark Mode Support */
[data-theme="dark"] .modal {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(13, 17, 23, 0.9) 100%);
}

[data-theme="dark"] .modal-content {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%);
    border-color: rgba(88, 166, 255, 0.2);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .modal-content::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .modal-content h2 {
    color: #58a6ff;
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.08) 0%, rgba(26, 35, 50, 0.95) 100%);
    border-bottom-color: rgba(88, 166, 255, 0.2);
}

[data-theme="dark"] .close {
    color: #8a9ba8;
    background: rgba(26, 35, 50, 0.8);
}

[data-theme="dark"] .close:hover {
    color: #f85149;
    background: rgba(248, 81, 73, 0.1);
    border-color: #f85149;
}

[data-theme="dark"] .modal-content label {
    color: #c9d1d9;
}

[data-theme="dark"] .modal-content label::before {
    color: #58a6ff;
}

[data-theme="dark"] .input-field {
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 100%);
    border-color: rgba(88, 166, 255, 0.3);
    color: #c9d1d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .input-field:focus {
    border-color: #58a6ff;
    box-shadow: 0 0 0 4px rgba(88, 166, 255, 0.2), 0 4px 12px rgba(0, 0, 0, 0.3);
    background: #2a3441;
}

[data-theme="dark"] .input-field:disabled {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%);
    color: #6e7681;
}

[data-theme="dark"] .input-field::placeholder {
    color: #8a9ba8;
}

/* Dark Mode Custom Select Field */
[data-theme="dark"] .select-wrapper::after {
    color: #58a6ff;
}

[data-theme="dark"] .select-wrapper:hover::after {
    color: #4dabf7;
}

[data-theme="dark"] input[type="file"].input-field {
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 100%);
    border-color: rgba(88, 166, 255, 0.4);
}

[data-theme="dark"] input[type="file"].input-field:hover {
    border-color: #58a6ff;
    background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
}

[data-theme="dark"] input[type="file"].input-field::file-selector-button {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] input[type="file"].input-field::file-selector-button:hover {
    background: linear-gradient(135deg, #4dabf7 0%, #1976d2 100%);
}

[data-theme="dark"] small {
    color: #8a9ba8;
}

[data-theme="dark"] .item-fields {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.05) 0%, rgba(26, 35, 50, 0.95) 100%);
    border-color: rgba(88, 166, 255, 0.2);
}

[data-theme="dark"] .item-fields::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] input[type="checkbox"] {
    accent-color: #58a6ff;
}

[data-theme="dark"] .add-btn {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.3);
}

[data-theme="dark"] .add-btn:hover {
    box-shadow: 0 10px 30px rgba(88, 166, 255, 0.4);
}

[data-theme="dark"] .error-message {
    color: #f85149;
    background: rgba(248, 81, 73, 0.1);
    border-color: rgba(248, 81, 73, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        width: 98%;
        max-width: none;
        margin: 10px;
        border-radius: 15px;
    }
    
    .modal-content h2 {
        font-size: 1.5rem;
        padding: 20px 25px 15px 25px;
    }
    
    .modal-content form {
        padding: 20px;
        gap: 15px;
    }
    
    .input-field {
        padding: 12px 15px;
        font-size: 0.95rem;
    }
    
    .add-btn {
        padding: 15px 25px;
        font-size: 1rem;
    }
    
    .close {
        top: 12px;
        left: 15px;
        font-size: 24px;
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .modal {
        padding: 10px 0;
    }
    
    .modal-content {
        width: 95%;
        margin: 5px;
        border-radius: 12px;
    }
    
    .modal-content h2 {
        font-size: 1.3rem;
        padding: 15px 20px 12px 20px;
    }
    
    .modal-content form {
        padding: 15px;
        gap: 12px;
    }
    
    .input-field {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
    
    .add-btn {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .item-fields {
        padding: 15px;
        margin: 10px 0;
    }
    
    .close {
        top: 10px;
        left: 12px;
        font-size: 20px;
        width: 30px;
        height: 30px;
    }
}

/* Enhanced Current Images Container */
.current-images {
    margin: 15px 0;
    padding: 20px;
    border: 2px solid var(--color-primary);
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.current-images::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 1;
}

.current-images:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.current-images h4 {
    margin-bottom: 15px;
    color: var(--color-primary);
    font-size: 1.2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    z-index: 2;
}

.current-images h4::before {
    content: '📷';
    font-size: 1.1rem;
}

/* Dark Mode Support for Current Images */
[data-theme="dark"] .current-images {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.08) 0%, rgba(26, 35, 50, 0.95) 100%);
    border-color: #58a6ff;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .current-images::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .current-images h4 {
    color: #58a6ff;
}

/* Enhanced Image Container */
.image-container {
    display: inline-block;
    margin: 8px;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid transparent;
}

.image-container:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--color-primary);
}

.image-container img {
    width: 110px;
    height: 110px;
    object-fit: cover;
    border-radius: 10px;
    transition: all 0.3s ease;
    display: block;
}

.image-container:hover img {
    transform: scale(1.05);
}

.delete-image-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    z-index: 10;
}

.delete-image-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    transform: scale(1.1);
    box-shadow: 0 6px 18px rgba(220, 53, 69, 0.6);
}

.delete-image-btn:active {
    transform: scale(0.95);
}

/* Dark Mode Support for Image Container */
[data-theme="dark"] .image-container {
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .image-container:hover {
    border-color: #58a6ff;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .delete-image-btn {
    border-color: #242b3d;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.6);
}

/* Enhanced Image Icons */
.no-images-icon {
    background: linear-gradient(135deg, #ff8c00 0%, #ff7700 100%) !important;
    color: white !important;
    border: 2px solid rgba(255, 140, 0, 0.3) !important;
    box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3) !important;
    transition: all 0.3s ease !important;
}

.no-images-icon:hover {
    background: linear-gradient(135deg, #ff7700 0%, #e66600 100%) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 6px 20px rgba(255, 140, 0, 0.5) !important;
}

.has-images-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    border: 2px solid rgba(40, 167, 69, 0.3) !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
    transition: all 0.3s ease !important;
}

.has-images-icon:hover {
    background: linear-gradient(135deg, #218838 0%, #17a2b8 100%) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.5) !important;
}

/* Enhanced Images Gallery */
.images-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 15px;
    padding: 20px 0;
    max-height: 450px;
    overflow-y: auto;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.5) 0%, rgba(233, 236, 239, 0.3) 100%);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(63, 81, 181, 0.1);
}

.modal-image-item {
    text-align: center;
    border: 2px solid var(--color-primary);
    border-radius: 15px;
    padding: 10px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.modal-image-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 1;
}

.modal-image-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #58a6ff;
}

.modal-image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.modal-image-item img:hover {
    transform: scale(1.08);
}

/* Enhanced No Images Message */
.no-images-message {
    text-align: center;
    padding: 60px 40px;
    color: var(--color-fg);
    font-style: italic;
    font-size: 1.2rem;
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
    border: 2px dashed var(--color-primary);
    border-radius: 20px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.no-images-message::before {
    content: '📷';
    font-size: 3rem;
    display: block;
    margin-bottom: 15px;
    opacity: 0.6;
}

.no-images-message::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 1;
}

/* Enhanced Image Preview Modal */
.image-preview-modal {
    display: none;
    position: fixed;
    z-index: 1600;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 35, 50, 0.95) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.image-preview-content {
    margin: auto;
    display: block;
    width: 85%;
    max-width: 800px;
    max-height: 85%;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

/* Dark Mode Support for Enhanced Elements */
[data-theme="dark"] .images-gallery {
    background: linear-gradient(135deg, rgba(26, 35, 50, 0.5) 0%, rgba(36, 43, 61, 0.3) 100%);
    border-color: rgba(88, 166, 255, 0.2);
}

[data-theme="dark"] .modal-image-item {
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 100%);
    border-color: #58a6ff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modal-image-item::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .modal-image-item:hover {
    border-color: #4dabf7;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .no-images-message {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.08) 0%, rgba(26, 35, 50, 0.95) 100%);
    border-color: #58a6ff;
    color: #c9d1d9;
}

[data-theme="dark"] .no-images-message::after {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .image-preview-modal {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(13, 17, 23, 0.98) 100%);
}

/* Favorite Button Styles */
.favorite-btn {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    border: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.favorite-btn.not-favorite {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
}

.favorite-btn.not-favorite:hover {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.favorite-btn.is-favorite {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.favorite-btn.is-favorite:hover {
    background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(253, 126, 20, 0.4);
}

/* Filter Container Styles - High Priority */
.filter-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%) !important;
    padding: 25px 30px !important;
    border-radius: 25px !important;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.08), 0 4px 15px rgba(63, 81, 181, 0.1) !important;
    margin-bottom: 30px !important;
    display: flex !important;
    align-items: center !important;
    gap: 25px !important;
    flex-wrap: nowrap !important;
    flex-direction: row !important;
    border: 2px solid rgba(63, 81, 181, 0.08) !important;
    position: relative !important;
    overflow: hidden !important;
}

.filter-container::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 3px !important;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%) !important;
    z-index: 1 !important;
}

/* Dark Mode Filter Container */
[data-theme="dark"] .filter-container {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 50%, #2a3441 100%) !important;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.4), 0 4px 15px rgba(88, 166, 255, 0.15) !important;
    border: 2px solid rgba(88, 166, 255, 0.2) !important;
}

[data-theme="dark"] .filter-container::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%) !important;
}

.filter-label {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    font-weight: 800 !important;
    color: var(--color-primary) !important;
    font-size: 1.2rem !important;
    padding: 12px 20px !important;
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.08) 0%, rgba(63, 81, 181, 0.04) 100%) !important;
    border-radius: 20px !important;
    border: 1px solid rgba(63, 81, 181, 0.15) !important;
    position: relative !important;
    z-index: 2 !important;
}

.filter-label i {
    font-size: 1.3rem !important;
    background: linear-gradient(135deg, var(--color-primary) 0%, #58a6ff 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Dark Mode Filter Label */
[data-theme="dark"] .filter-label {
    color: #58a6ff !important;
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.12) 0%, rgba(88, 166, 255, 0.06) 100%) !important;
    border: 1px solid rgba(88, 166, 255, 0.25) !important;
}

[data-theme="dark"] .filter-label i {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.filter-container .filter-btn,
button.filter-btn {
    width: 60px !important;
    height: 60px !important;
    border: 3px solid rgba(63, 81, 181, 0.2) !important;
    border-radius: 50% !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%) !important;
    color: #6c757d !important;
    font-size: 1.3rem !important;
    cursor: pointer !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(63, 81, 181, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
    outline: none !important;
    padding: 0 !important;
    margin: 0 !important;
    min-width: 60px !important;
    min-height: 60px !important;
    max-width: 60px !important;
    max-height: 60px !important;
}

/* Dark Mode Filter Buttons */
[data-theme="dark"] .filter-container .filter-btn,
[data-theme="dark"] button.filter-btn {
    border: 3px solid rgba(88, 166, 255, 0.3) !important;
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 50%, #1a2332 100%) !important;
    color: #8a9ba8 !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(88, 166, 255, 0.15) !important;
}

.filter-container .filter-btn i,
button.filter-btn i {
    z-index: 2 !important;
    position: relative !important;
}

.filter-container .filter-btn.active,
button.filter-btn.active {
    border-color: var(--color-primary) !important;
    background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 50%, #1976d2 100%) !important;
    color: white !important;
    box-shadow: 0 8px 25px rgba(63, 81, 181, 0.4), 0 4px 15px rgba(63, 81, 181, 0.2) !important;
    transform: scale(1.08) !important;
}

.filter-container .filter-btn:hover:not(.active),
button.filter-btn:hover:not(.active) {
    transform: scale(1.12) translateY(-3px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 4px 15px rgba(63, 81, 181, 0.2) !important;
    border-color: var(--color-primary) !important;
    background: linear-gradient(135deg, #ffffff 0%, rgba(63, 81, 181, 0.05) 100%) !important;
}

.filter-container .filter-btn:active,
button.filter-btn:active {
    transform: scale(0.95) !important;
}

/* Dark Mode Active and Hover States */
[data-theme="dark"] .filter-container .filter-btn.active,
[data-theme="dark"] button.filter-btn.active {
    border-color: #58a6ff !important;
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 50%, #1976d2 100%) !important;
    color: white !important;
    box-shadow: 0 8px 25px rgba(88, 166, 255, 0.4), 0 4px 15px rgba(88, 166, 255, 0.2) !important;
    transform: scale(1.08) !important;
}

[data-theme="dark"] .filter-container .filter-btn:hover:not(.active),
[data-theme="dark"] button.filter-btn:hover:not(.active) {
    transform: scale(1.12) translateY(-3px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 4px 15px rgba(88, 166, 255, 0.3) !important;
    border-color: #58a6ff !important;
    background: linear-gradient(135deg, #2a3441 0%, rgba(88, 166, 255, 0.1) 100%) !important;
    color: #b8c5d1 !important;
}

/* Default Layout for Large Screens */
.filter-buttons-row {
    display: flex !important;
    gap: 20px !important;
    justify-content: flex-start !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
}

/* Special colors for status filter buttons */
.filter-btn[data-filter="active"] {
    border-color: rgba(40, 167, 69, 0.3) !important;
}

.filter-btn[data-filter="active"].active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 50%, #17a2b8 100%) !important;
    border-color: #28a745 !important;
}

.filter-btn[data-filter="inactive"] {
    border-color: rgba(220, 53, 69, 0.3) !important;
}

.filter-btn[data-filter="inactive"].active {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 50%, #c0392b 100%) !important;
    border-color: #dc3545 !important;
}

/* Dark mode status buttons */
[data-theme="dark"] .filter-btn[data-filter="active"] {
    border-color: rgba(76, 175, 80, 0.4) !important;
}

[data-theme="dark"] .filter-btn[data-filter="active"].active {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 50%, #81c784 100%) !important;
    border-color: #4caf50 !important;
}

[data-theme="dark"] .filter-btn[data-filter="inactive"] {
    border-color: rgba(244, 67, 54, 0.4) !important;
}

[data-theme="dark"] .filter-btn[data-filter="inactive"].active {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 50%, #e57373 100%) !important;
    border-color: #f44336 !important;
}

/* Large Screens - Ensure Horizontal Layout */
@media (min-width: 769px) {
    .filter-container {
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
    }

    .filter-buttons-row {
        display: flex !important;
        flex-direction: row !important;
        gap: 20px !important;
        justify-content: flex-start !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) and (min-width: 601px) {
    .filter-container {
        padding: 20px 25px !important;
        gap: 20px !important;
        border-radius: 20px !important;
        flex-direction: row !important;
        flex-wrap: wrap !important;
    }
    
    .filter-container .filter-btn,
    button.filter-btn {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.1rem !important;
        min-width: 50px !important;
        min-height: 50px !important;
        max-width: 50px !important;
        max-height: 50px !important;
    }
    
    .filter-label {
        font-size: 1.1rem !important;
        padding: 10px 16px !important;
        gap: 12px !important;
    }

    .filter-label i {
        font-size: 1.2rem !important;
    }

    .filter-buttons-row {
        display: flex !important;
        gap: 12px !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
    }
}

@media (max-width: 600px) {
    .filter-container {
        padding: 15px 20px !important;
        gap: 15px !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .filter-label {
        margin-bottom: 5px !important;
    }

    .filter-buttons-row {
        justify-content: flex-start !important;
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        padding: 8px 5px !important;
        width: 100% !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        gap: 15px !important;
    }

    .filter-buttons-row::-webkit-scrollbar {
        display: none !important;
    }

    .filter-container .filter-btn,
    button.filter-btn {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.1rem !important;
        min-width: 50px !important;
        min-height: 50px !important;
        max-width: 50px !important;
        max-height: 50px !important;
        flex-shrink: 0 !important;
    }
}

@media (max-width: 480px) {
    .filter-container {
        padding: 12px 15px !important;
        gap: 12px !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .filter-label {
        justify-content: center !important;
        margin-bottom: 5px !important;
        font-size: 0.95rem !important;
        padding: 6px 12px !important;
    }

    .filter-buttons-row {
        display: flex !important;
        justify-content: flex-start !important;
        gap: 12px !important;
        flex-wrap: nowrap !important;
        width: 100% !important;
        overflow-x: auto !important;
        padding: 8px 5px !important;
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE and Edge */
    }

    .filter-buttons-row::-webkit-scrollbar {
        display: none !important; /* Chrome, Safari and Opera */
    }

    .filter-container .filter-btn,
    button.filter-btn {
        width: 42px !important;
        height: 42px !important;
        font-size: 0.95rem !important;
        min-width: 42px !important;
        min-height: 42px !important;
        max-width: 42px !important;
        max-height: 42px !important;
        flex-shrink: 0 !important;
    }
}

@media (max-width: 360px) {
    .filter-container {
        padding: 10px 12px !important;
        gap: 10px !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .filter-label {
        font-size: 0.85rem !important;
        padding: 5px 10px !important;
        margin-bottom: 3px !important;
    }

    .filter-buttons-row {
        gap: 10px !important;
        justify-content: flex-start !important;
        overflow-x: auto !important;
        padding: 6px 3px !important;
        width: 100% !important;
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE and Edge */
    }

    .filter-buttons-row::-webkit-scrollbar {
        display: none !important; /* Chrome, Safari and Opera */
    }

    .filter-container .filter-btn,
    button.filter-btn {
        width: 38px !important;
        height: 38px !important;
        font-size: 0.85rem !important;
        min-width: 38px !important;
        min-height: 38px !important;
        max-width: 38px !important;
        max-height: 38px !important;
        flex-shrink: 0 !important;
    }
}

/* Extra small screens - ensure all 6 buttons are visible */
@media (max-width: 320px) {
    .filter-container {
        padding: 8px 10px !important;
        gap: 8px !important;
    }

    .filter-label {
        font-size: 0.8rem !important;
        padding: 4px 8px !important;
    }

    .filter-buttons-row {
        gap: 8px !important;
        padding: 5px 2px !important;
    }

    .filter-container .filter-btn,
    button.filter-btn {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.8rem !important;
        min-width: 35px !important;
        min-height: 35px !important;
        max-width: 35px !important;
        max-height: 35px !important;
    }
}

/* Similar Items Styles */
.similar-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #fff;
    transition: all 0.3s ease;
}

.similar-item:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.similar-item.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.similar-item-checkbox {
    margin-left: 10px;
}

.similar-item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.similar-item-name {
    font-weight: bold;
    color: #333;
}

.similar-item-store {
    color: #666;
    font-size: 0.9em;
}

.similar-item-barcode {
    color: #888;
    font-size: 0.85em;
    font-family: 'Courier New', monospace;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    margin: 2px 0;
}

.similar-item-details {
    display: flex;
    gap: 15px;
    font-size: 0.85em;
    color: #777;
}

.loading-similar-items {
    text-align: center;
    padding: 20px;
    color: #666;
}

.no-similar-items {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* Dark Mode Support for Similar Items */
[data-theme="dark"] .similar-item {
    background-color: #2a3441;
    border-color: #3a4a5c;
}

[data-theme="dark"] .similar-item:hover {
    background-color: #3a4a5c;
    border-color: #4a5a6c;
}

[data-theme="dark"] .similar-item.selected {
    background-color: #1e3a5f;
    border-color: #58a6ff;
}

[data-theme="dark"] .similar-item-name {
    color: #e6edf3;
}

[data-theme="dark"] .similar-item-store {
    color: #8a9ba8;
}

[data-theme="dark"] .similar-item-barcode {
    color: #8a9ba8;
    background-color: #3a4a5c;
}

[data-theme="dark"] .similar-item-details {
    color: #8a9ba8;
}

[data-theme="dark"] .loading-similar-items {
    color: #8a9ba8;
}

[data-theme="dark"] .no-similar-items {
    color: #6e7681;
}

/* Dark Mode Support for Other Stores Edit Container */
[data-theme="dark"] #other_stores_items {
    background-color: #1a2332;
    border-color: #3a4a5c;
}

[data-theme="dark"] #other_stores_items h4 {
    color: #8a9ba8;
}

[data-theme="dark"] #similar_items_list {
    background-color: #242b3d;
    border: 1px solid #3a4a5c;
    border-radius: 5px;
}

/* Dark Mode for Edit Modal Container */
[data-theme="dark"] .modal-content div[style*="background-color: #f9f9f9"] {
    background-color: #1a2332 !important;
    border-color: #3a4a5c !important;
}

[data-theme="dark"] .modal-content label[style*="color: #333"] span {
    color: #e6edf3 !important;
}

[data-theme="dark"] .modal-content .fas.fa-info-circle {
    color: #58a6ff !important;
}

/* Other Stores Edit Container Styles */
.other-stores-edit-container {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.other-stores-edit-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.other-stores-edit-text {
    font-weight: bold;
    color: #333;
}

.other-stores-info-icon {
    color: #666;
}

.other-stores-items-container {
    display: none;
    margin-top: 15px;
}

.other-stores-items-title {
    color: #666;
    margin-bottom: 10px;
}

.similar-items-list {
    max-height: 200px;
    overflow-y: auto;
}

/* Dark Mode for Other Stores Edit Container */
[data-theme="dark"] .other-stores-edit-container {
    background-color: #1a2332;
    border-color: #3a4a5c;
}

[data-theme="dark"] .other-stores-edit-text {
    color: #e6edf3;
}

[data-theme="dark"] .other-stores-info-icon {
    color: #58a6ff;
}

[data-theme="dark"] .other-stores-items-container {
    background-color: #1a2332;
}

[data-theme="dark"] .other-stores-items-title {
    color: #8a9ba8;
}

[data-theme="dark"] .similar-items-list {
    background-color: #242b3d;
    border: 1px solid #3a4a5c;
    border-radius: 5px;
}

/* Dark Mode for Checkbox */
[data-theme="dark"] .other-stores-edit-label input[type="checkbox"] {
    accent-color: #58a6ff;
}

[data-theme="dark"] .similar-item-checkbox {
    accent-color: #58a6ff;
}

/* Dark Mode Scrollbar for Similar Items List */
[data-theme="dark"] .similar-items-list::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] .similar-items-list::-webkit-scrollbar-track {
    background: #1a2332;
    border-radius: 4px;
}

[data-theme="dark"] .similar-items-list::-webkit-scrollbar-thumb {
    background: #3a4a5c;
    border-radius: 4px;
}

[data-theme="dark"] .similar-items-list::-webkit-scrollbar-thumb:hover {
    background: #4a5a6c;
}

/* Other Stores Add Container Styles */
.other-stores-add-container {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.other-stores-add-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.other-stores-add-text {
    font-weight: bold;
    color: #333;
}

.available-stores-list {
    max-height: 200px;
    overflow-y: auto;
    margin-top: 10px;
}

.available-store {
    display: flex;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #fff;
    transition: all 0.3s ease;
}

.available-store:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.available-store.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.store-checkbox {
    margin-left: 10px;
}

.store-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.store-name {
    font-weight: bold;
    color: #333;
}

.store-categories {
    color: #666;
    font-size: 0.9em;
}

.loading-stores {
    text-align: center;
    padding: 20px;
    color: #666;
}

.no-stores {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* Dark Mode Support for Other Stores Add Container */
[data-theme="dark"] .other-stores-add-container {
    background-color: #1a2332;
    border-color: #3a4a5c;
}

[data-theme="dark"] .other-stores-add-text {
    color: #e6edf3;
}

[data-theme="dark"] .available-store {
    background-color: #2a3441;
    border-color: #3a4a5c;
}

[data-theme="dark"] .available-store:hover {
    background-color: #3a4a5c;
    border-color: #4a5a6c;
}

[data-theme="dark"] .available-store.selected {
    background-color: #1e3a5f;
    border-color: #58a6ff;
}

[data-theme="dark"] .store-name {
    color: #e6edf3;
}

[data-theme="dark"] .store-categories {
    color: #8a9ba8;
}

[data-theme="dark"] .loading-stores {
    color: #8a9ba8;
}

[data-theme="dark"] .no-stores {
    color: #6e7681;
}

[data-theme="dark"] .store-checkbox {
    accent-color: #58a6ff;
}

/* Dark Mode Scrollbar for Available Stores List */
[data-theme="dark"] .available-stores-list::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] .available-stores-list::-webkit-scrollbar-track {
    background: #1a2332;
    border-radius: 4px;
}

[data-theme="dark"] .available-stores-list::-webkit-scrollbar-thumb {
    background: #3a4a5c;
    border-radius: 4px;
}

[data-theme="dark"] .available-stores-list::-webkit-scrollbar-thumb:hover {
    background: #4a5a6c;
}

/* Enhanced Items Table Styles */
.items-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(63, 81, 181, 0.1);
    margin-bottom: 30px;
    position: relative;
}

.items-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 0;
}

.items-table thead th {
    background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 100%);
    color: white;
    padding: 18px 15px;
    font-weight: 700;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    z-index: 2;
}

.items-table tbody tr {
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.items-table tbody tr:nth-child(even) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.items-table tbody tr:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(63, 81, 181, 0.15);
}

.items-table tbody td {
    padding: 15px;
    border-bottom: 1px solid rgba(63, 81, 181, 0.1);
    font-weight: 500;
    color: var(--color-fg);
    vertical-align: middle;
}

/* Enhanced Status Cell */
.status-cell {
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.status-cell:hover {
    transform: scale(1.05);
}

/* Enhanced Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-btn:hover::before {
    opacity: 1;
}

.action-btn:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.action-btn:active {
    transform: translateY(0) scale(0.95);
}

/* Dark Mode Support for Enhanced Table */
[data-theme="dark"] .items-table {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%);
    border-color: rgba(88, 166, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .items-table::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .items-table thead th {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
}

[data-theme="dark"] .items-table tbody tr {
    background: linear-gradient(135deg, #1a2332 0%, #1f2937 100%);
}

[data-theme="dark"] .items-table tbody tr:nth-child(even) {
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 100%);
}

[data-theme="dark"] .items-table tbody tr:hover {
    background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
    box-shadow: 0 4px 15px rgba(88, 166, 255, 0.25);
}

[data-theme="dark"] .items-table tbody td {
    border-bottom-color: rgba(88, 166, 255, 0.15);
    color: #c9d1d9;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .items-table {
        border-radius: 12px;
        margin-bottom: 20px;
    }
    
    .items-table thead th {
        padding: 12px 8px;
        font-size: 0.85rem;
    }
    
    .items-table tbody td {
        padding: 10px 8px;
        font-size: 0.9rem;
    }
    
    .action-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .favorite-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .items-table {
        border-radius: 10px;
    }
    
    .items-table thead th {
        padding: 10px 6px;
        font-size: 0.8rem;
    }
    
    .items-table tbody td {
        padding: 8px 6px;
        font-size: 0.85rem;
    }
    
    .action-btn {
        width: 32px;
        height: 32px;
        font-size: 0.85rem;
    }
    
    .favorite-btn {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
    
    .action-buttons {
        gap: 6px;
    }
}

/* Enhanced Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.95) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 15px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(63, 81, 181, 0.2);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

[data-theme="dark"] .loading-overlay {
    background: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(36, 43, 61, 0.95) 100%);
}

[data-theme="dark"] .loading-spinner {
    border-color: rgba(88, 166, 255, 0.2);
    border-top-color: #58a6ff;
}

/* Enhanced Empty State */
.empty-state {
    text-align: center;
    padding: 60px 40px;
    color: var(--color-fg);
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
    border: 2px dashed var(--color-primary);
    border-radius: 20px;
    margin: 30px 0;
    position: relative;
    overflow: hidden;
}

.empty-state::before {
    content: '📦';
    font-size: 4rem;
    display: block;
    margin-bottom: 20px;
    opacity: 0.6;
}

.empty-state::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 1;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: var(--color-primary);
    margin-bottom: 10px;
    font-weight: 700;
}

.empty-state p {
    font-size: 1.1rem;
    opacity: 0.8;
    margin: 0;
}

[data-theme="dark"] .empty-state {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.08) 0%, rgba(26, 35, 50, 0.95) 100%);
    border-color: #58a6ff;
    color: #c9d1d9;
}

[data-theme="dark"] .empty-state::after {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .empty-state h3 {
    color: #58a6ff;
}

/* Enhanced Page Header Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px 30px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 2px solid rgba(63, 81, 181, 0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 1;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, var(--color-primary) 0%, #58a6ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.header-text h2 {
    margin: 0 0 8px 0;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--color-primary);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.page-store-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    color: var(--color-fg);
    opacity: 0.8;
}

.page-store-info i {
    color: var(--color-primary);
    font-size: 1rem;
}

/* Enhanced Add Button */
.enhanced-add-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 100%);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(63, 81, 181, 0.3);
    position: relative;
    overflow: hidden;
}

.enhanced-add-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.enhanced-add-btn:hover::before {
    left: 100%;
}

.enhanced-add-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(63, 81, 181, 0.4);
}

.enhanced-add-btn:active {
    transform: translateY(-1px) scale(1.02);
}

.enhanced-add-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.enhanced-add-btn:hover i {
    transform: rotate(90deg);
}

/* Dark Mode Support for Page Header */
[data-theme="dark"] .page-header {
    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%);
    border-color: rgba(88, 166, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .page-header::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .header-icon {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="dark"] .header-text h2 {
    color: #58a6ff;
}

[data-theme="dark"] .page-store-info {
    color: #c9d1d9;
}

[data-theme="dark"] .page-store-info i {
    color: #58a6ff;
}

[data-theme="dark"] .enhanced-add-btn {
    background: linear-gradient(135deg, #58a6ff 0%, #4dabf7 100%);
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.3);
}

[data-theme="dark"] .enhanced-add-btn:hover {
    box-shadow: 0 10px 30px rgba(88, 166, 255, 0.4);
}

/* Responsive Design for Page Header */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-icon {
        font-size: 2.5rem;
    }

    .header-text h2 {
        font-size: 1.8rem;
    }

    .page-store-info {
        justify-content: center;
        font-size: 1rem;
    }

    .enhanced-add-btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 15px;
        margin-bottom: 20px;
    }

    .header-icon {
        font-size: 2rem;
    }

    .header-text h2 {
        font-size: 1.5rem;
    }

    .page-store-info {
        font-size: 0.9rem;
    }

    .enhanced-add-btn {
        padding: 10px 18px;
        font-size: 0.85rem;
        gap: 8px;
    }

    .enhanced-add-btn span {
        display: none;
    }

    .enhanced-add-btn i {
        font-size: 1.2rem;
    }
}

/* Enhanced Search Container */
.search-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto 30px auto;
}

.search-container .search-bar {
    width: 100%;
    padding: 18px 60px 18px 28px;
    border: 2px solid rgba(63, 81, 181, 0.2);
    border-radius: 25px;
    font-size: 1.1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    outline: none;
    color: var(--color-fg);
}

.search-container .search-bar:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(63, 81, 181, 0.15), 0 12px 35px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.search-container .search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-primary);
    font-size: 1.2rem;
    pointer-events: none;
    transition: all 0.3s ease;
}

.search-container:focus-within .search-icon {
    color: var(--color-primary);
    transform: translateY(-50%) scale(1.1);
}

/* Dark Mode Support for Search Container */
[data-theme="dark"] .search-container .search-bar {
    background: linear-gradient(135deg, #242b3d 0%, #2a3441 100%);
    border-color: rgba(88, 166, 255, 0.3);
    color: #c9d1d9;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .search-container .search-bar:focus {
    border-color: #58a6ff;
    box-shadow: 0 0 0 4px rgba(88, 166, 255, 0.2), 0 12px 35px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .search-container .search-bar::placeholder {
    color: #8a9ba8;
}

[data-theme="dark"] .search-container .search-icon {
    color: #58a6ff;
}

/* Responsive Design for Search Container */
@media (max-width: 768px) {
    .search-container {
        max-width: 100%;
        margin-bottom: 25px;
    }

    .search-container .search-bar {
        padding: 15px 50px 15px 20px;
        font-size: 1rem;
    }

    .search-container .search-icon {
        right: 15px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .search-container .search-bar {
        padding: 12px 45px 12px 18px;
        font-size: 0.9rem;
    }

    .search-container .search-icon {
        right: 12px;
        font-size: 1rem;
    }
}
/* Custom Price Container Styles */
.custom-price-container {
    margin: 15px 0;
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
    border: 2px solid rgba(63, 81, 181, 0.2);
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.custom-price-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, #58a6ff 50%, var(--color-primary) 100%);
    z-index: 1;
}

.custom-price-container:hover {
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.1) 0%, rgba(240, 244, 255, 1) 100%);
    border-color: var(--color-primary);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(63, 81, 181, 0.15);
}

.custom-price-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 600;
    color: var(--color-primary);
    margin: 0;
    padding: 0;
    border: none;
    text-transform: none;
    letter-spacing: normal;
    font-size: 1rem;
    position: relative;
    z-index: 2;
}

.custom-price-label::before {
    display: none;
}

.custom-price-text {
    flex: 1;
    font-weight: 600;
    color: var(--color-primary);
}

.custom-price-info-icon {
    color: var(--color-primary);
    font-size: 1.1rem;
    cursor: help;
    transition: all 0.3s ease;
}

.custom-price-info-icon:hover {
    color: #303f9f;
    transform: scale(1.1);
}

/* Custom checkbox styling for custom price */
.custom-price-label input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--color-primary);
    cursor: pointer;
    border: 2px solid rgba(63, 81, 181, 0.3);
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.custom-price-label input[type="checkbox"]:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(63, 81, 181, 0.3);
}

.custom-price-label input[type="checkbox"]:checked {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(63, 81, 181, 0.4);
}

/* Dark Mode Support for Custom Price Container */
[data-theme="dark"] .custom-price-container {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.08) 0%, rgba(26, 35, 50, 0.95) 100%);
    border-color: rgba(88, 166, 255, 0.3);
}

[data-theme="dark"] .custom-price-container::before {
    background: linear-gradient(90deg, #58a6ff 0%, #4dabf7 50%, #58a6ff 100%);
}

[data-theme="dark"] .custom-price-container:hover {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.15) 0%, rgba(30, 58, 95, 1) 100%);
    border-color: #58a6ff;
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.2);
}

[data-theme="dark"] .custom-price-label {
    color: #58a6ff;
}

[data-theme="dark"] .custom-price-text {
    color: #58a6ff;
}

[data-theme="dark"] .custom-price-info-icon {
    color: #58a6ff;
}

[data-theme="dark"] .custom-price-info-icon:hover {
    color: #4dabf7;
}

[data-theme="dark"] .custom-price-label input[type="checkbox"] {
    accent-color: #58a6ff;
    border-color: rgba(88, 166, 255, 0.4);
}

[data-theme="dark"] .custom-price-label input[type="checkbox"]:hover {
    box-shadow: 0 4px 15px rgba(88, 166, 255, 0.4);
}

[data-theme="dark"] .custom-price-label input[type="checkbox"]:checked {
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.5);
}