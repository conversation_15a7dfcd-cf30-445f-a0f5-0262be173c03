/* Services Bar Container Styles */
.services-bar-container {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

/* Services Bar Styles */
.services-bar {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

.services-bar::-webkit-scrollbar {
    display: none !important;
}

/* Scroll Arrows Styles */
.services-scroll-arrow {
    font-family: 'Cairo', sans-serif !important;
    position: relative !important;
    overflow: hidden !important;
    z-index: 10 !important;
}

.services-scroll-arrow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.4s ease;
}

.services-scroll-arrow:hover::before {
    left: 100%;
}

.services-scroll-arrow:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.services-scroll-arrow:active {
    transform: scale(0.95) !important;
}

.services-scroll-arrow:disabled {
    opacity: 0.3 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

.dark-mode .services-scroll-arrow {
    background: var(--dark-surface) !important;
    border-color: var(--dark-border-light) !important;
    color: var(--blue-soft) !important;
}

.dark-mode .services-scroll-arrow:hover {
    box-shadow: 0 4px 12px rgba(91, 155, 213, 0.4) !important;
}

/* Arrow Icons */
.services-scroll-arrow i {
    transition: all 0.3s ease !important;
}

.services-scroll-arrow:hover i {
    transform: scale(1.2) !important;
}

/* Left Arrow Specific Styles */
.left-arrow {
    order: 3 !important;
}

/* Right Arrow Specific Styles */
.right-arrow {
    order: 1 !important;
}

/* Services Bar in Container */
.services-bar-container .services-bar {
    order: 2 !important;
}

/* Service Button Styles */
.service-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.service-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.service-button:hover::before {
    left: 100%;
}

.service-button:active {
    transform: translateY(1px) !important;
}

/* Services Modal Styles */
.services-modal-popup {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

.services-modal-title {
    color: var(--primary-color) !important;
    font-weight: 700 !important;
    margin-bottom: 20px !important;
}

.dark-mode .services-modal-title {
    color: var(--blue-soft) !important;
}

.dark-mode .services-modal-popup {
    background: var(--dark-surface-light) !important;
    color: var(--dark-text) !important;
    border: 1px solid var(--dark-border-light) !important;
}

/* Provider Tabs */
.provider-tab {
    font-family: 'Cairo', sans-serif !important;
    position: relative !important;
    overflow: hidden !important;
}

.provider-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.provider-tab:hover::before {
    left: 100%;
}

.provider-tab:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Service Type Tabs */
.service-type-tab {
    font-family: 'Cairo', sans-serif !important;
    position: relative !important;
    overflow: hidden !important;
}

.service-type-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.4s ease;
}

.service-type-tab:hover::before {
    left: 100%;
}

.service-type-tab:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Service Cards */
.service-card {
    font-family: 'Cairo', sans-serif !important;
    position: relative !important;
    overflow: hidden !important;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    border-color: var(--primary-color) !important;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2) !important;
}

.dark-mode .service-card:hover {
    border-color: var(--blue-soft) !important;
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3) !important;
}

.dark-mode .service-card::before {
    background: linear-gradient(90deg, transparent, rgba(91, 155, 213, 0.1), transparent);
}

/* Favorite Star Animation */
.favorite-star {
    transition: all 0.3s ease !important;
    z-index: 2 !important;
    position: relative !important;
}

.favorite-star:hover {
    transform: scale(1.2) rotate(15deg) !important;
    filter: drop-shadow(0 0 8px rgba(255, 193, 7, 0.6)) !important;
}

.favorite-star:active {
    transform: scale(0.9) !important;
}

/* Add Service Button */
.add-service-btn {
    font-family: 'Cairo', sans-serif !important;
    position: relative !important;
    overflow: hidden !important;
    z-index: 2 !important;
}

.add-service-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.4s ease;
}

.add-service-btn:hover::before {
    left: 100%;
}

.add-service-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
}

.dark-mode .add-service-btn:hover {
    box-shadow: 0 4px 12px rgba(91, 155, 213, 0.4) !important;
}

.add-service-btn:active {
    transform: translateY(1px) !important;
}

/* Services Grid Responsive */
.services-grid {
    gap: 15px !important;
}

@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
        gap: 10px !important;
    }
    
    .service-card {
        padding: 10px !important;
    }
    
    .provider-tab,
    .service-type-tab {
        padding: 8px 12px !important;
        font-size: 12px !important;
    }
}

@media (max-width: 480px) {
    .services-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
        gap: 8px !important;
    }
    
    .service-card {
        padding: 8px !important;
    }
    
    .service-card h6 {
        font-size: 12px !important;
    }
    
    .provider-tab,
    .service-type-tab {
        padding: 6px 10px !important;
        font-size: 11px !important;
    }
}

/* Services Bar Container Responsive */
@media (max-width: 768px) {
    .services-bar-container {
        bottom: 70px !important;
        left: 10px !important;
        right: 10px !important;
    }
    
    .services-bar {
        padding: 10px !important;
        gap: 8px !important;
    }
    
    .services-scroll-arrow {
        width: 35px !important;
        height: 35px !important;
        margin: 0 3px !important;
    }
    
    .services-scroll-arrow i {
        font-size: 14px !important;
    }
    
    .service-button {
        min-width: 60px !important;
        padding: 8px 10px !important;
        font-size: 11px !important;
    }
    
    .service-button i {
        font-size: 14px !important;
    }
}

@media (max-width: 480px) {
    .services-bar-container {
        bottom: 60px !important;
        left: 5px !important;
        right: 5px !important;
    }
    
    .services-bar {
        padding: 8px !important;
        gap: 6px !important;
    }
    
    .services-scroll-arrow {
        width: 30px !important;
        height: 30px !important;
        margin: 0 2px !important;
    }
    
    .services-scroll-arrow i {
        font-size: 12px !important;
    }
    
    .service-button {
        min-width: 50px !important;
        padding: 6px 8px !important;
        font-size: 10px !important;
    }
    
    .service-button i {
        font-size: 12px !important;
    }
}

/* Arrow Animation for Mobile */
@media (max-width: 768px) {
    .services-scroll-arrow:hover {
        transform: scale(1.05) !important;
    }
    
    .services-scroll-arrow:active {
        transform: scale(0.9) !important;
    }
}

/* Animation for services bar appearance */
@keyframes slideUpFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.services-bar {
    animation: slideUpFadeIn 0.5s ease-out !important;
}

/* Pulse animation for favorite services */
@keyframes favoritePulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

.service-button.favorite {
    animation: favoritePulse 2s infinite;
}

/* Loading animation for services */
@keyframes serviceLoading {
    0% {
        opacity: 0.5;
        transform: scale(0.95);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
    100% {
        opacity: 0.5;
        transform: scale(0.95);
    }
}

.service-card.loading {
    animation: serviceLoading 1.5s ease-in-out infinite;
}

/* Success animation when service is added */
@keyframes serviceAdded {
    0% {
        transform: scale(1);
        background-color: transparent;
    }
    50% {
        transform: scale(1.05);
        background-color: rgba(40, 167, 69, 0.2);
    }
    100% {
        transform: scale(1);
        background-color: transparent;
    }
}

.service-card.added {
    animation: serviceAdded 0.6s ease-out;
}

/* Dark mode specific animations */
.dark-mode .service-card.added {
    animation: serviceAddedDark 0.6s ease-out;
}

@keyframes serviceAddedDark {
    0% {
        transform: scale(1);
        background-color: transparent;
    }
    50% {
        transform: scale(1.05);
        background-color: rgba(91, 155, 213, 0.2);
    }
    100% {
        transform: scale(1);
        background-color: transparent;
    }
}

/* Smooth transitions for theme changes */
.services-bar,
.service-button,
.service-card,
.provider-tab,
.service-type-tab {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Focus styles for accessibility */
.service-button:focus,
.provider-tab:focus,
.service-type-tab:focus,
.add-service-btn:focus,
.favorite-star:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

.dark-mode .service-button:focus,
.dark-mode .provider-tab:focus,
.dark-mode .service-type-tab:focus,
.dark-mode .add-service-btn:focus,
.dark-mode .favorite-star:focus {
    outline-color: var(--blue-soft) !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .service-button,
    .service-card,
    .provider-tab,
    .service-type-tab {
        border-width: 2px !important;
    }
    
    .service-button:hover,
    .service-card:hover,
    .provider-tab:hover,
    .service-type-tab:hover {
        border-width: 3px !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .service-button,
    .service-card,
    .provider-tab,
    .service-type-tab,
    .add-service-btn,
    .favorite-star {
        transition: none !important;
        animation: none !important;
    }
    
    .service-button::before,
    .service-card::before,
    .provider-tab::before,
    .service-type-tab::before,
    .add-service-btn::before {
        display: none !important;
    }
}