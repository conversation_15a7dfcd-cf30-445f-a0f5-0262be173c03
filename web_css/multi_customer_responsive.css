/* Multi Customer Responsive CSS */

/* Base styles for multi-customer mode - Updated to match add_invoice.css */
.multi-customer-mode {
    --customer-primary: #007bff;
    --customer-secondary: #0056b3;
    --customer-success: #28a745;
    --customer-danger: #dc3545;
    --customer-warning: #ffc107;
    --customer-info: #17a2b8;
    --customer-blue-soft: #6ba3d6;
    --customer-blue-muted: #4a7ba7;
    --customer-blue-accent: #5b9bd5;
}

/* Enhanced responsive layout for multi-customer */
@media (max-width: 1200px) {
    .multi-customer-tabs {
        padding: 8px 15px;
    }
    
    .customer-tabs-container {
        gap: 8px;
    }
    
    .customer-tab {
        padding: 10px 14px;
        min-width: 75px;
    }
    
    .customer-number {
        font-size: 15px;
    }
}

@media (max-width: 992px) {
    .multi-customer-tabs {
        padding: 6px 12px;
        border-radius: 0 0 10px 10px;
    }
    
    .customer-tab {
        padding: 8px 12px;
        min-width: 70px;
        gap: 6px;
    }
    
    .customer-number {
        font-size: 14px;
    }
    
    .items-indicator {
        width: 18px;
        height: 18px;
        font-size: 9px;
        top: -3px;
        right: -3px;
    }
    
    .add-customer-tab {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    .multi-customer-tabs {
        padding: 5px 10px;
        margin-bottom: 15px;
    }
    
    .customer-tabs-container {
        gap: 6px;
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 5px;
    }
    
    .customer-tab {
        padding: 6px 10px;
        min-width: 65px;
        flex-shrink: 0;
    }
    
    .customer-number {
        font-size: 13px;
    }
    
    .add-customer-tab {
        width: 40px;
        height: 40px;
        font-size: 14px;
        flex-shrink: 0;
    }
    
    /* Scrollbar styling for customer tabs */
    .customer-tabs-container::-webkit-scrollbar {
        height: 4px;
    }
    
    .customer-tabs-container::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
    }
    
    .customer-tabs-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }
    
    .customer-tabs-container::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }
}

@media (max-width: 576px) {
    .multi-customer-tabs {
        padding: 4px 8px;
        margin-bottom: 10px;
    }
    
    .customer-tab {
        padding: 5px 8px;
        min-width: 60px;
        gap: 4px;
    }
    
    .customer-number {
        font-size: 12px;
    }
    
    .customer-tab .fas {
        font-size: 12px;
    }
    
    .items-indicator {
        width: 16px;
        height: 16px;
        font-size: 8px;
    }
    
    .add-customer-tab {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
    
    .remove-customer {
        width: 18px;
        height: 18px;
        font-size: 8px;
        top: -6px;
        right: -6px;
    }
}

/* Enhanced responsive table for selected items */
@media (max-width: 768px) {
    .selected-items .table-wrapper {
        max-height: calc(100vh - 300px);
    }
    
    .selected-items table {
        font-size: 12px;
    }
    
    .selected-items th,
    .selected-items td {
        padding: 6px 4px;
    }
    
    .quantity-input {
        width: 45px !important;
        max-width: 45px !important;
        min-width: 45px !important;
        font-size: 11px !important;
        padding: 2px 1px !important;
    }
    
    .remove-btn {
        padding: 2px 4px !important;
        font-size: 10px;
    }
}

@media (max-width: 576px) {
    .selected-items table {
        font-size: 11px;
    }
    
    .selected-items th:first-child,
    .selected-items td:first-child {
        width: 40%;
        padding-right: 8px;
    }
    
    .selected-items th:nth-child(2),
    .selected-items td:nth-child(2) {
        width: 12%;
        font-size: 10px;
    }
    
    .selected-items th:nth-child(3),
    .selected-items td:nth-child(3) {
        width: 8%;
    }
    
    .selected-items th:nth-child(4),
    .selected-items td:nth-child(4) {
        width: 15%;
        font-size: 10px;
    }
    
    .selected-items th:nth-child(5),
    .selected-items td:nth-child(5) {
        width: 25%;
    }
    
    .quantity-input {
        width: 40px !important;
        max-width: 40px !important;
        min-width: 40px !important;
        font-size: 10px !important;
    }
}

/* Enhanced responsive layout for three-column view */
@media (max-width: 1200px) {
    .categories,
    .items,
    .selected-items {
        height: 500px;
    }
}

@media (max-width: 992px) {
    .categories,
    .items,
    .selected-items {
        height: 450px;
        padding: 12px;
    }
    
    .categories h3,
    .items h3,
    .selected-items h3 {
        font-size: 1.3rem;
        margin-bottom: 12px;
    }
}

@media (max-width: 768px) {
    /* Stack layout vertically on mobile */
    .responsive-layout {
        flex-direction: column !important;
        gap: 15px !important;
    }
    
    .categories,
    .items,
    .selected-items {
        flex: none !important;
        width: 100% !important;
        height: 300px;
        min-width: auto !important;
    }
    
    .selected-items {
        height: 350px; /* Slightly taller for better usability */
        order: -1; /* Move selected items to top */
    }
    
    .categories h3,
    .items h3,
    .selected-items h3 {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }
    
    /* Adjust search inputs */
    .search-bar {
        font-size: 14px;
        padding: 6px 10px;
    }
    
    /* Adjust category list */
    #categoryList li {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    /* Adjust items table */
    .items table {
        font-size: 12px;
    }
    
    .items th,
    .items td {
        padding: 6px 4px;
    }
    
    .items .add-btn {
        padding: 4px 6px;
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .categories,
    .items,
    .selected-items {
        height: 250px;
        padding: 8px;
    }
    
    .selected-items {
        height: 300px;
    }
    
    .categories h3,
    .items h3,
    .selected-items h3 {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }
    
    .search-bar {
        font-size: 13px;
        padding: 5px 8px;
    }
    
    #categoryList li {
        padding: 6px 10px;
        font-size: 13px;
        margin: 3px 0;
    }
    
    .items table {
        font-size: 11px;
    }
    
    .items th,
    .items td {
        padding: 4px 2px;
    }
    
    .items .add-btn {
        padding: 3px 5px;
        font-size: 11px;
    }
}

/* Enhanced total display for mobile */
@media (max-width: 768px) {
    .total-display {
        padding: 10px 15px !important;
        font-size: 14px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .total-display button {
        width: 100%;
        padding: 10px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .total-display {
        padding: 8px 12px !important;
        font-size: 13px;
    }
    
    .total-display button {
        padding: 8px;
        font-size: 13px;
    }
}

/* Enhanced dark mode support for responsive */
.dark-mode .categories,
.dark-mode .items,
.dark-mode .selected-items {
    background-color: var(--dark-surface);
    border: 1px solid var(--dark-border);
}

.dark-mode .search-bar {
    background-color: var(--dark-surface-light);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

.dark-mode .search-bar:focus {
    border-color: var(--customer-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dark-mode #categoryList li {
    background-color: var(--dark-surface-light);
    color: var(--dark-text-muted);
}

.dark-mode #categoryList li:hover,
.dark-mode #categoryList li.active {
    background-color: var(--customer-primary);
    color: white;
}

/* Enhanced animations for responsive mode */
@media (max-width: 768px) {
    .customer-tab {
        transition: all 0.2s ease;
    }
    
    .customer-tab:active {
        transform: scale(0.95);
    }
    
    .quantity-input {
        transition: all 0.2s ease;
    }
    
    .remove-btn:active,
    .add-btn:active {
        transform: scale(0.9);
    }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
    .customer-tab {
        min-height: 44px;
        min-width: 44px;
    }
    
    .add-customer-tab {
        min-height: 44px;
        min-width: 44px;
    }
    
    .remove-btn,
    .add-btn {
        min-height: 36px;
        min-width: 36px;
    }
    
    .quantity-input {
        min-height: 36px;
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .categories,
    .items,
    .selected-items {
        height: 200px;
    }
    
    .selected-items {
        height: 250px;
    }
    
    .multi-customer-tabs {
        padding: 3px 8px;
        margin-bottom: 8px;
    }
    
    .customer-tab {
        padding: 4px 8px;
    }
}

/* Print styles for multi-customer mode */
@media print {
    .multi-customer-tabs,
    .add-customer-tab,
    .remove-customer {
        display: none !important;
    }
    
    .selected-items {
        page-break-inside: avoid;
    }
    
    .customer-tab.active::after {
        content: " (نشط)";
        font-weight: bold;
    }
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
    .customer-tab,
    .quantity-input,
    .remove-btn,
    .add-btn {
        transition: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .customer-tab {
        border-width: 3px;
    }
    
    .customer-tab.active {
        border-color: #000;
        background: #fff;
        color: #000;
    }
    
    .items-indicator {
        border-width: 3px;
        border-color: #fff;
    }
}

/* Focus management for keyboard navigation */
.customer-tab:focus-visible {
    outline: 3px solid var(--customer-primary);
    outline-offset: 2px;
}

.add-customer-tab:focus-visible {
    outline: 3px solid var(--customer-primary);
    outline-offset: 2px;
}

.quantity-input:focus-visible {
    outline: 2px solid var(--customer-primary);
    outline-offset: 1px;
}

/* Enhanced loading states for responsive */
.customer-loading {
    position: relative;
    overflow: hidden;
}

.customer-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.dark-mode .customer-loading::after {
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
}