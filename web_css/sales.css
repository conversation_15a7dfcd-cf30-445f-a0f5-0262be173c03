 /* Modal improvements */
       
        
 .modal.active {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* Modal content sizing */
.modal .modal-content {
    width: 95%;
    max-width: 800px; /* زيادة العرض الأقصى للشاشات الكبيرة */
}

/* على الشاشات الكبيرة جداً، جعل الموديل أكبر */
@media (min-width: 1200px) {
    .modal .modal-content {
        max-width: 1000px; /* عرض أكبر على الشاشات الكبيرة */
        width: 80%;
    }
    
    #orderDetailsModal .modal-content,
    #userOrdersModal .modal-content {
        max-width: 1200px; /* عرض أكبر لموديلات تفاصيل الطلبات */
    }
}

@media (min-width: 1600px) {
    .modal .modal-content {
        max-width: 1400px; /* عرض أكبر على الشاشات الكبيرة جداً */
    }
}

/* تحسينات للجداول داخل الموديل */
.modal-content table {
    width: 100%;
    margin-top: 15px;
}

.modal-content table th,
.modal-content table td {
padding: 12px;
text-align: center;
}

/* New responsive styles for the invoice details modal */
#orderDetailsModal .modal-content {
background: linear-gradient(135deg, #f5f7fa, #e9ecef);
border-radius: 20px;
box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
border: 1px solid rgba(0, 0, 0, 0.1);
padding: 30px;
max-width: 95%;
width: 1200px;
max-height: 95vh;
overflow-y: auto;
display: flex;
flex-direction: column;
}

#orderDetailsModal h2 {
font-size: 2rem;
font-weight: 700;
color: var(--color-primary);
text-align: center;
margin-bottom: 25px;
text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
flex-shrink: 0;
}

#orderDetailsModal .table-wrapper {
border-radius: 15px;
overflow: auto;
border: 1px solid #dee2e6;
flex: 1;
max-height: 70vh;
-webkit-overflow-scrolling: touch;
}

#orderDetailsModal table {
width: 100%;
border-collapse: separate;
border-spacing: 0;
border: 1px solid #dee2e6;
background-color: #ffffff;
border-radius: 12px;
overflow: hidden;
box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
font-family: 'Cairo', Arial, sans-serif;
min-width: 800px;
}

#orderDetailsModal th {
background: linear-gradient(135deg, var(--color-primary), #667eea);
color: white;
font-size: 14px;
padding: 15px;
text-transform: uppercase;
letter-spacing: 0.5px;
position: sticky;
top: 0;
z-index: 10;
border: none;
border-bottom: 2px solid rgba(255, 255, 255, 0.2);
font-weight: 700;
text-align: center;
}

#orderDetailsModal td {
padding: 15px;
font-size: 14px;
border-bottom: 1px solid #dee2e6;
color: #495057;
background-color: #ffffff;
transition: all 0.3s ease;
text-align: center;
}

#orderDetailsModal tbody tr:nth-child(even) {
background-color: #f8f9fa;
}

#orderDetailsModal tbody tr:nth-child(even) td {
background-color: #f8f9fa;
}

#orderDetailsModal tbody tr:last-child td {
border-bottom: none;
}

#orderDetailsModal tbody tr:hover {
background-color: #e3f2fd !important;
transform: translateY(-1px);
box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

#orderDetailsModal tbody tr:hover td {
background-color: #e3f2fd;
color: #1565c0;
}

#orderDetailsModal .action-btn {
margin-top: 20px;
padding: 12px 25px;
font-size: 15px;
border-radius: 10px;
flex-shrink: 0;
}

/* Custom scrollbar for the table */
#orderDetailsModal .table-wrapper::-webkit-scrollbar {
width: 8px;
height: 8px;
}

#orderDetailsModal .table-wrapper::-webkit-scrollbar-track {
background: #f1f1f1;
border-radius: 4px;
}

#orderDetailsModal .table-wrapper::-webkit-scrollbar-thumb {
background: var(--color-primary);
border-radius: 4px;
}

#orderDetailsModal .table-wrapper::-webkit-scrollbar-thumb:hover {
background: #0056b3;
}

/* Dark mode styles */
[data-theme="dark"] #orderDetailsModal .modal-content {
background: linear-gradient(135deg, #1a1d23, #0d1117);
border-color: #30363d;
color: #c9d1d9;
}

[data-theme="dark"] #orderDetailsModal h2 {
color: #e9ecef;
}

[data-theme="dark"] #orderDetailsModal .table-wrapper {
border-color: #30363d;
}

[data-theme="dark"] #orderDetailsModal .table-wrapper::-webkit-scrollbar-track {
background: #2c3034;
}

[data-theme="dark"] #orderDetailsModal .table-wrapper::-webkit-scrollbar-thumb {
background: var(--color-primary);
}

[data-theme="dark"] #orderDetailsModal td {
border-bottom-color: #30363d;
color: #c9d1d9;
}

[data-theme="dark"] #orderDetailsModal tbody tr:hover {
background-color: rgba(255, 255, 255, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
#orderDetailsModal .modal-content {
padding: 20px;
border-radius: 15px;
max-height: 95vh;
}

#orderDetailsModal h2 {
font-size: 1.5rem;
margin-bottom: 15px;
}

#orderDetailsModal .table-wrapper {
max-height: 50vh;
}

#orderDetailsModal th,
#orderDetailsModal td {
padding: 12px 8px;
font-size: 12px;
}

#orderDetailsModal table {
min-width: 700px;
}
}

@media (max-width: 576px) {
#orderDetailsModal .modal-content {
padding: 15px;
border-radius: 10px;
max-height: 98vh;
}

#orderDetailsModal h2 {
font-size: 1.2rem;
margin-bottom: 10px;
}

#orderDetailsModal .table-wrapper {
max-height: 45vh;
}

#orderDetailsModal th,
#orderDetailsModal td {
padding: 10px 6px;
font-size: 11px;
}

#orderDetailsModal table {
min-width: 600px;
}

#orderDetailsModal .action-btn {
margin-top: 15px;
padding: 10px 20px;
font-size: 14px;
}
}

/* تحسين حجم النص داخل الموديل */
.modal-content h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
}

/* تحسين مظهر الأزرار في أسفل الموديل */
.modal-content .action-btn {
    margin: 15px 5px 5px 5px;
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 5px;
}

/* خلايا قابلة للتعديل */


/* أنماط النوافذ المنبثقة في الوضع المظلم */
[data-theme="dark"] .modal-content {
    background-color: #0d1117;
    border-color: var(--color-primary);
    color: #c9d1d9;
}

[data-theme="dark"] #orderDetailsModal table {
    background-color: #161b22;
    border: 1px solid #30363d;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] #orderDetailsModal th {
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    color: #ffffff;
    border-bottom-color: #30363d;
}

[data-theme="dark"] #orderDetailsModal td {
    background-color: #161b22;
    border-bottom-color: #30363d;
    color: #c9d1d9;
}

[data-theme="dark"] #orderDetailsModal tbody tr:nth-child(even) {
    background-color: #21262d;
}

[data-theme="dark"] #orderDetailsModal tbody tr:nth-child(even) td {
    background-color: #21262d;
}

[data-theme="dark"] #orderDetailsModal tbody tr:hover {
    background-color: #1a1d23 !important;
    box-shadow: 0 2px 8px rgba(88, 166, 255, 0.2);
}

[data-theme="dark"] #orderDetailsModal tbody tr:hover td {
    background-color: #1a1d23;
    color: #58a6ff;
}

[data-theme="dark"] .modal-content [contenteditable="true"] {
    background-color: rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .modal-content [contenteditable="true"]:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modal-content [contenteditable="true"]:focus {
    background-color: rgba(0, 0, 0, 0.4);
}

/* تحسين عرض الزر بجانب الأيقونة في الموديل */
.modal-content td button.action-btn {
    margin: 0 5px;
    padding: 5px 15px;
    display: inline-block;
}

/* تحسين عرض موديل المستخدم */
#userOrdersModal .summary-bar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-bottom: 20px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

#userOrdersModal .summary-item {
    padding: 10px 15px;
    margin: 5px;
    border-radius: 8px;
    background-color: var(--color-primary);
    color: white;
}

/* تنسيق شريط الفلترة المحسن */
.search-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0,123,255,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-container:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, var(--color-primary) 0%, #667eea 100%);
    color: white;
    margin: 0;
}

.search-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.search-header h3 i {
    margin-left: 10px;
    color: #ffd700;
}

.toggle-search-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-search-btn:hover {
    background: rgba(255,255,255,0.3);
}

.search-form {
    padding: 25px;
    display: block;
}

.search-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
}

.search-field {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
}

/* تحسين عرض قسم الفلترة في الشاشات الكبيرة */
@media (min-width: 1200px) {
    .search-container {
        max-width: 1400px;
        margin: 0 auto 30px auto;
    }
    
    .search-form {
        padding: 30px 40px;
    }
    
    .search-row {
        gap: 25px;
        margin-bottom: 25px;
    }
    
    .search-field {
        min-width: 220px;
    }
    
    .search-field input, 
    .search-field select {
        padding: 14px 18px;
        font-size: 15px;
        border-radius: 12px;
    }
    
    .search-actions {
        gap: 15px;
    }
    
    .search-btn, .clear-btn, .refresh-btn {
        padding: 14px 25px;
        font-size: 15px;
        border-radius: 12px;
        min-width: 120px;
    }
}

@media (min-width: 1400px) {
    .search-container {
        max-width: 1600px;
    }
    
    .search-form {
        padding: 35px 50px;
    }
    
    .search-row {
        gap: 30px;
        margin-bottom: 30px;
    }
    
    .search-field {
        min-width: 250px;
    }
    
    .search-field input, 
    .search-field select {
        padding: 16px 20px;
        font-size: 16px;
        border-radius: 14px;
    }
    
    .search-actions {
        gap: 20px;
    }
    
    .search-btn, .clear-btn, .refresh-btn {
        padding: 16px 30px;
        font-size: 16px;
        border-radius: 14px;
        min-width: 140px;
    }
}

.search-field label {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-field label i {
    color: var(--color-primary);
    font-size: 16px;
}

.search-field input, 
.search-field select {
    padding: 12px 15px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    background-color: #fff;
    color: #495057;
    transition: all 0.3s ease;
    font-size: 14px;
    font-family: inherit;
}

.search-field input:focus, 
.search-field select:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.search-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-btn, .clear-btn, .refresh-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.search-btn {
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.clear-btn {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.refresh-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

[data-theme="dark"] .search-container {
    background: linear-gradient(135deg, #2c3034 0%, #24282c 100%);
    border-color: #495057;
}

[data-theme="dark"] .search-field label {
    color: #adb5bd;
}

[data-theme="dark"] .search-field input, 
[data-theme="dark"] .search-field select {
    background-color: #343a40;
    border-color: #495057;
    color: #e9ecef;
}

/* تحسين الحاوية الرئيسية */
.container {
    max-width: 1400px;
    margin: 120px auto 0;
    padding: 20px;
    padding-bottom: 120px !important; /* مساحة كافية للشريط الثابت ��الفوتر */
    background-color: var(--color-bg);
    min-height: calc(100vh - 120px);
}

/* تصميم رأس الصفحة المحسن */
.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px 0;
    background: var(--color-primary);
    border-radius: 20px;
    color: var(--color-header-text);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    position: relative;
    z-index: 1;
}

.page-title i {
    margin-left: 15px;
    color: var(--color-secondary);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
    position: relative;
    z-index: 1;
}

/* بطاقات الإحصائيات */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--color-primary);
    transition: width 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stat-card:hover::before {
    width: 8px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: var(--color-primary);
}

.stat-content p {
    margin: 0;
    color: #6c757d;
    font-weight: 500;
}

[data-theme="dark"] .stat-card {
    background: linear-gradient(135deg, #2c3034 0%, #24282c 100%);
    border-color: #495057;
}

[data-theme="dark"] .stat-content h3 {
    color: #e9ecef;
}

[data-theme="dark"] .stat-content p {
    color: #adb5bd;
}

/* تحسين بطاقات الإحصائيات في الشاشات الكبيرة */
@media (min-width: 1200px) {
    .stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        margin-bottom: 35px;
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .stat-card {
        padding: 30px;
        border-radius: 18px;
        gap: 25px;
    }
    
    .stat-icon {
        width: 70px;
        height: 70px;
        font-size: 28px;
    }
    
    .stat-content h3 {
        font-size: 2.2rem;
    }
    
    .stat-content p {
        font-size: 1.1rem;
    }
}

@media (min-width: 1400px) {
    .stats-container {
        max-width: 1600px;
        gap: 30px;
        margin-bottom: 40px;
    }
    
    .stat-card {
        padding: 35px;
        border-radius: 20px;
        gap: 30px;
    }
    
    .stat-icon {
        width: 80px;
        height: 80px;
        font-size: 32px;
    }
    
    .stat-content h3 {
        font-size: 2.5rem;
    }
    
    .stat-content p {
        font-size: 1.2rem;
    }
}

/* تحسين طريقة عرض الجدول */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: none;
    margin-bottom: 30px;
    background-color: var(--color-secondary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

table th {
    background-color: var(--color-primary);
    color: var(--color-header-text);
    text-transform: uppercase;
    font-size: 16px;
    padding: 15px;
    font-weight: bold;
    text-align: center;
    letter-spacing: 0.5px;
}

table td {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 14px;
    transition: background-color 0.3s ease;
}

table tr:hover {
    background-color: var(--color-hover);
}

/* أنماط الجداول في الوضع المظلم */
[data-theme="dark"] table {
    background-color: #161b22;
    border: 1px solid #30363d;
}

[data-theme="dark"] table th {
    background-color: var(--color-primary);
    color: #ffffff;
    border-bottom-color: #30363d;
}

[data-theme="dark"] table td {
    border-bottom-color: #30363d;
    color: #c9d1d9;
}

[data-theme="dark"] table tr:hover {
    background-color: #21262d;
    color: #c9d1d9;
}

/* الشريط الثابت المحسن */
.fixed-navbar {
    position: fixed;
    bottom: 45px;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    backdrop-filter: blur(10px);
    border-top: 3px solid var(--color-primary);
    box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.15);
    z-index: 995;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    max-width: 1400px;
    margin: 0 auto;
}

.selection-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--color-primary);
    background: rgba(0, 123, 255, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    border: 2px solid rgba(0, 123, 255, 0.2);
}

.selection-info span:first-child {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--color-primary);
}

.navbar-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.navbar-buttons .action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.navbar-buttons .action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.navbar-buttons .action-btn:not(:disabled):hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.navbar-buttons .action-btn i {
    font-size: 16px;
}

.btn-text {
    font-weight: 600;
}

/* أنماط الأزرار المختلفة */
.print-btn {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.view-btn {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.delete-btn {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.export-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.quick-actions {
    display: flex;
    gap: 8px;
}

.quick-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.quick-btn:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

/* الوضع المظلم */
[data-theme="dark"] .fixed-navbar {
    background: linear-gradient(135deg, #2c3034 0%, #24282c 100%);
    border-top-color: var(--color-primary);
    box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .selection-info {
    background: rgba(0, 123, 255, 0.2);
    border-color: rgba(0, 123, 255, 0.3);
    color: #e9ecef;
}

[data-theme="dark"] .selection-info span:first-child {
    color: #e9ecef;
}

/* تحسينات الاستجابة */
@media (max-width: 1200px) {
    .navbar-content {
        padding: 12px 20px;
    }
    
    .navbar-buttons .action-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .btn-text {
        display: none;
    }
    
    .navbar-buttons .action-btn i {
        margin: 0;
    }
}

@media (max-width: 768px) {
    .fixed-navbar {
        bottom: 40px;
        /* تقليل ارتفاع الشريط الثابت */
    }
    
    .navbar-content {
        flex-direction: column;
        gap: 10px; /* تقليل المسافة بين العناصر */
        padding: 12px 15px; /* تقليل الحشو */
    }
    
    .navbar-buttons {
        flex-wrap: wrap;
        justify-content: center;
        gap: 6px; /* تقليل المسافة بين الأزرار */
    }
    
    .navbar-buttons .action-btn {
        padding: 8px 12px; /* تقليل حجم الأزرار */
        font-size: 11px;
        min-width: auto;
        border-radius: 8px;
    }
    
    .quick-actions {
        order: -1;
        gap: 6px;
    }
    
    .quick-btn {
        width: 35px; /* تقليل حجم الأزرار السريعة */
        height: 35px;
        font-size: 14px;
    }
    
    .selection-info {
        font-size: 13px;
        padding: 5px 10px; /* تقليل الحشو */
        border-radius: 15px;
    }
    
    .selection-info span:first-child {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .fixed-navbar {
        bottom: 35px; /* تقليل المسافة من الأسفل أكثر */
    }
    
    .navbar-content {
        gap: 8px; /* تقليل المسافة أكثر */
        padding: 10px 12px; /* تقليل الحشو أكثر */
    }
    
    .navbar-buttons .action-btn {
        padding: 6px 10px; /* تقليل حجم الأزرار أكثر */
        font-size: 10px;
        border-radius: 6px;
    }
    
    .quick-btn {
        width: 32px; /* تقليل حجم الأزرار السريعة أكثر */
        height: 32px;
        font-size: 12px;
    }
    
    .selection-info {
        font-size: 12px;
        padding: 4px 8px; /* تقليل الحشو أكثر */
        border-radius: 12px;
    }
    
    .selection-info span:first-child {
        font-size: 0.9rem;
    }
}

/* إخفاء أعمدة المكسب في المرتجعات */
.returns-mode .profit-column {
    display: none !important;
}

/* تنسيق التبويبات المحسن */
.tabs-container {
    margin-bottom: 30px;
    width: 100%;
    display: flex;
    justify-content: center;
}

.tabs {
    display: flex;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    overflow: hidden;
    border: 2px solid #e3e6f0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    max-width: 700px;
    width: 100%;
    position: relative;
}

.tab-btn {
    flex: 1;
    padding: 20px 30px;
    border: none;
    background: transparent;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s ease;
    position: relative;
    color: #6c757d;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.tab-btn i {
    font-size: 18px;
}

.tab-count {
    background: rgba(0,0,0,0.1);
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    min-width: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.tab-btn.active .tab-count {
    background: rgba(255,255,255,0.2);
    color: white;
}

.tab-btn:not(.active)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), #667eea);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 2px 2px 0 0;
}

.tab-btn:hover:not(.active) {
    background: rgba(0,0,0,0.05);
    transform: translateY(-1px);
}

.tab-btn:hover:not(.active)::after {
    width: 60%;
}

.tab-btn:hover:not(.active) .tab-count {
    background: rgba(0,0,0,0.15);
}

/* تعديل الوضع المظلم للتبويبات */
[data-theme="dark"] .tabs {
    background: linear-gradient(135deg, #2c3034 0%, #24282c 100%);
    border-color: #495057;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .tab-btn {
    color: #e9ecef;
}

[data-theme="dark"] .tab-btn:hover:not(.active) {
    background: rgba(255,255,255,0.05);
}

[data-theme="dark"] .tab-count {
    background: rgba(255,255,255,0.1);
    color: #e9ecef;
}

/* تحسين التبويبات في الشاشات الكبيرة */
@media (min-width: 1200px) {
    .tabs-container {
        margin-bottom: 35px;
    }
    
    .tabs {
        max-width: 800px;
        border-radius: 18px;
    }
    
    .tab-btn {
        padding: 25px 35px;
        font-size: 18px;
        gap: 12px;
    }
    
    .tab-btn i {
        font-size: 20px;
    }
    
    .tab-count {
        font-size: 14px;
        padding: 6px 10px;
        border-radius: 15px;
    }
}

@media (min-width: 1400px) {
    .tabs {
        max-width: 900px;
        border-radius: 20px;
    }
    
    .tab-btn {
        padding: 30px 40px;
        font-size: 20px;
        gap: 15px;
    }
    
    .tab-btn i {
        font-size: 22px;
    }
    
    .tab-count {
        font-size: 15px;
        padding: 7px 12px;
        border-radius: 18px;
    }
}

/* تصميم قسم الجدول المحسن */
.table-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0,0,0,0.05);
    margin-bottom: 30px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.table-title h3 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
}

.table-subtitle {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

.table-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
}

.control-btn i {
    font-size: 16px;
}

/* نمط خاص لزر إخفاء/إظهار أعمدة المكسب والتكلفة */
#toggle-profit-btn {
    position: relative;
}

#toggle-profit-btn .fa-eye-slash {
    color: #ffd700;
}

#toggle-profit-btn:hover .fa-eye-slash {
    color: #ffed4e;
}

/* تأثير مرئي عند إخفاء الأعمدة */
#toggle-profit-btn.columns-hidden {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

#toggle-profit-btn.columns-hidden:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
}

[data-theme="dark"] .table-section {
    background: linear-gradient(135deg, #2c3034 0%, #24282c 100%);
    border-color: #495057;
}

[data-theme="dark"] .table-header {
    background: linear-gradient(135deg, #343a40 0%, #2c3034 100%);
    border-bottom-color: #495057;
}

[data-theme="dark"] .table-title h3 {
    color: #e9ecef;
}

[data-theme="dark"] .table-subtitle {
    color: #adb5bd;
}

/* تحسين قسم الجدول في الشاشات الكبيرة */
@media (min-width: 1200px) {
    .table-section {
        border-radius: 22px;
        margin-bottom: 35px;
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .table-header {
        padding: 30px 35px;
    }
    
    .table-title h3 {
        font-size: 1.7rem;
    }
    
    .table-subtitle {
        font-size: 15px;
    }
    
    .control-btn {
        padding: 12px 18px;
        font-size: 15px;
        border-radius: 12px;
        gap: 12px;
    }
    
    .enhanced-table th {
        padding: 20px 18px;
        font-size: 15px;
    }
    
    .enhanced-table td {
        padding: 18px 18px;
        font-size: 15px;
    }
}

@media (min-width: 1400px) {
    .table-section {
        max-width: 1600px;
        border-radius: 25px;
        margin-bottom: 40px;
    }
    
    .table-header {
        padding: 35px 40px;
    }
    
    .table-title h3 {
        font-size: 1.9rem;
    }
    
    .table-subtitle {
        font-size: 16px;
    }
    
    .control-btn {
        padding: 14px 22px;
        font-size: 16px;
        border-radius: 14px;
        gap: 15px;
    }
    
    .enhanced-table th {
        padding: 22px 20px;
        font-size: 16px;
    }
    
    .enhanced-table td {
        padding: 20px 20px;
        font-size: 16px;
    }
}

/* تحسينات الجدول المحسن - نفس استايل item_reports */
.enhanced-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: 1px solid #dee2e6;
    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-family: 'Cairo', Arial, sans-serif;
}

.enhanced-table th {
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    color: white;
    text-transform: uppercase;
    font-size: 14px;
    padding: 18px 15px;
    font-weight: 700;
    text-align: center;
    letter-spacing: 0.5px;
    position: relative;
    border: none;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.enhanced-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
}

.enhanced-table th.sortable:hover {
    background: linear-gradient(135deg, #0056b3, #5a67d8);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.enhanced-table th i {
    margin-left: 8px;
    font-size: 16px;
}

.enhanced-table th .sort-icon {
    margin-right: 8px;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.enhanced-table th.sortable:hover .sort-icon {
    opacity: 1;
}

.enhanced-table td {
    padding: 16px 15px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #ffffff;
    color: #495057;
}

.enhanced-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.enhanced-table tbody tr:hover {
    background-color: #e3f2fd !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.enhanced-table tbody tr:hover td {
    color: #1565c0;
}

.enhanced-table .checkbox-column,
.enhanced-table .action-column {
    width: 80px;
    padding: 12px;
}

.enhanced-table .checkbox-column input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--color-primary);
    border-radius: 3px;
}

/* الوضع المظلم للجدول المحسن */
[data-theme="dark"] .enhanced-table {
    background-color: #161b22;
    border-color: #30363d;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .enhanced-table th {
    background: linear-gradient(135deg, var(--color-primary), #667eea);
    color: #ffffff;
    border-bottom-color: #30363d;
}

[data-theme="dark"] .enhanced-table td {
    background-color: #161b22;
    border-bottom-color: #30363d;
    color: #c9d1d9;
}

[data-theme="dark"] .enhanced-table tbody tr:nth-child(even) {
    background-color: #21262d;
}

[data-theme="dark"] .enhanced-table tbody tr:nth-child(even) td {
    background-color: #21262d;
}

[data-theme="dark"] .enhanced-table tbody tr:hover {
    background-color: #1a1d23 !important;
    box-shadow: 0 2px 8px rgba(88, 166, 255, 0.2);
}

[data-theme="dark"] .enhanced-table tbody tr:hover td {
    background-color: #1a1d23;
    color: #58a6ff;
}

/* حالة فارغة محسنة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #495057;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 30px;
    opacity: 0.8;
}

[data-theme="dark"] .empty-state {
    color: #adb5bd;
}

[data-theme="dark"] .empty-state h3 {
    color: #e9ecef;
}

/* مؤشر التحميل المحسن */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 40px 20px;
    color: var(--color-primary);
}

.loading-spinner.active {
    display: block;
}

.spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

.loading-text p {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-primary);
}

.loading-dots {
    display: inline-block;
    animation: dots 1.5s infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

/* تنسيق حالات الطلبات */
.status-frame {
    padding: 5px 10px;
    border-radius: 30px;
    font-size: 13px;
    font-weight: 600;
    display: inline-block;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

[data-theme="dark"] .status-frame {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.status-frame.pending {
    background-color: #ffc107;
    color: #212529;
}

.status-frame.delayed {
    background-color: #dc3545;
    color: white;
}

.status-frame.confirmed {
    background-color: #28a745;
    color: white;
}

/* تحسين أيقونات الإجراءات */
.fa-eye, .fa-print, .fa-trash-alt {
    transition: all 0.3s ease;
}

.fa-eye:hover {
    color: #0069d9 !important;
    transform: scale(1.2);
}

.fa-print:hover {
    color: #17a2b8 !important;
    transform: scale(1.2);
}

.fa-trash-alt:hover {
    color: #c82333 !important;
    transform: scale(1.2);
}

/* تحسينات ريسبونسف للأجهزة اللوحية */
@media (max-width: 992px) {
    .container {
        padding: 15px;
        padding-bottom: 160px !important; /* زيادة المساحة السفلية للأجهزة اللوحية */
        max-width: 100%;
    }
    
    .page-header {
        padding: 25px 0;
        margin-bottom: 25px;
        border-radius: 18px;
    }
    
    .page-title {
        font-size: 2.2rem;
        margin-bottom: 12px;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
        margin-bottom: 25px;
    }
    
    .stat-card {
        padding: 22px;
        border-radius: 13px;
    }
    
    .stat-icon {
        width: 55px;
        height: 55px;
        font-size: 22px;
    }
    
    .stat-content h3 {
        font-size: 1.7rem;
    }
    
    .tabs {
        max-width: 100%;
        border-radius: 13px;
    }
    
    .tab-btn {
        padding: 15px 20px;
        font-size: 15px;
    }
    
    .search-container {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 18px;
    }
    
    .search-form {
        gap: 15px;
    }
    
    .search-field {
        min-width: 200px;
    }
    
    .table-section {
        border-radius: 18px;
        margin-bottom: 25px;
    }
    
    .enhanced-table th,
    .enhanced-table td {
        padding: 15px 10px;
        font-size: 14px;
    }
    
    .modal-content {
        width: 95%;
        max-width: 95%;
        border-radius: 15px;
    }
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        padding-bottom: 200px !important; /* زيادة المساحة السفلية للهواتف */
    }
    
    .page-header {
        padding: 20px 0;
        margin-bottom: 20px;
        border-radius: 15px;
    }
    
    .page-title {
        font-size: 2rem;
        margin-bottom: 8px;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        padding: 20px;
        border-radius: 12px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .stat-content h3 {
        font-size: 1.5rem;
    }
    
    .search-container {
        border-radius: 15px;
        margin-bottom: 20px;
    }
    
    .search-header {
        padding: 15px 20px;
    }
    
    .search-header h3 {
        font-size: 1.1rem;
    }
    
    .search-form {
        padding: 20px;
    }
    
    .search-row {
        flex-direction: column;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .search-field {
        min-width: 100%;
    }
    
    .search-field input, 
    .search-field select {
        padding: 12px 15px;
        font-size: 16px; /* منع التكبير على iOS */
        border-radius: 8px;
    }
    
    .search-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .search-btn, .clear-btn, .refresh-btn {
        padding: 10px 16px;
        font-size: 13px;
        border-radius: 8px;
    }
    
    .tabs {
        border-radius: 12px;
        max-width: 100%;
    }
    
    .tab-btn {
        padding: 15px 20px;
        font-size: 14px;
        flex-direction: column;
        gap: 5px;
    }
    
    .tab-btn i {
        font-size: 16px;
    }
    
    .tab-count {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .table-section {
        border-radius: 15px;
        margin-bottom: 20px;
    }
    
    .table-header {
        padding: 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .table-controls {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .control-btn {
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 8px;
    }
    
    /* جعل الجدول قابل للتمرير أفقياً */
    .table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 0;
    }
    
    .enhanced-table {
        min-width: 900px; /* عرض أدنى للجدول */
    }
    
    .enhanced-table th,
    .enhanced-table td {
        padding: 12px 8px;
        font-size: 12px;
        white-space: nowrap;
    }
    
    .enhanced-table th {
        position: sticky;
        top: 0;
        z-index: 10;
        font-size: 11px;
    }
    
    /* تحسين الأيقونات */
    .fa-eye, .fa-print, .fa-trash-alt {
        font-size: 16px !important;
        padding: 6px;
    }
    
    /* تحسين النوافذ المنبثقة */
    .modal-content {
        width: 98%;
        max-width: 98%;
        margin: 10px;
        border-radius: 12px;
    }
    
    .modal-content h2 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }
    
    .empty-state {
        padding: 40px 15px;
    }
    
    .empty-icon {
        font-size: 3rem;
    }
    
    .empty-state h3 {
        font-size: 1.3rem;
    }
}

/* تحسينات للشاشات الصغيرة جدًا (الهواتف المحمولة) */
@media (max-width: 576px) {
    .container {
        padding: 8px;
        padding-bottom: 220px !important; /* زيادة المساحة السفلية للشاشات الصغيرة جداً */
    }
    
    .page-header {
        padding: 15px 0;
        margin-bottom: 15px;
        border-radius: 12px;
    }
    
    .page-title {
        font-size: 1.6rem;
        margin-bottom: 5px;
    }
    
    .page-subtitle {
        font-size: 0.9rem;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        gap: 12px;
        margin-bottom: 15px;
    }
    
    .stat-card {
        padding: 15px;
        border-radius: 10px;
        flex-direction: row;
        gap: 15px;
    }
    
    .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .stat-content h3 {
        font-size: 1.3rem;
    }
    
    .stat-content p {
        font-size: 13px;
    }
    
    .search-container {
        border-radius: 12px;
        margin-bottom: 15px;
    }
    
    .search-header {
        padding: 12px 15px;
    }
    
    .search-header h3 {
        font-size: 1rem;
    }
    
    .search-form {
        padding: 15px;
    }
    
    .search-row {
        gap: 12px;
        margin-bottom: 12px;
    }
    
    .search-field input, 
    .search-field select {
        padding: 10px 12px;
        font-size: 16px;
        border-radius: 6px;
    }
    
    .search-actions {
        gap: 6px;
    }
    
    .search-btn, .clear-btn, .refresh-btn {
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 6px;
        flex: 1;
    }
    
    .tabs {
        border-radius: 10px;
    }
    
    .tab-btn {
        padding: 12px 15px;
        font-size: 12px;
        gap: 4px;
    }
    
    .tab-btn i {
        font-size: 14px;
    }
    
    .tab-count {
        font-size: 10px;
        padding: 2px 5px;
    }
    
    .table-section {
        border-radius: 12px;
        margin-bottom: 15px;
    }
    
    .table-header {
        padding: 15px;
        gap: 12px;
    }
    
    .table-title h3 {
        font-size: 1.2rem;
    }
    
    .table-subtitle {
        font-size: 12px;
    }
    
    .control-btn {
        padding: 6px 10px;
        font-size: 11px;
        border-radius: 6px;
    }
    
    .enhanced-table {
        min-width: 800px;
    }
    
    .enhanced-table th,
    .enhanced-table td {
        padding: 10px 6px;
        font-size: 11px;
    }
    
    .enhanced-table th {
        font-size: 10px;
        padding: 12px 6px;
    }
    
    .fa-eye, .fa-print, .fa-trash-alt {
        font-size: 14px !important;
        padding: 4px;
    }
    
    .modal-content {
        padding: 15px;
        border-radius: 10px;
    }
    
    .modal-content h2 {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }
    
    .modal-content table th,
    .modal-content table td {
        padding: 8px 4px;
        font-size: 11px;
    }
    
    .status-frame {
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 15px;
    }
    
    .empty-state {
        padding: 30px 10px;
    }
    
    .empty-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }
    
    .empty-state h3 {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }
    
    .empty-state p {
        font-size: 0.9rem;
        margin-bottom: 20px;
    }
    
    .loading-spinner {
        padding: 30px 15px;
    }
    
    .spinner {
        width: 35px;
        height: 35px;
    }
    
    .loading-text p {
        font-size: 1rem;
    }
}

/* تغليف الجدول لإضافة التمرير الأفقي على الشاشات الصغيرة */
.table-wrapper {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 15px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .table-wrapper {
    border: 1px solid #30363d;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* شريط التمرير المخصص للجدول */
.table-wrapper::-webkit-scrollbar {
    height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* مؤشر التحميل */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
    color: var(--color-primary);
}

.loading-spinner.active {
    display: block;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين أزرار الحالة */
.status-frame {
    font-size: 12px;
    font-weight: 600;
    border-radius: 20px;
    padding: 6px 12px;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-frame.pending {
    background: linear-gradient(135deg, #ffc107, #ffca28);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.status-frame.delayed {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.status-frame.confirmed {
    background: linear-gradient(135deg, #28a745, #2ecc71);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* تنسيق عمود التاريخ والوقت */
.sale-time {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    color: #495057;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 10px 14px !important;
    border-radius: 10px;
    font-size: 12px;
    letter-spacing: 0.3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-align: center;
    line-height: 1.4;
    min-width: 120px;
    white-space: nowrap;
}

.sale-time::before {
    content: '📅';
    position: absolute;
    left: 6px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    opacity: 0.7;
}

.sale-time:hover {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

/* تنسيق عمود الوقت في الوضع المظلم */
[data-theme="dark"] .sale-time {
    background: linear-gradient(135deg, #2c3034, #24282c);
    color: #e9ecef;
    border-color: rgba(255, 255, 255, 0.1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .sale-time:hover {
    background: linear-gradient(135deg, #1a1d23, #0d1117);
    border-color: var(--color-primary);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* تحسين عرض التاريخ والوقت في الجداول الصغيرة */
@media (max-width: 768px) {
    .sale-time {
        font-size: 10px;
        padding: 8px 10px !important;
        border-radius: 8px;
        letter-spacing: 0.2px;
        min-width: 100px;
        line-height: 1.3;
    }
    
    .sale-time::before {
        font-size: 10px;
        left: 4px;
    }
}

@media (max-width: 576px) {
    .sale-time {
        font-size: 9px;
        padding: 6px 8px !important;
        border-radius: 6px;
        letter-spacing: 0.1px;
        min-width: 90px;
        line-height: 1.2;
    }
    
    .sale-time::before {
        font-size: 8px;
        left: 3px;
    }
}

/* تحسين عرض التاريخ والوقت في الشاشات الكبيرة */
@media (min-width: 1200px) {
    .sale-time {
        font-size: 13px;
        padding: 12px 16px !important;
        border-radius: 12px;
        letter-spacing: 0.4px;
        min-width: 140px;
        line-height: 1.5;
    }
    
    .sale-time::before {
        font-size: 14px;
        left: 8px;
    }
}

@media (min-width: 1400px) {
    .sale-time {
        font-size: 14px;
        padding: 14px 18px !important;
        border-radius: 14px;
        letter-spacing: 0.5px;
        min-width: 160px;
        line-height: 1.6;
    }
    
    .sale-time::before {
        font-size: 15px;
        left: 10px;
    }
}

/* تحسين عرض عمود التاريخ والوقت في النوافذ المنبثقة */
#orderDetailsModal .sale-time {
    display: inline-block;
    margin: 0 auto;
    min-width: 120px;
    text-align: center;
    max-width: 180px;
    word-wrap: break-word;
}

/* تأثير انيميشن لعمود الوقت عند التحديث */
.sale-time.updated {
    animation: timeUpdate 0.6s ease-in-out;
}

@keyframes timeUpdate {
    0% {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, #d4edda, #a8e6cf);
        transform: scale(1.05);
    }
    100% {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        transform: scale(1);
    }
}

/* تحسين ترتيب الأعمدة في النافذة المنبثقة */
#orderDetailsModal table th:nth-child(9) {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#orderDetailsModal table th:nth-child(9):hover {
    background: linear-gradient(135deg, #138496, #1e7e34);
}

/* تحسينات للنافذة المجمعة للفواتير */
#combinedOrdersModal .modal-content {
    max-width: 95% !important;
    width: 1400px !important;
    max-height: 90vh;
    overflow-y: auto;
}

#combinedOrdersModal .summary-bar {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border: 2px solid rgba(0, 123, 255, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#combinedOrdersModal .summary-item {
    background: linear-gradient(135deg, var(--color-primary), #667eea) !important;
    color: white !important;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
}

#combinedOrdersModal .summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

#combinedOrdersModal .enhanced-table {
    font-size: 13px;
}

#combinedOrdersModal .enhanced-table th {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    font-size: 12px;
    padding: 12px 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#combinedOrdersModal .enhanced-table td {
    padding: 10px 8px;
    font-size: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#combinedOrdersModal .sale-time {
    font-size: 10px !important;
    padding: 8px 10px !important;
    min-width: 100px;
    line-height: 1.3;
}

/* تحسينات للوضع المظلم في النافذة المجمعة */
[data-theme="dark"] #combinedOrdersModal .modal-content {
    background-color: #0d1117;
    border-color: var(--color-primary);
    color: #c9d1d9;
}

[data-theme="dark"] #combinedOrdersModal .summary-bar {
    background: linear-gradient(135deg, #2c3034, #24282c) !important;
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] #combinedOrdersModal .enhanced-table {
    background-color: #161b22;
}

[data-theme="dark"] #combinedOrdersModal .enhanced-table td {
    background-color: #161b22;
    border-bottom-color: #30363d;
    color: #c9d1d9;
}

[data-theme="dark"] #combinedOrdersModal .enhanced-table tr:hover td {
    background-color: #21262d;
}

/* تحسينات ريسبونسف للنافذة المجمعة */
@media (max-width: 1200px) {
    #combinedOrdersModal .modal-content {
        width: 98% !important;
        max-width: 98% !important;
    }
    
    #combinedOrdersModal .enhanced-table {
        font-size: 12px;
    }
    
    #combinedOrdersModal .enhanced-table th,
    #combinedOrdersModal .enhanced-table td {
        padding: 8px 6px;
        font-size: 11px;
    }
    
    #combinedOrdersModal .sale-time {
        font-size: 9px !important;
        padding: 6px 8px !important;
        min-width: 90px;
        line-height: 1.2;
    }
}

@media (max-width: 768px) {
    #combinedOrdersModal .modal-content {
        margin: 5px;
        max-height: 95vh;
    }
    
    #combinedOrdersModal .summary-bar {
        flex-direction: column;
        gap: 8px;
    }
    
    #combinedOrdersModal .summary-item {
        margin: 0 !important;
        text-align: center;
        padding: 8px 12px !important;
    }
    
    #combinedOrdersModal .enhanced-table {
        min-width: 800px;
        font-size: 11px;
    }
    
    #combinedOrdersModal .enhanced-table th,
    #combinedOrdersModal .enhanced-table td {
        padding: 6px 4px;
        font-size: 10px;
    }
    
    #combinedOrdersModal .sale-time {
        font-size: 8px !important;
        padding: 4px 6px !important;
        min-width: 80px;
        line-height: 1.1;
    }
}

@media (max-width: 576px) {
    #combinedOrdersModal .modal-content h2 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }
    
    #combinedOrdersModal .enhanced-table {
        min-width: 700px;
    }
    
    #combinedOrdersModal .enhanced-table th,
    #combinedOrdersModal .enhanced-table td {
        padding: 4px 3px;
        font-size: 9px;
    }
    
    #combinedOrdersModal .sale-time {
        font-size: 7px !important;
        padding: 3px 5px !important;
        min-width: 70px;
        line-height: 1.0;
    }
    
    #combinedOrdersModal .status-frame {
        font-size: 8px;
        padding: 1px 4px;
    }
}