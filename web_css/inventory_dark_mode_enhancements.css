/* تحسينات إضافية للوضع المظلم - ملف منفصل */

/* تحسين تنبيه حالة الحفظ */
[data-theme="dark"] #saveStatusAlert {
    background-color: var(--color-secondary) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] #saveStatusAlert.alert-success {
    background-color: rgba(35, 134, 54, 0.2) !important;
    border-color: var(--badge-success-bg) !important;
    color: var(--badge-success-bg) !important;
}

[data-theme="dark"] #saveStatusAlert.alert-warning {
    background-color: rgba(255, 193, 7, 0.2) !important;
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

[data-theme="dark"] #saveStatusAlert.alert-info {
    background-color: rgba(31, 111, 235, 0.2) !important;
    border-color: var(--badge-info-bg) !important;
    color: var(--badge-info-bg) !important;
}

/* تحسين حاوية الأزرار */
[data-theme="dark"] .action-buttons-container {
    background: linear-gradient(135deg, var(--color-secondary), #1a1f26) !important;
    border-color: var(--input-border) !important;
    color: var(--color-fg) !important;
}

/* تحسين الأزرار السريعة */
[data-theme="dark"] .quick-btn {
    background: var(--input-bg) !important;
    border-color: var(--input-border) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] .quick-btn:hover {
    background: var(--color-primary) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-button-text) !important;
}

[data-theme="dark"] .quick-btn.active {
    background: var(--color-primary) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-button-text) !important;
}

[data-theme="dark"] .quick-btn.has-pending {
    background: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

/* تحسين التنبيهات */
[data-theme="dark"] .offline-alert {
    background-color: rgba(255, 193, 7, 0.2) !important;
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

/* تحسين مؤشر الحفظ */
[data-theme="dark"] #saveIndicator {
    background-color: var(--color-secondary) !important;
    border: 1px solid var(--input-border) !important;
    color: var(--color-fg) !important;
}

/* تحسين النماذج والحقول */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select,
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] select,
[data-theme="dark"] textarea {
    background-color: var(--input-bg) !important;
    border-color: var(--input-border) !important;
    color: var(--input-text) !important;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus,
[data-theme="dark"] input:focus,
[data-theme="dark"] select:focus,
[data-theme="dark"] textarea:focus {
    background-color: var(--input-bg) !important;
    border-color: var(--color-primary) !important;
    color: var(--input-text) !important;
    box-shadow: 0 0 0 0.2rem rgba(88, 166, 255, 0.25) !important;
}

/* تحسين الأزرار */
[data-theme="dark"] .btn-primary {
    background-color: var(--color-primary) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-button-text) !important;
}

[data-theme="dark"] .btn-primary:hover {
    background-color: #4dabf7 !important;
    border-color: #4dabf7 !important;
}

[data-theme="dark"] .btn-success {
    background-color: var(--badge-success-bg) !important;
    border-color: var(--badge-success-bg) !important;
}

[data-theme="dark"] .btn-success:hover {
    background-color: #2d8f3f !important;
    border-color: #2d8f3f !important;
}

[data-theme="dark"] .btn-danger {
    background-color: var(--badge-danger-bg) !important;
    border-color: var(--badge-danger-bg) !important;
}

[data-theme="dark"] .btn-danger:hover {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
}

[data-theme="dark"] .btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

[data-theme="dark"] .btn-warning:hover {
    background-color: #e0a800 !important;
    border-color: #e0a800 !important;
}

[data-theme="dark"] .btn-info {
    background-color: var(--badge-info-bg) !important;
    border-color: var(--badge-info-bg) !important;
}

[data-theme="dark"] .btn-info:hover {
    background-color: #0f5132 !important;
    border-color: #0f5132 !important;
}

[data-theme="dark"] .btn-secondary {
    background-color: var(--badge-secondary-bg) !important;
    border-color: var(--badge-secondary-bg) !important;
}

[data-theme="dark"] .btn-secondary:hover {
    background-color: #5a6268 !important;
    border-color: #5a6268 !important;
}

/* تحسين التنبيهات */
[data-theme="dark"] .alert {
    border-width: 1px !important;
}

[data-theme="dark"] .alert-primary {
    background-color: rgba(88, 166, 255, 0.2) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-primary) !important;
}

[data-theme="dark"] .alert-success {
    background-color: rgba(35, 134, 54, 0.2) !important;
    border-color: var(--badge-success-bg) !important;
    color: var(--badge-success-bg) !important;
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(218, 54, 51, 0.2) !important;
    border-color: var(--badge-danger-bg) !important;
    color: var(--badge-danger-bg) !important;
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(255, 193, 7, 0.2) !important;
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

[data-theme="dark"] .alert-info {
    background-color: rgba(31, 111, 235, 0.2) !important;
    border-color: var(--badge-info-bg) !important;
    color: var(--badge-info-bg) !important;
}

/* تحسين البطاقات */
[data-theme="dark"] .card {
    background-color: var(--color-secondary) !important;
    border-color: var(--input-border) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] .card-header {
    background-color: var(--color-hover) !important;
    border-bottom-color: var(--input-border) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] .card-footer {
    background-color: var(--color-hover) !important;
    border-top-color: var(--input-border) !important;
}

/* تحسين النصوص */
[data-theme="dark"] .text-muted {
    color: #7d8590 !important;
}

[data-theme="dark"] .text-primary {
    color: var(--color-primary) !important;
}

[data-theme="dark"] .text-success {
    color: var(--badge-success-bg) !important;
}

[data-theme="dark"] .text-danger {
    color: var(--badge-danger-bg) !important;
}

[data-theme="dark"] .text-warning {
    color: #ffc107 !important;
}

[data-theme="dark"] .text-info {
    color: var(--badge-info-bg) !important;
}

/* تحسين الخلفيات */
[data-theme="dark"] .bg-light {
    background-color: var(--color-secondary) !important;
}

[data-theme="dark"] .bg-white {
    background-color: var(--color-secondary) !important;
}

[data-theme="dark"] .bg-primary {
    background-color: var(--color-primary) !important;
}

[data-theme="dark"] .bg-success {
    background-color: var(--badge-success-bg) !important;
}

[data-theme="dark"] .bg-danger {
    background-color: var(--badge-danger-bg) !important;
}

[data-theme="dark"] .bg-warning {
    background-color: #ffc107 !important;
}

[data-theme="dark"] .bg-info {
    background-color: var(--badge-info-bg) !important;
}

/* تحسين حالة الاتصال */
[data-theme="dark"] .connection-status.online {
    background: rgba(35, 134, 54, 0.3);
    color: var(--badge-success-bg);
    border-color: var(--badge-success-bg);
}

[data-theme="dark"] .connection-status.offline {
    background: rgba(218, 54, 51, 0.3);
    color: var(--badge-danger-bg);
    border-color: var(--badge-danger-bg);
}

/* تحسين أزرار الإجراءات */
[data-theme="dark"] .action-btn {
    background: linear-gradient(135deg, var(--color-primary), #4dabf7) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-button-text) !important;
}

[data-theme="dark"] .action-btn:hover {
    background: linear-gradient(135deg, #4dabf7, #339af0) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(88, 166, 255, 0.4) !important;
}

/* تحسين أزرار الإجراءات المختلفة */
[data-theme="dark"] .action-btn.btn-success {
    background: linear-gradient(135deg, var(--badge-success-bg), #2d8f3f) !important;
    border-color: var(--badge-success-bg) !important;
}

[data-theme="dark"] .action-btn.btn-danger {
    background: linear-gradient(135deg, var(--badge-danger-bg), #c82333) !important;
    border-color: var(--badge-danger-bg) !important;
}

[data-theme="dark"] .action-btn.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

[data-theme="dark"] .action-btn.btn-info {
    background: linear-gradient(135deg, var(--badge-info-bg), #0f5132) !important;
    border-color: var(--badge-info-bg) !important;
}

[data-theme="dark"] .action-btn.btn-secondary {
    background: linear-gradient(135deg, var(--badge-secondary-bg), #5a6268) !important;
    border-color: var(--badge-secondary-bg) !important;
}

/* تحسينات إضافية خاصة بصفحة الجرد */

/* تحسين SweetAlert2 للوضع المظلم */
[data-theme="dark"] .swal2-popup {
    background-color: var(--color-secondary) !important;
    color: var(--color-fg) !important;
    border: 1px solid var(--color-primary) !important;
}

[data-theme="dark"] .swal2-title {
    color: var(--color-fg) !important;
}

[data-theme="dark"] .swal2-html-container {
    color: var(--color-fg) !important;
}

[data-theme="dark"] .swal2-confirm {
    background-color: var(--color-primary) !important;
    border-color: var(--color-primary) !important;
}

[data-theme="dark"] .swal2-cancel {
    background-color: var(--badge-danger-bg) !important;
    border-color: var(--badge-danger-bg) !important;
}

/* تحسين الجدول للوضع المظلم */
[data-theme="dark"] .table-container-enhanced {
    background-color: var(--color-secondary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .table-header-enhanced {
    background: var(--table-header-bg) !important;
    color: var(--color-header-text) !important;
}

/* تحسين شريط البحث للوضع المظلم */
[data-theme="dark"] .search-container-enhanced {
    background-color: var(--color-secondary) !important;
    border: 1px solid var(--input-border) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] .search-container-enhanced.sticky {
    background-color: var(--color-secondary) !important;
    border-color: var(--color-primary) !important;
}

/* تحسين الإحصائيات للوضع المظلم */
[data-theme="dark"] .inventory-stats .stat-card {
    background-color: var(--color-secondary) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] .stat-number {
    color: var(--color-primary) !important;
}

[data-theme="dark"] .stat-label {
    color: var(--color-fg) !important;
}

/* تحسين شريط التقدم للوضع المظلم */
[data-theme="dark"] .progress-container {
    background-color: var(--color-secondary) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] .progress-bar {
    background-color: var(--progress-bg) !important;
}

/* تحسين الملخص العائم للوضع المظلم */
[data-theme="dark"] .floating-summary {
    background-color: var(--color-secondary) !important;
    border-color: var(--color-primary) !important;
    color: var(--color-fg) !important;
}

/* تحسين زر العودة للأعلى للوضع المظلم */
[data-theme="dark"] .back-to-top {
    background-color: var(--color-primary) !important;
    color: var(--color-button-text) !important;
}

[data-theme="dark"] .back-to-top:hover {
    background-color: #4dabf7 !important;
}

/* تحسين الحاسبة للوضع المظلم */
[data-theme="dark"] #calculator {
    background-color: var(--color-secondary) !important;
    border-color: var(--input-border) !important;
}

[data-theme="dark"] #calcHeader {
    background: var(--table-header-bg) !important;
    color: var(--color-header-text) !important;
}

[data-theme="dark"] #calcDisplay {
    background-color: var(--input-bg) !important;
    color: var(--input-text) !important;
}

[data-theme="dark"] #calcResult {
    background-color: var(--progress-bg) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] #calcButtons button {
    background-color: var(--input-bg) !important;
    border-color: var(--input-border) !important;
    color: var(--color-fg) !important;
}

[data-theme="dark"] #calcButtons button:hover {
    background-color: var(--color-primary) !important;
    color: var(--color-button-text) !important;
    border-color: var(--color-primary) !important;
}