/* ملف CSS خاص بتقارير الأصناف وتحسينات تجربة المستخدم */

/* أنماط وضع الاستعلامات المحسنة */
.queries-mode-active {
    display: block !important;
    animation: fadeInUp 0.4s ease-out;
}

.queries-mode-inactive {
    display: none !important;
}

.queries-header {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-bg));
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 20px;
    border: 2px solid var(--color-primary);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.queries-header h3 {
    color: var(--color-primary);
    margin-bottom: 20px;
    font-size: 1.8em;
    font-weight: 700;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.barcode-status-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.barcode-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
    border-radius: 25px;
    border: 2px solid #4caf50;
    color: #2e7d32;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
    transition: all 0.3s ease;
}

.barcode-status i {
    font-size: 1.2em;
    color: #4caf50;
}

.control-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.control-buttons .btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Cairo', Arial, sans-serif;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #ff9800);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #f57c00);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #343a40);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

/* أنماط زر البحث في الوضع المظلم */
[data-theme="dark"] .btn-primary {
    background: linear-gradient(135deg, #1f6feb, #58a6ff);
    box-shadow: 0 4px 15px rgba(31, 111, 235, 0.3);
}

[data-theme="dark"] .btn-primary:hover {
    background: linear-gradient(135deg, #1a5cd8, #4dabf7);
    box-shadow: 0 6px 20px rgba(31, 111, 235, 0.4);
}

/* أنماط جدول ال��ستعلامات */
#queriesTable {
    margin-top: 20px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

#queriesTable .quantity-input {
    width: 80px;
    padding: 8px 12px;
    border: 2px solid var(--color-primary);
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    background: var(--color-secondary);
    color: var(--color-fg);
    transition: all 0.3s ease;
}

#queriesTable .quantity-input:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
    background: #f8fff9;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: #ffffff;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-sm {
    padding: 6px 10px;
    font-size: 0.9em;
}

/* ملخص الاستعلامات */
.queries-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 25px;
    padding: 25px;
    background: linear-gradient(135deg, var(--color-secondary), var(--color-bg));
    border-radius: 15px;
    border: 2px solid var(--color-primary);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.summary-item span:first-child {
    font-weight: 600;
    color: var(--color-fg);
    font-size: 1.1em;
}

.summary-item span:last-child {
    font-weight: 700;
    color: var(--color-primary);
    font-size: 1.3em;
}

/* أنماط Toastr مخصصة للوضع الفاتح والمظلم */
.toast {
    border-radius: 12px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    font-weight: 600 !important;
    border: 2px solid transparent !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    min-width: 300px !important;
    padding: 15px 20px !important;
}

.toast-success {
    background: linear-gradient(135deg, var(--color-primary), #4caf50) !important;
    border-color: #4caf50 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3) !important;
}

.toast-error {
    background: linear-gradient(135deg, #f85149, #dc3545) !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(248, 81, 73, 0.3) !important;
}

.toast-warning {
    background: linear-gradient(135deg, #ffc107, #ff9800) !important;
    border-color: #ff9800 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3) !important;
}

.toast-info {
    background: linear-gradient(135deg, var(--color-primary), #17a2b8) !important;
    border-color: #17a2b8 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3) !important;
}

/* أنماط Toastr للوضع المظلم */
[data-theme="dark"] .toast-success {
    background: linear-gradient(135deg, #238636, #2ea043) !important;
    border-color: #2ea043 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(35, 134, 54, 0.4) !important;
}

[data-theme="dark"] .toast-error {
    background: linear-gradient(135deg, #da3633, #f85149) !important;
    border-color: #f85149 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(218, 54, 51, 0.4) !important;
}

[data-theme="dark"] .toast-warning {
    background: linear-gradient(135deg, #fb8500, #ffc107) !important;
    border-color: #ffc107 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(251, 133, 0, 0.4) !important;
}

[data-theme="dark"] .toast-info {
    background: linear-gradient(135deg, #1f6feb, #58a6ff) !important;
    border-color: #58a6ff !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(31, 111, 235, 0.4) !important;
}

/* تحسينات إضافية لـ Toastr */
.toast-title {
    font-weight: 700 !important;
    font-size: 16px !important;
    margin-bottom: 8px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.toast-message {
    font-size: 14px !important;
    line-height: 1.5 !important;
    margin-top: 5px !important;
}

.toast-close-button {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 20px !important;
    font-weight: bold !important;
    opacity: 0.8 !important;
    background: none !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.toast-close-button:hover {
    opacity: 1 !important;
    color: #ffffff !important;
    transform: scale(1.1) !important;
}

.toast-progress {
    background-color: rgba(255, 255, 255, 0.3) !important;
    height: 4px !important;
    border-radius: 2px !important;
}

/* تأثيرات الحركة المحسنة */
@keyframes toastSlideInLeft {
    from {
        transform: translateX(-100%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

@keyframes toastSlideOutLeft {
    from {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
    to {
        transform: translateX(-100%) scale(0.8);
        opacity: 0;
    }
}

.toast {
    animation: toastSlideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.toast.toast-closing {
    animation: toastSlideOutLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* أنماط الوضع المظلم للاستعلامات */
[data-theme="dark"] .queries-header {
    background: linear-gradient(135deg, #21262d, #161b22);
    border-color: var(--color-primary);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .barcode-status {
    background: linear-gradient(135deg, #1a2e1a, #2d5a2d);
    border-color: #3fb950;
    color: #4caf50;
}

[data-theme="dark"] .barcode-status i {
    color: #3fb950;
}

[data-theme="dark"] .summary-item {
    background: linear-gradient(135deg, #21262d, #161b22);
    border-left-color: var(--color-primary);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .summary-item span:first-child {
    color: #c9d1d9;
}

[data-theme="dark"] .summary-item span:last-child {
    color: var(--color-primary);
}

[data-theme="dark"] .queries-summary {
    background: linear-gradient(135deg, #21262d, #161b22);
    border-color: var(--color-primary);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] #queriesTable .quantity-input {
    background: #21262d;
    border-color: var(--color-primary);
    color: #c9d1d9;
}

[data-theme="dark"] #queriesTable .quantity-input:focus {
    border-color: #3fb950;
    background: #1a2e1a;
    box-shadow: 0 0 0 3px rgba(63, 185, 80, 0.2);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .barcode-status-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-buttons {
        justify-content: center;
    }
    
    .queries-summary {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 20px;
    }
    
    .summary-item {
        padding: 12px 15px;
    }
    
    .summary-item span:first-child {
        font-size: 1em;
    }
    
    .summary-item span:last-child {
        font-size: 1.2em;
    }
    
    .toast {
        margin: 10px !important;
        min-width: 280px !important;
        max-width: calc(100vw - 20px) !important;
    }
    
    .toast-title {
        font-size: 14px !important;
    }
    
    .toast-message {
        font-size: 13px !important;
    }
    
    .queries-header h3 {
        font-size: 1.5em;
        flex-direction: column;
        gap: 5px;
    }
    
    .barcode-status {
        padding: 12px 15px;
        font-size: 0.9em;
    }
    
    .control-buttons .btn {
        padding: 10px 15px;
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    .queries-header {
        padding: 20px 15px;
    }
    
    .queries-header h3 {
        font-size: 1.3em;
    }
    
    .barcode-status {
        padding: 10px 12px;
        font-size: 0.85em;
    }
    
    .control-buttons .btn {
        padding: 8px 12px;
        font-size: 0.85em;
    }
    
    #queriesTable .quantity-input {
        width: 70px;
        padding: 6px 8px;
        font-size: 0.9em;
    }
}

/* تأثيرات إضافية للتفاعل */
.queries-header:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.barcode-status.active {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
    }
    50% {
        box-shadow: 0 4px 25px rgba(76, 175, 80, 0.4);
    }
    100% {
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
    }
}

/* تحسينات إضافية للأزرار */
.queries-mode-btn {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.queries-mode-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3) !important;
}

[data-theme="dark"] .queries-mode-btn {
    background: linear-gradient(135deg, #1f6feb, #58a6ff) !important;
}

[data-theme="dark"] .queries-mode-btn:hover {
    background: linear-gradient(135deg, #1a5cd8, #4dabf7) !important;
    box-shadow: 0 4px 15px rgba(31, 111, 235, 0.4) !important;
}

/* تحسينات للأيقونات */
.fa-barcode {
    animation: barcodeGlow 3s ease-in-out infinite;
}

@keyframes barcodeGlow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
    }
    50% {
        text-shadow: 0 0 15px rgba(76, 175, 80, 0.8);
    }
}

[data-theme="dark"] .fa-barcode {
    animation: barcodeGlowDark 3s ease-in-out infinite;
}

@keyframes barcodeGlowDark {
    0%, 100% {
        text-shadow: 0 0 5px rgba(63, 185, 80, 0.5);
    }
    50% {
        text-shadow: 0 0 15px rgba(63, 185, 80, 0.8);
    }
}