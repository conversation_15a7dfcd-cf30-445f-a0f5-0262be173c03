/* Multi Customer Tabs Styles */

/* CSS Variables for consistency with add_invoice.css */
:root {
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #4dabf7;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --light-bg: #f0f2f5;
    
    /* ألوان محسنة للثيم الداكن - أكثر راحة للعين */
    --dark-bg: #0f1419;
    --dark-surface: #1a2332;
    --dark-surface-light: #242b3d;
    --dark-surface-hover: #2a3441;
    --dark-text: #e1e8f0;
    --dark-text-secondary: #b8c5d1;
    --dark-text-muted: #8a9ba8;
    --border-color: #dee2e6;
    --dark-border: #2d3748;
    --dark-border-light: #3a4553;
    
    /* ألوان زرقاء ناعمة ومريحة */
    --blue-gradient-start: #1e3a8a;
    --blue-gradient-end: #3b82f6;
    --blue-accent: #5b9bd5;
    --blue-hover: #4a90c2;
    --blue-soft: #6ba3d6;
    --blue-muted: #4a7ba7;
}

.multi-customer-tabs {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    padding: 10px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 15px 15px;
    margin-bottom: 20px;
    position: relative;
    z-index: 100;
}

.customer-tabs-container {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.customer-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-width: 80px;
    justify-content: center;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.customer-tab:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.customer-tab.active {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    border-color: white;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.customer-tab .fas {
    font-size: 14px;
}

.customer-number {
    font-weight: bold;
    font-size: 16px;
}

.items-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

.items-indicator.has-items {
    display: flex !important;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.add-customer-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px dashed rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 18px;
}

.add-customer-tab:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
}

.remove-customer {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
}

.remove-customer:hover {
    background: #ff3742;
    transform: scale(1.1);
}

/* Dark Mode Support */
body.dark-mode .multi-customer-tabs {
    background: var(--dark-surface-light);
    border: 1px solid var(--dark-border-light);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

body.dark-mode .customer-tab {
    background: var(--dark-surface);
    border-color: var(--dark-border-light);
    color: var(--dark-text);
}

body.dark-mode .customer-tab:hover {
    background: rgba(91, 155, 213, 0.1);
    border-color: var(--blue-soft);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

body.dark-mode .customer-tab.active {
    background: var(--blue-soft);
    color: white;
    border-color: var(--blue-soft);
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.4);
}

body.dark-mode .add-customer-tab {
    background: var(--dark-surface);
    border-color: var(--dark-border-light);
    color: var(--blue-soft);
}

body.dark-mode .add-customer-tab:hover {
    background: rgba(91, 155, 213, 0.1);
    border-color: var(--blue-soft);
    color: var(--blue-soft);
}

body.dark-mode .customer-notification {
    background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
    box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .multi-customer-tabs {
        padding: 8px 15px;
    }
    
    .customer-tab {
        padding: 10px 12px;
        min-width: 70px;
    }
    
    .customer-number {
        font-size: 14px;
    }
    
    .add-customer-tab {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .customer-tabs-container {
        gap: 8px;
    }
    
    .customer-tab {
        padding: 8px 10px;
        min-width: 60px;
    }
    
    .customer-number {
        font-size: 12px;
    }
    
    .items-indicator {
        width: 18px;
        height: 18px;
        font-size: 9px;
    }
}

/* Animation for tab switching */
.customer-tab-switching {
    animation: tabSwitch 0.3s ease;
}

@keyframes tabSwitch {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Notification styles for customer actions */
.customer-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced visual feedback */
.customer-tab-highlight {
    animation: highlight 0.6s ease;
}

@keyframes highlight {
    0%, 100% {
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    }
    50% {
        box-shadow: 0 5px 30px rgba(0, 123, 255, 0.4);
    }
}