/* Page Loader Styles */
#page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--color-bg);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

/* Brand Logo Container */
.loader-brand {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.brand-name {
    font-size: 3.5rem;
    font-weight: 800;
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--color-primary), #667eea, #764ba2);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    letter-spacing: 2px;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

/* Simple Sequential Bouncing Dots Loader - Under brand name */
.dots-loader {
    margin: 0 auto 20px;
    width: 80px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.dot {
    width: 12px;
    height: 12px;
    background-color: var(--color-primary);
    border-radius: 50%;
    display: inline-block;
    animation: simpleBounce 1.2s infinite ease-in-out;
}

.dot:nth-child(1) { 
    animation-delay: 0s;
}

.dot:nth-child(2) { 
    animation-delay: 0.2s;
}

.dot:nth-child(3) { 
    animation-delay: 0.4s;
}

/* Particles Background */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--color-primary);
    border-radius: 50%;
    opacity: 0.6;
    animation: particleFloat 6s linear infinite;
}

.particle:nth-child(odd) {
    animation-duration: 8s;
    background: rgba(63, 81, 181, 0.3);
}

.particle:nth-child(even) {
    animation-duration: 10s;
    background: rgba(63, 81, 181, 0.5);
}

#page-loader .loading-text {
    color: var(--color-fg);
    font-size: 18px;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    text-align: center;
    margin-bottom: 10px;
    animation: textPulse 2s ease-in-out infinite;
}

#page-loader .loading-subtitle {
    color: var(--color-fg);
    opacity: 0.7;
    font-size: 14px;
    font-family: 'Cairo', sans-serif;
    text-align: center;
    animation: subtitleFloat 3s ease-in-out infinite;
}

/* Animations */
@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0.6;
        transform: translateY(0);
    }
    50% {
        opacity: 1;
        transform: translateY(-2px);
    }
}

/* Simple bounce animation for sequential dots */
@keyframes simpleBounce {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.7;
    }
    30% {
        transform: translateY(-15px);
        opacity: 1;
    }
}

@keyframes sk-bouncedelay {
    0%, 80%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    } 
    40% {
        -webkit-transform: scale(1.0);
        transform: scale(1.0);
    }
}

@-webkit-keyframes sk-bouncedelay {
    0%, 80%, 100% { 
        -webkit-transform: scale(0);
    }
    40% { 
        -webkit-transform: scale(1.0);
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) translateX(50px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes textPulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

@keyframes subtitleFloat {
    0%, 100% {
        opacity: 0.7;
        transform: translateY(0);
    }
    50% {
        opacity: 0.9;
        transform: translateY(-3px);
    }
}

/* Dark theme support for loader */
[data-theme="dark"] #page-loader {
    background: var(--color-bg);
}

[data-theme="dark"] .brand-name {
    background: linear-gradient(135deg, var(--color-primary), #79c0ff, #a5b4fc);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="dark"] .brand-subtitle {
    color: var(--color-fg);
}

[data-theme="dark"] .dot {
    background: var(--color-primary);
    box-shadow: 0 0 25px rgba(88, 166, 255, 0.8);
}

[data-theme="dark"] .dot::before {
    background: linear-gradient(45deg, var(--color-primary), transparent, var(--color-primary));
}

[data-theme="dark"] .floating-dot {
    background: var(--color-primary);
    box-shadow: 0 0 15px rgba(88, 166, 255, 0.6);
}

/* Enhanced dark mode animations - using simple bounce with glow effect */
[data-theme="dark"] .dots-loader .dot {
    animation: simpleBounce 1.2s infinite ease-in-out;
    box-shadow: 0 0 15px rgba(88, 166, 255, 0.6);
}

[data-theme="dark"] .dot:nth-child(1) { 
    animation-delay: 0s;
}

[data-theme="dark"] .dot:nth-child(2) { 
    animation-delay: 0.2s;
}

[data-theme="dark"] .dot:nth-child(3) { 
    animation-delay: 0.4s;
}


[data-theme="dark"] #page-loader .loading-text {
    color: var(--color-fg);
}

[data-theme="dark"] #page-loader .loading-subtitle {
    color: var(--color-fg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .brand-name {
        font-size: 2.5rem;
        letter-spacing: 1px;
    }
    
    .brand-subtitle {
        font-size: 1rem;
    }
    
    .dots-loader {
        gap: 6px;
        margin-bottom: 25px;
    }
    
    .dot {
        width: 10px;
        height: 10px;
    }
    
    #page-loader .loading-text {
        font-size: 16px;
    }
    
    #page-loader .loading-subtitle {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .brand-name {
        font-size: 2rem;
        letter-spacing: 0.5px;
    }
    
    .brand-subtitle {
        font-size: 0.9rem;
    }
    
    .dots-loader {
        gap: 5px;
        margin-bottom: 20px;
    }
    
    .dot {
        width: 8px;
        height: 8px;
    }
    
    #page-loader .loading-text {
        font-size: 14px;
    }
    
    #page-loader .loading-subtitle {
        font-size: 11px;
    }
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

/* استيراد خط القاهرة والمتغيرات من style_web.css */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* متغيرات الألوان للآلة الحاسبة - متوافقة مع style_web.css */
:root {
    --calc-bg: #ffffff;
    --calc-header-bg: #3f51b5;
    --calc-border: #e9ecef;
    --calc-text: #333;
    --calc-button-hover: rgba(63, 81, 181, 0.1);
    --calc-shadow: rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] {
    --calc-bg: #161b22;
    --calc-header-bg: #58a6ff;
    --calc-border: #30363d;
    --calc-text: #c9d1d9;
    --calc-button-hover: rgba(88, 166, 255, 0.1);
    --calc-shadow: rgba(0, 0, 0, 0.5);
}
/* Enhanced Sidebar Style */
#sidebar.styled-sidebar {
    background: linear-gradient(145deg, #ffffff, #f8f9ff);
    width: 300px;
    position: fixed;
    right: 0;
    top: 0; /* Reset top position */
    height: calc(100% - 60px); /* Adjust height to account for the footer bar */
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #3f51b5 #f5f7fa;
    transition: transform 0.5s cubic-bezier(0.25, 1, 0.5, 1);
    transform: translateX(100%);
    z-index: 1300; /* updated to be above the header (header has z-index: 1200) */
    padding: 25px 20px;
    box-shadow: -8px 0 30px rgba(0, 0, 0, 0.15);
    border-radius: 24px 0 0 24px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

#sidebar.styled-sidebar::-webkit-scrollbar {
    width: 6px;
}

#sidebar.styled-sidebar::-webkit-scrollbar-thumb {
    background-color: #3f51b5;
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: padding-box;
}

#sidebar.styled-sidebar::-webkit-scrollbar-track {
    background-color: rgba(245, 247, 250, 0.5);
    border-radius: 10px;
}

#sidebar.styled-sidebar::-webkit-scrollbar-thumb:hover {
    background-color: #303f9f;
}

#sidebar.open {
    transform: translateX(0);
}

/* Modern Toggle Icon */
.menu-icon {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #3f51b5, #1abc9c);
    color: white;
    border-radius: 50%;
    padding: 15px;
    cursor: pointer;
    z-index: 1100;
    box-shadow: 0 4px 20px rgba(63, 81, 181, 0.4);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.menu-icon:hover {
    background: linear-gradient(135deg, #1abc9c, #3f51b5);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 30px rgba(63, 81, 181, 0.6);
    border-color: rgba(255, 255, 255, 0.4);
}

.menu-icon i {
    font-size: 22px;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* إضافة أنيميشن البَلس للأيقونة */
.menu-icon i.bounce {
    animation: pulse 0.5s;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.menu-icon.open i {
    transform: rotate(180deg); /* Rotate icon when sidebar is open */
}

#sidebar ul {
    list-style: none;
    padding: 0;
    margin-top: 30px;
}

#sidebar ul li {
    margin-bottom: 8px;
    position: relative;
}

#sidebar ul li a,
#sidebar ul li span {
    display: flex;
    align-items: center;
    color: #333;
    background-color: transparent;
    padding: 12px 18px;
    text-decoration: none;
    border-radius: 12px;
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    font-weight: 500;
    letter-spacing: 0.3px;
}

#sidebar ul li a::before,
#sidebar ul li span::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background-color: #3f51b5;
    transform: scaleY(0);
    transition: transform 0.3s ease;
    border-radius: 0 3px 3px 0;
}

#sidebar ul li a i,
#sidebar ul li span i {
    margin-left: 12px;
    font-size: 18px;
    transition: all 0.3s ease;
    width: 24px;
    text-align: center;
}

#sidebar ul li a:hover,
#sidebar ul li span:hover {
    background-color: rgba(63, 81, 181, 0.08);
    transform: translateX(-5px);
    padding-right: 23px;
}

#sidebar ul li a:hover::before,
#sidebar ul li span:hover::before {
    transform: scaleY(1);
}

#sidebar ul li a:hover i,
#sidebar ul li span:hover i {
    transform: scale(1.2);
    color: #3f51b5;
}

#sidebar ul li a.active {
    background: linear-gradient(145deg, #3f51b5, #5c6bc0);
    color: white;
    font-weight: 600;
    box-shadow: 0 6px 15px rgba(63, 81, 181, 0.4);
    position: relative;
    overflow: hidden;
    transform: translateX(-5px);
    padding-right: 23px;
    border-radius: 12px 0 0 12px;
    border-right: 4px solid #ffeb3b;
    animation: activeBorder 2s ease-in-out infinite;
}

@keyframes activeBorder {
    0% { border-right-color: #ffeb3b; }
    50% { border-right-color: #ff9800; }
    100% { border-right-color: #ffeb3b; }
}

#sidebar ul li a.active::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background-color: #ffeb3b;
    transform: scaleY(1);
}

#sidebar ul li a.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    animation: activeLink 3s ease-in-out infinite;
}

@keyframes activeLink {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

#sidebar ul li a.active i {
    color: white;
    animation: activeIcon 2s ease-in-out infinite;
    transform-origin: center;
    margin-left: 15px;
    font-size: 1.2em;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

#sidebar ul li a.active i::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    animation: iconGlow 2s ease-in-out infinite;
}

@keyframes iconGlow {
    0% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.2); }
    100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
}

@keyframes activeIcon {
    0% { transform: scale(1) rotate(0deg); text-shadow: 0 0 0 rgba(255,255,255,0); }
    25% { transform: scale(1.2) rotate(5deg); text-shadow: 0 0 15px rgba(255,255,255,0.7); }
    50% { transform: scale(1.1) rotate(0deg); text-shadow: 0 0 10px rgba(255,255,255,0.5); }
    75% { transform: scale(1.15) rotate(-5deg); text-shadow: 0 0 15px rgba(255,255,255,0.7); }
    100% { transform: scale(1) rotate(0deg); text-shadow: 0 0 0 rgba(255,255,255,0); }
}

/* Submenu */
#sidebar ul li ul {
    padding-right: 15px;
    margin-top: 5px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out;
}

#sidebar ul li:hover ul {
    max-height: 500px; /* Large enough to contain all submenu items */
}

/* تنسيقات عنصر القائمة مع السهم */
.menu-item-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.menu-item-container a {
    flex: 1;
}

.submenu-toggle {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.submenu-toggle:hover {
    background-color: rgba(63, 81, 181, 0.1);
}

.submenu-toggle i {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.submenu-toggle.open i {
    transform: rotate(180deg);
}

/* تنسيقات قائمة التصنيفات الفرعية */
.categories-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out;
    margin-top: 5px;
    padding-right: 15px;
}

.categories-submenu.open {
    max-height: 300px !important;
    overflow-y: auto !important;
    display: block !important;
    opacity: 1 !important;
    border-right: 2px solid rgba(63, 81, 181, 0.3);
    margin-top: 8px !important;
    padding-top: 5px !important;
    padding-bottom: 5px !important;
}

/* تنسيق scrollbar للقائمة الفرعية */
.categories-submenu::-webkit-scrollbar {
    width: 4px;
}

.categories-submenu::-webkit-scrollbar-track {
    background: transparent;
}

.categories-submenu::-webkit-scrollbar-thumb {
    background-color: rgba(63, 81, 181, 0.5);
    border-radius: 4px;
}

.loading-item span {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    color: #666;
    font-size: 0.9rem;
}

.loading-item i {
    margin-left: 8px;
}

/* Always show accounts submenu - targeting the accounts menu specifically */
#sidebar ul li:nth-child(9) ul {
    max-height: 500px !important;
    overflow: visible !important;
    display: block !important;
    opacity: 1 !important;
}

#sidebar ul li ul li {
    margin-bottom: 5px;
    opacity: 0.9;
    transform: translateX(5px);
    transition: all 0.3s ease;
}

#sidebar ul li:hover ul li {
    transform: translateX(0);
}

#sidebar ul li ul li a {
    font-size: 0.9rem;
    background-color: rgba(63, 81, 181, 0.05);
    padding: 10px 15px;
    border-radius: 8px;
    position: relative;
}

#sidebar ul li ul li a.subcategory-link {
    background-color: rgba(63, 81, 181, 0.08);
    border-right: 2px solid #3f51b5;
    font-weight: 500;
    transition: all 0.3s ease;
}

#sidebar ul li ul li a.subcategory-link:hover {
    background-color: rgba(63, 81, 181, 0.12);
    transform: translateX(-3px);
}

#sidebar ul li ul li a::before {
    width: 2px;
}

#sidebar ul li span {
    cursor: pointer;
}

/* Logout Button */
#sidebar ul li.logout {
    margin-top: 20px;
}

#sidebar ul li.logout a {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    text-align: center;
    color: #fff;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
    position: relative;
    overflow: hidden;
}

#sidebar ul li.logout a::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
    opacity: 0;
    transition: opacity 0.3s ease;
}

#sidebar ul li.logout a:hover {
    background: linear-gradient(135deg, #c0392b, #e74c3c);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4);
}

#sidebar ul li.logout a:hover::after {
    opacity: 1;
}

#sidebar ul li.logout a:hover i {
    animation: logoutIconPulse 0.6s infinite alternate;
    color: white;
}

@keyframes logoutIconPulse {
    from { transform: scale(1) rotate(0); }
    to { transform: scale(1.2) rotate(-10deg); }
}

/* Store Name Container */
.store-name-container {
    text-align: center;
    margin-bottom: 25px;
    padding: 18px 15px;
    background: linear-gradient(135deg, #3f51b5, #5c6bc0);
    border-radius: 16px;
    color: white;
    box-shadow: 0 6px 15px rgba(63, 81, 181, 0.3);
    position: relative;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.store-name-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.store-name-container:hover::before {
    opacity: 1;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.store-name {
    font-size: 1.6em;
    font-weight: bold;
    margin: 0;
    color: var(--color-header-text); /* إضافة لون صريح للوضع الفاتح */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.store-name i {
    margin-left: 10px;
    font-size: 1.2em;
    color: var(--color-header-text); /* إضافة لون صريح للأيقونة في الوضع الفاتح */
    animation: storeIconFloat 3s ease-in-out infinite;
}

@keyframes storeIconFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

/* Store Status Indicator */
#store-status {
    position: fixed !important; /* Use !important to ensure it stays fixed */
    top: 140px !important; /* Positioned right below the header */
    right: 15px !important;
    padding: 10px 16px;
    border-radius: 12px;
    font-size: 14px;
    z-index: 1000; /* Lower z-index to not appear above sidebar */
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--color-secondary, #ffffff);
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    color: var(--color-fg, #333333);
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    opacity: 1;
    cursor: pointer;
    width: auto;
    max-width: 150px; /* Limit the width */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-left: 3px solid var(--color-primary, #3f51b5);
    position: relative;
    overflow: hidden;
}

#store-status::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

#store-status:hover::before {
    opacity: 1;
    animation: shimmer 2s infinite;
}

#store-status:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Stores Dropdown */
#stores-dropdown {
    position: fixed !important; /* Use !important to ensure it stays fixed */
    top: 180px !important; /* Position below the store status indicator */
    right: 15px !important; /* Same right position as store status */
    width: 180px;
    max-height: 300px;
    overflow-y: auto;
    background-color: var(--color-secondary, #ffffff);
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    z-index: 1001; /* Higher than store-status but lower than sidebar */
    display: none;
    padding: 10px 0;
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary, #3f51b5) transparent;
}

/* Custom scrollbar for webkit browsers */
#stores-dropdown::-webkit-scrollbar {
    width: 6px;
}

#stores-dropdown::-webkit-scrollbar-track {
    background: transparent;
}

#stores-dropdown::-webkit-scrollbar-thumb {
    background-color: var(--color-primary, #3f51b5);
    border-radius: 6px;
}

#stores-dropdown.show {
    display: block !important; /* Force display with !important */
    animation: dropdownFadeIn 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    visibility: visible !important; /* Ensure visibility */
    opacity: 1 !important; /* Ensure opacity */
    pointer-events: auto !important; /* Ensure clickable */
    transform-origin: top right;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
        box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }
}

#stores-dropdown .store-item {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-right: 3px solid transparent;
    margin: 2px 0;
    animation: slideIn 0.3s ease forwards;
    opacity: 0;
    transform: translateX(10px);
}

/* Animation for items appearing */
@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Apply delay to each item */
#stores-dropdown .store-item:nth-child(1) { animation-delay: 0.05s; }
#stores-dropdown .store-item:nth-child(2) { animation-delay: 0.1s; }
#stores-dropdown .store-item:nth-child(3) { animation-delay: 0.15s; }
#stores-dropdown .store-item:nth-child(4) { animation-delay: 0.2s; }
#stores-dropdown .store-item:nth-child(5) { animation-delay: 0.25s; }
#stores-dropdown .store-item:nth-child(6) { animation-delay: 0.3s; }
#stores-dropdown .store-item:nth-child(7) { animation-delay: 0.35s; }
#stores-dropdown .store-item:nth-child(8) { animation-delay: 0.4s; }
#stores-dropdown .store-item:nth-child(9) { animation-delay: 0.45s; }
#stores-dropdown .store-item:nth-child(10) { animation-delay: 0.5s; }

#stores-dropdown .store-item:hover {
    background-color: var(--color-hover, rgba(63, 81, 181, 0.1));
    border-right-color: var(--color-primary, #3f51b5);
    padding-right: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

#stores-dropdown .store-item.current {
    background-color: var(--color-primary, #3f51b5);
    color: white;
    border-right-color: #fff;
    box-shadow: 0 2px 8px rgba(63, 81, 181, 0.3);
}

#stores-dropdown .store-item.message {
    font-style: italic;
    color: #888;
    justify-content: center;
    cursor: default;
}

#stores-dropdown .store-item.message:hover {
    background-color: transparent;
}



#stores-dropdown .store-item i {
    font-size: 16px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(63, 81, 181, 0.1);
    color: var(--color-primary, #3f51b5);
    transition: all 0.3s ease;
}

#stores-dropdown .store-item:hover i {
    transform: scale(1.1);
    background-color: rgba(63, 81, 181, 0.2);
}

#stores-dropdown .store-item.current i {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Check icon for current store */
#stores-dropdown .store-item.current i.fa-check {
    background-color: transparent;
    color: rgba(255, 255, 255, 0.8);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Dark mode support for stores dropdown */
[data-theme="dark"] #stores-dropdown {
    background-color: var(--color-secondary, #2c2e31);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] #stores-dropdown .store-item:hover {
    background-color: var(--color-hover, rgba(63, 81, 181, 0.2));
}

[data-theme="dark"] #stores-dropdown .store-item i {
    background-color: rgba(63, 81, 181, 0.2);
}

[data-theme="dark"] #stores-dropdown .store-item:hover i {
    background-color: rgba(63, 81, 181, 0.3);
}

[data-theme="dark"] #stores-dropdown::-webkit-scrollbar-thumb {
    background-color: var(--color-primary, #3f51b5);
}

#store-status .branch-icon {
    color: var(--color-primary, #3f51b5);
    font-size: 22px;
    margin-left: 10px;
    animation: storeIconFloat 3s ease-in-out infinite;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

#store-status:hover .branch-icon {
    transform: translateY(-2px) rotate(5deg);
}

#store-status .branch-icon.clicked {
    animation: branch-click 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
    transform-origin: center;
}

@keyframes branch-click {
    0% { transform: scale(1); }
    40% { transform: scale(1.15); }
    80% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* Dark mode support */
[data-theme="dark"] #store-status {
    background-color: var(--color-secondary, #2c2e31);
    border-left: 3px solid var(--color-primary, #3f51b5);
    color: var(--color-fg, #e4e6eb);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] #store-status .status-text {
    color: var(--color-fg, #e4e6eb);
}

[data-theme="dark"] #store-status .branch-icon {
    color: var(--color-primary, #3f51b5);
    animation: storeIconFloat 3s ease-in-out infinite;
}

[data-theme="dark"] #store-status .branch-icon.clicked {
    animation: branch-click 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

[data-theme="dark"] #store-status:hover .branch-icon {
    transform: translateY(-2px) rotate(5deg);
}

[data-theme="dark"] #store-status .fa-chevron-down {
    color: var(--color-fg, #e4e6eb);
}

/* Media Query for Small Screens */
@media (max-width: 768px) {
    #sidebar.styled-sidebar {
        width: 70%;
        border-radius: 0;
    }

    .menu-icon {
        right: 15px;
    }

    #sidebar ul li a {
        font-size: 0.8rem;
        padding: 10px 12px;
    }

    .store-name-container {
        font-size: 1em;
        padding: 10px;
    }
}

/* Dark Theme for Sidebar */
[data-theme="dark"] #sidebar.styled-sidebar {
    background: var(--color-secondary);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] #sidebar ul li a,
[data-theme="dark"] #sidebar ul li span {
    color: var(--color-fg);
    transition: background-color 0.3s, color 0.3s;
}

[data-theme="dark"] #sidebar ul li a:hover,
[data-theme="dark"] #sidebar ul li span:hover {
    background-color: var(--color-hover);
    color: var(--color-fg);
    transform: translateX(-5px);
    padding-right: 23px;
    transition: background-color 0.3s, color 0.3s, transform 0.3s;
}

[data-theme="dark"] #sidebar ul li a:hover i,
[data-theme="dark"] #sidebar ul li span:hover i {
    transform: scale(1.2);
    color: var(--color-primary);
}

[data-theme="dark"] #sidebar ul li a:hover::before,
[data-theme="dark"] #sidebar ul li span:hover::before {
    transform: scaleY(1);
}

/* Dark mode styling for submenu items */
[data-theme="dark"] #sidebar ul li ul li a {
    background-color: rgba(63, 81, 181, 0.1);
    color: var(--color-fg);
}

[data-theme="dark"] #sidebar ul li ul li a:hover {
    background-color: rgba(63, 81, 181, 0.2);
}

[data-theme="dark"] #sidebar ul li ul li a.subcategory-link {
    background-color: rgba(63, 81, 181, 0.15);
    border-right: 2px solid var(--color-primary);
}

[data-theme="dark"] #sidebar ul li ul li a.subcategory-link:hover {
    background-color: rgba(63, 81, 181, 0.25);
}

[data-theme="dark"] .submenu-toggle:hover {
    background-color: rgba(63, 81, 181, 0.2);
}

[data-theme="dark"] .submenu-toggle i {
    color: var(--color-fg);
}

[data-theme="dark"] .loading-item span {
    color: var(--color-fg);
}

[data-theme="dark"] .categories-submenu.open {
    border-right: 2px solid rgba(63, 81, 181, 0.5);
    background-color: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .categories-submenu::-webkit-scrollbar-thumb {
    background-color: rgba(63, 81, 181, 0.6);
}

[data-theme="dark"] #sidebar ul li a.active {
    background: linear-gradient(145deg, var(--color-primary), #5c6bc0);
    color: var(--color-header-text);
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    transform: translateX(-5px);
    padding-right: 23px;
    border-radius: 12px 0 0 12px;
    border-right: 4px solid #ffeb3b;
    animation: activeBorder 2s ease-in-out infinite;
}

[data-theme="dark"] #sidebar ul li a.active::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background-color: #ffeb3b;
    transform: scaleY(1);
}

[data-theme="dark"] #sidebar ul li a.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    animation: activeLink 3s ease-in-out infinite;
}

[data-theme="dark"] #sidebar ul li a.active i {
    color: var(--color-header-text);
    animation: activeIcon 2s ease-in-out infinite;
    transform-origin: center;
    margin-left: 15px;
    font-size: 1.2em;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
    position: relative;
    z-index: 2;
}

[data-theme="dark"] #sidebar ul li a.active i::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    animation: iconGlow 2s ease-in-out infinite;
}

[data-theme="dark"] .store-name-container {
    background: linear-gradient(135deg, var(--color-primary), #5c6bc0);
    color: var(--color-header-text);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .store-name {
    color: var(--color-header-text);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .store-name i {
    color: var(--color-header-text);
}

[data-theme="dark"] .menu-icon {
    background: linear-gradient(to right, var(--color-primary), #1abc9c);
    color: var(--color-header-text);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .menu-icon:hover {
    background: linear-gradient(to right, #1abc9c, var(--color-primary));
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.7);
}

[data-theme="dark"] #sidebar ul li.logout a {
    background-color: #e74c3c;
    color: var(--color-header-text);
}

[data-theme="dark"] #sidebar ul li.logout a:hover {
    background-color: #c0392b;
}

#sidebar ul li a.theme-toggle-link {
    color: var(--color-fg);
    background-color: transparent;
    padding: 12px 18px;
    text-decoration: none;
    border-radius: 12px;
    transition: none;
    display: flex;
    align-items: center;
}

#sidebar ul li a.theme-toggle-link:hover {
    background-color: var(--color-hover);
    transform: scale(1.05);
    transition: background-color 0.2s, transform 0.2s;
}

#sidebar ul li a.theme-toggle-link i {
    margin-left: 12px;
    transition: none;
}

#sidebar ul li a.theme-toggle-link[data-theme="dark"] i {
    transform: rotate(180deg); /* Rotate icon for dark mode */
}

/* Animation for Theme Toggle - Instant transitions */
:root {
    --transition-duration: 0s;
}

[data-theme="dark"],
[data-theme="light"] {
    transition: none;
}

.header-container {
    position: fixed; /* جديد: اجعل الشريط ثابت */
    top: 0;          /* جديد */
    left: 0;         /* جديد */
    right: 0;        /* جديد */
    z-index: 1200;   /* جديد */
    display: flex;
    flex-direction: column; /* Updated to align header title and icons */
    align-items: center;    /* Center align header title */
    padding: 10px 20px;
    background-color: var(--color-secondary);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid var(--color-primary);
    margin-bottom: 80px; /* تمت إضافة هذه المسافة لضمان ظهور المحتوى */
}

.menu-icon-container,
.notification-icon-container,
.theme-toggle-container,
.account-icon,
.back-arrow-container {
    color: var(--color-primary); /* لون الأيقونات يتبع لون العنوان */
    background-color: var(--color-bg);
    cursor: pointer;
    font-size: 24px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.menu-icon-container:hover,
.notification-icon-container:hover,
.theme-toggle-container:hover,
.account-icon:hover,
.back-arrow-container:hover {
    background-color: var(--color-hover); /* لون الهوفر */
    transform: scale(1.1);
}

.notification-icon-container {
    position: relative;
}

.notification-icon-container .notification-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: red;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: notificationPulse 2s infinite;
}

/* Enhanced notification bell animation */
.notification-icon-container i {
    transition: all 0.3s ease;
}

.notification-icon-container:hover i {
    animation: bellRing 0.6s ease-in-out;
    transform-origin: top center;
}

.notification-icon-container.new-notification i {
    animation: bellAlert 1s ease-in-out;
    color: #ff6b6b;
}

@keyframes notificationPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

@keyframes bellRing {
    0%, 100% { transform: rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: rotate(-10deg); }
    20%, 40%, 60%, 80% { transform: rotate(10deg); }
}

@keyframes bellAlert {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        filter: drop-shadow(0 0 0 transparent);
    }
    25% {
        transform: scale(1.1) rotate(-5deg);
        filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.6));
    }
    50% {
        transform: scale(1.2) rotate(0deg);
        filter: drop-shadow(0 0 12px rgba(255, 107, 107, 0.8));
    }
    75% {
        transform: scale(1.1) rotate(5deg);
        filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.6));
    }
}

[data-theme="dark"] .header-container {
    background-color: var(--color-bg);
    border-bottom: 1px solid #58a6ff; /* تم التعديل ليصبح باللون الأزرق في الثيم الدارك */
}

[data-theme="dark"] .menu-icon-container,
[data-theme="dark"] .notification-icon-container,
[data-theme="dark"] .theme-toggle-container,
[data-theme="dark"] .account-icon,
[data-theme="dark"] .back-arrow-container {
    color: var(--color-primary); /* لون الأيقونات في الوضع الداكن */
    background-color: var(--color-secondary);
}

[data-theme="dark"] .notification-icon-container .notification-count {
    background-color: #e74c3c;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.6), 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: notificationPulseDark 2s infinite;
}

/* Enhanced dark mode notification animations */
[data-theme="dark"] .notification-icon-container i {
    color: #58a6ff;
    text-shadow: 0 0 8px rgba(88, 166, 255, 0.3);
    transition: all 0.3s ease;
}

[data-theme="dark"] .notification-icon-container:hover i {
    color: #79c0ff;
    text-shadow: 0 0 12px rgba(121, 192, 255, 0.6);
    animation: bellRingDark 0.6s ease-in-out;
    transform-origin: top center;
}

[data-theme="dark"] .notification-icon-container.new-notification i {
    animation: bellAlertDark 1s ease-in-out;
    color: #ff7b7b;
    text-shadow: 0 0 15px rgba(255, 123, 123, 0.8);
}

@keyframes notificationPulseDark {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.6), 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.3);
        opacity: 0.9;
        box-shadow: 0 0 20px rgba(231, 76, 60, 0.9), 0 4px 8px rgba(0, 0, 0, 0.4);
    }
}

@keyframes bellRingDark {
    0%, 100% {
        transform: rotate(0deg);
        filter: drop-shadow(0 0 8px rgba(88, 166, 255, 0.3));
    }
    10%, 30%, 50%, 70%, 90% {
        transform: rotate(-12deg);
        filter: drop-shadow(0 0 12px rgba(88, 166, 255, 0.6));
    }
    20%, 40%, 60%, 80% {
        transform: rotate(12deg);
        filter: drop-shadow(0 0 12px rgba(88, 166, 255, 0.6));
    }
}

@keyframes bellAlertDark {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        filter: drop-shadow(0 0 8px rgba(255, 123, 123, 0.4));
        text-shadow: 0 0 15px rgba(255, 123, 123, 0.8);
    }
    25% {
        transform: scale(1.15) rotate(-8deg);
        filter: drop-shadow(0 0 15px rgba(255, 123, 123, 0.8));
        text-shadow: 0 0 25px rgba(255, 123, 123, 1);
    }
    50% {
        transform: scale(1.3) rotate(0deg);
        filter: drop-shadow(0 0 20px rgba(255, 123, 123, 1));
        text-shadow: 0 0 30px rgba(255, 123, 123, 1);
    }
    75% {
        transform: scale(1.15) rotate(8deg);
        filter: drop-shadow(0 0 15px rgba(255, 123, 123, 0.8));
        text-shadow: 0 0 25px rgba(255, 123, 123, 1);
    }
}

.theme-toggle-container i,
.account-icon i {
    color: var(--color-primary);
}

/* في الثيم الداكن */
[data-theme="dark"] .theme-toggle-container i,
[data-theme="dark"] .account-icon i {
    color: var(--color-primary); /* لون الأيقونات في الوضع الداكن */
    background-color: var(--color-secondary);    }

.theme-toggle-container i {
    transition: transform 0.15s ease-out;
}

.theme-toggle-container.animate i {
    animation: rotateThemeIcon 0.2s ease-out;
}

@keyframes rotateThemeIcon {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

/* New styles for footer and heart icon */
.heart-icon {
    display: inline-block;
    color: var(--color-primary);
    font-size: 1rem;
    animation: heartbeat 1.5s infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1) rotate(-10deg); }
    25% { transform: scale(1.2) rotate(-10deg); }
    50% { transform: scale(1) rotate(-10deg); }
    75% { transform: scale(1.2) rotate(-10deg); }
}

.footer-text {
    font-family: 'Poppins', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
}

/* أنماط الآلة الحاسبة */
#calculator {
    position: fixed;
    top: 50px;
    left: 50px; 
    width: 280px;
    background: var(--calc-bg);
    border-radius: 15px;
    overflow: hidden;
    z-index: 10000;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تأثيرات انتقالية للآلة الحاسبة */
#calculator.show {
    animation: slideInFromRight 0.3s ease-out;
}

#calculator.hide {
    animation: slideOutToRight 0.3s ease-in;
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* تحسين للوضع المظلم */
[data-theme="dark"] #calculator {
    background: var(--calc-bg);
    border-color: var(--calc-border);
    box-shadow: 0 10px 30px var(--calc-shadow);
}

/* رأس الآلة */
#calcHeader {
    cursor: move;
    background: linear-gradient(135deg, var(--calc-header-bg), #303f9f);
    color: #ffffff;
    padding: 12px;
    font-size: 16px;
    text-align: center;
    font-weight: 600;
    user-select: none;
    font-family: 'Cairo', sans-serif;
}

/* زر الإغلاق */
#calcCloseBtn:hover {
    color: #ffffff !important;
    transform: scale(1.2);
    text-shadow: 0 0 5px rgba(255,255,255,0.5);
}

/* شاشة الإدخال */
#calcDisplay {
    width: 100%;
    padding: 12px;
    border: none;
    text-align: right;
    font-size: 18px;
    outline: none;
    background-color: var(--calc-bg);
    color: var(--calc-text);
    font-family: 'Cairo', 'Courier New', monospace;
    box-sizing: border-box;
}

/* شاشة النتائج */
#calcResult {
    width: 100%;
    padding: 10px 12px;
    text-align: right;
    font-weight: bold;
    background-color: var(--calc-button-hover);
    color: var(--calc-header-bg);
    font-size: 16px;
    font-family: 'Cairo', 'Courier New', monospace;
    border-bottom: 1px solid var(--calc-header-bg);
    box-sizing: border-box;
}

/* تحسين للوضع المظلم */
[data-theme="dark"] #calcResult {
    background-color: var(--calc-button-hover);
    color: var(--calc-header-bg);
}

/* أزرار الآلة */
#calcButtons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    padding: 12px;
    background-color: var(--calc-bg);
}

#calcButtons button {
    padding: 15px 8px;
    font-size: 16px;
    border: 1px solid var(--calc-border);
    border-radius: 8px;
    background: var(--calc-bg);
    color: var(--calc-text);
    transition: all 0.2s ease;
    cursor: pointer;
    font-weight: 600;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

#calcButtons button:hover {
    background: var(--calc-header-bg);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--calc-button-hover);
    border-color: var(--calc-header-bg);
}

#calcButtons button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px var(--calc-button-hover);
}

/* تحسين للوضع المظلم */
[data-theme="dark"] #calcButtons button:hover {
    box-shadow: 0 4px 8px var(--calc-button-hover);
}

[data-theme="dark"] #calcButtons button:active {
    box-shadow: 0 2px 4px var(--calc-button-hover);
}

/* أنماط خاصة لأزرار معينة */
#calcButtons button[onclick*="calcClear"] {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    font-weight: 700;
}

#calcButtons button[onclick*="calcClear"]:hover {
    background: #c82333;
    border-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

#calcButtons button[onclick*="calcUndo"] {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;
    font-weight: 600;
}

#calcButtons button[onclick*="calcUndo"]:hover {
    background: #e0a800;
    border-color: #e0a800;
    color: #212529;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

#calcButtons button[onclick*="calcCompute"] {
    background: #28a745;
    color: white;
    border-color: #28a745;
    font-weight: 700;
    font-size: 18px;
}

#calcButtons button[onclick*="calcCompute"]:hover {
    background: #218838;
    border-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* أيقونة الآلة الحاسبة في رأس الصفحة */
.calculator-icon-container {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--calc-header-bg), #303f9f);
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px var(--calc-button-hover);
    position: relative;
    overflow: hidden;
}

.calculator-icon-container:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(63, 81, 181, 0.4);
}

/* تأثير hover عند الحالة active */
.calculator-icon-container.active:hover {
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 0 25px rgba(255, 107, 53, 0.8), 0 0 50px rgba(255, 107, 53, 0.6);
}

.calculator-icon-container:active {
    transform: scale(0.95);
}

/* حالة active للأيقونة عند فتح الآلة الحاسبة */
.calculator-icon-container.active {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.6), 0 0 40px rgba(255, 107, 53, 0.4);
    animation: calculatorActiveGlow 2s ease-in-out infinite;
}

@keyframes calculatorActiveGlow {
    0% { 
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.6), 0 0 40px rgba(255, 107, 53, 0.4);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 30px rgba(255, 107, 53, 0.8), 0 0 60px rgba(255, 107, 53, 0.6);
        transform: scale(1.05);
    }
    100% { 
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.6), 0 0 40px rgba(255, 107, 53, 0.4);
        transform: scale(1);
    }
}

.calculator-icon-container i {
    font-size: 18px;
    z-index: 1;
}

/* تأثير الأيقونة عند الحالة active */
.calculator-icon-container.active i {
    animation: calculatorIconPulse 1.5s ease-in-out infinite;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

@keyframes calculatorIconPulse {
    0% { 
        transform: scale(1);
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    }
    50% { 
        transform: scale(1.2);
        text-shadow: 0 0 15px rgba(255, 255, 255, 1);
    }
    100% { 
        transform: scale(1);
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    }
}

/* تأثير الضغط */
.calculator-icon-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.5s ease;
}

.calculator-icon-container:active::before {
    width: 100%;
    height: 100%;
}

/* تحسين للوضع المظلم */
[data-theme="dark"] .calculator-icon-container {
    background: linear-gradient(135deg, var(--calc-header-bg), #4dabf7);
    box-shadow: 0 4px 12px var(--calc-button-hover);
}

[data-theme="dark"] .calculator-icon-container:hover {
    box-shadow: 0 6px 20px var(--calc-button-hover);
}

/* حالة active للأيقونة في الوضع المظلم */
[data-theme="dark"] .calculator-icon-container.active {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.8), 0 0 40px rgba(255, 107, 53, 0.6);
}

[data-theme="dark"] #calcHeader {
    background: linear-gradient(135deg, var(--calc-header-bg), #4dabf7);
}

/* تحسينات للهاتف المحمول */
@media (max-width: 768px) {
    #calculator {
        width: 90%;
        max-width: 300px;
        left: 50%;
        top: 20px;
        transform: translateX(-50%);
    }
    
    #calcButtons button {
        padding: 12px 6px;
        font-size: 14px;
        border-radius: 6px;
    }
    
    #calcDisplay {
        font-size: 16px;
        padding: 10px;
    }
    
    #calcResult {
        font-size: 14px;
        padding: 8px 10px;
    }
    
    #calcHeader {
        padding: 10px;
        font-size: 14px;
    }
    
    .calculator-icon-container {
        width: 35px;
        height: 35px;
    }
    
    .calculator-icon-container i {
        font-size: 16px;
    }
}

/* Tooltip للآلة الحاسبة */
.calculator-icon-container::after {
    content: 'آلة حاسبة (مفاتيح: ESC للإغلاق)';
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
    font-family: 'Cairo', sans-serif;
}

.calculator-icon-container:hover::after {
    opacity: 1;
}

[data-theme="dark"] .calculator-icon-container::after {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}