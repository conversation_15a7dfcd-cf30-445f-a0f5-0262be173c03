/* Customer Animations CSS */

/* Animation for customer switching */
@keyframes customerSwitch {
    0% {
        opacity: 0.7;
        transform: scale(0.95);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.customer-switching {
    animation: customerSwitch 0.4s ease-out;
}

/* Animation for items table update */
@keyframes tableUpdate {
    0% {
        opacity: 0.8;
        transform: translateY(-5px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.table-updating {
    animation: tableUpdate 0.3s ease-out;
}

/* Animation for new item addition */
@keyframes itemAdded {
    0% {
        background-color: rgba(40, 167, 69, 0.3);
        transform: scale(1.02);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

.item-added {
    animation: itemAdded 0.6s ease-out;
}

/* Animation for item removal */
@keyframes itemRemoved {
    0% {
        opacity: 1;
        transform: scale(1);
        background-color: rgba(220, 53, 69, 0.2);
    }
    50% {
        opacity: 0.5;
        transform: scale(0.98);
        background-color: rgba(220, 53, 69, 0.3);
    }
    100% {
        opacity: 0;
        transform: scale(0.95);
        height: 0;
        padding: 0;
        margin: 0;
    }
}

.item-removing {
    animation: itemRemoved 0.4s ease-out forwards;
}

/* Animation for quantity change */
@keyframes quantityChange {
    0% {
        background-color: rgba(255, 193, 7, 0.3);
        transform: scale(1.05);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

.quantity-changed {
    animation: quantityChange 0.5s ease-out;
}

/* Animation for total update */
@keyframes totalUpdate {
    0% {
        color: #28a745;
        transform: scale(1.1);
        text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    100% {
        color: inherit;
        transform: scale(1);
        text-shadow: none;
    }
}

.total-updated {
    animation: totalUpdate 0.6s ease-out;
}

/* Animation for customer tab activation */
@keyframes tabActivate {
    0% {
        transform: scale(0.95);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    }
}

.tab-activating {
    animation: tabActivate 0.5s ease-out;
}

/* Animation for empty state */
@keyframes emptyState {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.empty-state {
    animation: emptyState 0.4s ease-out;
}

/* Animation for loading state */
@keyframes loadingPulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading-state {
    animation: loadingPulse 1.5s ease-in-out infinite;
}

/* Animation for success notification */
@keyframes successNotification {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    10% {
        transform: translateX(0);
        opacity: 1;
    }
    90% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

.success-notification {
    animation: successNotification 3s ease-out;
}

/* Animation for error notification */
@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

.error-shake {
    animation: errorShake 0.6s ease-out;
}

/* Smooth transitions for all interactive elements */
.customer-tab,
.quantity-input,
.remove-btn,
.add-btn,
.item-total {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects with animations */
.customer-tab:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-out;
}

.remove-btn:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease-out;
}

.add-btn:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease-out;
}

/* Focus animations */
.quantity-input:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    transition: all 0.2s ease-out;
}

/* Animation for responsive mode toggle */
@keyframes responsiveToggle {
    0% {
        opacity: 0.5;
        transform: scale(0.9);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.responsive-toggling {
    animation: responsiveToggle 0.5s ease-out;
}

/* Animation for data synchronization */
@keyframes syncAnimation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.syncing {
    animation: syncAnimation 1s linear infinite;
}

/* Stagger animation for multiple items */
.stagger-animation {
    animation-delay: calc(var(--stagger-index, 0) * 0.1s);
}

/* Dark mode specific animations */
.dark-mode .item-added {
    animation: itemAddedDark 0.6s ease-out;
}

@keyframes itemAddedDark {
    0% {
        background-color: rgba(72, 187, 120, 0.3);
        transform: scale(1.02);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

.dark-mode .item-removing {
    animation: itemRemovedDark 0.4s ease-out forwards;
}

@keyframes itemRemovedDark {
    0% {
        opacity: 1;
        transform: scale(1);
        background-color: rgba(245, 101, 101, 0.2);
    }
    50% {
        opacity: 0.5;
        transform: scale(0.98);
        background-color: rgba(245, 101, 101, 0.3);
    }
    100% {
        opacity: 0;
        transform: scale(0.95);
        height: 0;
        padding: 0;
        margin: 0;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Performance optimizations */
.customer-tab,
.quantity-input,
.remove-btn,
.add-btn {
    will-change: transform;
}

.item-added,
.item-removing,
.quantity-changed,
.total-updated {
    will-change: transform, opacity, background-color;
}

/* Animation utilities */
.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.4s ease-out;
}

.animate-slide-down {
    animation: slideDown 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}