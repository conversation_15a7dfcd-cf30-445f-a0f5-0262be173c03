const express = require('express');
const admin = require('firebase-admin');
const bodyParser = require('body-parser');
const serviceAccount = require('./test-c60d6-firebase-adminsdk-fbsvc-78904eea16.json');

const app = express();
app.use(bodyParser.json());

// Initialize Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
});

// Function to send notification
async function sendNotification(token, title, body) {
    const message = {
        notification: {
            title: title,
            body: body
        },
        token: token
    };

    try {
        const response = await admin.messaging().send(message);
        console.log('Successfully sent message:', response);
        return response;
    } catch (error) {
        console.log('Error sending message:', error);
        throw error;
    }
}

// Endpoint to receive token and send notification
app.post('/sendNotification', async(req, res) => {
    const token = req.body.token;
    console.log('Received token:', token); // Log the received token
    try {
        const response = await sendNotification(token, 'اختبار الإشعار', 'هذا إشعار اختبار. اضغط هنا للتفاعل.');
        res.send('Notification sent');
    } catch (error) {
        res.status(500).send('Failed to send notification');
    }
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});