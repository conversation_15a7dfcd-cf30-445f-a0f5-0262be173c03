<?php
/**
 * Mark Notification as Read
 *
 * This file marks a specific notification as read for a user
 */

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(0);

// Include database connection
require_once 'db_connection.php';

// Set content type to JSON
header('Content-Type: application/json');

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

require_once 'encryption_functions.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid JSON input'
        ]);
        exit;
    }

    $notification_id = isset($input['notification_id']) ? (int)$input['notification_id'] : 0;

    // Validate input
    if (!$notification_id) {
        echo json_encode([
            'success' => false,
            'message' => 'Missing notification ID'
        ]);
        exit;
    }

    // Get current user ID from encrypted account_id in session
    $user_id = null;
    if (isset($_SESSION['account_id']) && !empty($_SESSION['account_id'])) {
        $key = getenv('ENCRYPTION_KEY');
        $user_id = decrypt($_SESSION['account_id'], $key);
    }

    if (!$user_id) {
        echo json_encode([
            'success' => false,
            'message' => 'User not logged in'
        ]);
        exit;
    }

    // Check if notification exists and get its status
    $check_query = "SELECT id, status FROM notifications WHERE id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $notification_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Notification not found'
        ]);
        exit;
    }

    $notification = $check_result->fetch_assoc();
    if ($notification['status'] === 'read') {
        echo json_encode([
            'success' => true,
            'message' => 'Notification already marked as read'
        ]);
        exit;
    }

    $check_stmt->close();
    $check_stmt = null;

    // Mark notification as read by updating status
    $update_query = "UPDATE notifications SET status = 'read' WHERE id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("i", $notification_id);

    if ($update_stmt->execute()) {
        // Also insert record in notification_reads for tracking
        $insert_query = "INSERT INTO notification_reads (notification_id, account_id, viewed_at) VALUES (?, ?, NOW())";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("ii", $notification_id, $user_id);
        $insert_stmt->execute();
        $insert_stmt->close();
        $insert_stmt = null; // Set to null to avoid double closing

        echo json_encode([
            'success' => true,
            'message' => 'Notification marked as read successfully'
        ]);
        exit;
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to mark notification as read'
        ]);
        exit;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
    exit;
}

?>
