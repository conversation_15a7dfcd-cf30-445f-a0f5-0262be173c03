<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

// فحص صلاحية الوصول
checkPagePermission('items', 'access');

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['item_id'])) {
    $encrypted_item_id = $_GET['item_id'];
    $item_id = decrypt($encrypted_item_id, $key);
    
    if (!$item_id) {
        echo json_encode(['success' => false, 'message' => 'معرف الصنف غير صحيح']);
        exit();
    }
    
    // جلب بيانات الصنف الحالي
    $current_item_sql = "SELECT barcode, store_id FROM items WHERE item_id = ?";
    $stmt = $conn->prepare($current_item_sql);
    $stmt->bind_param("i", $item_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $current_item = $result->fetch_assoc();
    $stmt->close();
    
    if (!$current_item || empty($current_item['barcode'])) {
        echo json_encode(['success' => false, 'message' => 'لا يوجد باركود للصنف الحالي']);
        exit();
    }
    
    // البحث عن الأصناف المشابهة في الفروع الأخرى
    $similar_items_sql = "SELECT i.item_id, i.name, i.cost, i.price, i.quantity, i.type, 
                                 i.pieces_per_box, i.status, i.barcode, s.name as store_name, s.store_id
                          FROM items i 
                          JOIN stores s ON i.store_id = s.store_id 
                          WHERE i.barcode = ? AND i.store_id != ? AND i.item_id != ?
                          ORDER BY s.name";
    
    $stmt = $conn->prepare($similar_items_sql);
    $stmt->bind_param("sii", $current_item['barcode'], $current_item['store_id'], $item_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $similar_items = [];
    while ($row = $result->fetch_assoc()) {
        $row['encrypted_item_id'] = encrypt($row['item_id'], $key);
        $similar_items[] = $row;
    }
    $stmt->close();
    
    echo json_encode([
        'success' => true, 
        'similar_items' => $similar_items,
        'current_barcode' => $current_item['barcode']
    ]);
    
} else {
    echo json_encode(['success' => false, 'message' => 'طلب غير صحيح']);
}

$conn->close();
?>