<?php
include 'db_connection.php';
include 'encryption_functions.php';

try {
    $key = getenv('ENCRYPTION_KEY');

    // Handle store_id with error handling
    $encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
    if (empty($encrypted_store_id)) {
        $encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';
    }

    if (!empty($encrypted_store_id)) {
        $store_id = decrypt($encrypted_store_id, $key);
    } else {
        $store_id = 1; // Default fallback
    }

    // Handle account_id with error handling
    $encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';
    if (!empty($encrypted_account_id)) {
        $account_id = decrypt($encrypted_account_id, $key);
    } else {
        $account_id = 1; // Default fallback
    }

} catch (Exception $e) {
    // Handle decryption errors gracefully
    error_log("Decryption error in notifications_accounts.php: " . $e->getMessage());
    $store_id = 1;
    $account_id = 1;
}

// Fetch unread notifications count for the specific user
$unreadQuery = "SELECT COUNT(*) as unread_count
                FROM notifications n
                LEFT JOIN notification_reads nr ON n.id = nr.notification_id AND nr.account_id = ?
                WHERE n.store_id = ? AND nr.id IS NULL AND n.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
$unreadStmt = $conn->prepare($unreadQuery);
$unreadStmt->bind_param("ii", $account_id, $store_id);
$unreadStmt->execute();
$unreadResult = $unreadStmt->get_result();
$unreadCount = $unreadResult->fetch_assoc()['unread_count'];
?>

<body>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .notification-icon {
            font-size: 30px;
            color: white;
            cursor: pointer;
            position: absolute;
            top: 40px;
            left: 40px;
        }

        .notification-icon .notification-count {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: red;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: bold;
        }

        .notification-container {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
        }

        .notification-item {
            background-color: #f1f1f1;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3B82F6;
            transition: all 0.3s ease;
            position: relative;
        }

        .notification-item:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .notification-item.high-priority {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #fff5f5 0%, #f1f1f1 100%);
        }

        .notification-item.read {
            opacity: 0.7;
            background-color: #f9f9f9;
        }

        .notification-header {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .notification-type {
            font-size: 11px;
            color: #888;
            font-weight: bold;
            text-transform: uppercase;
        }

        .priority-badge {
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .notification-message {
            font-size: 16px;
            margin: 0;
        }

        .notification-time {
            font-size: 12px;
            color: #888;
        }

        /* Firebase Notification Styles */
        .firebase-notification {
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 999999 !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
            min-width: 320px !important;
            max-width: 400px !important;
            animation: slideInRight 0.5s ease-out !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        .firebase-notification-content {
            display: flex;
            align-items: flex-start;
            padding: 16px;
            gap: 12px;
        }

        .firebase-notification-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .firebase-notification-icon i {
            font-size: 18px;
            color: white;
        }

        .firebase-notification-text {
            flex: 1;
            min-width: 0;
        }

        .firebase-notification-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .firebase-notification-body {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .firebase-notification-close {
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: background 0.3s ease;
            flex-shrink: 0;
        }

        .firebase-notification-close:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .firebase-notification-close i {
            font-size: 12px;
            color: white;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Ensure Firebase notifications appear above everything */
        .firebase-notification {
            z-index: 999999 !important;
        }

        /* Override any conflicting styles */
        body .firebase-notification {
            position: fixed !important;
            z-index: 999999 !important;
            display: block !important;
        }

        /* Additional CSS to ensure notifications appear */
        .firebase-notification {
            transform: translateZ(0) !important;
            will-change: transform !important;
        }

        /* Toast notification styles for SweetAlert2 alternative */
        .firebase-notification-toast {
            z-index: 999999 !important;
        }

        .firebase-notification-toast .swal2-container {
            z-index: 999999 !important;
        }

        /* Ensure no other elements interfere */
        .firebase-notification * {
            box-sizing: border-box !important;
        }

        /* Dark mode support for Firebase notifications */
        [data-theme="dark"] .firebase-notification {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-color: rgba(88, 166, 255, 0.3);
        }

        /* Responsive design for mobile */
        @media (max-width: 768px) {
            .firebase-notification {
                right: 10px;
                left: 10px;
                min-width: auto;
                max-width: none;
            }

            .firebase-notification-content {
                padding: 12px;
            }

            .firebase-notification-title {
                font-size: 14px;
            }

            .firebase-notification-body {
                font-size: 13px;
            }
        }
    </style>
    <i class="fas fa-bell notification-icon" onclick="showNotifications()">
        <span class="notification-count"><?= $unreadCount ?></span>
    </i>

    <audio id="notificationSound" preload="auto">
        <source src="/elwaled_market/sounds/notification.mp3" type="audio/mpeg">
        <source src="/sounds/notification.mp3" type="audio/mpeg">
        <source src="/sound.mp3" type="audio/mpeg">
    </audio>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Firebase Configuration - Updated to match other files
        const firebaseConfig = {
            apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
            authDomain: "macm-84114.firebaseapp.com",
            projectId: "macm-84114",
            storageBucket: "macm-84114.firebasestorage.app",
            messagingSenderId: "860043675105",
            appId: "1:860043675105:web:72586005d5bd035ff8bea0"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // VAPID Key for web push notifications - Updated to match other files
        const vapidKey = "BMVJO7rt5hONuPb0UzJm2B9T52CuXtcjsWDmHKXf8ass2zyctrBrjXWncazpezhWSdBbcrr8pPcegRixWaTiSBI";

        let previousUnreadCount = <?= $unreadCount ?>;
        let fcmToken = null;
        let audioEnabled = false;

        // Enable audio after first user interaction
        function enableAudio() {
            if (!audioEnabled) {
                const notificationSound = document.getElementById('notificationSound');
                if (notificationSound) {
                    // Try to play and immediately pause to enable audio context
                    notificationSound.play().then(() => {
                        notificationSound.pause();
                        notificationSound.currentTime = 0;
                        audioEnabled = true;
                        console.log('Audio enabled after user interaction');
                    }).catch(e => {
                        console.log('Audio enable failed:', e.message);
                    });
                }
            }
        }

        // Add event listeners for user interaction
        document.addEventListener('click', enableAudio, { once: true });
        document.addEventListener('touchstart', enableAudio, { once: true });
        document.addEventListener('keydown', enableAudio, { once: true });

        // Initialize Firebase Messaging
        async function initializeFirebaseMessaging() {
            try {
                console.log('🔔 Starting Firebase Messaging initialization...');

                // Check if messaging is available
                if (!messaging) {
                    console.error('❌ Firebase messaging not available');
                    return;
                }

                console.log('✅ Firebase messaging object available');

                // Request notification permission
                console.log('🔐 Requesting notification permission...');
                const permission = await Notification.requestPermission();
                console.log('🔐 Permission result:', permission);

                if (permission === 'granted') {
                    console.log('✅ Notification permission granted.');

                    // Get FCM token
                    console.log('🎫 Getting FCM token...');
                    fcmToken = await messaging.getToken({ vapidKey: vapidKey });
                    console.log('🎫 FCM Token received:', fcmToken);

                    // Save token to server
                    console.log('💾 Saving FCM token to server...');
                    await saveFCMToken(fcmToken);

                    // Listen for foreground messages
                    console.log('👂 Setting up foreground message listener...');
                    messaging.onMessage((payload) => {
                        console.log('📨 Message received in foreground:', payload);
                        handleForegroundMessage(payload);
                    });

                    console.log('✅ Firebase Messaging initialization completed successfully!');

                } else {
                    console.log('❌ Notification permission denied:', permission);
                }
            } catch (error) {
                console.error('❌ Error initializing Firebase messaging:', error);
                console.error('❌ Error details:', error.message);
                console.error('❌ Error stack:', error.stack);
            }
        }

        // Save FCM token to server
        async function saveFCMToken(token) {
            try {
                const response = await fetch('save_fcm_token.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: token,
                        account_id: <?= $account_id ?>,
                        store_id: <?= $store_id ?>
                    })
                });

                const result = await response.json();
                if (result.success) {
                    console.log('FCM token saved successfully');
                } else {
                    console.error('Failed to save FCM token:', result.error);
                }
            } catch (error) {
                console.error('Error saving FCM token:', error);
            }
        }

        // Handle foreground messages
        function handleForegroundMessage(payload) {
            console.log('Handling foreground message:', payload);
            const { notification, data } = payload;

            // Play notification sound
            const notificationSound = document.getElementById('notificationSound');
            if (notificationSound && audioEnabled) {
                notificationSound.play().catch(e => console.log('Sound play failed:', e));
            } else if (!audioEnabled) {
                console.log('Audio not enabled yet - waiting for user interaction');
            }

            // Show notification popup
            showFirebaseNotification(notification, data);

            // Update unread count and refresh notifications if modal is open
            // (No need to save to database as it's already saved when sent)
            updateUnreadCount();
            refreshNotificationsIfOpen();
        }

        // Test function to manually show a notification (for debugging)
        window.testFirebaseNotification = function() {
            console.log('🧪 Testing Firebase notification...');
            const testNotification = {
                title: 'إشعار تجريبي',
                body: 'هذا إشعار تجريبي للتأكد من عمل النظام'
            };
            const element = showFirebaseNotification(testNotification, {});
            console.log('🧪 Test notification element:', element);
            return element;
        }

        // Enhanced test function with different types
        window.testNotificationTypes = function() {
            console.log('🧪 Testing different notification types...');

            // Test basic notification
            setTimeout(() => {
                showFirebaseNotification({
                    title: 'إشعار أساسي',
                    body: 'هذا إشعار أساسي'
                }, {});
            }, 0);

            // Test success notification
            setTimeout(() => {
                showFirebaseNotification({
                    title: 'تم بنجاح!',
                    body: 'العملية تمت بنجاح'
                }, { type: 'success' });
            }, 2000);

            // Test warning notification
            setTimeout(() => {
                showFirebaseNotification({
                    title: 'تحذير!',
                    body: 'يرجى الانتباه'
                }, { type: 'warning' });
            }, 4000);
        }

        // Show Firebase notification popup
        function showFirebaseNotification(notification, data) {
            console.log('🔔 Showing Firebase notification:', notification);
            console.log('📍 Current page body:', document.body);

            // Remove any existing notifications first
            const existingNotifications = document.querySelectorAll('.firebase-notification');
            console.log('🗑️ Removing existing notifications:', existingNotifications.length);
            existingNotifications.forEach(notif => notif.remove());

            // Create notification element
            const notificationElement = document.createElement('div');
            notificationElement.className = 'firebase-notification';

            // Set styles directly on the element
            Object.assign(notificationElement.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: '999999',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                borderRadius: '12px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
                minWidth: '320px',
                maxWidth: '400px',
                animation: 'slideInRight 0.5s ease-out',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                display: 'block',
                visibility: 'visible',
                opacity: '1',
                pointerEvents: 'auto'
            });

            // Create content with proper structure
            const contentDiv = document.createElement('div');
            contentDiv.style.cssText = 'display: flex; align-items: flex-start; padding: 16px; gap: 12px;';

            const iconDiv = document.createElement('div');
            iconDiv.style.cssText = 'background: rgba(255, 255, 255, 0.2); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;';
            iconDiv.innerHTML = '<i class="fas fa-bell" style="font-size: 18px; color: white;"></i>';

            const textDiv = document.createElement('div');
            textDiv.style.cssText = 'flex: 1; min-width: 0;';

            const titleDiv = document.createElement('div');
            titleDiv.style.cssText = 'font-weight: bold; font-size: 16px; margin-bottom: 4px; line-height: 1.3;';
            titleDiv.textContent = notification.title || 'إشعار جديد';

            const bodyDiv = document.createElement('div');
            bodyDiv.style.cssText = 'font-size: 14px; opacity: 0.9; line-height: 1.4; word-wrap: break-word;';
            bodyDiv.textContent = notification.body || '';

            const closeDiv = document.createElement('div');
            closeDiv.style.cssText = 'cursor: pointer; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; border-radius: 50%; background: rgba(255, 255, 255, 0.2); transition: background 0.3s ease; flex-shrink: 0;';
            closeDiv.innerHTML = '<i class="fas fa-times" style="font-size: 12px; color: white;"></i>';
            closeDiv.onclick = () => {
                console.log('🗑️ Closing notification manually');
                notificationElement.remove();
            };

            // Assemble the notification
            textDiv.appendChild(titleDiv);
            textDiv.appendChild(bodyDiv);
            contentDiv.appendChild(iconDiv);
            contentDiv.appendChild(textDiv);
            contentDiv.appendChild(closeDiv);
            notificationElement.appendChild(contentDiv);

            // Add to page
            document.body.appendChild(notificationElement);
            console.log('✅ Notification element added to body:', notificationElement);
            console.log('📏 Element dimensions:', {
                width: notificationElement.offsetWidth,
                height: notificationElement.offsetHeight,
                top: notificationElement.offsetTop,
                left: notificationElement.offsetLeft
            });

            // Force a reflow to ensure the element is rendered
            notificationElement.offsetHeight;

            // Check if element is actually visible
            const computedStyle = window.getComputedStyle(notificationElement);
            console.log('👁️ Element visibility:', {
                display: computedStyle.display,
                visibility: computedStyle.visibility,
                opacity: computedStyle.opacity,
                zIndex: computedStyle.zIndex,
                position: computedStyle.position
            });

            // Auto remove after 8 seconds (increased time for testing)
            setTimeout(() => {
                if (notificationElement.parentNode) {
                    console.log('⏰ Auto-removing notification after 8 seconds');
                    notificationElement.remove();
                }
            }, 8000);

            // Show SweetAlert for important notifications
            if (data && data.important === 'true') {
                Swal.fire({
                    title: notification.title || 'إشعار مهم',
                    text: notification.body || '',
                    icon: 'info',
                    confirmButtonText: 'موافق',
                    confirmButtonColor: '#3B82F6'
                });
            }

            // Verify the element is actually visible after a short delay
            setTimeout(() => {
                const isVisible = notificationElement.offsetParent !== null;
                const rect = notificationElement.getBoundingClientRect();
                console.log('🔍 Notification visibility check:', {
                    isVisible: isVisible,
                    inViewport: rect.top >= 0 && rect.left >= 0 && rect.bottom <= window.innerHeight && rect.right <= window.innerWidth,
                    rect: rect
                });

                // If not visible, try alternative approach
                if (!isVisible || rect.width === 0 || rect.height === 0) {
                    console.log('⚠️ Notification not visible, trying alternative approach...');
                    showAlternativeNotification(notification);
                }
            }, 100);

            // Return the element for debugging
            return notificationElement;
        }

        // Alternative notification method using SweetAlert2 toast
        function showAlternativeNotification(notification) {
            console.log('🔄 Showing alternative notification using SweetAlert2...');

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: notification.title || 'إشعار جديد',
                    text: notification.body || '',
                    icon: 'info',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true,
                    customClass: {
                        container: 'firebase-notification-toast'
                    },
                    didOpen: () => {
                        console.log('✅ Alternative notification shown successfully');
                    }
                });
            } else {
                console.log('❌ SweetAlert2 not available for alternative notification');
                // Fallback to browser notification
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification(notification.title || 'إشعار جديد', {
                        body: notification.body || '',
                        icon: '/uploads/img/logo2.png'
                    });
                }
            }
        }

        // Note: Notification saving is handled server-side when sent
        // No need to save again when received in foreground

        // Register Service Worker for background notifications
        async function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    // Try multiple paths for service worker
                    let registration;
                    try {
                        registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                        console.log('✅ Service Worker registered at root:', registration);
                    } catch (e) {
                        console.log('❌ Failed to register SW at root, trying elwaled_market path...');
                        registration = await navigator.serviceWorker.register('/elwaled_market/firebase-messaging-sw.js');
                        console.log('✅ Service Worker registered at elwaled_market:', registration);
                    }

                    // Update messaging with service worker
                    messaging.useServiceWorker(registration);

                } catch (error) {
                    console.error('❌ Service Worker registration failed completely:', error);
                }
            } else {
                console.log('❌ Service Worker not supported in this browser');
            }
        }

        // Initialize Firebase when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM Content Loaded - Starting Firebase initialization...');
            console.log('🔧 Firebase config:', firebaseConfig);
            console.log('🔑 VAPID key:', vapidKey);

            registerServiceWorker().then(() => {
                console.log('📱 Service Worker registration completed, initializing messaging...');
                initializeFirebaseMessaging();
            }).catch(error => {
                console.error('❌ Service Worker registration failed:', error);
                // Try to initialize messaging anyway
                console.log('🔄 Attempting to initialize messaging without service worker...');
                initializeFirebaseMessaging();
            });
        });

        // Function to refresh notifications if modal is open
        function refreshNotificationsIfOpen() {
            if (typeof Swal !== 'undefined' && Swal.isVisible()) {
                console.log('Refreshing notifications in open modal...');
                const params = new URLSearchParams({
                    store_id: <?= $store_id ?>,
                    account_id: <?= $account_id ?>,
                    limit: 50,
                    time_filter: '1 MONTH'
                });

                fetch(`fetch_account_notifications_improved.php?${params}`)
                    .then(response => response.json())
                    .then(data => {
                        if (Swal.isVisible()) {
                            Swal.update({
                                html: data.notifications
                            });
                            console.log('Notifications refreshed in modal');
                        }
                    })
                    .catch(error => {
                        console.error('Error refreshing notifications:', error);
                    });
            }
        }

        window.updateUnreadCount = function() {
            // Use improved fetch with current parameters
            const params = new URLSearchParams({
                store_id: <?= $store_id ?>,
                account_id: <?= $account_id ?>,
                limit: 50,
                time_filter: '1 MONTH'
            });

            fetch(`fetch_account_notifications_improved.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    const currentUnreadCount = data.unreadCount;
                    document.querySelector('.notification-count').textContent = currentUnreadCount;

                    // Play notification sound if unread count increases
                    if (currentUnreadCount > previousUnreadCount) {
                        const notificationSound = document.getElementById('notificationSound');
                        if (notificationSound && audioEnabled) {
                            notificationSound.play().catch(e => console.log('Sound play failed:', e));
                        } else if (!audioEnabled) {
                            console.log('Audio not enabled yet - waiting for user interaction');
                        }
                    }

                    previousUnreadCount = currentUnreadCount;
                    console.log('Updated unread count:', currentUnreadCount);
                })
                .catch(error => {
                    console.error('Error updating unread count:', error);
                });
        }

        // Global variable to track if notifications modal is open
        let notificationsModalOpen = false;
        let notificationsRefreshInterval = null;

        // Function to refresh notifications if modal is open
        function refreshNotificationsIfOpen() {
            if (notificationsModalOpen && Swal.isVisible()) {
                console.log('Refreshing notifications in open modal...');
                loadNotificationsData();
            }
        }

        // Function to load notifications data
        function loadNotificationsData() {
            const params = new URLSearchParams({
                store_id: <?= $store_id ?>,
                account_id: <?= $account_id ?>,
                limit: 50,
                time_filter: '1 MONTH'
            });

            return fetch(`fetch_account_notifications_improved.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (notificationsModalOpen && Swal.isVisible()) {
                        Swal.update({
                            html: data.notifications
                        });
                    }
                    return data;
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    return null;
                });
        }

        window.showNotifications = function() {
            notificationsModalOpen = true;

            const params = new URLSearchParams({
                store_id: <?= $store_id ?>,
                account_id: <?= $account_id ?>,
                limit: 50,
                time_filter: '1 MONTH'
            });

            fetch(`fetch_account_notifications_improved.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    Swal.fire({
                        title: 'الإشعارات',
                        html: data.notifications,
                        width: '600px',
                        padding: '3em',
                        background: '#fff',
                        showCloseButton: true,
                        focusConfirm: false,
                        confirmButtonText: 'إغلاق',
                        confirmButtonColor: '#3B82F6',
                        didOpen: () => {
                            // Set up auto-refresh every 3 seconds
                            notificationsRefreshInterval = setInterval(() => {
                                loadNotificationsData().then(() => {
                                    updateUnreadCount();
                                });
                            }, 3000);
                        },
                        willClose: () => {
                            // Clean up when modal closes
                            notificationsModalOpen = false;
                            if (notificationsRefreshInterval) {
                                clearInterval(notificationsRefreshInterval);
                                notificationsRefreshInterval = null;
                            }
                        }
                    });

                    // Log the notification view in the notification_reads table
                    data.notificationData.forEach(notification => {
                        fetch('log_notification_view.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ notification_id: notification.id, account_id: <?= $account_id ?> })
                        });
                    });
                })
                .catch(error => {
                    console.error('Error showing notifications:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ في تحميل الإشعارات',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                });
        }

        setInterval(updateUnreadCount, 5000); // Refresh unread count every 5 seconds

        // Add debugging information to console
        console.log('🚀 Firebase notification system initialized');
        console.log('🧪 Test functions available:');
        console.log('   - window.testFirebaseNotification()');
        console.log('   - window.testNotificationTypes()');

        // Check for required elements
        function checkRequiredElements() {
            const body = document.body;
            const fontAwesome = document.querySelector('link[href*="font-awesome"]') || document.querySelector('link[href*="fontawesome"]');

            console.log('🔍 System check:');
            console.log('   - Body element:', body ? '✅' : '❌');
            console.log('   - FontAwesome loaded:', fontAwesome ? '✅' : '❌');
            console.log('   - Document ready state:', document.readyState);

            return body && fontAwesome;
        }

        // Run system check
        checkRequiredElements();

        // Add test buttons for development
        if (window.location.hostname === 'localhost') {
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                z-index: 999999;
                display: flex;
                flex-direction: column;
                gap: 5px;
            `;

            const testButton1 = document.createElement('button');
            testButton1.textContent = 'اختبار إشعار واحد';
            testButton1.style.cssText = `
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 11px;
            `;
            testButton1.onclick = window.testFirebaseNotification;

            const testButton2 = document.createElement('button');
            testButton2.textContent = 'اختبار إشعارات متعددة';
            testButton2.style.cssText = `
                background: #28a745;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 11px;
            `;
            testButton2.onclick = window.testNotificationTypes;

            buttonContainer.appendChild(testButton1);
            buttonContainer.appendChild(testButton2);
            document.body.appendChild(buttonContainer);
        }
    </script>
</body>
</html>



