<?php
include 'db_connection.php';
require_once 'security.php';
// استلام معرف المخزن المشفر ووضعه في المتغير $encrypted_store_id
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';

$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>
<!DOCTYPE html>
<html lang="ar">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>شرح النظام - Elwaled Market</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <!-- Include SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
	<link rel="stylesheet" href="web_css/style_web.css">
	<style>
		.summary-container {
			max-width: 1000px;
			margin: 50px auto;
			padding: 20px;
			padding-top: 20px; /* Increased padding-top for more spacing */
			background-color: var(--color-secondary);
			border: 1px solid var(--color-primary);
			border-radius: 10px;
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		}
		.summary-container h1 {
			color: var(--color-primary);
			margin-bottom: 20px;
			text-align: center;
		}
		.summary-section {
			margin-bottom: 30px;
			line-height: 1.6;
		}
		/* زيادة المسافة بين الأسطر للفقرة داخل مميزات النظام */
		.summary-section p {
			line-height: 3;
		}
		.summary-section h2 {
			color: var(--color-primary);
			border-bottom: 2px solid var(--color-primary);
			padding-bottom: 8px;
			margin-bottom: 15px;
		}
	</style>
</head>
<body>
	<?php include 'sidebar.php'; ?>
    <!-- Added padding element before summary-container -->
    <div style="padding-top: 90px;"></div>
	<div class="summary-container">
		<h1> نظام Elwaled Market</h1>
		<div class="summary-section">
			<h2>مميزات النظام</h2>
			<p>
				 • لوحة تحكم سهلة الاستخدام مع واجهة متكاملة وسريعة الاستجابة.<br>
				• تقارير شاملة عن حركة الأصناف تبدأ من الرصيد الافتتاحي وتغطي عمليات الشراء والبيع بالجملة.<br>
				• إدارة كاملة للأصناف: إضافة، تعديل، حذف وتصنيف الأصناف بكل سهولة.<br>
				• إدارة كاملة لفواتير الشراء مع سهولة في إضافتها وتعديلها.<br>
				• نظام جرد مفصل لكل فرع، يُسجل كافة التفاصيل والعمليات التي تحدث خلال الشهر.<br>
				• إدارة كاملة لحسابات المستخدمين والعمال، مع القدرة على التحكم في فرع كل مستخدم.<br>
				• إدارة كاملة لطلبات العمال والمستخدمين الجدد.<br>
				• إمكانية نقل الأصناف بين الفروع مع استعراض بيانات كل صنف وتصنيفه بدقة.<br>
				• إدارة لصلاحيات الأصناف وتحديثها بشكل دوري.<br>
				• إدارة دقيقة للمصاريف الشهرية.<br>
				• تقفيل الورديات مع تقرير مفصل لكل عامل وورديته.<br>
				• دعم كامل لعمليات تحويلات الرصيد للمزودين الافتراضيين مثل فوري وضامن، بالإضافة لإمكانية إضافة تعديلات جديدة.<br>
				• نظام إشعارات متطور ينبه المستخدم عند تغيير سعر صنف أو انتهاء صلاحية صنف معين.<br>
				• النظام مربوط بنظام متكامل للعمال والمندوبين لتسجيل الفواتير سواء الشراء أو البيع بناءً على الفرع الذي تم ربطهم به.<br>
				• تسجيل تقفيلات الوردية مدعوم بتاريخ الوردية مع إمكانية إضافة ملاحظات.<br>
				• نظام إشعارات متكامل لإبلاغ المستخدمين بتحديثات أسعار الأصناف والأصناف المنتهية الصلاحية أو تلك التي اقتربت من انتهاء صلاحيتها.
			</p>
		</div>
		<div class="summary-section">
			<h2>ملاحظات عامة</h2>
			<p>
				يعتمد النظام على تشفير المعرفات لضمان أمان البيانات، كما تم تصميم الواجهات لتكون متجاوبة وسهلة الاستخدام. ويتضمن نظام إشعارات يضمن بقاء المستخدمين على اطلاع دائم بكل العمليات الحرجة والتغييرات.
			</p>
		</div>
		 <!-- تحديث قسم التواصل -->
		<div class="summary-section">
			<h2>التواصل مع المهندس</h2>
			<p>
				للتواصل مع المهندس <strong>Eyad Waled</strong> عبر الهاتف أو الواتساب على الرقم: <a href="https://wa.me/201015056013" target="_blank">01015056013</a>
			</p>
		</div>
		<!-- قسم تطوير النظام -->
		<div class="summary-section">
			<h2>تم تطوير النظام</h2>
			<p>
				تم تطوير النظام من خلال المهندس <strong>Eyad Waled</strong>.
			</p>
		</div>
	</div>
	<?php include 'notifications.php'; ?>
</body>
</html>
