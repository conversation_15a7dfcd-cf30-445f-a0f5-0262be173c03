<?php
/**
 * إضافة تحويل رصيد جديد - مع فحص الصلاحيات
 * الصلاحية المطلوبة: add_transfer في وحدة balance_transfers
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

// فحص صلاحية إضافة تحويل رصيد
if (!hasPermission('balance_transfers', 'add_transfer')) {
    echo json_encode(['success' => false, 'error' => 'ليس لديك صلاحية لإضافة تحويل رصيد جديد']);
    exit();
}

// Retrieve and decrypt store_id from GET and account_id from session

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : null;

if ($encrypted_store_id && $encrypted_account_id) {
    $store_id = decrypt($encrypted_store_id, $key);
    $account_id = decrypt($encrypted_account_id, $key);
} else {
    echo json_encode(['success' => false, 'error' => 'Missing or invalid data.']);
    exit();
}

// Retrieve and validate input fields
$provider = isset($_POST['provider']) ? trim($_POST['provider']) : null;
$cost = isset($_POST['cost']) ? floatval($_POST['cost']) : null;
$sale_price = isset($_POST['sale_price']) ? floatval($_POST['sale_price']) : null;
$value = isset($_POST['value']) ? floatval($_POST['value']) : null;

// Validate required fields
if (!$provider || !$cost || !$sale_price || !$value) {
    echo json_encode(['success' => false, 'error' => 'Missing required fields.']);
    exit();
}

// Fetch all valid providers dynamically from the ENUM column
$result = $conn->query("SHOW COLUMNS FROM balance_transfers LIKE 'provider'");
$row = $result->fetch_assoc();
$enum_values = str_replace(["enum(", ")", "'"], "", $row['Type']);
$valid_providers = explode(",", $enum_values);

// Ensure valid provider
if (!in_array($provider, $valid_providers)) {
    echo json_encode(['success' => false, 'error' => 'Invalid provider.']);
    exit();
}

// Insert the new balance transfer
$stmt = $conn->prepare("INSERT INTO balance_transfers (store_id, account_id, provider, cost, sale_price, value) 
                        VALUES (?, ?, ?, ?, ?, ?)");
$stmt->bind_param("iissdd", $store_id, $account_id, $provider, $cost, $sale_price, $value);

if ($stmt->execute()) {
    $balance_transfer_id = $stmt->insert_id; // Get the ID of the newly created balance transfer

    // Log the balance transfer addition action
    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'add', 'balance_transfers', ?, ?)";
    $description = "تم إضافة تحويل رصيد جديد من المزود $provider بقيمة $value";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $balance_transfer_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    echo json_encode(['success' => true, 'message' => 'تم إضافة تحويل الرصيد بنجاح.']);
} else {
    echo json_encode(['success' => false, 'error' => 'فشل في إضافة تحويل الرصيد.']);
}

$stmt->close();
$conn->close();
?>
