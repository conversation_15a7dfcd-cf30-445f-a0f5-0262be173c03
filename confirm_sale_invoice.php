<?php
include 'db_connection.php';
include 'encryption_functions.php';

// Read data from $_POST
$store_id = $_POST['store_id'] ?? null;
$account_id = $_POST['account_id'] ?? null;
$branch_id = $_POST['branch_id'] ?? null;
$account_buyer_id = $_POST['account_buyer_id'] ?? null;
$items_json = $_POST['items'] ?? '[]';
$items = json_decode($items_json, true);
$image_paths = $_POST['images'] ?? []; // Receive image paths directly

if (!$store_id || !$account_id || !$branch_id || !$account_buyer_id || empty($items)) {
    http_response_code(400);
    echo json_encode(['error' => 'Incomplete data']);
    exit();
}

// Calculate total amount
$total_amount = 0;
foreach ($items as $item) {
    $stmt = $conn->prepare("SELECT cost FROM items WHERE item_id = ?");
    $stmt->bind_param("i", $item['id']);
    $stmt->execute();
    $cost = $stmt->get_result()->fetch_assoc()['cost'] ?? 0;
    $total_amount += $cost * $item['quantity'];
}

// Insert into wholesale_invoices
$stmt = $conn->prepare("INSERT INTO wholesale_invoices (store_id, buyer, total_amount, account_id_buyer, account_id, status) VALUES (?, ?, ?, ?, ?, 'pending')");
$stmt->bind_param("iisii", $store_id, $branch_id, $total_amount, $account_buyer_id, $account_id);
$stmt->execute();
$invoice_id = $stmt->insert_id;

// Insert into whosales
foreach ($items as $item) {
    $stmt = $conn->prepare("SELECT cost FROM items WHERE item_id = ?");
    $stmt->bind_param("i", $item['id']);
    $stmt->execute();
    $cost = $stmt->get_result()->fetch_assoc()['cost'] ?? 0;

    $totalItemAmount = $item['quantity'] * $cost;
    $stmt = $conn->prepare("INSERT INTO whosales (invoice_id, store_id, item_id, quantity, total_amount) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("iiiii", $invoice_id, $store_id, $item['id'], $item['quantity'], $totalItemAmount);
    $stmt->execute();
}

// Handle image paths
foreach ($image_paths as $path) {
    $stmt = $conn->prepare("INSERT INTO wholesale_invoice_images (wholesale_invoice_id, img_path) VALUES (?, ?)");
    $stmt->bind_param("is", $invoice_id, $path);
    $stmt->execute();
}

// Fetch the store name
$query = "SELECT name FROM stores WHERE store_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
$store = $result->fetch_assoc();
$store_name = $store['name'] ?? 'غير معروف';
$stmt->close();

// Log the invoice confirmation action
$log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
            VALUES (?, 'add', 'wholesale_invoices', ?, ?)";
$description = "تم إضافة فاتورة بيع جديدة برقم $invoice_id بقيمة $total_amount إلى الفرع $store_name";
$log_stmt = $conn->prepare($log_sql);
$log_stmt->bind_param("iis", $account_id, $invoice_id, $description);
$log_stmt->execute();
$log_stmt->close();

// Delete the JSON file after confirming the invoice
$jsonFilePath = __DIR__ . "/saved_invoices/account_{$account_id}.json";
if (file_exists($jsonFilePath)) {
    unlink($jsonFilePath);
}

header('Content-Type: application/json');
echo json_encode(['success' => true]);
?>
