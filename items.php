<?php


include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول لوحدة الأصناف
checkPagePermission('items', 'access');

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['category_id'])) {
    $encrypted_category_id = $_GET['category_id'];
    $category_id = decrypt($encrypted_category_id, $key);

    if ($category_id) {
        $re_encrypted_category_id = encrypt($category_id, $key);

        // Fetch store ID, store name, and category name using category ID
        $store_sql = "SELECT store_id, name AS category_name, (SELECT name FROM stores WHERE store_id = categories.store_id) AS store_name FROM categories WHERE category_id = ?";
        $store_stmt = $conn->prepare($store_sql);
        $store_stmt->bind_param("i", $category_id);
        $store_stmt->execute();
        $store_result = $store_stmt->get_result();
        $store_row = $store_result->fetch_assoc();
        $store_id = $store_row ? $store_row['store_id'] : null;
        $store_name = $store_row ? $store_row['store_name'] : null;
        $category_name = $store_row ? $store_row['category_name'] : null;
        $store_stmt->close();

        if ($store_id) {
            $encrypted_store_id = encrypt($store_id, $key);
            // تعيين متغير encrypted_category_id ليكون متاحًا في السايد بار
            $encrypted_category_id = $re_encrypted_category_id;

            // معالجة طلبات POST مع فحص الصلاحيات المحددة
            if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_item_id'])) {
                // فحص صلاحية حذف الأصناف
                if (!hasPermission('items', 'delete_item')) {
                    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لحذف الأصناف']);
                    exit();
                }

                $delete_item_id = $_POST['delete_item_id'];

                $stmt = $conn->prepare("DELETE FROM items WHERE item_id = ?");
                $stmt->bind_param("i", $delete_item_id);
                $stmt->execute();
                $stmt->close();
                echo json_encode(['success' => true]);
                exit();
            }

            if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_item_id'])) {
                // فحص صلاحية تغيير حالة الصنف
                if (!hasPermission('items', 'change_item_status')) {
                    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لتغيير حالة الأصناف']);
                    exit();
                }

                $toggle_item_id = $_POST['toggle_item_id'];
                $current_status = $_POST['current_status'];
                $new_status = $current_status === 'active' ? 'inactive' : 'active';
                $stmt = $conn->prepare("UPDATE items SET status = ? WHERE item_id = ?");
                $stmt->bind_param("si", $new_status, $toggle_item_id);
                $stmt->execute();
                $stmt->close();
                echo json_encode(['success' => true, 'new_status' => $new_status]);
                exit();
            }

            // Fetch items with image count and favorite status
            $account_id = isset($_SESSION['account_id']) ? decrypt($_SESSION['account_id'], $key) : null;
            
            if ($account_id) {
                $sql = "SELECT i.*, COUNT(img.img_id) as image_count,
                        (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) as is_favorite
                        FROM items i 
                        LEFT JOIN item_images img ON i.item_id = img.item_id 
                        WHERE i.category_id = ? 
                        GROUP BY i.item_id 
                        ORDER BY (SELECT COUNT(*) FROM user_favorites WHERE item_id = i.item_id AND account_id = ?) DESC, i.item_id DESC";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("iii", $account_id, $category_id, $account_id);
            } else {
                $sql = "SELECT i.*, COUNT(img.img_id) as image_count, 0 as is_favorite
                        FROM items i 
                        LEFT JOIN item_images img ON i.item_id = img.item_id 
                        WHERE i.category_id = ? 
                        GROUP BY i.item_id 
                        ORDER BY i.item_id DESC";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $category_id);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            
            // حفظ النتائج في مصفوفة لاستخدامها في الإحصائيات
            $items = [];
            while($row = $result->fetch_assoc()) {
                $items[] = $row;
            }
            
            // حساب الإحصائيات
            $total_items = count($items);
            $active_items = 0;
            $inactive_items = 0;
            $items_with_images = 0;
            $items_without_images = 0;
            $favorite_items = 0;
            $total_quantity = 0;
            $total_value = 0;
            $low_stock_items = 0;
            $out_of_stock_items = 0;
            
            // تجميع الأصناف حسب النوع
            $items_by_type = [
                'piece' => 0,
                'box' => 0,
                'fridge' => 0,
                'other' => 0
            ];
            
            foreach($items as $item) {
                // حالة الصنف
                if($item['status'] === 'active') {
                    $active_items++;
                } else {
                    $inactive_items++;
                }
                
                // الصور
                if($item['image_count'] > 0) {
                    $items_with_images++;
                } else {
                    $items_without_images++;
                }
                
                // المفضلة
                if($item['is_favorite'] > 0) {
                    $favorite_items++;
                }
                
                // الكمية والقيمة
                $quantity = floatval($item['quantity']);
                $cost = floatval($item['cost']);
                $total_quantity += $quantity;
                $total_value += ($quantity * $cost);
                
                // المخزون المنخفض والنافد
                if($quantity <= 0) {
                    $out_of_stock_items++;
                } elseif($quantity <= 5) { // يمكن تعديل هذا الرقم حسب الحاجة
                    $low_stock_items++;
                }
                
                // تجميع حسب النوع
                if(isset($items_by_type[$item['type']])) {
                    $items_by_type[$item['type']]++;
                }
            }
            
            $stmt->close();
        } else {
            echo "Category ID is invalid.";
            exit();
        }
    } else {
        echo "Category ID is invalid.";
        exit();
    }
} else {
    echo "Category ID is missing.";
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الأصناف</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="web_css/items.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
   
</head>
<body>

    <?php include 'sidebar.php'; ?>

<div class="container" style="padding-top: 120px;">
    <!-- Enhanced Header Section -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-icon">📦</div>
            <div class="header-text">
                <h2>قائمة الأصناف</h2>
                <div class="store-info">
                    <i class="fas fa-store"></i>
                    <span>الفرع: <?php echo htmlspecialchars($store_name); ?></span>
                </div>
            </div>
        </div>
        
        <!-- زر إضافة صنف جديد - يظهر فقط للمستخدمين الذين لديهم صلاحية إضافة صنف -->
        <?php if (hasPermission('items', 'add_item_from_items')): ?>
            <button class="add-btn enhanced-add-btn" onclick="showAddItemForm()">
                <i class="fas fa-plus"></i>
                <span>إضافة صنف جديد</span>
            </button>
        <?php endif; ?>
    </div>

    <!-- Enhanced Statistics Dashboard -->
    <div class="stats-dashboard">
        <div class="stats-grid">
            <!-- إجمالي الأصناف -->
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($total_items); ?></div>
                    <div class="stat-label">إجمالي الأصناف</div>
                    <div class="stat-sublabel">في التصنيف: <?php echo htmlspecialchars($category_name); ?></div>
                </div>
            </div>

            <!-- الأصناف النشطة -->
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($active_items); ?></div>
                    <div class="stat-label">أصناف نشطة</div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo $total_items > 0 ? ($active_items / $total_items) * 100 : 0; ?>%"></div>
                    </div>
                </div>
            </div>

            <!-- القيمة الإجمالية -->
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($total_value, 2); ?></div>
                    <div class="stat-label">القيمة الإجمالية</div>
                    <div class="stat-sublabel">بالجنيه المصري</div>
                </div>
            </div>

            <!-- الأصناف المفضلة -->
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($favorite_items); ?></div>
                    <div class="stat-label">أصناف مفضلة</div>
                    <div class="stat-sublabel">من إجمالي <?php echo $total_items; ?> صنف</div>
                </div>
            </div>

            <!-- الأصناف مع الصور -->
            <div class="stat-card secondary">
                <div class="stat-icon">
                    <i class="fas fa-images"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($items_with_images); ?></div>
                    <div class="stat-label">أصناف مع صور</div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo $total_items > 0 ? ($items_with_images / $total_items) * 100 : 0; ?>%"></div>
                    </div>
                </div>
            </div>

            <!-- تنبيه المخزون -->
            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($out_of_stock_items + $low_stock_items); ?></div>
                    <div class="stat-label">تنبيهات المخزون</div>
                    <div class="stat-sublabel"><?php echo $out_of_stock_items; ?> نافد، <?php echo $low_stock_items; ?> منخفض</div>
                </div>
            </div>
        </div>

        <!-- تفاصيل إضافية -->
        <div class="stats-details">
            <div class="detail-section">
                <h4><i class="fas fa-chart-pie"></i> توزيع الأصناف حسب النوع</h4>
                <div class="type-distribution">
                    <div class="type-item">
                        <span class="type-label">قطع</span>
                        <span class="type-count"><?php echo $items_by_type['piece']; ?></span>
                        <div class="type-bar">
                            <div class="type-progress" style="width: <?php echo $total_items > 0 ? ($items_by_type['piece'] / $total_items) * 100 : 0; ?>%"></div>
                        </div>
                    </div>
                    <div class="type-item">
                        <span class="type-label">كراتين</span>
                        <span class="type-count"><?php echo $items_by_type['box']; ?></span>
                        <div class="type-bar">
                            <div class="type-progress" style="width: <?php echo $total_items > 0 ? ($items_by_type['box'] / $total_items) * 100 : 0; ?>%"></div>
                        </div>
                    </div>
                    <div class="type-item">
                        <span class="type-label">ثلاجة</span>
                        <span class="type-count"><?php echo $items_by_type['fridge']; ?></span>
                        <div class="type-bar">
                            <div class="type-progress" style="width: <?php echo $total_items > 0 ? ($items_by_type['fridge'] / $total_items) * 100 : 0; ?>%"></div>
                        </div>
                    </div>
                    <div class="type-item">
                        <span class="type-label">أخرى</span>
                        <span class="type-count"><?php echo $items_by_type['other']; ?></span>
                        <div class="type-bar">
                            <div class="type-progress" style="width: <?php echo $total_items > 0 ? ($items_by_type['other'] / $total_items) * 100 : 0; ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-info-circle"></i> معلومات سريعة</h4>
                <div class="quick-info">
                    <div class="info-item">
                        <i class="fas fa-pause-circle text-danger"></i>
                        <span>أصناف متوقفة: <strong><?php echo $inactive_items; ?></strong></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-image text-muted"></i>
                        <span>بدون صور: <strong><?php echo $items_without_images; ?></strong></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-weight text-primary"></i>
                        <span>إجمالي الكمية: <strong><?php echo number_format($total_quantity, 2); ?></strong></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dynamic Tabs -->
    <div class="tabs-container">
        <button class="tab-btn active" data-tab="all">الكل</button>
        <button class="tab-btn" data-tab="products">منتجات</button>
        <button class="tab-btn" data-tab="services">خدمات</button>
    </div>

    <!-- Products Table Container -->
    <div id="products-view" class="table-view active">
        <div class="table-responsive">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>المفضلة</th>
                        <th>اسم الصنف</th>
                        <th>التكلفة</th>
                        <th>سعر البيع</th>
                        <th>الكمية</th>
                        <th>الباركود</th>
                        <th>نوع الصنف</th>
                        <th>عدد القطع</th>
                        <th>الحالة</th>
                        <th>الصور</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="itemsTable">
                    <?php
                    if (!empty($items)) {
                        foreach($items as $row) {
                            if ($row['type'] === 'service') continue; // Skip services in this table
                            $quantity_unit = '';
                            if ($row['type'] == 'box') $quantity_unit = ' كرتونة';
                            elseif ($row['type'] == 'fridge') $quantity_unit = ' كيلو';
                            else $quantity_unit = ' قطعة';
                            
                            $encrypted_item_id = encrypt($row['item_id'], $key);
                            $status_class = $row['status'] === 'active' ? 'confirmed' : 'pending';
                            $status_text = $row['status'] === 'active' ? 'نشط' : 'متوقف';
                            $image_class = $row['image_count'] > 0 ? 'has-images-icon' : 'no-images-icon';
                            $image_title = $row['image_count'] > 0 ? 'يحتوي على ' . $row['image_count'] . ' صورة/صور' : 'لا يحتوي على صور';
                            $is_favorite = $row['is_favorite'] > 0;
                            $favorite_class = $is_favorite ? 'is-favorite' : 'not-favorite';
                            $favorite_title = $is_favorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة';

                            echo "<tr id='item-{$row['item_id']}' data-type='{$row['type']}'>
                                    <td onclick='event.stopPropagation()'><button class='favorite-btn $favorite_class' data-item-id='{$row['item_id']}' title='$favorite_title' onclick='toggleFavorite({$row['item_id']}, this, event)'><i class='fas fa-star'></i></button></td>
                                    <td>" . htmlspecialchars($row['name']) . "</td>
                                    <td>" . htmlspecialchars($row['cost']) . "</td>
                                    <td>" . htmlspecialchars($row['price']) . "</td>
                                    <td>" . htmlspecialchars($row['quantity']) . $quantity_unit . "</td>
                                    <td>" . htmlspecialchars($row['barcode']) . "</td>
                                    <td>" . htmlspecialchars($row['type']) . "</td>
                                    <td>" . htmlspecialchars($row['pieces_per_box']) . "</td>
                                    <td class='status-cell $status_class' onclick='event.stopPropagation(); toggleItemStatus({$row["item_id"]}, \"{$row["status"]}\")'><span class='status-frame $status_class'>$status_text</span></td>
                                    <td onclick='event.stopPropagation()'><button class='action-btn $image_class' title='$image_title' onclick='viewItemImages(\"{$encrypted_item_id}\")'><i class='fas fa-images'></i></button></td>
                                    <td onclick='event.stopPropagation()'>
                                        <button class='action-btn' onclick='editItem(\"{$encrypted_item_id}\")'><i class='fas fa-edit'></i></button>
                                        <button class='action-btn' onclick='deleteItem({$row['item_id']})'><i class='fas fa-trash-alt'></i></button>
                                    </td>
                                  </tr>";
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Services Table Container -->
    <div id="services-view" class="table-view">
        <div class="table-responsive">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>المفضلة</th>
                        <th>اسم الخدمة</th>
                        <th>المزود</th>
                        <th>نوع الخدمة</th>
                        <th>الرصيد/الكمية</th>
                        <th>حد العمولة</th>
                        <th>عمولة ثابتة</th>
                        <th>عمولة لكل ألف</th>
                        <th>الحالة</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="servicesTable">
                    <?php
                    if (!empty($items)) {
                        foreach($items as $row) {
                            if ($row['type'] !== 'service') continue; // Skip non-services
                            
                            $encrypted_item_id = encrypt($row['item_id'], $key);
                            $status_class = $row['status'] === 'active' ? 'confirmed' : 'pending';
                            $status_text = $row['status'] === 'active' ? 'نشط' : 'متوقف';
                            $is_favorite = $row['is_favorite'] > 0;
                            $favorite_class = $is_favorite ? 'is-favorite' : 'not-favorite';
                            $favorite_title = $is_favorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة';

                            echo "<tr id='item-{$row['item_id']}' data-type='service'>
                                    <td onclick='event.stopPropagation()'><button class='favorite-btn $favorite_class' data-item-id='{$row['item_id']}' title='$favorite_title' onclick='toggleFavorite({$row['item_id']}, this, event)'><i class='fas fa-star'></i></button></td>
                                    <td>" . htmlspecialchars($row['name']) . "</td>
                                    <td>" . htmlspecialchars($row['service_provider']) . "</td>
                                    <td>" . htmlspecialchars($row['service_type']) . "</td>
                                    <td>" . htmlspecialchars($row['quantity']) . "</td>
                                    <td>" . htmlspecialchars($row['commission_threshold']) . "</td>
                                    <td>" . htmlspecialchars($row['commission_fixed']) . "</td>
                                    <td>" . htmlspecialchars($row['commission_per_thousand']) . "</td>
                                    <td class='status-cell $status_class' onclick='event.stopPropagation(); toggleItemStatus({$row["item_id"]}, \"{$row["status"]}\")'><span class='status-frame $status_class'>$status_text</span></td>
                                    <td onclick='event.stopPropagation()'>
                                        <button class='action-btn' onclick='editItem(\"{$encrypted_item_id}\")'><i class='fas fa-edit'></i></button>
                                        <button class='action-btn' onclick='deleteItem({$row['item_id']})'><i class='fas fa-trash-alt'></i></button>
                                    </td>
                                  </tr>";
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
    </div>
</div>

<div id="editItemModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span><br>
        <h2>تعديل تفاصيل الصنف</h2>
        <form method="POST" action="edit_item.php" enctype="multipart/form-data" style="width: 100%; max-width: 500px; margin: 50 auto;">
            <input type="hidden" name="edit_item_id" id="edit_item_id">
            <input type="hidden" name="category_id" value="<?php echo isset($encrypted_category_id) ? htmlspecialchars($encrypted_category_id) : ''; ?>">

            <input type="hidden" name="item_type" id="edit_item_type" readonly>

            <label for="edit_barcode">باركود المنتج:</label>
            <input type="text" name="barcode" id="edit_barcode" class="input-field" placeholder="باركود المنتج">

            <label for="edit_item_images">صور الصنف:</label>
            <input type="file" name="item_images[]" id="edit_item_images" class="input-field" multiple accept="image/*">
            <small>يمكنك اختيار صور جديدة للصنف</small>
            
            <div id="current_images" class="current-images">
                <!-- سيتم عرض الصور الحالية هنا -->
            </div>

            <div id="editPieceFields" class="item-fields">
                <label for="edit_item_name_piece">اسم الصنف:</label>
                <input type="text" name="item_name_piece" id="edit_item_name_piece" class="input-field" placeholder="اسم الصنف">

                <label for="edit_cost_piece">تكلفة المنتج:</label>
                <input type="number" step="0.01" name="cost_piece" id="edit_cost_piece" class="input-field" placeholder="جملة الصنف">

                <label for="edit_price1_piece">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_piece" id="edit_price1_piece" class="input-field" placeholder="سعر القطاعي">

                <label for="edit_quantity_piece">الكمية:</label>
                <input type="number" step="0.01" name="quantity_piece" id="edit_quantity_piece" class="input-field" placeholder="الكمية">
            </div>

            <div id="editBoxFields" class="item-fields">
                <label for="edit_item_name_box">اسم الكرتونة:</label>
                <input type="text" name="item_name_box" id="edit_item_name_box" class="input-field" placeholder="اسم الكرتونة">

                <label for="edit_cost_box">تكلفة الكرتونة:</label>
                <input type="number" step="0.01" name="cost_box" id="edit_cost_box" class="input-field" placeholder="جملة الكرتونة">

                <label for="edit_price1_box">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_box" id="edit_price1_box" class="input-field" placeholder="سعر القطاعي">

                <label for="edit_pieces_per_box">عدد القطع داخل الكرتونة:</label>
                <input type="number" step="0.01" name="pieces_per_box" id="edit_pieces_per_box" class="input-field" placeholder="عدد القطع">

                <label for="edit_quantity_box">كمية الكراتين:</label>
                <input type="number" step="0.01" name="quantity_box" id="edit_quantity_box" class="input-field" placeholder="كمية الكراتين">
            </div>

            <div id="editFridgeFields" class="item-fields">
                <label for="edit_item_name_fridge">اسم الصنف:</label>
                <input type="text" name="item_name_fridge" id="edit_item_name_fridge" class="input-field" placeholder="اسم الصنف">

                <label for="edit_cost_fridge">سعر الكيلو جملة:</label>
                <input type="number" step="0.01" name="cost_fridge" id="edit_cost_fridge" class="input-field" placeholder="سعر الكيلو جملة">

                <label for="edit_price1_fridge">سعر الكيلو قطاعي:</label>
                <input type="number" step="0.01" name="price1_fridge" id="edit_price1_fridge" class="input-field" placeholder="سعر الكيلو قطاعي">

                <label for="edit_quantity_fridge">الوزن الموجود:</label>
                <input type="number" step="0.01" name="quantity_fridge" id="edit_quantity_fridge" class="input-field" placeholder="الوزن الموجود">
            </div>

<div id="editServiceFields" class="item-fields" style="display: none;">
    <label for="edit_service_provider">مزود الخدمة:</label>
    <select name="service_provider" id="edit_service_provider" class="input-field">
        <option value="vodafone">فودافون</option>
        <option value="orange">أورانج</option>
        <option value="etisalat">إتصالات</option>
        <option value="we">وي</option>
        <option value="other">أخرى</option>
    </select>

    <label for="edit_service_type">نوع الخدمة:</label>
    <select name="service_type" id="edit_service_type" class="input-field" onchange="toggleCommissionFields('edit')">
        <option value="topup_card">كرت فكة</option>
        <option value="balance">شحن رصيد</option>
        <option value="bundle">باقة</option>
        <option value="bill">فاتورة</option>
        <option value="cash_withdraw">سحب كاش</option>
        <option value="cash_deposit">إيداع كاش</option>
        <option value="other">أخرى</option>
    </select>

    <label for="edit_item_name_service">اسم الخدمة:</label>
    <input type="text" name="item_name_service" id="edit_item_name_service" class="input-field" placeholder="اسم الخدمة">

    <label for="edit_cost_service">تكلفة الخدمة:</label>
    <input type="number" step="0.01" name="cost_service" id="edit_cost_service" class="input-field" placeholder="تكلفة الخدمة">

    <label for="edit_price_service">سعر الخدمة:</label>
    <input type="number" step="0.01" name="price_service" id="edit_price_service" class="input-field" placeholder="سعر الخدمة">

    <label id="quantity_label_edit" for="edit_quantity_service">الكمية:</label>
    <input type="number" step="0.01" name="quantity_service" id="edit_quantity_service" class="input-field" placeholder="الكمية">

    <div id="commissionFields_edit" style="display: none;">
        <label for="edit_commission_threshold">حد العمولة:</label>
        <input type="number" step="0.01" name="commission_threshold" id="edit_commission_threshold" class="input-field" value="1000.00">

        <label for="edit_commission_fixed">عمولة ثابتة:</label>
        <input type="number" step="0.01" name="commission_fixed" id="edit_commission_fixed" class="input-field" value="10.00">

        <label for="edit_commission_per_thousand">عمولة لكل 1000:</label>
        <input type="number" step="0.01" name="commission_per_thousand" id="edit_commission_per_thousand" class="input-field" value="15.00">
    </div>

    <div class="custom-price-container">
        <label class="custom-price-label">
            <input type="checkbox" name="is_custom_priced" id="edit_is_custom_priced">
            <span class="custom-price-text">سعر مخصص للخدمة</span>
            <i class="fas fa-info-circle custom-price-info-icon" title="تفعيل هذا الخيار يسمح بتحديد سعر مخصص لهذه الخدمة"></i>
        </label>
    </div>
</div>

            <!-- خيار التعديل في فروع أخرى -->
            <div class="other-stores-edit-container">
                <label class="other-stores-edit-label">
                    <input type="checkbox" id="edit_in_other_stores" onchange="toggleOtherStoresEdit()">
                    <span class="other-stores-edit-text">تعديل في فروع أخرى</span>
                    <i class="fas fa-info-circle other-stores-info-icon" title="البحث عن أصناف بنفس الباركود في فروع أخرى وتعديلها معاً"></i>
                </label>
                <div id="other_stores_items" class="other-stores-items-container">
                    <h4 class="other-stores-items-title">الأصناف المشابهة في فروع أخرى:</h4>
                    <div id="similar_items_list" class="similar-items-list">
                        <!-- سيتم عرض الأصناف المشابهة هنا -->
                    </div>
                </div>
            </div>

            <button type="submit" class="add-btn">تعديل الصنف</button>
        </form>
    </div>
</div>

<div id="addItemModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeAddItemForm()">&times;</span><br>
        <h2>إضافة صنف جديد</h2>
        <form method="POST" action="add_item_from_items.php" enctype="multipart/form-data" style="width: 100%; max-width: 500px; margin: 50 auto;">
            <input type="hidden" name="category_id" value="<?php echo isset($re_encrypted_category_id) ? htmlspecialchars($re_encrypted_category_id) : ''; ?>">

            <label for="item_type">نوع الصنف:</label>
            <div class="select-wrapper">
                <select name="item_type" id="item_type" class="input-field" onchange="toggleAddItemFields()">
                    <option value="piece">قطعة</option>
                    <option value="box">كرتونة</option>
                    <option value="fridge">ثلاجة</option>
                    <option value="other">أخرى</option>
                    <option value="service">خدمة</option>
                </select>
            </div>

            <div id="addPieceFields" class="item-fields">
                <label for="item_name_piece">اسم الصنف:</label>
                <input type="text" name="item_name_piece" id="item_name_piece" class="input-field" placeholder="اسم الصنف">

                <label for="cost_piece">تكلفة المنتج:</label>
                <input type="number" step="0.01" name="cost_piece" id="cost_piece" class="input-field" placeholder="جملة الصنف">

                <label for="price1_piece">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_piece" id="price1_piece" class="input-field" placeholder="سعر القطاعي">

                <label for="quantity_piece">الكمية:</label>
                <input type="number" step="0.01" name="quantity_piece" id="quantity_piece" class="input-field" placeholder="الكمية">
            </div>

            <div id="addBoxFields" class="item-fields" style="display: none;">
                <label for="item_name_box">اسم الكرتونة:</label>
                <input type="text" name="item_name_box" id="item_name_box" class="input-field" placeholder="اسم الكرتونة">

                <label for="cost_box">تكلفة الكرتونة:</label>
                <input type="number" step="0.01" name="cost_box" id="cost_box" class="input-field" placeholder="جملة الكرتونة">

                <label for="price1_box">سعر البيع:</label>
                <input type="number" step="0.01" name="price1_box" id="price1_box" class="input-field" placeholder="سعر القطاعي">

                <label for="pieces_per_box">عدد القطع داخل الكرتونة:</label>
                <input type="number" step="0.01" name="pieces_per_box" id="pieces_per_box" class="input-field" placeholder="عدد القطع">

                <label for="quantity_box">كمية الكراتين:</label>
                <input type="number" step="0.01" name="quantity_box" id="quantity_box" class="input-field" placeholder="كمية الكراتين">
            </div>

            <div id="addFridgeFields" class="item-fields" style="display: none;">
                <label for="item_name_fridge">اسم الصنف:</label>
                <input type="text" name="item_name_fridge" id="item_name_fridge" class="input-field" placeholder="اسم الصنف">

                <label for="cost_fridge">سعر الكيلو جملة:</label>
                <input type="number" step="0.01" name="cost_fridge" id="cost_fridge" class="input-field" placeholder="سعر الكيلو جملة">

                <label for="price1_fridge">سعر الكيلو قطاعي:</label>
                <input type="number" step="0.01" name="price1_fridge" id="price1_fridge" class="input-field" placeholder="سعر الكيلو قطاعي">

                <label for="quantity_fridge">الوزن الموجود:</label>
                <input type="number" step="0.01" name="quantity_fridge" id="quantity_fridge" class="input-field" placeholder="الوزن الموجود">
            </div>

<div id="addServiceFields" class="item-fields" style="display: none;">
    <label for="service_provider">مزود الخدمة:</label>
    <select name="service_provider" id="service_provider" class="input-field">
        <option value="vodafone">فودافون</option>
        <option value="orange">أورانج</option>
        <option value="etisalat">إتصالات</option>
        <option value="we">وي</option>
        <option value="other">أخرى</option>
    </select>

    <label for="add_service_type">نوع الخدمة:</label>
    <select name="service_type" id="add_service_type" class="input-field" onchange="toggleCommissionFields('add')">
        <option value="topup_card">كرت فكة</option>
        <option value="balance">شحن رصيد</option>
        <option value="bundle">باقة</option>
        <option value="bill">فاتورة</option>
        <option value="cash_withdraw">سحب كاش</option>
        <option value="cash_deposit">إيداع كاش</option>
        <option value="other">أخرى</option>
    </select>

    <label for="item_name_service">اسم الخدمة:</label>
    <input type="text" name="item_name_service" id="item_name_service" class="input-field" placeholder="اسم الخدمة">

    <label for="add_cost_service">تكلفة الخدمة:</label>
    <input type="number" step="0.01" name="cost_service" id="add_cost_service" class="input-field" placeholder="تكلفة الخدمة">

    <label for="add_price_service">سعر الخدمة:</label>
    <input type="number" step="0.01" name="price_service" id="add_price_service" class="input-field" placeholder="سعر الخدمة">

    <label id="quantity_label_add" for="quantity_service">الكمية:</label>
    <input type="number" step="0.01" name="quantity_service" id="quantity_service" class="input-field" placeholder="الكمية">

    <div id="commissionFields_add" style="display: none;">
        <label for="commission_threshold">حد العمولة:</label>
        <input type="number" step="0.01" name="commission_threshold" id="commission_threshold" class="input-field" value="1000.00">

        <label for="commission_fixed">عمولة ثابتة:</label>
        <input type="number" step="0.01" name="commission_fixed" id="commission_fixed" class="input-field" value="10.00">

        <label for="commission_per_thousand">عمولة لكل 1000:</label>
        <input type="number" step="0.01" name="commission_per_thousand" id="commission_per_thousand" class="input-field" value="15.00">
    </div>

    <div class="custom-price-container">
        <label class="custom-price-label">
            <input type="checkbox" name="is_custom_priced" id="add_is_custom_priced">
            <span class="custom-price-text">سعر مخصص للخدمة</span>
            <i class="fas fa-info-circle custom-price-info-icon" title="تفعيل هذا الخيار يسمح بتحديد سعر مخصص لهذه الخدمة"></i>
        </label>
    </div>
</div>

            <label for="barcode">باركود المنتج:</label>
            <input type="text" name="barcode" id="barcode" class="input-field" placeholder="باركود المنتج" disabled>

            <label>
                <input type="checkbox" name="auto_generate_barcode" id="auto_generate_barcode" onclick="toggleBarcode()" checked> تحديد الباركود تلقائي
            </label>
            <div id="barcode_error" class="error-message"></div>

            <label for="item_images">صور الصنف:</label>
            <input type="file" name="item_images[]" id="item_images" class="input-field" multiple accept="image/*">
            <small>يمكنك اختيار عدة صور للصنف</small>

            <!-- خيار الإضافة في فروع أخرى -->
            <div class="other-stores-add-container">
                <label class="other-stores-add-label">
                    <input type="checkbox" id="add_to_other_stores" onchange="toggleOtherStoresAdd()">
                    <span class="other-stores-add-text">إضافة في فروع أخرى</span>
                    <i class="fas fa-info-circle other-stores-info-icon" title="إضافة نفس الصنف في فروع أخرى (شرط عدم وجود صنف بنفس الباركود)"></i>
                </label>
                <div id="other_stores_add_list" class="other-stores-add-container">
                    <h4 class="other-stores-add-title">الفروع المتاحة للإضافة:</h4>
                    <div id="available_stores_list" class="available-stores-list">
                        <!-- سيتم عرض الفروع المتاحة هنا -->
                    </div>
                </div>
            </div>

            <button type="submit" class="add-btn">إضافة الصنف</button>
        </form>
    </div>
</div>

<!-- Modal for viewing item images -->
<div id="viewImagesModal" class="modal">
    <div class="modal-content" style="max-width: 800px;">
        <span class="close" onclick="closeViewImagesModal()">&times;</span>
        <h2 id="modalItemName">صور الصنف</h2>
        <div id="modalImagesContainer" class="images-gallery">
            <!-- سيتم عرض الصور هنا -->
        </div>
        <div style="text-align: center; margin-top: 20px;">
            <button id="viewAllImagesBtn" class="add-btn" onclick="openFullImagePage()">
                <i class="fas fa-expand"></i> عرض جميع الصور في صفحة منفصلة
            </button>
        </div>
    </div>
</div>

<!-- Modal for image preview -->
<div id="imagePreviewModal" class="image-preview-modal">
    <span class="close" onclick="closeImagePreview()" style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;">&times;</span>
    <img class="image-preview-content" id="previewImage">
</div>

<script>
    // تمرير المتغيرات من PHP إلى JavaScript قبل تحميل ملف items.js
    window.itemsPermissions = {
        canCreate: <?php echo hasPermission('items', 'add_item_from_items') ? 'true' : 'false'; ?>,
        canEdit: <?php echo hasPermission('items', 'edit_item') ? 'true' : 'false'; ?>,
        canDelete: <?php echo hasPermission('items', 'delete_item') ? 'true' : 'false'; ?>,
        canChangeStatus: <?php echo hasPermission('items', 'change_item_status') ? 'true' : 'false'; ?>
    };
    
    window.encrypted_store_id = "<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>";
    window.encrypted_account_id = "<?php echo isset($_SESSION['account_id']) ? $_SESSION['account_id'] : ''; ?>";
    window.encrypted_category_id = "<?php echo isset($re_encrypted_category_id) ? $re_encrypted_category_id : ''; ?>";
</script>

<!-- تضمين ملف JavaScript المنفصل -->
<script src="js/items.js"></script>

<div class="popup-message" id="popupMessage"></div>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
