<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['inventory_id'])) {
    $encrypted_inventory_id = $_POST['inventory_id'];
    $inventory_id = decrypt($encrypted_inventory_id, $key);

    // Fetch inventory details
    $inventory_sql = "SELECT * FROM monthly_inventory WHERE inventory_id = ?";
    $stmt = $conn->prepare($inventory_sql);
    $stmt->bind_param("i", $inventory_id);
    $stmt->execute();
    $inventory_result = $stmt->get_result();
    $inventory = $inventory_result->fetch_assoc();
    $stmt->close();

    // Delete purchases and purchase invoices with status 'confirmed'
    $delete_purchases_sql = "DELETE FROM purchases WHERE invoice_id IN (
                                SELECT invoice_id FROM purchase_invoices WHERE store_id = ? AND status = 'confirmed')";
    $stmt = $conn->prepare($delete_purchases_sql);
    $stmt->bind_param("i", $inventory['store_id']);
    $stmt->execute();
    $stmt->close();

    $delete_purchase_invoices_sql = "DELETE FROM purchase_invoices WHERE store_id = ? AND status = 'confirmed'";
    $stmt = $conn->prepare($delete_purchase_invoices_sql);
    $stmt->bind_param("i", $inventory['store_id']);
    $stmt->execute();
    $stmt->close();

    $delete_wholesales_sql = "DELETE FROM whosales WHERE invoice_id IN (SELECT invoice_id FROM wholesale_invoices WHERE store_id = ? AND status = 'confirmed')";
    $stmt = $conn->prepare($delete_wholesales_sql);
    $stmt->bind_param("i", $inventory['store_id']);
    $stmt->execute();
    $stmt->close();

    $delete_wholesale_invoices_sql = "DELETE FROM wholesale_invoices WHERE store_id = ? AND status = 'confirmed'";
    $stmt = $conn->prepare($delete_wholesale_invoices_sql);
    $stmt->bind_param("i", $inventory['store_id']);
    $stmt->execute();
    $stmt->close();

    // Delete expenses based on store_id
    $delete_expenses_sql = "DELETE FROM expenses WHERE store_id = ?";
    $stmt = $conn->prepare($delete_expenses_sql);
    $stmt->bind_param("i", $inventory['store_id']);
    $stmt->execute();
    $stmt->close();

    // Delete shifts based on store_id and status 'active'
    $delete_shifts_sql = "DELETE FROM shift_closures WHERE store_id = ? AND status = 'active'";
    $stmt = $conn->prepare($delete_shifts_sql);
    $stmt->bind_param("i", $inventory['store_id']);
    $stmt->execute();
    $stmt->close();

    // Delete balance transfers based on store_id
    $delete_balance_transfers_sql = "DELETE FROM balance_transfers WHERE store_id = ?";
    $stmt = $conn->prepare($delete_balance_transfers_sql);
    $stmt->bind_param("i", $inventory['store_id']);
    $stmt->execute();
    $stmt->close();

    // Delete related item transactions
    $delete_itemtransactions_sql = "DELETE FROM itemtransactions WHERE item_id IN (SELECT item_id FROM monthly_inventory_items WHERE inventory_id = ?)";
    $stmt = $conn->prepare($delete_itemtransactions_sql);
    $stmt->bind_param("i", $inventory_id);
    $stmt->execute();
    $stmt->close();

    // Update item quantities and log transactions
    $update_items_sql = "UPDATE items 
                         JOIN monthly_inventory_items ON items.item_id = monthly_inventory_items.item_id 
                         SET items.quantity = monthly_inventory_items.closing_quantity 
                         WHERE monthly_inventory_items.inventory_id = ?";
    $stmt = $conn->prepare($update_items_sql);
    $stmt->bind_param("i", $inventory_id);
    $stmt->execute();
    $stmt->close();

    // Log transactions
    $log_transactions_sql = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) 
                             SELECT item_id, 'opening_balance', inventory_item_id, closing_quantity 
                             FROM monthly_inventory_items 
                             WHERE inventory_id = ?";
    $stmt = $conn->prepare($log_transactions_sql);
    $stmt->bind_param("i", $inventory_id);
    $stmt->execute();
    $stmt->close();

    // Update inventory status to 'confirmed'
    $update_inventory_status_sql = "UPDATE monthly_inventory SET status = 'confirmed' WHERE inventory_id = ?";
    $stmt = $conn->prepare($update_inventory_status_sql);
    $stmt->bind_param("i", $inventory_id);
    $stmt->execute();
    $stmt->close();

    // Redirect to inventory list
    header("Location: inventory.php");
    exit();
} else {
    die("خطأ: طلب غير صالح.");
}
?>
