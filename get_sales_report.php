<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$key = getenv('ENCRYPTION_KEY');

try {
    // Get parameters
    $store_id = null;
    if (isset($_GET['store_id']) && !empty($_GET['store_id'])) {
        $encrypted_store_id = $_GET['store_id'];
        $store_id = decrypt($encrypted_store_id, $key);
    }
    
    $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : null;
    $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : null;
    $status = isset($_GET['status']) ? $_GET['status'] : null;
    $data_type = isset($_GET['data_type']) ? $_GET['data_type'] : 'sales';
    $report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'comprehensive';
    
    // Build WHERE conditions
    $conditions = [];
    $params = [];
    $types = '';
    
    // Base table alias for conditions
    $alias = ($data_type === 'returns' || $data_type === 'service_returns') ? 'r' : 's';

    if ($store_id) {
        $conditions[] = "$alias.store_id = ?";
        $params[] = $store_id;
        $types .= 'i';
    }
    
    if ($start_date) {
        $conditions[] = "DATE($alias.time) >= ?";
        $params[] = $start_date;
        $types .= 's';
    }
    
    if ($end_date) {
        $conditions[] = "DATE($alias.time) <= ?";
        $params[] = $end_date;
        $types .= 's';
    }
    
    if ($status && ($data_type === 'sales' || $data_type === 'services')) {
        $conditions[] = "s.status = ?";
        $params[] = $status;
        $types .= 's';
    }
    
    $where_clause = !empty($conditions) ? " WHERE " . implode(" AND ", $conditions) : "";

    // Main query logic
    if ($data_type === 'sales') {
        $sql = "SELECT 
                    COUNT(DISTINCT CONCAT(s.account_id, '_', DATE(s.time))) as total_orders,
                    COUNT(s.sale_id) as total_items,
                    SUM(s.quantity) as total_quantity,
                    SUM(s.price * s.quantity) as total_amount,
                    SUM(COALESCE((s.price - s.cost) * s.quantity, 0)) as total_profit,
                    SUM(COALESCE(s.discount, 0)) as total_discounts
                FROM sales s
                JOIN items i ON s.item_id = i.item_id
                WHERE i.type != 'service'" . (!empty($conditions) ? " AND " . implode(" AND ", $conditions) : "");
    } elseif ($data_type === 'services') {
        $sql = "SELECT 
                    COUNT(DISTINCT CONCAT(s.account_id, '_', DATE(s.time))) as total_orders,
                    COUNT(s.sale_id) as total_items,
                    SUM(s.quantity) as total_quantity,
                    SUM(s.price * s.quantity) as total_amount,
                    SUM(COALESCE((s.price - s.cost) * s.quantity, 0)) as total_profit,
                    SUM(COALESCE(s.discount, 0)) as total_discounts
                FROM sales s
                JOIN items i ON s.item_id = i.item_id
                WHERE i.type = 'service'" . (!empty($conditions) ? " AND " . implode(" AND ", $conditions) : "");
    } elseif ($data_type === 'service_returns') {
        // مرتجعات الخدمات فقط
        $sql = "SELECT 
                    COUNT(DISTINCT CONCAT(r.account_id, '_', DATE(r.time))) as total_orders,
                    COUNT(r.return_id) as total_items,
                    SUM(r.quantity) as total_quantity,
                    SUM(COALESCE(r.refund_amount, 0)) as total_amount
                FROM returns r
                JOIN items i ON r.item_id = i.item_id
                WHERE i.type = 'service'" . (!empty($conditions) ? " AND " . implode(" AND ", $conditions) : "");
    } else { // returns - جميع المرتجعات أو مرتجعات المبيعات العادية
        $sql = "SELECT 
                    COUNT(DISTINCT CONCAT(r.account_id, '_', DATE(r.time))) as total_orders,
                    COUNT(r.return_id) as total_items,
                    SUM(r.quantity) as total_quantity,
                    SUM(COALESCE(r.refund_amount, 0)) as total_amount
                FROM returns r" . $where_clause;
    }
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_assoc();
    $stmt->close();
    
    // Set default values for main data
    $data['total_orders'] = $data['total_orders'] ?? 0;
    $data['total_items'] = $data['total_items'] ?? 0;
    $data['total_quantity'] = $data['total_quantity'] ?? 0;
    $data['total_amount'] = $data['total_amount'] ?? 0;
    
    if ($data_type === 'sales' || $data_type === 'services') {
        $data['total_profit'] = $data['total_profit'] ?? 0;
        // حساب متوسط قيمة الفاتورة بشكل صحيح
        $data['avg_order_value'] = $data['total_orders'] > 0 ? $data['total_amount'] / $data['total_orders'] : 0;
        $data['total_discounts'] = $data['total_discounts'] ?? 0;
    }
    
    // Additional comprehensive report data for sales and services
    if (($data_type === 'sales' || $data_type === 'services') && $report_type === 'comprehensive') {
        $service_condition = $data_type === 'services' ? "i.type = 'service'" : "i.type != 'service'";
        
        // Status breakdown
        $status_sql = "SELECT 
                        s.status,
                        COUNT(DISTINCT CONCAT(s.account_id, '_', DATE(s.time))) as order_count,
                        SUM(s.price * s.quantity) as amount
                       FROM sales s
                       JOIN items i ON s.item_id = i.item_id
                       WHERE $service_condition" . (!empty($conditions) ? " AND " . implode(" AND ", $conditions) : "") . " GROUP BY s.status";
        
        $status_stmt = $conn->prepare($status_sql);
        if (!empty($params)) {
            $status_stmt->bind_param($types, ...$params);
        }
        $status_stmt->execute();
        $status_result = $status_stmt->get_result();
        
        $status_breakdown = ['confirmed' => ['order_count' => 0, 'amount' => 0], 'pending' => ['order_count' => 0, 'amount' => 0], 'delayed' => ['order_count' => 0, 'amount' => 0]];
        while ($row = $status_result->fetch_assoc()) {
            if (isset($status_breakdown[$row['status']])) {
                $status_breakdown[$row['status']] = ['order_count' => (int)$row['order_count'], 'amount' => (float)$row['amount']];
            }
        }
        $status_stmt->close();
        $data['status_breakdown'] = $status_breakdown;

        // User sales report
        $user_sales_sql = "SELECT
                                a.username,
                                COUNT(DISTINCT CONCAT(s.account_id, '_', DATE(s.time))) as order_count,
                                SUM(s.price * s.quantity) as total_amount,
                                SUM(COALESCE((s.price - s.cost) * s.quantity, 0)) as total_profit
                            FROM sales s
                            JOIN accounts a ON s.account_id = a.account_id
                            JOIN items i ON s.item_id = i.item_id
                            WHERE $service_condition" . (!empty($conditions) ? " AND " . implode(" AND ", $conditions) : "") . " GROUP BY s.account_id, a.username ORDER BY total_amount DESC";

        $user_sales_stmt = $conn->prepare($user_sales_sql);
        if (!empty($params)) {
            $user_sales_stmt->bind_param($types, ...$params);
        }
        $user_sales_stmt->execute();
        $user_sales_result = $user_sales_stmt->get_result();
        
        $user_sales_report = [];
        while ($row = $user_sales_result->fetch_assoc()) {
            $user_sales_report[] = $row;
        }
        $user_sales_stmt->close();
        $data['user_sales_report'] = $user_sales_report;

        // Item Analysis Reports
        $analysis_reports = [];
        $report_types = [
            'most_sold' => 'SUM(s.quantity)',
            'highest_revenue' => 'SUM(s.price * s.quantity)',
            'most_profitable' => 'SUM((s.price - s.cost) * s.quantity)'
        ];

        foreach ($report_types as $type => $aggregation) {
            $item_sql = "SELECT 
                            i.name, 
                            $aggregation as value
                        FROM sales s
                        JOIN items i ON s.item_id = i.item_id
                        WHERE $service_condition" . (!empty($conditions) ? " AND " . implode(" AND ", $conditions) : "") .
                        " GROUP BY s.item_id, i.name
                        HAVING value IS NOT NULL AND value > 0
                        ORDER BY value DESC
                        LIMIT 5";

            $item_stmt = $conn->prepare($item_sql);
            if (!empty($params)) {
                $item_stmt->bind_param($types, ...$params);
            }
            $item_stmt->execute();
            $item_result = $item_stmt->get_result();
            $analysis_reports[$type] = $item_result->fetch_all(MYSQLI_ASSOC);
            $item_stmt->close();
        }
        $data['item_analysis_reports'] = $analysis_reports;
    }
    
    echo json_encode(['success' => true, 'data' => $data]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'حدث خطأ في تحميل البيانات: ' . $e->getMessage()]);
}

$conn->close();
?>