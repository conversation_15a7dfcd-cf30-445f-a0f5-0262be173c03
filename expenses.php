<?php
/**
 * ملف إدارة المصاريف - مدمج مع نظام الصلاحيات
 * 
 * الصلاحيات المطبقة:
 * - view: عرض المصاريف (مطلوبة للوصول للصفحة)
 * - add_expense: إضافة مصروف جديد
 * - edit_expense: تعديل المصاريف
 * - delete_expense: حذف المصاريف
 * - comprehensive_report: عرض التقارير الشاملة
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول لوحدة المصاريف
checkPagePermission('expenses', 'view');

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';
$store_id = decrypt($encrypted_store_id, $key);

function translateExpenseType($type) {
    switch ($type) {
        case 'Credit to Store':
            return 'أجل للمحل';
        case 'Credit on Store':
            return 'أجل على المحل';
        case 'Expenses and Damages':
            return 'مصروفات وتوالف';
        default:
            return $type;
    }
}

// Fetch expenses based on store_id
$query = "SELECT * FROM expenses WHERE store_id = ? ORDER BY expense_date DESC";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();

// Fetch total expenses for each type
$queryTotal = "SELECT expense_type, SUM(amount) as total_amount FROM expenses WHERE store_id = ? GROUP BY expense_type";
$stmtTotal = $conn->prepare($queryTotal);
$stmtTotal->bind_param("i", $store_id);
$stmtTotal->execute();
$resultTotal = $stmtTotal->get_result();

$totals = [
    'Credit to Store' => 0,
    'Credit on Store' => 0,
    'Expenses and Damages' => 0
];

while ($row = $resultTotal->fetch_assoc()) {
    $totals[$row['expense_type']] = $row['total_amount'];
}
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
$stmtTotal->close();
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المصاريف</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">

</head>
<body>
    <?php include 'sidebar.php'; ?>
<div class="container">
    <h2>إدارة المصاريف</h2>
    
    <!-- أزرار العمليات مع فحص الصلاحيات -->
    <?php if (hasPermission('expenses', 'add_expense')): ?>
        <button class="add-btn" id="addExpenseBtn" title="إضافة مصروف جديد">+</button>
    <?php endif; ?>
    
    <?php if (hasPermission('expenses', 'comprehensive_report')): ?>
        <button class="add-btn" id="reportBtn" title="عرض التقرير الشامل">تقرير</button>
    <?php endif; ?>

    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>اسم المصروف</th>
                    <th>نوع المصروف</th>
                    <th>القيمة</th>
                    <th>تاريخ تسجيل</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $translatedType = translateExpenseType($row['expense_type']);
                        echo "<tr id='expense-{$row['expense_id']}'>
                                <td>{$row['expense_name']}</td>
                                <td>{$translatedType}</td>
                                <td>{$row['amount']}</td>
                                <td>{$row['expense_date']}</td>
                                <td>
                                    <div class='action-buttons'>";
                        
                        // زر التعديل مع فحص الصلاحية
                        if (hasPermission('expenses', 'edit_expense')) {
                            echo "<button class='action-btn' onclick='openEditExpenseModal({$row['expense_id']}, \"{$row['expense_name']}\", \"{$row['expense_type']}\", {$row['amount']})' title='تعديل المصروف'>
                                    <i class='fas fa-edit'></i>
                                  </button>";
                        }
                        
                        // زر الحذف مع فحص الصلاحية
                        if (hasPermission('expenses', 'delete_expense')) {
                            echo "<button class='action-btn' onclick='deleteExpense({$row['expense_id']})' title='حذف المصروف'>
                                    <i class='fas fa-trash-alt'></i>
                                  </button>";
                        }
                        
                        echo "    </div>
                                </td>
                              </tr>";
                    }
                } else {
                    echo "<tr><td colspan='5'>لا توجد مصاريف حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>

    <div id="expenseModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>إضافة مصروف جديد</h2>
            <form id="addExpenseForm" method="POST">
                <input type="hidden" name="store_id" value="<?php echo $encrypted_store_id; ?>">
                <input type="text" name="expense_name" class="input-field" placeholder="اسم المصروف" required>
                <select name="expense_type" class="input-field" required>
                    <option value="">اختر نوع المصروف</option>
                    <option value="Credit to Store">أجل للمحل</option>
                    <option value="Credit on Store">أجل على المحل</option>
                    <option value="Expenses and Damages">مصروفات وتوالف</option>
                </select>
                <input type="number" step="0.01" name="amount" class="input-field" placeholder="قيمة المصروف" required>
                <button type="submit" class="add-btn">إضافة المصروف</button>
            </form>
        </div>
    </div>

    <div id="editExpenseModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تعديل المصروف</h2>
            <form id="editExpenseForm" method="POST">
                <input type="hidden" name="edit_expense_id" id="edit_expense_id">
                <input type="text" name="edit_expense_name" id="edit_expense_name" class="input-field" placeholder="اسم المصروف" required>
                <select name="edit_expense_type" id="edit_expense_type" class="input-field" required>
                    <option value="Expenses and Damages">مصروفات وتوالف</option>
                    <option value="Credit to Store">أجل للمحل</option>
                    <option value="Credit on Store">أجل على المحل</option>
                </select>
                <input type="number" name="edit_amount" id="edit_amount" class="input-field" placeholder="القيمة" required>
                <button type="submit" class="add-btn">تعديل المصروف</button>
            </form>
        </div>
    </div>
</div>

<script>
    // متغيرات الصلاحيات
    const permissions = {
        add_expense: <?php echo hasPermission('expenses', 'add_expense') ? 'true' : 'false'; ?>,
        edit_expense: <?php echo hasPermission('expenses', 'edit_expense') ? 'true' : 'false'; ?>,
        delete_expense: <?php echo hasPermission('expenses', 'delete_expense') ? 'true' : 'false'; ?>,
        comprehensive_report: <?php echo hasPermission('expenses', 'comprehensive_report') ? 'true' : 'false'; ?>
    };

    var expenseModal = document.getElementById("expenseModal");
    var addExpenseBtn = document.getElementById("addExpenseBtn");
    var expenseModalClose = document.querySelector("#expenseModal .close");

    var editExpenseModal = document.getElementById("editExpenseModal");
    var editExpenseModalClose = document.querySelector("#editExpenseModal .close");

    // فحص وجود زر الإضافة قبل ربط الحدث
    if (addExpenseBtn) {
        addExpenseBtn.onclick = function() {
            if (!permissions.add_expense) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لإضافة المصاريف'
                });
                return;
            }
            expenseModal.classList.add("active");
        }
    }

    expenseModalClose.onclick = function() {
        expenseModal.classList.remove("active");
    }

    editExpenseModalClose.onclick = function() {
        editExpenseModal.classList.remove("active");
    }

    window.onclick = function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove("active");
        }
    }

    function openEditExpenseModal(expenseId, expenseName, expenseType, amount) {
        if (!permissions.edit_expense) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لتعديل المصاريف'
            });
            return;
        }
        
        document.getElementById("edit_expense_id").value = expenseId;
        document.getElementById("edit_expense_name").value = expenseName;
        document.getElementById("edit_expense_type").value = expenseType;
        document.getElementById("edit_amount").value = amount;
        editExpenseModal.classList.add("active");
    }

    document.getElementById("addExpenseForm").addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(this);

        fetch('add_expense.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم إضافة المصروف بنجاح',
                    showConfirmButton: false,
                    timer: 3000
                });
                expenseModal.classList.remove("active");
                setTimeout(() => {
                    location.reload(); // Reload the page to reflect changes
                }, 3000);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'حدث خطأ أثناء إضافة المصروف',
                    text: data.message
                });
            }
        })
        .catch(error => console.error('Error:', error));
    });

    document.getElementById("editExpenseForm").addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(this);

        fetch('edit_expense.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم تعديل المصروف بنجاح',
                    showConfirmButton: false,
                    timer: 2000
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.message || 'حدث خطأ أثناء تعديل المصروف.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء تعديل المصروف.'
            });
        });
    });

    // فحص وجود زر التقرير قبل ربط الحدث
    const reportBtn = document.getElementById("reportBtn");
    if (reportBtn) {
        reportBtn.addEventListener('click', function() {
            if (!permissions.comprehensive_report) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لعرض التقارير الشاملة'
                });
                return;
            }
            
            // تحديد الوضع الحالي (فاتح أم مظلم)
            const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
            
            // ألوان متكيفة مع الوضع
            const colors = {
                background: isDarkMode ? '#21262d' : '#f8f9fa',
                text: isDarkMode ? '#e6edf3' : '#333333',
                title: isDarkMode ? '#58a6ff' : '#3498db',
                totalBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#e8f5e8',
                totalBorder: isDarkMode ? '#28a745' : '#4CAF50',
                totalText: isDarkMode ? '#4caf50' : '#2e7d32',
                cardBorder: isDarkMode ? '#30363d' : 'transparent',
                shadow: isDarkMode ? '0 4px 15px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)'
            };

            Swal.fire({
                title: 'تقرير المصاريف الشامل',
                html: `
                    <div style="text-align: right; padding: 25px; font-family: 'Cairo', sans-serif;">
                        <div style="text-align: center; margin-bottom: 25px;">
                            <h3 style="color: ${colors.title}; margin-bottom: 8px; font-weight: 700;">
                                <i class="fas fa-chart-pie" style="margin-left: 10px;"></i>
                                ملخص المصاريف
                            </h3>
                            <p style="color: ${colors.text}; opacity: 0.8; margin: 0; font-size: 16px;">
                                <?php echo htmlspecialchars($store_name); ?>
                            </p>
                            <div style="width: 60px; height: 3px; background: ${colors.title}; margin: 15px auto; border-radius: 2px;"></div>
                        </div>
                        
                        <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                            <div style="background: ${colors.background}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.cardBorder}; box-shadow: ${colors.shadow}; transition: transform 0.2s ease;">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div>
                                        <div style="color: ${colors.text}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">أجل على المحل</div>
                                        <div style="color: ${colors.text}; font-size: 24px; font-weight: 700;"><?php echo number_format($totals['Credit on Store'], 2); ?> جنيه</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-arrow-up" style="color: white; font-size: 20px;"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: ${colors.background}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.cardBorder}; box-shadow: ${colors.shadow}; transition: transform 0.2s ease;">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div>
                                        <div style="color: ${colors.text}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">أجل للمحل</div>
                                        <div style="color: ${colors.text}; font-size: 24px; font-weight: 700;"><?php echo number_format($totals['Credit to Store'], 2); ?> جنيه</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #f39c12, #e67e22); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-arrow-down" style="color: white; font-size: 20px;"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: ${colors.background}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.cardBorder}; box-shadow: ${colors.shadow}; transition: transform 0.2s ease;">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div>
                                        <div style="color: ${colors.text}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">مصروفات وتوالف</div>
                                        <div style="color: ${colors.text}; font-size: 24px; font-weight: 700;"><?php echo number_format($totals['Expenses and Damages'], 2); ?> جنيه</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #9b59b6, #8e44ad); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-exclamation-triangle" style="color: white; font-size: 20px;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div style="background: ${colors.totalBg}; padding: 25px; border-radius: 15px; border: 2px solid ${colors.totalBorder}; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, transparent 70%); pointer-events: none;"></div>
                            <div style="position: relative; z-index: 1;">
                                <div style="color: ${colors.totalText}; font-size: 16px; margin-bottom: 10px; font-weight: 600;">
                                    <i class="fas fa-calculator" style="margin-left: 8px;"></i>
                                    إجمالي جميع المصاريف
                                </div>
                                <div style="color: ${colors.totalText}; font-size: 32px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                    <?php echo number_format(array_sum($totals), 2); ?> جنيه
                                </div>
                                <div style="width: 80px; height: 2px; background: ${colors.totalBorder}; margin: 15px auto; border-radius: 1px;"></div>
                                <div style="color: ${colors.totalText}; font-size: 12px; opacity: 0.8;">
                                    تم حساب الإجمالي بناءً على جميع أنواع المصاريف
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 20px; padding: 15px; background: ${isDarkMode ? 'rgba(88, 166, 255, 0.1)' : 'rgba(52, 152, 219, 0.1)'}; border-radius: 10px; border: 1px dashed ${colors.title};">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <i class="fas fa-info-circle" style="color: ${colors.title}; font-size: 16px;"></i>
                                <span style="color: ${colors.title}; font-weight: 600; font-size: 14px;">معلومات إضافية</span>
                            </div>
                            <div style="color: ${colors.text}; font-size: 13px; line-height: 1.5; opacity: 0.9;">
                                • يتم تحديث هذا التقرير تلقائياً عند إضافة أو تعديل المصاريف<br>
                                • جميع المبالغ محسوبة بالجنيه المصري<br>
                                • التقرير يشمل جميع المصاريف المسجلة في هذا الفرع
                            </div>
                        </div>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'إغلاق',
                width: '700px',
                customClass: {
                    popup: isDarkMode ? 'swal2-dark' : '',
                    title: isDarkMode ? 'swal2-title-dark' : '',
                    content: isDarkMode ? 'swal2-content-dark' : ''
                },
                background: isDarkMode ? '#0d1117' : '#ffffff',
                color: isDarkMode ? '#e6edf3' : '#333333',
                showClass: {
                    popup: 'animate__animated animate__fadeInUp animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutDown animate__faster'
                }
            });
        });
    }

    function deleteExpense(expenseId) {
        if (!permissions.delete_expense) {
            Swal.fire({
                icon: 'error',
                title: 'غير مسموح',
                text: 'ليس لديك صلاحية لحذف المصاريف'
            });
            return;
        }
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفه!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('delete_expense.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ expense_id: expenseId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById(`expense-${expenseId}`).remove();
                        Swal.fire(
                            'تم الحذف!',
                            'تم حذف المصروف بنجاح.',
                            'success'
                        );
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message
                        });
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    }
</script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
