<?php
// تحميل نظام الحماية والاتصال بقاعدة البيانات
require_once 'security.php';

// التحقق من صلاحية الوصول
checkPagePermission('cashier_invoices', 'access');

// التأكد من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// التحقق من وجود البيانات المطلوبة
if (!isset($_POST['service_id']) || !isset($_POST['account_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit;
}

$service_id = intval($_POST['service_id']);
$encrypted_account_id = $_POST['account_id'];

// فك تشفير معرف الحساب
$account_id = decrypt($encrypted_account_id, $key);
if ($account_id === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid account ID']);
    exit;
}

try {
    // التحقق من وجود الخد��ة في المفضلة
    $check_query = "SELECT favorite_id FROM user_favorites WHERE item_id = ? AND account_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $service_id, $account_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        // إزالة من المفضلة
        $delete_query = "DELETE FROM user_favorites WHERE item_id = ? AND account_id = ?";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bind_param("ii", $service_id, $account_id);
        
        if ($delete_stmt->execute()) {
            echo json_encode(['success' => true, 'action' => 'removed', 'message' => 'Service removed from favorites']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to remove service from favorites']);
        }
    } else {
        // إضافة للمفضلة
        
        // الحصول على أعلى ترتيب حالي
        $sort_query = "SELECT COALESCE(MAX(sort_order), 0) + 1 as next_sort FROM user_favorites WHERE account_id = ?";
        $sort_stmt = $conn->prepare($sort_query);
        $sort_stmt->bind_param("i", $account_id);
        $sort_stmt->execute();
        $sort_result = $sort_stmt->get_result();
        $next_sort = $sort_result->fetch_assoc()['next_sort'];
        
        // إدراج في المفضلة
        $insert_query = "INSERT INTO user_favorites (item_id, account_id, sort_order, created_at) VALUES (?, ?, ?, NOW())";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("iii", $service_id, $account_id, $next_sort);
        
        if ($insert_stmt->execute()) {
            echo json_encode(['success' => true, 'action' => 'added', 'message' => 'Service added to favorites']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add service to favorites']);
        }
    }
    
} catch (Exception $e) {
    error_log("Service favorites error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

// إغلاق الاتصال
$conn->close();
?>