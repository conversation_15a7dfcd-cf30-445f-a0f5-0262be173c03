<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
$store_id = decrypt($encrypted_store_id, $key);
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';
$account_id = decrypt($encrypted_account_id, $key);

// Check if user is logged in
if (!$account_id) {
    header("Location: index.php");
    exit;
}

// Get store name
$store_query = "SELECT name FROM stores WHERE store_id = ?";
$store_stmt = $conn->prepare($store_query);
$store_stmt->bind_param("i", $store_id);
$store_stmt->execute();
$store_result = $store_stmt->get_result();
$store_data = $store_result->fetch_assoc();
$store_name = $store_data ? $store_data['name'] : 'غير معروف';

// Get FCM tokens for this account
$tokens_query = "SELECT id, token, created_at, last_updated FROM fcm_tokens WHERE account_id = ? AND store_id = ?";
$tokens_stmt = $conn->prepare($tokens_query);
$tokens_stmt->bind_param("ii", $account_id, $store_id);
$tokens_stmt->execute();
$tokens_result = $tokens_stmt->get_result();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة الإشعارات - <?php echo htmlspecialchars($store_name); ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="web_css/style_web.css">
    <style>
        .status-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .token-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .token-text {
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-indicator.active {
            background-color: #28a745;
        }

        .status-indicator.inactive {
            background-color: #dc3545;
        }

        [data-theme='dark'] .status-card {
            background-color: #2d3748;
        }

        [data-theme='dark'] .token-item {
            background-color: #1a202c;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <div class="content">
        <div class="container mt-4">
            <h1 class="mb-4">حالة الإشعارات</h1>

            <div class="row">
                <div class="col-md-12">
                    <div class="status-card">
                        <h3 class="mb-3">حالة تسجيل الإشعارات</h3>

                        <div id="permissionStatus" class="mb-4">
                            <p><strong>حالة إذن الإشعارات:</strong> <span id="permissionStatusText">جاري التحقق...</span></p>
                            <button id="requestPermissionBtn" class="btn btn-primary d-none">طلب إذن الإشعارات</button>
                        </div>

                        <div id="tokenStatus" class="mb-4">
                            <p><strong>حالة رمز FCM:</strong> <span id="tokenStatusText">جاري التحقق...</span></p>
                            <div id="currentToken" class="token-text d-none"></div>
                        </div>

                        <div id="serviceWorkerStatus" class="mb-4">
                            <p><strong>حالة Service Worker:</strong> <span id="serviceWorkerStatusText">جاري التحقق...</span></p>
                        </div>

                        <button id="testNotificationBtn" class="btn btn-success">إرسال إشعار تجريبي</button>
                    </div>

                    <div class="status-card">
                        <h3 class="mb-3">رموز FCM المسجلة</h3>

                        <?php if ($tokens_result->num_rows > 0): ?>
                            <div id="tokensList">
                                <?php while ($row = $tokens_result->fetch_assoc()): ?>
                                    <div class="token-item">
                                        <p><span class="status-indicator active"></span> <strong>تم التسجيل في:</strong> <?php echo htmlspecialchars($row['created_at']); ?></p>
                                        <p><strong>آخر تحديث:</strong> <?php echo htmlspecialchars($row['last_updated']); ?></p>
                                        <p class="token-text"><?php echo htmlspecialchars($row['token']); ?></p>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <p>لا توجد رموز FCM مسجلة لهذا الحساب.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <!-- Firebase Messaging -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>
    <!-- Firebase Initialization -->
    <script src="/js/firebase-init.js"></script>

    <!-- Notification Sound -->
    <audio id="notificationSound" src="/sounds/notification.mp3" preload="auto"></audio>

    <script>
        $(document).ready(function() {
            // Check notification permission
            checkNotificationPermission();

            // Check service worker registration
            checkServiceWorkerRegistration();

            // Handle request permission button
            $('#requestPermissionBtn').on('click', function() {
                requestNotificationPermission();
            });

            // Handle test notification button
            $('#testNotificationBtn').on('click', function() {
                sendTestNotification();
            });
        });

        // Check notification permission
        function checkNotificationPermission() {
            if (!('Notification' in window)) {
                $('#permissionStatusText').text('غير مدعوم في هذا المتصفح');
                return;
            }

            const permission = Notification.permission;

            if (permission === 'granted') {
                $('#permissionStatusText').html('<span class="text-success">تم منح الإذن</span>');
                $('#requestPermissionBtn').addClass('d-none');
            } else if (permission === 'denied') {
                $('#permissionStatusText').html('<span class="text-danger">تم رفض الإذن</span>');
                $('#requestPermissionBtn').removeClass('d-none');
            } else {
                $('#permissionStatusText').html('<span class="text-warning">لم يتم طلب الإذن بعد</span>');
                $('#requestPermissionBtn').removeClass('d-none');
            }
        }

        // Request notification permission
        function requestNotificationPermission() {
            Notification.requestPermission().then(permission => {
                checkNotificationPermission();

                if (permission === 'granted') {
                    // Reload the page to trigger FCM token registration
                    location.reload();
                }
            });
        }

        // Check service worker registration
        function checkServiceWorkerRegistration() {
            if (!('serviceWorker' in navigator)) {
                $('#serviceWorkerStatusText').text('غير مدعوم في هذا المتصفح');
                return;
            }

            navigator.serviceWorker.getRegistrations().then(registrations => {
                if (registrations.length === 0) {
                    $('#serviceWorkerStatusText').html('<span class="text-danger">غير مسجل</span>');
                    return;
                }

                let firebaseSwRegistered = false;

                registrations.forEach(registration => {
                    if (registration.scope.includes('firebase-messaging-sw.js') ||
                        (registration.active && registration.active.scriptURL &&
                         registration.active.scriptURL.includes('firebase-messaging-sw.js'))) {
                        firebaseSwRegistered = true;
                    }
                });

                if (firebaseSwRegistered) {
                    $('#serviceWorkerStatusText').html('<span class="text-success">مسجل بنجاح</span>');
                } else {
                    $('#serviceWorkerStatusText').html('<span class="text-warning">مسجل ولكن ليس لـ Firebase</span>');
                }
            });
        }

        // Send test notification
        function sendTestNotification() {
            // Show loading
            Swal.fire({
                title: 'جاري إرسال إشعار تجريبي...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send notification
            $.ajax({
                url: 'send_test_notification.php',
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    Swal.close();

                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم إرسال الإشعار التجريبي',
                            text: response.message,
                            confirmButtonText: 'حسناً'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: response.message,
                            confirmButtonText: 'حسناً'
                        });
                    }
                },
                error: function() {
                    Swal.close();

                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء إرسال الإشعار التجريبي',
                        confirmButtonText: 'حسناً'
                    });
                }
            });
        }
    </script>
</body>
</html>
