<?php
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

if (!function_exists('base64_url_encode')) {
    function base64_url_encode($input) {
        return strtr(base64_encode($input), '+/=', '-_,');
    }
}

if (!function_exists('base64_url_decode')) {
    function base64_url_decode($input) {
        return base64_decode(strtr($input, '-_,', '+/='));
    }
}

if (!function_exists('encrypt')) {
    function encrypt($data, $key) {
        if (empty($data)) {
            return false; // Do not encrypt empty values
        }
        $method = 'aes-128-cbc';
        $key = substr(hash('sha256', $key, true), 0, 16); // Ensure key length is 16 bytes
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($method));
        $encrypted = openssl_encrypt($data, $method, $key, 0, $iv);
        if ($encrypted === false) {
            throw new Exception('Encryption failed: ' . openssl_error_string());
        }
        return base64_url_encode($encrypted . '::' . $iv);
    }
}

if (!function_exists('decrypt')) {
    function decrypt($data, $key) {
        $method = 'aes-128-cbc';
        $key = substr(hash('sha256', $key, true), 0, 16); // Ensure key length is 16 bytes
        $iv_length = openssl_cipher_iv_length($method);
        $decoded_data = base64_url_decode($data);
        $parts = explode('::', $decoded_data, 2);
        if (count($parts) !== 2) {
            throw new Exception('Decryption failed: Invalid data format.');
        }
        list($encrypted_data, $iv) = $parts;
        if (strlen($iv) !== $iv_length) {
            error_log("IV length is " . strlen($iv) . " bytes, expected " . $iv_length . " bytes.");
            throw new Exception('Decryption failed: Invalid IV length.');
        }
        $decrypted = openssl_decrypt($encrypted_data, $method, $key, 0, $iv);
        if ($decrypted === false) {
            throw new Exception('Decryption failed: ' . openssl_error_string());
        }
        return $decrypted;
    }
}
?>
