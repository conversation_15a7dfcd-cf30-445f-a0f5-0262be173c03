<?php
include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['account_id']) || !isset($input['favorites_order'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit();
    }
    
    $encrypted_account_id = $input['account_id'];
    $favorites_order = $input['favorites_order'];
    
    // فك تشفير معرف الحساب
    $account_id = decrypt($encrypted_account_id, $key);
    
    if ($account_id === false) {
        echo json_encode(['success' => false, 'message' => 'فشل في فك تشفير معرف الحساب']);
        exit();
    }
    
    try {
        // بدء المعاملة
        $conn->begin_transaction();
        
        // تحديث ترتيب المفضلة
        foreach ($favorites_order as $favorite) {
            $item_id = intval($favorite['item_id']);
            $sort_order = intval($favorite['sort_order']);
            
            $stmt = $conn->prepare("UPDATE user_favorites SET sort_order = ? WHERE account_id = ? AND item_id = ?");
            $stmt->bind_param("iii", $sort_order, $account_id, $item_id);
            $stmt->execute();
        }
        
        // تأكيد المعاملة
        $conn->commit();
        
        echo json_encode(['success' => true, 'message' => 'تم تحديث ترتيب المفضلة بنجاح']);
        
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة الخطأ
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
    }
    
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
}
?>