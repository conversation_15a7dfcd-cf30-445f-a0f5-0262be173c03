<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $closure_id = $_POST['closure_id'];

    // Fetch shift closure details before deletion
    $stmt = $conn->prepare("SELECT shift_date, shift_type, shift_amount FROM shift_closures WHERE closure_id = ?");
    $stmt->bind_param("i", $closure_id);
    $stmt->execute();
    $stmt->bind_result($shift_date, $shift_type, $shift_amount);
    $stmt->fetch();
    $stmt->close();

    // Delete the shift closure
    $stmt = $conn->prepare("DELETE FROM shift_closures WHERE closure_id = ?");
    $stmt->bind_param("i", $closure_id);
    $success = $stmt->execute();
    $stmt->close();

    if ($success) {
        // Log the shift closure deletion action
        $key = getenv('ENCRYPTION_KEY');
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'delete', 'shift_closures', ?, ?)";
        $description = "تم حذف الوردية بتاريخ $shift_date من النوع $shift_type بمبلغ $shift_amount";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $closure_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => $stmt->error]);
    }

    $conn->close();
}
?>
