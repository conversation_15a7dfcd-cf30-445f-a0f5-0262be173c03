<?php
include 'db_connection.php';

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

$data = json_decode(file_get_contents('php://input'), true);
if (isset($data['account_id']) && isset($data['status'])) {
    $account_id = $data['account_id'];
    $status = $data['status'];

    $stmt = $conn->prepare("UPDATE accounts SET status = ? WHERE account_id = ?");
    $stmt->bind_param("si", $status, $account_id);
    if ($stmt->execute()) {
        // Log the account status change action
        session_start();
        include 'encryption_functions.php';
        $key = getenv('ENCRYPTION_KEY');
        $logged_in_account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        // Fetch the account name for logging
        $stmt_account = $conn->prepare("SELECT name FROM accounts WHERE account_id = ?");
        $stmt_account->bind_param("i", $account_id);
        $stmt_account->execute();
        $stmt_account->bind_result($account_name);
        $stmt_account->fetch();
        $stmt_account->close();

        // Translate status to Arabic
        $status_ar = $status === 'active' ? 'نشط' : 'متوقف';

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'update', 'accounts', ?, ?)";
        $description = "تم تغيير حالة الحساب $account_name إلى $status_ar";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $logged_in_account_id, $account_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
    $stmt->close();
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}

$conn->close();
?>