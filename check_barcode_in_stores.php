<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['barcode']) && isset($_POST['selected_stores'])) {
    $barcode = $_POST['barcode'];
    $selected_stores_json = $_POST['selected_stores'];
    $selected_stores = json_decode($selected_stores_json, true);
    
    if (!$selected_stores || !is_array($selected_stores)) {
        echo json_encode(['success' => false, 'message' => 'قائمة الفروع غير صحيحة']);
        exit();
    }
    
    $conflicts = [];
    
    foreach ($selected_stores as $store_data) {
        // التحقق من نوع البيانات المرسلة
        if (is_array($store_data) && isset($store_data['store_id'])) {
            // البيانات مرسلة كـ object مع store_id و category_id
            $encrypted_store_id = $store_data['store_id'];
        } elseif (is_string($store_data)) {
            // البيانات مرسلة كـ string مشفر مباشرة
            $encrypted_store_id = $store_data;
        } else {
            // تخطي البيانات غير الصحيحة
            continue;
        }
        
        $store_id = decrypt($encrypted_store_id, $key);
        
        if ($store_id === false) {
            continue; // تخطي الفروع التي لا يمكن فك تشفيرها
        }
        
        // البحث عن أصناف بنفس الباركود في هذا الفرع
        $sql = "SELECT i.item_id, i.name as item_name, s.name as store_name 
                FROM items i 
                JOIN categories c ON i.category_id = c.category_id 
                JOIN stores s ON c.store_id = s.store_id 
                WHERE i.barcode = ? AND s.store_id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("si", $barcode, $store_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $conflicts[] = [
                'store_id' => $store_id,
                'encrypted_store_id' => $encrypted_store_id,
                'store_name' => $row['store_name'],
                'item_id' => $row['item_id'],
                'item_name' => $row['item_name']
            ];
        }
        
        $stmt->close();
    }
    
    echo json_encode([
        'success' => true,
        'conflicts' => $conflicts
    ]);
    
} else {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
}

$conn->close();
?>