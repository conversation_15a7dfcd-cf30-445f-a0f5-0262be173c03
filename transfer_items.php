<?php
/**
 * ملف نقل الأصناف والتصنيفات - مدمج مع نظام الصلاحيات
 * 
 * الصلاحيات المطبقة:
 * - access: الوصول للوحدة (مطلوبة للدخول للصفحة)
 * - transfer_permission: تنفيذ عمليات النقل (مطلوبة لتنفيذ النقل)
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('transfer_items', 'access');

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // فحص صلاحية تنفيذ عمليات النقل
    if (!hasPermission('transfer_items', 'transfer_permission')) {
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح!',
                    text: 'ليس لديك صلاحية لتنفيذ عمليات النقل.',
                    confirmButtonText: 'حسناً'
                });
            });
        </script>";
        // إيقاف معالجة الطلب
        goto skip_processing;
    }

    $source_store_id = $_POST['source_store_id'];
    $target_store_id = $_POST['target_store_id'];
    $transfer_type = $_POST['transfer_type'];
    $selected_ids = $_POST['selected_ids'];
    $error_occurred = false;
    $category_not_found = false;

    if ($transfer_type == 'categories') {
        foreach ($selected_ids as $category_id) {
            $stmt = $conn->prepare("INSERT INTO categories (name, store_id) SELECT name, ? FROM categories WHERE category_id = ?");
            $stmt->bind_param("ii", $target_store_id, $category_id);
            $stmt->execute();
            if ($stmt->error) {
                $error_occurred = true;
                break;
            }
            $stmt->close();
        }
    } elseif ($transfer_type == 'items') {
        foreach ($selected_ids as $item_id) {
            $stmt = $conn->prepare("SELECT name, cost, price, barcode, type, pieces_per_box, category_id FROM items WHERE item_id = ?");
            $stmt->bind_param("i", $item_id);
            $stmt->execute();
            $stmt->bind_result($name, $cost, $price, $barcode, $type, $pieces_per_box, $category_id);
            $stmt->fetch();
            $stmt->close();

            $stmt = $conn->prepare("SELECT category_id FROM categories WHERE name = (SELECT name FROM categories WHERE category_id = ?) AND store_id = ?");
            $stmt->bind_param("ii", $category_id, $target_store_id);
            $stmt->execute();
            $stmt->bind_result($new_category_id);
            $stmt->fetch();
            $stmt->close();

            if (!$new_category_id) {
                $category_not_found = true;
                break;
            }

            $stmt = $conn->prepare("INSERT INTO items (name, cost, price, barcode, type, pieces_per_box, store_id, category_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("sddssiii", $name, $cost, $price, $barcode, $type, $pieces_per_box, $target_store_id, $new_category_id);
            $stmt->execute();
            if ($stmt->error) {
                $error_occurred = true;
                break;
            }
            $stmt->close();
        }
    }

    if ($category_not_found) {
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: 'لا يوجد تصنيف لهذا الصنف في المخزن الهدف.',
                    confirmButtonText: 'حسناً'
                });
            });
        </script>";
    } elseif ($error_occurred) {
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء النقل، يرجى المحاولة مرة أخرى.',
                    confirmButtonText: 'حسناً'
                });
            });
        </script>";
    } else {
        header("Location: transfer_items.php?success=1");
        exit();
    }

    skip_processing:
    // تم تخطي معالجة الطلب بسبب عدم وجود صلاحية
}

// Fetch stores
$stores_sql = "SELECT * FROM stores";
$stores_result = $conn->query($stores_sql);
$stores = [];
if ($stores_result->num_rows > 0) {
    while ($store = $stores_result->fetch_assoc()) {
        $stores[] = $store;
    }
}
$encrypted_store_id = $_GET['store_id'] ?? $_SESSION['store_id'] ?? null;
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقل الأصناف أو التصنيفات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">

    <script>
        function fetchTransferOptions() {
            const sourceStoreId = document.getElementById('source_store_id').value;
            const targetStoreId = document.getElementById('target_store_id').value;
            const transferType = document.querySelector('input[name="transfer_type"]:checked').value;

            if (sourceStoreId && targetStoreId && transferType) {
                fetch(`fetch_transfer_options.php?source_store_id=${sourceStoreId}&target_store_id=${targetStoreId}&transfer_type=${transferType}`)
                    .then(response => response.text())
                    .then(data => {
                        document.getElementById('transfer_options').innerHTML = data;
                        $('#transferTable').DataTable({
                            language: {
                                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                            }
                        });
                    });
            }
        }

        function confirmTransfer(event) {
            event.preventDefault();
            
            // فحص الصلاحيات في JavaScript
            const hasTransferPermission = <?php echo hasPermission('transfer_items', 'transfer_permission') ? 'true' : 'false'; ?>;
            
            if (!hasTransferPermission) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح!',
                    text: 'ليس لديك صلاحية لتنفيذ عمليات النقل.',
                    confirmButtonText: 'حسناً'
                });
                return;
            }
            
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: "لن تتمكن من التراجع عن هذا!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'نعم، انقل!',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('transferForm').submit();
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('success')) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم النقل بنجاح',
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    window.location.href = 'transfer_items.php';
                });
            }
        });
    </script>
</head>
<body>
    <?php include 'sidebar.php'; ?>
<div class="container my-5">
    <h2 class="mb-4 text-center">نقل الأصناف أو التصنيفات</h2>
    
    <?php if (!hasPermission('transfer_items', 'transfer_permission')): ?>
        <div class="alert alert-warning text-center" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>تنبيه:</strong> يمكنك عرض البيانات فقط. ليس لديك صلاحية لتنفيذ عمليات النقل.
        </div>
    <?php endif; ?>
    
    <form id="transferForm" method="POST" action="" onsubmit="confirmTransfer(event)">
        <div class="mb-3">
            <label for="source_store_id" class="form-label">المخزن المصدر</label>
            <select id="source_store_id" name="source_store_id" class="form-select" required onchange="fetchTransferOptions()">
                <option value="">اختر المخزن</option>
                <?php foreach ($stores as $store): ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label for="target_store_id" class="form-label">المخزن الهدف</label>
            <select id="target_store_id" name="target_store_id" class="form-select" required onchange="fetchTransferOptions()">
                <option value="">اختر المخزن</option>
                <?php foreach ($stores as $store): ?>
                    <option value="<?php echo $store['store_id']; ?>"><?php echo $store['name']; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">نوع النقل</label>
            <div class="form-check">
                <input type="radio" id="transfer_categories" name="transfer_type" value="categories" class="form-check-input" required onchange="fetchTransferOptions()">
                <label for="transfer_categories" class="form-check-label">تصنيفات</label>
            </div>
            <div class="form-check">
                <input type="radio" id="transfer_items" name="transfer_type" value="items" class="form-check-input" required onchange="fetchTransferOptions()">
                <label for="transfer_items" class="form-check-label">أصناف</label>
            </div>
        </div>
        <div id="transfer_options"></div>
        <div class="text-center">
            <?php if (hasPermission('transfer_items', 'transfer_permission')): ?>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-exchange-alt"></i> نقل
                </button>
            <?php else: ?>
                <button type="button" class="btn btn-secondary" disabled title="ليس لديك صلاحية لتنفيذ عمليات النقل">
                    <i class="fas fa-lock"></i> نقل (غير مسموح)
                </button>
            <?php endif; ?>
        </div>
    </form>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php 
$conn->close(); 


?>
