<?php
session_start();   // لازم يكون أول حاجة
include 'db_connection.php';
include 'encryption_functions.php';

// تعطيل التخزين المؤقت
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: application/json; charset=utf-8');

$key = getenv('ENCRYPTION_KEY');

if (
    $_SERVER['REQUEST_METHOD'] === 'POST' &&
    isset($_POST['encrypted_invoice_id'])
) {
    $encrypted_invoice_id = $_POST['encrypted_invoice_id'];
    $invoice_id = decrypt($encrypted_invoice_id, $key);

    // التحقق من وجود الفاتورة وحالتها
    $check_stmt = $conn->prepare(
        "SELECT status FROM purchase_invoices WHERE invoice_id = ?"
    );
    $check_stmt->bind_param("i", $invoice_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $check_row = $check_result->fetch_assoc();
    $check_stmt->close();

    if (!$check_row) {
        echo json_encode([
            'success' => false,
            'message' => 'الفاتورة غير موجودة'
        ]);
        exit();
    }

    if (trim($check_row['status']) === 'confirmed') {
        echo json_encode([
            'success' => false,
            'message' => 'الفاتورة مؤكدة مسبقاً ولا يمكن تأكيدها مرة أخرى'
        ]);
        exit();
    }

    // تحقق من أن المستخدم مسجل دخول
    if (!isset($_SESSION['account_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'المستخدم غير مسجل دخول'
        ]);
        exit();
    }

    $conn->autocommit(false);
    $conn->begin_transaction();
    try {
        // Fetch invoice date
        $stmt = $conn->prepare(
            "SELECT created_at FROM purchase_invoices WHERE invoice_id = ?"
        );
        $stmt->bind_param("i", $invoice_id);
        $stmt->execute();
        $stmt->bind_result($invoice_date);
        $stmt->fetch();
        $stmt->close();

        // Fetch purchased items
        $stmt = $conn->prepare(
            "SELECT item_id, quantity, purchases_id FROM purchases WHERE invoice_id = ?"
        );
        $stmt->bind_param("i", $invoice_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();

        // Update items and insert transactions
        while ($row = $result->fetch_assoc()) {
            $item_id     = $row['item_id'];
            $quantity    = $row['quantity'];
            $purchase_id = $row['purchases_id'];

            // Insert into itemtransactions
            $stmt = $conn->prepare(
                "INSERT INTO itemtransactions
                 (item_id, transaction_type, transaction_id_ref, quantity, transaction_date)
                 VALUES (?, 'purchase', ?, ?, ?)"
            );
            $stmt->bind_param("iiis", $item_id, $purchase_id, $quantity, $invoice_date);
            if (!$stmt->execute()) {
                throw new Exception(
                    "Failed to insert into itemtransactions for item_id: $item_id"
                );
            }
            $stmt->close();

            // Update items quantity
            $stmt = $conn->prepare(
                "UPDATE items SET quantity = quantity + ? WHERE item_id = ?"
            );
            $stmt->bind_param("ii", $quantity, $item_id);
            if (!$stmt->execute()) {
                throw new Exception(
                    "Failed to update quantity for item_id: $item_id"
                );
            }
            $stmt->close();
        }

        // Update invoice status with check to avoid double-confirm
        $update_stmt = $conn->prepare(
            "UPDATE purchase_invoices
             SET status = 'confirmed'
             WHERE invoice_id = ? AND status <> 'confirmed'"
        );
        $update_stmt->bind_param("i", $invoice_id);
        if (!$update_stmt->execute()) {
            throw new Exception(
                "Failed to update invoice status for invoice_id: $invoice_id"
            );
        }

        // إذا تم التحديث فعلاً
        if ($update_stmt->affected_rows > 0) {
            // سجل التغيير في اللوج
            $logged_in_account_id = decrypt(
                $_SESSION['account_id'],
                $key
            );
            $description = "تم تأكيد الفاتورة رقم $invoice_id";
            $log_stmt = $conn->prepare(
                "INSERT INTO system_logs
                 (account_id, action_type, table_name, record_id, description)
                 VALUES (?, 'update', 'purchase_invoices', ?, ?)"
            );
            $log_stmt->bind_param(
                "iis",
                $logged_in_account_id,
                $invoice_id,
                $description
            );
            if (!$log_stmt->execute()) {
                throw new Exception(
                    "Failed to log invoice status change for invoice_id: $invoice_id"
                );
            }
            $log_stmt->close();

            // Commit and return success
            $conn->commit();
            $conn->autocommit(true);

            echo json_encode([
                'success'    => true,
                'new_status' => 'confirmed',
                'message'    => 'تم تأكيد الفاتورة بنجاح',
                'invoice_id' => $invoice_id
            ]);
        } else {
            // لم يحدث تغيير في الحالة
            $conn->rollback();
            $conn->autocommit(true);

            echo json_encode([
                'success' => false,
                'message' => 'الفاتورة مؤكدة مسبقاً ولا يمكن تأكيدها مرة أخرى'
            ]);
        }

    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        error_log(
            "Invoice status change error: " .
            $e->getMessage() .
            " | Invoice ID: " .
            $invoice_id
        );

        echo json_encode([
            'success' => false,
            'message' => 'فشل في تحديث حالة الفاتورة: ' . $e->getMessage()
        ]);
    }
    exit();
} else {
    echo json_encode([
        'success' => false,
        'message' => 'طلب غير صالح'
    ]);
}
