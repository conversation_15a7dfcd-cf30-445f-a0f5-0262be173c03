<?php
include 'db_connection.php';
include 'encryption_functions.php';

session_start();

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';

if (!$encrypted_store_id) {
    header('Content-Type: application/json');
    echo json_encode(['notifications' => '<div class="notification-item">Store ID is missing in the session.</div>', 'unreadCount' => 0]);
    exit();
}

$store_id = decrypt($encrypted_store_id, $key);

if (!$store_id) {
    header('Content-Type: application/json');
    echo json_encode(['notifications' => '<div class="notification-item">Failed to decrypt Store ID.</div>', 'unreadCount' => 0]);
    exit();
}

// Debugging: Log the decrypted store_id
error_log("Decrypted Store ID: " . $store_id);

// Fetch notifications from the last week based on store_id
$query = "SELECT id, message, created_at, status FROM notifications WHERE store_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK) ORDER BY created_at DESC";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();

// Debugging: Log the number of notifications fetched
error_log("Number of notifications fetched: " . $result->num_rows);

// Fetch unread notifications count based on store_id
$unreadQuery = "SELECT COUNT(*) as unread_count FROM notifications WHERE store_id = ? AND status = 'notread' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
$unreadStmt = $conn->prepare($unreadQuery);
$unreadStmt->bind_param("i", $store_id);
$unreadStmt->execute();
$unreadResult = $unreadStmt->get_result();
$unreadCount = $unreadResult->fetch_assoc()['unread_count'];

// Debugging: Log the unread notifications count
error_log("Unread notifications count: " . $unreadCount);

$notifications = '<div class="notification-container">';
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $notifications .= '<div class="notification-item ' . ($row['status'] == 'read' ? 'read' : '') . '" id="notification-' . $row['id'] . '">';
        $notifications .= '<p class="notification-message">' . htmlspecialchars($row['message']) . '</p>';
        $notifications .= '<small class="notification-time">' . htmlspecialchars($row['created_at']) . '</small>';
        if ($row['status'] == 'notread') {
            $notifications .= '<button class="mark-read-btn" onclick="markAsRead(' . $row['id'] . ')">تم</button>';
        }
        $notifications .= '</div>';
    }
} else {
    $notifications .= '<div class="notification-item">لا توجد إشعارات حالياً.</div>';
}
$notifications .= '</div>';

header('Content-Type: application/json');
echo json_encode(['notifications' => $notifications, 'unreadCount' => $unreadCount]);
?>
