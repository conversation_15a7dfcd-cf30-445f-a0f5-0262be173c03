<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['item_type'])) {
    $item_type = $_POST['item_type'];
    $category_id = $_POST['category_id'];
    $barcode = isset($_POST['barcode']) ? $_POST['barcode'] : '';
    $auto_generate_barcode = isset($_POST['auto_generate_barcode']) ? 1 : 0;
    $pieces_per_box = 0; // Default value

    // Fetch store ID using category ID
    $store_sql = "SELECT store_id FROM categories WHERE category_id = ?";
    $store_stmt = $conn->prepare($store_sql);
    $store_stmt->bind_param("i", $category_id);
    $store_stmt->execute();
    $store_result = $store_stmt->get_result();
    $store_id = $store_result->fetch_assoc()['store_id'];
    $store_stmt->close();

    if ($item_type == 'piece' || $item_type == 'other') {
        $item_name = $_POST['item_name_piece'];
        $cost = $_POST['cost_piece'];
        $price = $_POST['price1_piece'];
        $quantity = $_POST['quantity_piece'];
    } elseif ($item_type == 'box') {
        $item_name = $_POST['item_name_box'];
        $cost = $_POST['cost_box'];
        $price = $_POST['price1_box'];
        $quantity = $_POST['quantity_box'];
        $pieces_per_box = $_POST['pieces_per_box'];
    } elseif ($item_type == 'fridge') {
        $item_name = $_POST['item_name_fridge'];
        $cost = $_POST['cost_fridge'];
        $price = $_POST['price1_fridge'];
        $quantity = $_POST['quantity_fridge'];
    }

    if ($auto_generate_barcode) {
        // Fetch the highest barcode in the table and increment it by 1
        $barcode_sql = "SELECT MAX(CAST(barcode AS UNSIGNED)) AS max_barcode FROM items";
        $barcode_result = $conn->query($barcode_sql);
        $max_barcode = $barcode_result->fetch_assoc()['max_barcode'];
        $barcode = $max_barcode ? max($max_barcode + 1, 10000) : 10000; // Start from 10000 if no barcodes exist or max barcode is less than 10000
    } else {
        // Check if the manually entered barcode is unique in the current store only
        $barcode_check_sql = "SELECT COUNT(*) AS count FROM items i 
                             JOIN categories c ON i.category_id = c.category_id 
                             WHERE i.barcode = ? AND c.store_id = ?";
        $barcode_check_stmt = $conn->prepare($barcode_check_sql);
        $barcode_check_stmt->bind_param("si", $barcode, $store_id);
        $barcode_check_stmt->execute();
        $barcode_check_result = $barcode_check_stmt->get_result();
        $barcode_count = $barcode_check_result->fetch_assoc()['count'];
        $barcode_check_stmt->close();

        if ($barcode_count > 0) {
            echo json_encode(['success' => false, 'message' => 'الباركود المدخل مكرر في هذا الفرع.']);
            exit();
        }
    }

    $sql = "INSERT INTO items (name, cost, price, quantity, category_id, barcode, type, pieces_per_box, store_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sdddissii", $item_name, $cost, $price, $quantity, $category_id, $barcode, $item_type, $pieces_per_box, $store_id);

    if ($stmt->execute()) {
        $item_id = $stmt->insert_id;

        // Handle image uploads
        if (isset($_FILES['item_images']) && !empty($_FILES['item_images']['name'][0])) {
            $upload_dir = 'uploads/items/';
            
            // Create directory if it doesn't exist
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $uploaded_images = [];
            $total_files = count($_FILES['item_images']['name']);

            for ($i = 0; $i < $total_files; $i++) {
                if ($_FILES['item_images']['error'][$i] == UPLOAD_ERR_OK) {
                    $file_tmp = $_FILES['item_images']['tmp_name'][$i];
                    $file_name = $_FILES['item_images']['name'][$i];
                    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                    
                    // Check if file is an image
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                    if (in_array($file_ext, $allowed_extensions)) {
                        // Generate unique filename and check for duplicates
                        $counter = 0;
                        do {
                            $new_filename = uniqid() . ($counter > 0 ? '_' . $counter : '') . '.' . $file_ext;
                            $file_path = $upload_dir . $new_filename;
                            $counter++;
                            
                            // Check if file path already exists in database
                            $check_path_sql = "SELECT COUNT(*) as count FROM item_images WHERE img_path = ?";
                            $check_path_stmt = $conn->prepare($check_path_sql);
                            $check_path_stmt->bind_param("s", $file_path);
                            $check_path_stmt->execute();
                            $check_result = $check_path_stmt->get_result();
                            $path_exists_in_db = $check_result->fetch_assoc()['count'] > 0;
                            $check_path_stmt->close();
                            
                        } while (file_exists($file_path) || $path_exists_in_db);
                        
                        if (move_uploaded_file($file_tmp, $file_path)) {
                            // Save image path to database
                            $img_sql = "INSERT INTO item_images (item_id, img_path) VALUES (?, ?)";
                            $img_stmt = $conn->prepare($img_sql);
                            $img_stmt->bind_param("is", $item_id, $file_path);
                            $img_stmt->execute();
                            $img_stmt->close();
                            
                            $uploaded_images[] = $file_path;
                        }
                    }
                }
            }
        }

        // Log the item addition action
        session_start();
        $account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description)
                    VALUES (?, 'add', 'items', ?, ?)";
        $description = "تم إضافة صنف جديد باسم $item_name بقيمة $cost وسعر بيع $price";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $item_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        // Add the opening balance transaction
        $transaction_sql = "INSERT INTO itemtransactions (item_id, transaction_type, transaction_id_ref, quantity) VALUES (?, 'opening_balance', 0, ?)";
        $transaction_stmt = $conn->prepare($transaction_sql);
        $transaction_stmt->bind_param("id", $item_id, $quantity);
        $transaction_stmt->execute();
        $transaction_stmt->close();

        echo json_encode(['success' => true, 'message' => 'تم إضافة الصنف بنجاح.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة الصنف: ' . htmlspecialchars($stmt->error)]);
    }
    $stmt->close();
    exit();
}
?>
