<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

$response = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['account_id'])) {
    $account_id = decrypt($_POST['account_id'], $key);
    $username = $_POST['username'];
    $name = $_POST['name'];
    $phone = $_POST['phone'];
    $branch = $_POST['branch']; // Assuming branch is passed as a name
    $password = $_POST['password'];
    $theme = $_POST['theme'] ?? null;

    // Fetch the current account details from the database
    $stmt = $conn->prepare("SELECT username, password, name, phone, img_path, role, status, store_id, theme FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $stmt->bind_result($current_username, $current_password, $current_name, $current_phone, $current_img_path, $current_role, $current_status, $current_store_id, $current_theme);
    $stmt->fetch();
    $stmt->close();

    // Fetch the current store name
    $current_store_name = '';
    if ($current_store_id) {
        $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
        $stmt->bind_param("i", $current_store_id);
        $stmt->execute();
        $stmt->bind_result($current_store_name);
        $stmt->fetch();
        $stmt->close();
    }

    // Check if password is provided and has changed
    $password_changed = false;
    if (!empty($password) && !password_verify($password, $current_password)) {
        $password = password_hash($password, PASSWORD_BCRYPT);
        $password_changed = true;
    } else {
        $password = $current_password;
    }

    // Fetch the store_id based on the selected branch name
    // إذا لم يتم إرسال الفرع (بسبب عدم وجود صلاحية)، استخدم الفرع الحالي
    if (!empty($branch)) {
        $store_id_query = "SELECT store_id FROM stores WHERE name = ?";
        $store_id_stmt = $conn->prepare($store_id_query);
        $store_id_stmt->bind_param("s", $branch);
        $store_id_stmt->execute();
        $store_id_result = $store_id_stmt->get_result();
        $store_id = $store_id_result->fetch_assoc()['store_id'] ?? null;
        $store_id_stmt->close();

        if (!$store_id) {
            $response['success'] = false;
            $response['message'] = 'الفرع المحدد غير موجود.';
            header('Content-Type: application/json');
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit();
        }
    } else {
        // إذا لم يتم إرسال الفرع، استخدم الفرع الحالي
        $store_id = $current_store_id;
    }

    // Fetch the store name based on the store ID
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();

    // Handle profile image upload
    $img_path = $current_img_path;
    if (isset($_FILES['profile_img']) && $_FILES['profile_img']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/profile_images/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        $file_name = uniqid() . '_' . basename($_FILES['profile_img']['name']);
        $target_file = $upload_dir . $file_name;

        if (move_uploaded_file($_FILES['profile_img']['tmp_name'], $target_file)) {
            $img_path = $target_file;

            // Delete the old image if it exists
            if ($current_img_path && file_exists($current_img_path)) {
                unlink($current_img_path);
            }
        } else {
            $response['success'] = false;
            $response['message'] = 'فشل في رفع الصورة.';
            header('Content-Type: application/json');
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit();
        }
    }

    // Check for changes before updating
    $changes = [];
    if ($username !== $current_username) $changes[] = "اسم المستخدم من $current_username إلى $username";
    if ($name !== $current_name) $changes[] = "الاسم من $current_name إلى $name";
    if ($phone !== $current_phone) $changes[] = "الهاتف من $current_phone إلى $phone";
    if ($store_id != $current_store_id) $changes[] = "الفرع من $current_store_name إلى $store_name";
    if ($password_changed) $changes[] = "كلمة المرور";
    if ($theme !== $current_theme) $changes[] = "المظهر إلى $theme";
    if ($img_path !== $current_img_path) $changes[] = "الصورة الشخصية";

    // Only update if there are actual changes
    if (!empty($changes)) {
        // Update the account details, including store_id and theme
        $stmt = $conn->prepare("UPDATE accounts SET username = ?, password = ?, name = ?, phone = ?, img_path = ?, store_id = ?, theme = ? WHERE account_id = ?");
        $stmt->bind_param("sssssisi", $username, $password, $name, $phone, $img_path, $store_id, $theme, $account_id);

        if ($stmt->execute()) {
            // Log the account update action
            session_start();
            $logged_in_account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

            $description = "تم تعديل الحساب: " . implode(", ", $changes);

            $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description)
                        VALUES (?, 'update', 'accounts', ?, ?)";
            $log_stmt = $conn->prepare($log_sql);
            $log_stmt->bind_param("iis", $logged_in_account_id, $account_id, $description);
            $log_stmt->execute();
            $log_stmt->close();

            $response['success'] = true;
            $response['message'] = 'تم تعديل الحساب بنجاح.';
        } else {
            $response['success'] = false;
            $response['message'] = 'حدث خطأ أثناء تعديل الحساب: ' . $stmt->error;
        }
        $stmt->close();
    } else {
        $response['success'] = true;
        $response['message'] = 'لم يتم تعديل أي بيانات.';
    }
} else {
    $response['success'] = false;
    $response['message'] = 'طلب غير صالح.';
}

header('Content-Type: application/json');
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit();
?>