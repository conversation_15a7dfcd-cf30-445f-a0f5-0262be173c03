<?php

require_once 'security.php'; // includes db_connection internally

include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';
$store_id = decrypt($encrypted_store_id, $key);

if (!$store_id) {
    echo "Invalid or missing store ID.";
    exit();
}



// Fetch store name
$stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
$stmt->bind_param("i", $store_id);
$stmt->execute();
$stmt->bind_result($store_name);
$stmt->fetch();
$stmt->close();

?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($store_name) ?></title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <!-- تضمين مكتبة Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body style="background: var(--color-bg); margin: 0; padding: 0;">
<?php include 'sidebar.php'; ?>

    <div class="container">
        <div class="page-header">
            <h2>
                <i class="fas fa-store-alt header-icon"></i>
                <?= htmlspecialchars($store_name) ?>
            </h2>
            <p class="page-subtitle">اختر الخدمة المطلوبة من الاختصارات أدناه</p>
        </div>
        <div class="shortcuts-grid">
            <!-- الرئيسية - لوحة التحكم -->
            <?php if (hasModulePermission('dashboard')): ?>
            <div class="shortcut-card">
                <a href="stores.php">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- التقارير -->
            <?php if (hasModulePermission('reports')): ?>
            <div class="shortcut-card">
                <a href="item_reports.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-chart-line"></i>
                    <span>التقارير</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- التصنيفات -->
            <?php if (hasModulePermission('categories')): ?>
            <div class="shortcut-card">
                <a href="categories.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-th-list"></i>
                    <span>التصنيفات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- الأصناف -->
            <?php if (hasModulePermission('items')): ?>
            <div class="shortcut-card">
                <a href="item_store.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-boxes"></i>
                    <span>الأصناف</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- فواتير الشراء -->
            <?php if (hasModulePermission('purchase_invoices')): ?>
            <div class="shortcut-card">
                <a href="purchase_invoices.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>فواتير الشراء</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- فواتير البيع بالجملة -->
            <?php if (hasModulePermission('wholesale_invoices')): ?>
            <div class="shortcut-card">
                <a href="wholesale_invoices.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-shopping-cart"></i>
                    <span>فواتير البيع بالجملة</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- المبيعات -->
            <?php if (hasModulePermission('sales')): ?>
            <div class="shortcut-card">
                <a href="sales.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-receipt"></i>
                    <span>المبيعات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- الجرد -->
            <?php if (hasModulePermission('inventory')): ?>
            <div class="shortcut-card">
                <a href="inventory.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-warehouse"></i>
                    <span>الجرد</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- إدارة الحسابات -->
            <?php if (hasModulePermission('accounts')): ?>
            <div class="shortcut-card">
                <a href="accounts.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-user-cog"></i>
                    <span>إدارة الحسابات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- طلبات الحسابات -->
            <?php if (hasModulePermission('accounts')): ?>
            <div class="shortcut-card">
                <a href="requested_accounts.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-user-check"></i>
                    <span>طلبات الحسابات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- إدارة الصلاحيات - للمديرين فقط -->
            <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
            <div class="shortcut-card">
                <a href="manage_permissions.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة الصلاحيات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- نقل الأصناف -->
            <?php if (hasModulePermission('transfer_items', 'access')): ?>
            <div class="shortcut-card">
                <a href="transfer_items.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-exchange-alt"></i>
                    <span>نقل الأصناف</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- صلاحيات الأصناف -->
            <?php if (hasModulePermission('expired_items', 'access')): ?>
            <div class="shortcut-card">
                <a href="expired_items.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-hourglass-end"></i>
                    <span>صلاحيات الأصناف</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- المصاريف -->
            <?php if (hasModulePermission('expenses')): ?>
            <div class="shortcut-card">
                <a href="expenses.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المصاريف</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- تقفيل الورديات -->
            <?php if (hasModulePermission('shift_closures')): ?>
            <div class="shortcut-card">
                <a href="shift_closures.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-cash-register"></i>
                    <span>تقفيل الورديات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- تحويلات الرصيد -->
            <?php if (hasModulePermission('balance_transfers')): ?>
            <div class="shortcut-card">
                <a href="balance_transfers.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-wallet"></i>
                    <span>تحويلات الرصيد</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- الإشعارات -->
            <?php if (hasModulePermission('notifications', 'access')): ?>
            <div class="shortcut-card">
                <a href="notifications_page.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-bell"></i>
                    <span>الإشعارات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- إرسال الإشعارات -->
            <?php if (hasModulePermission('send_notifications', 'access')): ?>
            <div class="shortcut-card">
                <a href="send_notifications.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-paper-plane"></i>
                    <span>إرسال الإشعارات</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- سجلات النظام - للمديرين فقط -->
            <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
            <div class="shortcut-card">
                <a href="#" onclick="showSystemLogsMessage(event)">
                    <i class="fas fa-clipboard-list"></i>
                    <span>سجلات النظام</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- حسابي - متاح للجميع -->
            <div class="shortcut-card">
                <a href="profile.php?store_id=<?= urlencode($encrypted_store_id) ?>">
                    <i class="fas fa-user-circle"></i>
                    <span>حسابي</span>
                </a>
            </div>

            <!-- حول النظام - متاح للجميع -->
            <div class="shortcut-card">
                <a href="project_summary.php?store_id=<?= urlencode($encrypted_store_id); ?>">
                    <i class="fas fa-info-circle"></i>
                    <span>حول النظام</span>
                </a>
            </div>

            <!-- تسجيل الخروج - متاح للجميع -->
            <div class="shortcut-card logout" style="background-color: #e74c3c;">
                <a href="#" onclick="confirmLogout(); return false;" style="color: #ffffff;">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </div>
    <style>
    /* Light Theme - متوافق مع style_web.css */
    :root {
        --color-bg: linear-gradient(to right, #f5f7fa, #c3cfe2);
        --color-fg: #333;
        --color-primary: #3f51b5;
        --color-secondary: #ffffff;
        --color-hover: rgba(63, 81, 181, 0.1);
        --color-header-text: #ffffff;
        --color-button-text: #ffffff;
        
        /* ألوان إضافية للاختصارات */
        --color-success: #4caf50;
        --color-warning: #fdb813;
        --color-danger: #e74c3c;
        --color-info: #1f6feb;
        
        /* تدرجات متوافقة مع النظام */
        --gradient-primary: linear-gradient(135deg, #3f51b5 0%, #303f9f 100%);
        --gradient-success: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        --gradient-warning: linear-gradient(135deg, #fdb813 0%, #f57c00 100%);
        --gradient-danger: linear-gradient(135deg, #e74c3c 0%, #c62828 100%);
        --gradient-info: linear-gradient(135deg, #1f6feb 0%, #0d47a1 100%);
        
        /* ظلال للوضع الفاتح */
        --shadow-light: 0 4px 8px rgba(0, 0, 0, 0.1);
        --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
        --shadow-heavy: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    /* Dark Theme - متوافق مع style_web.css */
    [data-theme="dark"] {
        --color-bg: #0d1117;
        --color-fg: #c9d1d9;
        --color-primary: #58a6ff;
        --color-secondary: #161b22;
        --color-hover: rgba(88, 166, 255, 0.08);
        --color-header-text: #ffffff;
        --color-button-text: #ffffff;
        
        /* ألوان إضافية للوضع المظلم */
        --color-success: #2ea043;
        --color-warning: #d29922;
        --color-danger: #f85149;
        --color-info: #79c0ff;
        
        /* تدرجات متوافقة مع الوضع المظلم */
        --gradient-primary: linear-gradient(135deg, #58a6ff 0%, #1f6feb 100%);
        --gradient-success: linear-gradient(135deg, #2ea043 0%, #1f6f31 100%);
        --gradient-warning: linear-gradient(135deg, #d29922 0%, #b5841a 100%);
        --gradient-danger: linear-gradient(135deg, #f85149 0%, #d73a32 100%);
        --gradient-info: linear-gradient(135deg, #79c0ff 0%, #58a6ff 100%);
        
        /* ظلال للوضع المظلم */
        --shadow-light: 0 4px 10px rgba(0, 0, 0, 0.3);
        --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.4);
        --shadow-heavy: 0 15px 40px rgba(0, 0, 0, 0.5);
    }

    .container {
        padding: 30px;
        max-width: 1200px;
        margin: 30px auto;
        background-color: var(--color-secondary);
        border-radius: 20px;
        box-shadow: var(--shadow-medium);
        border: 1px solid rgba(63, 81, 181, 0.1);
    }

    [data-theme="dark"] .container {
        border-color: rgba(88, 166, 255, 0.1);
    }

    .page-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px 0;
        position: relative;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 2px;
        background: var(--gradient-primary);
        border-radius: 1px;
        opacity: 0.3;
    }

    .page-header h2 {
        font-size: 2.8rem;
        font-weight: 800;
        color: var(--color-fg);
        margin-bottom: 15px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
        font-family: 'Cairo', sans-serif;
        letter-spacing: -0.5px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-icon {
        font-size: 2.5rem;
        color: var(--color-primary);
        animation: pulse 2s ease-in-out infinite;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .page-subtitle {
        font-size: 1.2rem;
        color: var(--color-fg);
        opacity: 0.8;
        font-weight: 400;
        margin-top: 10px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
        font-family: 'Cairo', sans-serif;
        letter-spacing: 0.2px;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .shortcuts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 25px;
        margin-top: 40px;
        padding: 0 10px;
    }

    .shortcut-card {
        background: var(--color-secondary);
        border-radius: 15px;
        box-shadow: var(--shadow-light);
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        overflow: hidden;
        text-align: center;
        padding: 25px 20px;
        position: relative;
        border: 1px solid rgba(63, 81, 181, 0.2);
    }



    .shortcut-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 2;
    }

    .shortcut-card a {
        color: var(--color-fg);
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
        transition: all 0.3s ease;
    }

    .shortcut-card i {
        font-size: 3rem;
        background: var(--gradient-primary);
        color: white;
        padding: 20px;
        border-radius: 50%;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow: var(--shadow-light);
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .shortcut-card span {
        font-size: 1.1rem;
        font-weight: 600;
        margin-top: 5px;
        line-height: 1.4;
        transition: all 0.3s ease;
        font-family: 'Cairo', sans-serif;
        letter-spacing: 0.3px;
        text-align: center;
        word-wrap: break-word;
        max-width: 180px;
    }

    .shortcut-card:hover {
        transform: translateY(-6px) scale(1.05);
        box-shadow: var(--shadow-heavy);
        background-color: var(--color-hover);
        border-color: var(--color-primary);
    }

    .shortcut-card:hover::before {
        opacity: 1;
    }

    .shortcut-card:hover i {
        transform: scale(1.15) rotateY(15deg);
        box-shadow: var(--shadow-medium);
    }

    .shortcut-card:hover span {
        color: var(--color-primary);
        transform: translateY(-2px);
    }

    /* تصميم خاص لكل نوع من الاختصارات بناءً على المحتوى - متوافق مع النظام */
    .shortcut-card:has(a[href*="stores.php"]) i { background: var(--gradient-primary); }
    .shortcut-card:has(a[href*="reports"]) i { background: var(--gradient-info); }
    .shortcut-card:has(a[href*="categories"]) i { background: var(--gradient-warning); }
    .shortcut-card:has(a[href*="item"]) i { background: var(--gradient-success); }
    .shortcut-card:has(a[href*="purchase"]) i { background: var(--gradient-danger); }
    .shortcut-card:has(a[href*="wholesale"]) i { background: var(--gradient-success); }
    .shortcut-card:has(a[href*="inventory"]) i { background: var(--gradient-info); }
    .shortcut-card:has(a[href*="account"]) i { background: var(--gradient-primary); }
    .shortcut-card:has(a[href*="permission"]) i { background: var(--gradient-warning); }
    .shortcut-card:has(a[href*="transfer"]) i { background: var(--gradient-info); }
    .shortcut-card:has(a[href*="expired"]) i { background: var(--gradient-warning); }
    .shortcut-card:has(a[href*="expense"]) i { background: var(--gradient-danger); }
    .shortcut-card:has(a[href*="shift"]) i { background: var(--gradient-success); }
    .shortcut-card:has(a[href*="balance"]) i { background: var(--gradient-info); }
    .shortcut-card:has(a[href*="notification"]) i { background: var(--gradient-warning); }
    .shortcut-card:has(a[href*="profile"]) i { background: var(--gradient-primary); }
    .shortcut-card:has(a[href*="project_summary"]) i { background: var(--gradient-info); }
    
    /* احتياطي للمتصفحات التي لا تدعم :has */
    .shortcut-card:nth-child(4n+1) i { background: var(--gradient-primary); }
    .shortcut-card:nth-child(4n+2) i { background: var(--gradient-success); }
    .shortcut-card:nth-child(4n+3) i { background: var(--gradient-warning); }
    .shortcut-card:nth-child(4n+4) i { background: var(--gradient-info); }

    /* تأثيرات خاصة للاختصارات المهمة */
    .shortcut-card.logout {
        background: var(--color-danger);
        color: white;
        border: 1px solid var(--color-danger);
    }

    .shortcut-card.logout a {
        color: white;
    }

    .shortcut-card.logout i {
        background: rgba(255,255,255,0.2);
        color: white;
    }

    .shortcut-card.logout:hover {
        transform: translateY(-6px) scale(1.05);
        box-shadow: var(--shadow-heavy);
        background: #c62828;
    }

    /* تحسينات للجوال */
    @media (max-width: 768px) {
        .shortcuts-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 0 5px;
        }
        
        .shortcut-card {
            padding: 20px 15px;
        }
        
        .shortcut-card i {
            font-size: 2.2rem;
            padding: 15px;
            width: 55px;
            height: 55px;
        }
        
        .shortcut-card span {
            font-size: 0.95rem;
        }
        
        .page-header h2 {
            font-size: 2.2rem;
        }
        
        .page-subtitle {
            font-size: 1.1rem;
        }
    }

    @media (max-width: 480px) {
        .shortcut-card {
            padding: 18px 12px;
        }
        
        .shortcut-card i {
            font-size: 2rem;
            padding: 12px;
            width: 50px;
            height: 50px;
        }
        
        .shortcut-card span {
            font-size: 0.9rem;
        }
        
        .page-header h2 {
            font-size: 1.8rem;
        }
        
        .page-subtitle {
            font-size: 1rem;
        }
    }

    /* تحسينات للأجهزة اللوحية */
    @media (min-width: 769px) and (max-width: 1024px) {
        .shortcuts-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* تحسينات للشاشات الكبيرة */
    @media (min-width: 1400px) {
        .shortcuts-grid {
            grid-template-columns: repeat(6, 1fr);
        }
    }

    /* Dark mode improvements */
    [data-theme="dark"] .shortcut-card {
        background: var(--color-secondary);
        border-color: rgba(255,255,255,0.05);
    }

    [data-theme="dark"] .shortcut-card:hover {
        border-color: rgba(255,255,255,0.1);
    }

    /* تأثير الضوء المتحرك */
    .shortcut-card::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.1) 50%, transparent 60%);
        opacity: 0;
        transition: all 0.6s ease;
        transform: rotate(45deg);
    }

    .shortcut-card:hover::after {
        opacity: 1;
        animation: shine 0.6s ease-out;
    }

    @keyframes shine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    /* تحسين تأثير السحب للجوال */
    .shortcut-card.swiped-left {
        transform: translateX(-20px) scale(0.98);
        box-shadow: var(--shadow-medium);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    /* Loading animation للاختصارات */
    .shortcut-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
        cursor: pointer;
        will-change: transform;
    }

    .shortcut-card:nth-child(1) { animation-delay: 0.1s; }
    .shortcut-card:nth-child(2) { animation-delay: 0.2s; }
    .shortcut-card:nth-child(3) { animation-delay: 0.3s; }
    .shortcut-card:nth-child(4) { animation-delay: 0.4s; }
    .shortcut-card:nth-child(5) { animation-delay: 0.5s; }
    .shortcut-card:nth-child(6) { animation-delay: 0.6s; }
    .shortcut-card:nth-child(7) { animation-delay: 0.7s; }
    .shortcut-card:nth-child(8) { animation-delay: 0.8s; }
    .shortcut-card:nth-child(9) { animation-delay: 0.9s; }
    .shortcut-card:nth-child(10) { animation-delay: 1.0s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* تأثير النقر */
    .shortcut-card:active {
        transform: translateY(-5px) scale(0.98);
        transition: transform 0.1s ease;
    }

    /* تحسين الأداء */
    .shortcut-card,
    .shortcut-card i,
    .shortcut-card span {
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }

    /* تأثير التموج عند النقر */
    .shortcut-card {
        position: relative;
        overflow: hidden;
    }

    .shortcut-card .ripple-effect {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
        transform: scale(0);
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 1;
        pointer-events: none;
    }

    .shortcut-card:active .ripple-effect {
        transform: scale(1);
        opacity: 1;
        transition: all 0.1s ease;
    }

    /* تحسين الرؤية للألوان */
    .shortcut-card i {
        filter: contrast(1.1) brightness(1.05);
    }

    /* Dark mode improvements */
    [data-theme="dark"] .shortcut-card {
        background: var(--color-secondary);
        border: 1px solid rgba(88, 166, 255, 0.2);
    }

    [data-theme="dark"] .shortcut-card:hover {
        background-color: var(--color-hover);
        border-color: var(--color-primary);
    }

    /* تحسينات إضافية لتجربة المستخدم */
    .shortcut-card {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .shortcut-card:focus-within {
        outline: 3px solid var(--color-primary);
        outline-offset: 2px;
        box-shadow: var(--shadow-medium), 0 0 0 3px rgba(74, 144, 226, 0.3);
    }

    .shortcut-card a:focus {
        outline: none;
    }

    /* تحسين التباين للوضع المظلم */
    [data-theme="dark"] .shortcut-card span {
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    /* تأثير ناعم للانتقال بين الصفحات */
    .shortcut-card a {
        border-radius: inherit;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
    }

    /* تحسين الاستجابة للشاشات عالية الدقة */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .shortcut-card {
            border: 0.5px solid rgba(255,255,255,0.1);
        }
        
        .shortcut-card i {
            filter: contrast(1.1) brightness(1.05) saturate(1.1);
        }
    }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    // إضافة تأثير الريبل لجميع الاختصارات
    document.addEventListener('DOMContentLoaded', function() {
        const shortcutCards = document.querySelectorAll('.shortcut-card');
        
        shortcutCards.forEach(card => {
            // إضافة عنصر الريبل
            const ripple = document.createElement('div');
            ripple.className = 'ripple-effect';
            card.appendChild(ripple);
            
            // إضافة مستمع للنقر
            card.addEventListener('click', function(e) {
                ripple.style.transform = 'scale(1)';
                ripple.style.opacity = '1';
                
                setTimeout(() => {
                    ripple.style.transform = 'scale(0)';
                    ripple.style.opacity = '0';
                }, 200);
            });
        });
    });

    function confirmLogout() {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "هل تريد تسجيل الخروج؟",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، سجل الخروج',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = "logout.php";
            }
        });
    }

    // Function to show system logs message
    function showSystemLogsMessage(event) {
        event.preventDefault(); // Prevent default link behavior
        
        Swal.fire({
            title: 'ميزة مغلقة 🔒',
            text: 'هذه الميزة مغلقة لأنها تحت التطوير 🚧👨‍💻',
            icon: 'info',
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'حسناً',
            customClass: {
                popup: 'rtl-popup'
            }
        });
    }

    // Function to show permissions management message
    function showPermissionsMessage(event) {
        event.preventDefault(); // Prevent default link behavior
        
        Swal.fire({
            title: 'ميزة مغلقة 🔒',
            text: 'هذه الميزة مغلقة لأنها تحت التطوير 🚧👨‍💻',
            icon: 'info',
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'حسناً',
            customClass: {
                popup: 'rtl-popup'
            }
        });
    }
    </script>
<?php include 'notifications.php'; ?>
    <style>
        .page-footer {
            z-index: 2000 !important;
        }
    </style>
</body>
</html>

<?php
$conn->close();
?>
