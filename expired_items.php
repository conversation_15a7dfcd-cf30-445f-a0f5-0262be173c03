<?php
/**
 * ملف إدارة صلاح��ات الأصناف منتهية الصلاحية
 * 
 * الصلاحيات المطبقة:
 * - access: الوصول للوحدة (مطلوبة للدخول للصفحة)
 * - expiry_permission: إدارة صلاحيات الأصناف منتهية الصلاحية
 */

include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
// فحص صلاحية الوصول للوحدة
checkPagePermission('expired_items', 'access');

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = $_GET['store_id'] ?? '';
$store_id = null;

// Safely decrypt store_id if provided
if (!empty($encrypted_store_id)) {
    try {
        $store_id = decrypt($encrypted_store_id, $key);
    } catch (Exception $e) {
        // Log the decryption error for debugging purposes
        error_log('expired_items.php - Decryption error: ' . $e->getMessage());
    }
}

// Validate store_id (must be positive integer)
if (empty($store_id) || !ctype_digit((string) $store_id)) {
    http_response_code(400);
    echo "معرّف الفرع غير صالح أو مفقود";
    exit();
}

// Save encrypted id in session for reuse
$_SESSION['store_id'] = $encrypted_store_id;

// معالجة طلبات POST مع فحص الصلاحيات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // فحص صلاحية إدارة صلاحيات الأصناف
    if (!hasPermission('expired_items', 'expiry_permission')) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لإدارة صلاحيات الأصناف']);
        exit();
    }
    
    // معالجة العمليات المختلفة هنا
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_expiry':
                try {
                    $item_name = $_POST['item_name'] ?? '';
                    $new_date = $_POST['new_date'] ?? '';
                    
                    if (empty($item_name) || empty($new_date)) {
                        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
                        exit();
                    }
                    
                    // التحقق من صحة التاريخ
                    $date = DateTime::createFromFormat('Y-m-d', $new_date);
                    if (!$date || $date->format('Y-m-d') !== $new_date) {
                        echo json_encode(['success' => false, 'message' => 'تاريخ غير صحيح']);
                        exit();
                    }
                    
                    // تحديث تاريخ انتهاء الصلاحية
                    $update_query = "UPDATE item_expiry 
                                   SET expiry_date = ? 
                                   WHERE item_id = (SELECT item_id FROM items WHERE name = ?) 
                                   AND store_id = ?";
                    $update_stmt = $conn->prepare($update_query);
                    $update_stmt->bind_param("ssi", $new_date, $item_name, $store_id);
                    
                    if ($update_stmt->execute() && $update_stmt->affected_rows > 0) {
                        $update_stmt->close();
                        echo json_encode(['success' => true, 'message' => 'تم تحديث تاريخ انتهاء الصلاحية بنجاح']);
                    } else {
                        $update_stmt->close();
                        echo json_encode(['success' => false, 'message' => 'فشل في تحديث تاريخ انتهاء الصلاحية']);
                    }
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
                }
                break;
                
            case 'delete_expired_item':
                try {
                    $item_name = $_POST['item_name'] ?? '';
                    
                    if (empty($item_name)) {
                        echo json_encode(['success' => false, 'message' => 'اسم الصنف مطلوب']);
                        exit();
                    }
                    
                    // حذف الصنف من قائمة الأصناف منتهية الصلاحية
                    $delete_query = "DELETE FROM item_expiry 
                                   WHERE item_id = (SELECT item_id FROM items WHERE name = ?) 
                                   AND store_id = ?";
                    $delete_stmt = $conn->prepare($delete_query);
                    $delete_stmt->bind_param("si", $item_name, $store_id);
                    
                    if ($delete_stmt->execute() && $delete_stmt->affected_rows > 0) {
                        $delete_stmt->close();
                        echo json_encode(['success' => true, 'message' => 'تم حذف الصنف من قائمة الأصناف منتهية الصلاحية بنجاح']);
                    } else {
                        $delete_stmt->close();
                        echo json_encode(['success' => false, 'message' => 'فشل في حذف الصنف أو الصنف غير موجود']);
                    }
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
                }
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'عملية غير صحيحة']);
        }
        exit();
    }
}

// Fetch all items based on store_id, ordered by expiry date
$query = "SELECT items.name, item_expiry.expiry_date, item_expiry.quantity 
          FROM item_expiry 
          JOIN items ON item_expiry.item_id = items.item_id 
          WHERE item_expiry.store_id = ?
          ORDER BY item_expiry.expiry_date ASC";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();

$store_name = '';
if (is_numeric($store_id)) {
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar"> 
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات منتهية الصلاحية</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* تحسينات خاصة بصفحة صلاحيات الأصناف */
        .action-buttons-container {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .table-danger {
            background-color: rgba(220, 53, 69, 0.1) !important;
        }
        
        .table-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }
        
        .table-info {
            background-color: rgba(13, 202, 240, 0.1) !important;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25em 0.6em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-primary {
            color: #fff;
            background-color: #007bff;
        }
        
        .alert {
            position: relative;
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.25rem;
        }
        
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .info-card {
            transition: transform 0.2s ease-in-out;
        }
        
        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        
        .table td {
            vertical-align: middle;
        }
        
        .add-btn {
            transition: all 0.3s ease;
        }
        
        .add-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        /* تحسينات للوضع المظلم */
        [data-theme="dark"] .table th {
            background-color: #2d3748;
            color: #e2e8f0;
        }
        
        [data-theme="dark"] .table-danger {
            background-color: rgba(220, 53, 69, 0.2) !important;
        }
        
        [data-theme="dark"] .table-warning {
            background-color: rgba(255, 193, 7, 0.2) !important;
        }
        
        [data-theme="dark"] .table-info {
            background-color: rgba(13, 202, 240, 0.2) !important;
        }
        
        [data-theme="dark"] .info-card {
            background-color: #2d3748 !important;
            color: #e2e8f0;
        }
        
        [data-theme="dark"] .alert-warning {
            background-color: rgba(255, 243, 205, 0.1);
            border-color: rgba(255, 234, 167, 0.3);
            color: #d69e2e;
        }
        
        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .action-buttons-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .action-buttons-container .add-btn {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .info-section .row {
                flex-direction: column;
            }
            
            .info-section .col-md-6 {
                margin-bottom: 15px;
            }
        }
        
        /* تحسين مظهر الجدول على الشاشات الصغيرة */
        @media (max-width: 576px) {
            .table-responsive {
                font-size: 0.875rem;
            }
            
            .action-buttons {
                flex-direction: column;
                gap: 3px;
            }
            
            .action-buttons .add-btn {
                padding: 5px 8px;
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    <div class="container mt-5">
        <h2 class="text-center">
            <i class="fas fa-hourglass-end"></i> 
            صلاحيات الأصناف منتهية الصلاحية
            <?php if (!empty($store_name)): ?>
                - <?= htmlspecialchars($store_name) ?>
            <?php endif; ?>
        </h2>
        
        <!-- أزرار العمليات مع فحص الصلاحيات -->
        <div class="action-buttons-container" style="margin-bottom: 20px;">
            <?php if (hasPermission('expired_items', 'expiry_permission')): ?>
                <button class="add-btn" onclick="window.location.href='register_expiry.php?store_id=<?= urlencode($encrypted_store_id) ?>'" title="إضافة صنف منتهي الصلاحية">
                    <i class="fas fa-plus"></i> إضافة صنف منتهي الصلاحية
                </button>
            <?php endif; ?>
            
        </div>

        

        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th><i class="fas fa-box"></i> اسم المنتج</th>
                        <th><i class="fas fa-calendar-times"></i> تاريخ الانتهاء</th>
                        <th><i class="fas fa-sort-numeric-up"></i> الكمية</th>
                        <th><i class="fas fa-exclamation-circle"></i> حالة الانتهاء</th>
                        <?php if (hasPermission('expired_items', 'expiry_permission')): ?>
                            <th><i class="fas fa-cogs"></i> الإجراءات</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($result->num_rows > 0): ?>
                        <?php while($row = $result->fetch_assoc()): ?>
                            <?php
                            // حساب حالة الانتهاء
                            $expiry_date = new DateTime($row['expiry_date']);
                            $current_date = new DateTime();
                            $days_diff = $current_date->diff($expiry_date)->days;
                            $is_expired = $current_date > $expiry_date;
                            
                            // تحديد لون الصف حسب حالة الانتهاء
                            $row_class = '';
                            $status_text = '';
                            $status_color = '';
                            
                            if ($is_expired) {
                                $row_class = 'table-danger';
                                $status_text = 'منتهي الصلاحية';
                                $status_color = 'color: #dc3545;';
                            } elseif ($days_diff <= 7) {
                                $row_class = 'table-warning';
                                $status_text = "ينتهي خلال $days_diff أيام";
                                $status_color = 'color: #fd7e14;';
                            } elseif ($days_diff <= 30) {
                                $row_class = 'table-info';
                                $status_text = "ينتهي خلال $days_diff يوم";
                                $status_color = 'color: #0dcaf0;';
                            } else {
                                $status_text = "صالح لـ $days_diff يوم";
                                $status_color = 'color: #198754;';
                            }
                            ?>
                            <tr class="<?= $row_class ?>">
                                <td><?= htmlspecialchars($row['name']) ?></td>
                                <td><?= htmlspecialchars($row['expiry_date']) ?></td>
                                <td>
                                    <span class="badge badge-primary"><?= htmlspecialchars($row['quantity']) ?></span>
                                </td>
                                <td style="<?= $status_color ?> font-weight: bold;">
                                    <?= $status_text ?>
                                </td>
                                <?php if (hasPermission('expired_items', 'expiry_permission')): ?>
                                    <td>
                                        <div class='action-buttons'>
                                            <button class='add-btn' onclick='editExpiryItem("<?= htmlspecialchars($row['name']) ?>")' title='تعديل تاريخ الانتهاء' style="background-color: #28a745;">
                                                <i class='fas fa-edit'></i>
                                            </button>
                                            <button class='add-btn' onclick='deleteExpiryItem("<?= htmlspecialchars($row['name']) ?>")' title='حذف من قائمة الأصناف منتهية الصلاحية' style="background-color: #dc3545;">
                                                <i class='fas fa-trash-alt'></i>
                                            </button>
                                        </div>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="<?= hasPermission('expired_items', 'expiry_permission') ? '5' : '4' ?>" class="text-center">
                                <i class="fas fa-info-circle"></i> لا توجد منتجات منتهية الصلاحية في هذا الفرع.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- معلومات إضافية -->
        <div class="info-section" style="margin-top: 30px;">
            <div class="row">
                
                <div class="col-md-6">
                    <div class="info-card" style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;">
                        <h5><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h5>
                        <ul style="margin-bottom: 0;">
                            <li><strong>إجمالي الأصناف:</strong> <?= $result->num_rows ?></li>
                            <li><strong>الفرع:</strong> <?= htmlspecialchars($store_name ?: 'غير محدد') ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <?php include 'notifications.php'; ?>
    </div>

    <!-- JavaScript للتعامل مع العمليات -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // متغيرات الصلاحيات
        const permissions = {
            access: <?php echo hasPermission('expired_items', 'access') ? 'true' : 'false'; ?>,
            expiry_permission: <?php echo hasPermission('expired_items', 'expiry_permission') ? 'true' : 'false'; ?>
        };

        // فحص الصلاحيات في JavaScript
        function editExpiryItem(itemName) {
            if (!permissions.expiry_permission) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لتعديل الأصناف منتهية الصلاحية'
                });
                return;
            }
            
            Swal.fire({
                title: 'تعديل تاريخ انتهاء الصلاحية',
                text: `تعديل تاريخ انتهاء الصلاحية للصنف: ${itemName}`,
                input: 'date',
                inputAttributes: {
                    min: new Date().toISOString().split('T')[0]
                },
                showCancelButton: true,
                confirmButtonText: 'تحديث',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed && result.value) {
                    // إرسال طلب تحديث تاريخ الانتهاء
                    updateExpiryDate(itemName, result.value);
                }
            });
        }

        function deleteExpiryItem(itemName) {
            if (!permissions.expiry_permission) {
                Swal.fire({
                    icon: 'error',
                    title: 'غير مسموح',
                    text: 'ليس لديك صلاحية لحذف الأصناف منتهية الصلاحية'
                });
                return;
            }
            
            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف الصنف "${itemName}" من قائمة الأصناف منتهية الصلاحية؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // إرسال طلب حذف الصنف
                    deleteExpiredItem(itemName);
                }
            });
        }

        function updateExpiryDate(itemName, newDate) {
            // إرسال طلب AJAX لتحديث تاريخ الانتهاء
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_expiry&item_name=${encodeURIComponent(itemName)}&new_date=${newDate}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم التحديث!',
                        text: 'تم تحديث تاريخ انتهاء الصلاحية بنجاح',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء التحديث'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ أثناء الاتصال بالخادم'
                });
            });
        }

        function deleteExpiredItem(itemName) {
            // إرسال طلب AJAX لحذف الصنف
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=delete_expired_item&item_name=${encodeURIComponent(itemName)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحذف!',
                        text: 'تم حذف الصنف من قائمة الأصناف منتهية الصلاحية بنجاح',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء الحذف'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ أثناء الاتصال بالخادم'
                });
            });
        }

        // عرض رسالة ترحيب عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (!permissions.expiry_permission) {
                console.log('المستخدم لديه صلاحية عرض فقط');
            }
        });
    </script>
    
</body>

</html>

<?php
/**
 * تم تحديث ملف expired_items.php لتطبيق نظام الصلاحيات المتقدم
 *
 * التحديثات المطبقة:
 *
 */
?>
