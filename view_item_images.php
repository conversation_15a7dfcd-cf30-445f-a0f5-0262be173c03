<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['item_id'])) {
    $encrypted_item_id = $_GET['item_id'];
    $item_id = decrypt($encrypted_item_id, $key);
    
    if (!is_numeric($item_id)) {
        echo json_encode(['success' => false, 'message' => 'معرف الصنف غير صحيح']);
        exit;
    }
    
    // Get item details including category_id and store_id
    $item_sql = "SELECT i.name, i.category_id, c.store_id 
                 FROM items i 
                 JOIN categories c ON i.category_id = c.category_id 
                 WHERE i.item_id = ?";
    $item_stmt = $conn->prepare($item_sql);
    $item_stmt->bind_param("i", $item_id);
    $item_stmt->execute();
    $item_result = $item_stmt->get_result();
    
    if ($item_result->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'الصنف غير موجود']);
        exit;
    }
    
    $item = $item_result->fetch_assoc();
    $item_stmt->close();
    
    // Get item images
    $images_sql = "SELECT img_id, img_path FROM item_images WHERE item_id = ?";
    $images_stmt = $conn->prepare($images_sql);
    $images_stmt->bind_param("i", $item_id);
    $images_stmt->execute();
    $images_result = $images_stmt->get_result();
    
    $images = [];
    while ($image_row = $images_result->fetch_assoc()) {
        $images[] = $image_row;
    }
    $images_stmt->close();
    
} else {
    echo "معرف الصنف مفقود";
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صور الصنف - <?php echo htmlspecialchars($item['name']); ?></title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .images-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .image-item {
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 10px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
            transition: transform 0.3s;
        }
        .image-item img:hover {
            transform: scale(1.05);
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }
        .modal-content {
            margin: auto;
            display: block;
            width: 80%;
            max-width: 700px;
            max-height: 80%;
            object-fit: contain;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #bbb;
        }
        .no-images {
            text-align: center;
            padding: 50px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>صور الصنف: <?php echo htmlspecialchars($item['name']); ?></h2>
        
        <button onclick="goBack()" class="add-btn" style="margin-bottom: 20px;">
            <i class="fas fa-arrow-right"></i> العودة
        </button>
        
        <?php if (empty($images)): ?>
            <div class="no-images">
                <i class="fas fa-image" style="font-size: 48px; color: #ccc;"></i>
                <p>لا توجد صور لهذا الصنف</p>
            </div>
        <?php else: ?>
            <div class="images-gallery">
                <?php foreach ($images as $image): ?>
                    <div class="image-item">
                        <img src="<?php echo htmlspecialchars($image['img_path']); ?>" 
                             alt="صورة الصنف" 
                             onclick="openModal('<?php echo htmlspecialchars($image['img_path']); ?>')">
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Modal for full-size image -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>
    
    <script>
        function openModal(imageSrc) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = imageSrc;
        }
        
        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }
        
        function goBack() {
            // Try to get the referrer URL
            const referrer = document.referrer;
            
            if (referrer && referrer.includes(window.location.origin)) {
                // If we have a valid referrer from the same origin, go back
                window.location.href = referrer;
            } else {
                // Otherwise, try history.back() first
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    // If no history, redirect to items page with category_id
                    const categoryId = <?php echo json_encode(encrypt($item['category_id'], $key)); ?>;
                    if (categoryId) {
                        window.location.href = 'items.php?category_id=' + encodeURIComponent(categoryId);
                    } else {
                        window.location.href = 'categories.php';
                    }
                }
            }
        }
        
        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
<script src="js/theme.js"></script>

<?php $conn->close(); ?>