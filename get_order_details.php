<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// Decrypt the encrypted account ID
$encrypted_account_id = $_GET['account_id'];
$account_id = decrypt($encrypted_account_id, $key);

if ($account_id === false) {
    error_log("Failed to decrypt account ID."); // Log decryption error
    echo json_encode([]); // Return an empty array if decryption fails
    exit;
}

// نوع البيانات (مبيعات أو مرتجعات أو خدمات) - الافتراضي هو مبيعات
$data_type = isset($_GET['data_type']) ? $_GET['data_type'] : 'sales';

// تحديد الجدول المطلوب بناءً على نوع البيانات
$table_name = ($data_type === 'returns') ? 'returns' : 'sales';

// معاملات النطاق الزمني لتحديد الجلسة
$min_time = isset($_GET['min_time']) ? $_GET['min_time'] : null;
$max_time = isset($_GET['max_time']) ? $_GET['max_time'] : null;

// Support both `order_dates` and `order_date` as comma-separated strings
$order_dates = isset($_GET['order_dates']) 
    ? explode(',', $_GET['order_dates']) 
    : (isset($_GET['order_date']) ? explode(',', $_GET['order_date']) : []);

if ($min_time && $max_time) {
    // جلب تفاصيل الجلسة المحددة بالنطاق الزمني
    if ($table_name === 'returns') {
        $sql = "SELECT r.return_id as sale_id, i.name, r.quantity, r.refund_amount as price, 
                   r.refund_amount AS total_amount, 
                   COALESCE(r.refunded, 0) AS collected, 
                   'confirmed' AS status,
                   r.item_id, DATE(r.time) AS order_date, TIME(r.time) AS sale_time, r.time
            FROM returns r
            JOIN items i ON r.item_id = i.item_id
            WHERE r.account_id = ? AND r.time >= ? AND r.time <= ?";
        
        // إضافة فلترة نوع الصنف للمرتجعات
        if ($data_type === 'services') {
            $sql .= " AND i.type = 'service'";
        } elseif ($data_type === 'sales') {
            $sql .= " AND i.type != 'service'";
        }
        
        $sql .= " ORDER BY r.time ASC";
    } else {
        $sql = "SELECT s.sale_id, i.name, s.quantity, s.price, s.cost,
                   (s.price * s.quantity) AS total_amount, 
                   ((s.price - s.cost) * s.quantity) AS profit,
                   COALESCE(s.collected, 0) AS collected, 
                   s.status, s.item_id, DATE(s.time) AS order_date, TIME(s.time) AS sale_time, s.time
            FROM sales s
            JOIN items i ON s.item_id = i.item_id
            WHERE s.account_id = ? AND s.time >= ? AND s.time <= ?";
        
        // إضافة فلترة نوع الصنف للمبيعات
        if ($data_type === 'services') {
            $sql .= " AND i.type = 'service'";
        } elseif ($data_type === 'sales') {
            $sql .= " AND i.type != 'service'";
        }
        
        $sql .= " ORDER BY s.time ASC";
    }
    
    $params = [$account_id, $min_time, $max_time];
    $types = 'iss';
} else if (!empty($order_dates)) {
    // جلب الطلبات للحساب مفلترة بتواريخ محددة (السلوك القديم)
    $placeholders = implode(',', array_fill(0, count($order_dates), '?'));
    
    if ($table_name === 'returns') {
        $sql = "SELECT r.return_id as sale_id, i.name, r.quantity, r.refund_amount as price, 
                   r.refund_amount AS total_amount, 
                   COALESCE(r.refunded, 0) AS collected, 
                   'confirmed' AS status,
                   r.item_id, DATE(r.time) AS order_date, TIME(r.time) AS sale_time
            FROM returns r
            JOIN items i ON r.item_id = i.item_id
            WHERE r.account_id = ? AND DATE(r.time) IN ($placeholders)";
        
        // إضافة فلترة نوع الصنف للمرتجعات
        if ($data_type === 'services') {
            $sql .= " AND i.type = 'service'";
        } elseif ($data_type === 'sales') {
            $sql .= " AND i.type != 'service'";
        }
    } else {
        $sql = "SELECT s.sale_id, i.name, s.quantity, s.price, s.cost,
                   (s.price * s.quantity) AS total_amount, 
                   ((s.price - s.cost) * s.quantity) AS profit,
                   COALESCE(s.collected, 0) AS collected, 
                   s.status, s.item_id, DATE(s.time) AS order_date, TIME(s.time) AS sale_time
            FROM sales s
            JOIN items i ON s.item_id = i.item_id
            WHERE s.account_id = ? AND DATE(s.time) IN ($placeholders)";
        
        // إضافة فلترة نوع الصنف للمبيعات
        if ($data_type === 'services') {
            $sql .= " AND i.type = 'service'";
        } elseif ($data_type === 'sales') {
            $sql .= " AND i.type != 'service'";
        }
    }
    
    $params = array_merge([$account_id], $order_dates);
    $types = 'i' . str_repeat('s', count($order_dates));
} else {
    // جلب جميع الطلبات للحساب إذا لم يتم تحديد تواريخ
    if ($table_name === 'returns') {
        $sql = "SELECT r.return_id as sale_id, i.name, r.quantity, r.refund_amount as price, 
                   r.refund_amount AS total_amount, 
                   COALESCE(r.refunded, 0) AS collected, 
                   'confirmed' AS status,
                   r.item_id, DATE(r.time) AS order_date, TIME(r.time) AS sale_time
            FROM returns r
            JOIN items i ON r.item_id = i.item_id
            WHERE r.account_id = ?";
        
        // إضافة فلترة نوع الصنف للمرتجعات
        if ($data_type === 'services') {
            $sql .= " AND i.type = 'service'";
        } elseif ($data_type === 'sales') {
            $sql .= " AND i.type != 'service'";
        }
    } else {
        $sql = "SELECT s.sale_id, i.name, s.quantity, s.price, s.cost,
                   (s.price * s.quantity) AS total_amount, 
                   ((s.price - s.cost) * s.quantity) AS profit,
                   COALESCE(s.collected, 0) AS collected, 
                   s.status, s.item_id, DATE(s.time) AS order_date, TIME(s.time) AS sale_time
            FROM sales s
            JOIN items i ON s.item_id = i.item_id
            WHERE s.account_id = ?";
        
        // إضافة فلترة نوع الصنف للمبيعات
        if ($data_type === 'services') {
            $sql .= " AND i.type = 'service'";
        } elseif ($data_type === 'sales') {
            $sql .= " AND i.type != 'service'";
        }
    }
    $params = [$account_id];
    $types = 'i';
}

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    error_log("SQL prepare failed: " . $conn->error); // Log SQL preparation error
    echo json_encode([]); // Return an empty array if the query preparation fails
    exit;
}

$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

if ($result === false) {
    error_log("SQL execution failed: " . $stmt->error); // Log SQL execution error
    echo json_encode([]); // Return an empty array if the query execution fails
    exit;
}

$order_details = [];
while ($row = $result->fetch_assoc()) {
    // حساب الباقي
    $row['remaining'] = $row['total_amount'] - $row['collected'];
    $order_details[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($order_details);
?>
