<?php
// تقييد الوصول للمديرين فقط
$requiredRole = 'admin';
require_once 'security.php';

// التحقق من أن المستخدم مدير
requireAdmin();

require_once 'db_connection.php';
require_once 'encryption_functions.php';

// Set consistent timezone for PHP and MySQL
date_default_timezone_set('UTC');
try {
    $conn->query("SET time_zone = '+00:00'");
} catch (Exception $e) {
    error_log("Failed to set MySQL timezone in system_logs.php: " . $e->getMessage());
}

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$displayTimezone = getenv('APP_TIMEZONE') ?: 'Africa/Cairo';
// سيتم استخدام DISPLAY_TIMEZONE لعرض التوقيت للمستخدمين فقط
define('DISPLAY_TIMEZONE', $displayTimezone);

$key = getenv('ENCRYPTION_KEY');

// Get store_id from URL parameter
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : '';
$store_id = null;
$store_name = 'غير محدد';

if (!empty($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    
    if ($store_id) {
        // Get store name
        $store_query = "SELECT name FROM stores WHERE store_id = ?";
        $store_stmt = $conn->prepare($store_query);
        $store_stmt->bind_param("i", $store_id);
        $store_stmt->execute();
        $store_result = $store_stmt->get_result();
        if ($store_row = $store_result->fetch_assoc()) {
            $store_name = $store_row['name'];
        }
        $store_stmt->close();
    }
}

// معالجة الفلاتر
$action_filter = isset($_GET['action_type']) ? $_GET['action_type'] : '';
$table_filter = isset($_GET['table_name']) ? $_GET['table_name'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// بناء الاستعلام الأساسي
$query = "
    SELECT 
        sl.log_id,
        sl.account_id,
        sl.action_type,
        sl.table_name,
        sl.record_id,
        sl.description,
        sl.created_at,
        a.username,
        a.name
    FROM system_logs sl
    LEFT JOIN accounts a ON sl.account_id = a.account_id
    WHERE 1=1
";

$params = [];
$types = "";

// إضافة الفلاتر
if (!empty($action_filter)) {
    $query .= " AND sl.action_type = ?";
    $params[] = $action_filter;
    $types .= "s";
}

if (!empty($table_filter)) {
    $query .= " AND sl.table_name = ?";
    $params[] = $table_filter;
    $types .= "s";
}

if (!empty($date_from)) {
    $query .= " AND DATE(sl.created_at) >= ?";
    $params[] = $date_from;
    $types .= "s";
}

if (!empty($date_to)) {
    $query .= " AND DATE(sl.created_at) <= ?";
    $params[] = $date_to;
    $types .= "s";
}

if (!empty($search_term)) {
    $query .= " AND (sl.description LIKE ? OR a.username LIKE ? OR a.name LIKE ?)";
    $search_param = "%$search_term%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "sss";
}

$query .= " ORDER BY sl.created_at DESC LIMIT 1000";

// تنفيذ الاستعلام
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

// مصفوفة ربط أسماء الجداول بأسماء الملفات من السايد بار
$table_to_file_mapping = [
    'categories' => 'التصنيفات',
    'items' => 'الأصناف', 
    'purchase_invoices' => 'فواتير الشراء',
    'wholesale_invoices' => 'فواتير البيع بالجملة',
    'monthly_inventory' => 'الجرد',
    'accounts' => 'الحسابات',
    'expenses' => 'المصاريف',
    'shift_closures' => 'تقفيل الورديات',
    'balance_transfers' => 'تحويلات الرصيد',
    'stores' => 'لوحة التحكم',
    'notifications' => 'الإشعارات',
    'system_logs' => 'سجلات النظام',
    'permissions' => 'الصلاحيات',
    'sessions' => 'الجلسات',
    'reports' => 'التقارير',
    'item_reports' => 'التقارير'
];

// مصفوفة ربط أنواع العمليات بالعربية
$action_type_mapping = [
    'INSERT' => 'إضافة',
    'UPDATE' => 'تعديل',
    'DELETE' => 'حذف',
    'LOGIN' => 'تسجيل دخول',
    'LOGOUT' => 'تسجيل خروج',
    'CREATE' => 'إنشاء',
    'MODIFY' => 'تعديل',
    'REMOVE' => 'إزالة',
    'VIEW' => 'عرض',
    'EXPORT' => 'تصدير',
    'IMPORT' => 'استيراد',
    'BACKUP' => 'نسخ احتياطي',
    'RESTORE' => 'استعادة',
    'APPROVE' => 'موافقة',
    'REJECT' => 'رفض',
    'CANCEL' => 'إلغاء',
    'COMPLETE' => 'إكمال',
    'TRANSFER' => 'تحويل',
    'CLOSE' => 'إغلاق',
    'OPEN' => 'فتح',
    'auto_login' => 'دخول تلقائي',
    // أحرف صغيرة
    'insert' => 'إضافة',
    'update' => 'تعديل',
    'delete' => 'حذف',
    'login' => 'تسجيل دخول',
    'logout' => 'تسجيل خروج',
    'create' => 'إنشاء',
    'modify' => 'تعديل',
    'remove' => 'إزالة',
    'view' => 'عرض',
    'export' => 'تصدير',
    'import' => 'استيراد',
    'backup' => 'نسخ احتياطي',
    'restore' => 'استعادة',
    'approve' => 'موافقة',
    'reject' => 'رفض',
    'cancel' => 'إلغاء',
    'complete' => 'إكمال',
    'transfer' => 'تحويل',
    'close' => 'إغلاق',
    'open' => 'فتح'
];

// قائمة الجداول التي يجب إخفاؤها من العرض
$hidden_tables = ['inventory', 'modules'];

// دالة لتحويل اسم الجدول إلى اسم الملف
function getFileNameFromTable($table_name, $mapping, $hidden_tables = []) {
    // إخفاء الجداول غير المرغوب فيها
    if (in_array($table_name, $hidden_tables)) {
        return null;
    }
    return isset($mapping[$table_name]) ? $mapping[$table_name] : $table_name;
}

// دالة لتحويل نوع العملية إلى العربية
function getActionTypeInArabic($action_type, $mapping) {
    return isset($mapping[$action_type]) ? $mapping[$action_type] : $action_type;
}

// دالة لتحويل التوقيت إلى التنسيق العربي 12 ساعة مع إصلاح المنطقة الزمنية
function formatArabicDateTime($datetime) {
    if (empty($datetime)) return 'غير محدد';
    
    try {
        // إنشاء كائن DateTime بالتوقيت العالمي ثم ضبطه إلى المنطقة المحددة فى ملف البيئة
        $dt = new DateTime($datetime, new DateTimeZone('UTC'));
        // اضبط إلى المنطقة الزمنية المخصّصة للعرض (DISPLAY_TIMEZONE)
        $dt->setTimezone(new DateTimeZone(DISPLAY_TIMEZONE));
        
        $timestamp = $dt->getTimestamp();
        
        // تحويل إلى التنسيق المطلوب
        $date = $dt->format('Y-m-d');
        $time = $dt->format('g:i A');
        
        // تحويل AM/PM إلى العربية
        $time = str_replace(['AM', 'PM'], ['ص', 'م'], $time);
        
        // تحويل الأرقام إلى العربية
        $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        $arabic_date = str_replace($english_numbers, $arabic_numbers, $date);
        $arabic_time = str_replace($english_numbers, $arabic_numbers, $time);
        
        // تحويل أسماء الأيام إلى العربية
        $day_of_week = $dt->format('w');
        $arabic_days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        $day_name = $arabic_days[$day_of_week];
        
        // تحويل أسماء الشهور إلى العربية
        $month_num = $dt->format('n');
        $arabic_months = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];
        
        $day = str_replace($english_numbers, $arabic_numbers, $dt->format('j'));
        $year = str_replace($english_numbers, $arabic_numbers, $dt->format('Y'));
        
        return $day_name . '، ' . $day . ' ' . $arabic_months[$month_num] . ' ' . $year . ' - ' . $arabic_time;
        
    } catch (Exception $e) {
        error_log("Error formatting datetime in system_logs.php: " . $e->getMessage());
        return 'تاريخ غير صحيح';
    }
}

// الحصول على قوائم الفلاتر
$action_types_query = "SELECT DISTINCT action_type FROM system_logs WHERE action_type IS NOT NULL ORDER BY action_type";
$action_types_result = $conn->query($action_types_query);

$table_names_query = "SELECT DISTINCT table_name FROM system_logs WHERE table_name IS NOT NULL ORDER BY table_name";
$table_names_result = $conn->query($table_names_query);

// إحصائيات سريع��
$stats_query = "
    SELECT 
        COUNT(*) as total_logs,
        COUNT(DISTINCT account_id) as unique_users,
        COUNT(DISTINCT action_type) as unique_actions,
        COUNT(DISTINCT table_name) as unique_tables
    FROM system_logs
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجلات النظام - Elwaled Market</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        /* تحسينات عامة للصفحة */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            margin-top: 100px; /* Added space for sidebar/header overlap */
            padding: 20px;
        }
        
        .page-header {
            
            color: var(--color-primary);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .page-header h2 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .page-header .subtitle {
            margin-top: 10px;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: var(--color-secondary);
            border: 2px solid var(--color-primary);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .stat-card:hover::before {
            left: 100%;
        }
        
        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }
        
        .stat-icon {
            font-size: 3em;
            color: var(--color-primary);
            margin-bottom: 15px;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        .stat-number {
            font-size: 2.8em;
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: 10px;
            background: linear-gradient(45deg, var(--color-primary), #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            font-size: 1.1em;
            color: var(--color-fg);
            font-weight: 600;
            line-height: 1.4;
        }
        
        .filters-container {
            background: var(--color-secondary);
            border: 2px solid var(--color-primary);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .filters-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), #667eea, var(--color-primary));
            border-radius: 20px 20px 0 0;
        }
        
        .filters-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
            gap: 15px;
        }
        
        .filters-header h3 {
            margin: 0;
            color: var(--color-primary);
            font-size: 1.5em;
            font-weight: 700;
        }
        
        .filters-header .filter-icon {
            font-size: 1.8em;
            color: var(--color-primary);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        .filter-group label {
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--color-primary);
            font-size: 0.95em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-group label i {
            font-size: 1.1em;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 14px;
            background-color: var(--color-secondary);
            color: var(--color-fg);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .filter-group select:focus,
        .filter-group input:focus {
            border-color: var(--color-primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .filter-group select:hover,
        .filter-group input:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* Dark mode support for form inputs */
        [data-theme="dark"] .filter-group select,
        [data-theme="dark"] .filter-group input {
            background-color: #21262d;
            border-color: #30363d;
            color: #c9d1d9;
        }
        
        [data-theme="dark"] .filter-group select:focus,
        [data-theme="dark"] .filter-group input:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 5px rgba(88, 166, 255, 0.4);
        }
        
        .filter-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .filter-buttons .action-btn,
        .filter-buttons .export-btn,
        .filter-buttons .clear-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 140px;
            justify-content: center;
        }
        
        .filter-buttons .action-btn {
            background: linear-gradient(135deg, var(--color-primary), #667eea);
            color: white;
        }
        
        .filter-buttons .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(63, 81, 181, 0.3);
        }
        
        .filter-buttons .export-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .filter-buttons .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }
        
        .filter-buttons .clear-btn {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }
        
        .filter-buttons .clear-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
        }
        
        .logs-section {
            background: var(--color-secondary);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .logs-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997, #28a745);
            border-radius: 20px 20px 0 0;
        }
        
        .logs-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
            border-radius: 15px;
            border: 2px solid rgba(40, 167, 69, 0.2);
        }
        
        .logs-header .logs-count {
            font-size: 1.3em;
            font-weight: 700;
            color: var(--color-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .logs-header .logs-count i {
            font-size: 1.2em;
            color: #28a745;
        }
        
        .log-entry {
            background: var(--color-secondary);
            border: 2px solid #e8f4fd;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .log-entry::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, var(--color-primary), #667eea);
            transition: width 0.3s ease;
        }
        
        .log-entry:hover::before {
            width: 8px;
        }
        
        /* Dark mode support for log entries */
        [data-theme="dark"] .log-entry {
            border-color: #30363d;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .log-entry:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--color-primary);
        }
        
        [data-theme="dark"] .log-entry:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .log-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        
        .log-action {
            background: linear-gradient(135deg, var(--color-primary), #667eea);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: 700;
            box-shadow: 0 3px 10px rgba(63, 81, 181, 0.3);
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }
        
        .log-action:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(63, 81, 181, 0.4);
        }
        
        .log-action i {
            font-size: 0.9em;
        }
        
        .log-table {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            padding: 8px 14px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 700;
            border: 2px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }
        
        .log-table:hover {
            transform: scale(1.05);
            border-color: var(--color-primary);
        }
        
        .log-table i {
            font-size: 0.9em;
            color: var(--color-primary);
        }
        
        .record-id-badge {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 700;
            box-shadow: 0 3px 10px rgba(23, 162, 184, 0.3);
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        
        .record-id-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
        }
        
        .record-id-badge i {
            font-size: 0.8em;
        }
        
        /* Dark mode support for log table badge */
        [data-theme="dark"] .log-table {
            background: #21262d;
            color: #c9d1d9;
        }
        
        .log-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
        }
        
        .log-time {
            color: #6c757d;
            font-size: 0.9em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
            background: rgba(108, 117, 125, 0.1);
            padding: 6px 12px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .log-time:hover {
            background: rgba(108, 117, 125, 0.2);
            transform: scale(1.02);
        }
        
        .log-time i {
            font-size: 0.9em;
            color: var(--color-primary);
        }
        
        /* Dark mode support for log time */
        [data-theme="dark"] .log-time {
            color: #7d8590;
            background: rgba(125, 133, 144, 0.1);
        }
        
        [data-theme="dark"] .log-time:hover {
            background: rgba(125, 133, 144, 0.2);
        }
        
        .log-user {
            color: var(--color-primary);
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 6px;
            background: rgba(63, 81, 181, 0.1);
            padding: 6px 12px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .log-user:hover {
            background: rgba(63, 81, 181, 0.2);
            transform: scale(1.02);
        }
        
        .log-user i {
            font-size: 0.9em;
        }
        
        .log-description {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 12px;
            margin-top: 15px;
            border-right: 5px solid var(--color-primary);
            color: var(--color-fg);
            font-size: 0.95em;
            line-height: 1.6;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .log-description::before {
            content: '\f05a';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 15px;
            right: 15px;
            color: var(--color-primary);
            font-size: 1.1em;
        }
        
        .log-description:hover {
            transform: translateX(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .log-description strong {
            color: var(--color-primary);
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        /* Dark mode support for log description */
        [data-theme="dark"] .log-description {
            background: linear-gradient(135deg, #21262d, #161b22);
            color: #c9d1d9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        [data-theme="dark"] .log-description:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }
        
        .no-logs {
            text-align: center;
            padding: 60px 40px;
            color: #6c757d;
            font-size: 1.3em;
            background: linear-gradient(135deg, rgba(108, 117, 125, 0.05), rgba(108, 117, 125, 0.1));
            border-radius: 20px;
            border: 2px dashed #dee2e6;
            margin: 40px 0;
        }
        
        .no-logs .empty-icon {
            font-size: 4em;
            margin-bottom: 20px;
            color: #dee2e6;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .no-logs .empty-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--color-primary);
        }
        
        .no-logs .empty-subtitle {
            font-size: 1em;
            opacity: 0.8;
        }
        
        /* Dark mode support for no logs message */
        [data-theme="dark"] .no-logs {
            color: #7d8590;
            background: linear-gradient(135deg, rgba(125, 133, 144, 0.05), rgba(125, 133, 144, 0.1));
            border-color: #30363d;
        }
        
        [data-theme="dark"] .no-logs .empty-icon {
            color: #484f58;
        }
        
        .export-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        
        .export-btn:hover {
            background: #218838;
        }
        
        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        
        .clear-btn:hover {
            background: #c82333;
        }
        
        /* Dark mode hover effects for stat cards */
        [data-theme="dark"] .stat-card:hover {
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
        }
        
        /* Dark mode hover effects for filters container */
        [data-theme="dark"] .filters-container {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        /* Additional dark mode improvements */
        [data-theme="dark"] .filters-container h3 {
            color: var(--color-primary);
        }
        
        [data-theme="dark"] .logs-container > div:first-child {
            color: var(--color-primary);
        }
        
        /* Dark mode support for record ID badge */
        .record-id-badge {
            transition: background-color 0.3s ease;
        }
        
        [data-theme="dark"] .record-id-badge {
            background: #0969da !important;
            color: #ffffff !important;
        }
        
        /* Dark mode support for icons */
        [data-theme="dark"] .no-logs .fas {
            color: #484f58 !important;
        }
        
        /* Improved contrast for dark mode */
        [data-theme="dark"] .log-action {
            background: var(--color-primary);
            color: #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        /* Dark mode animation improvements */
        [data-theme="dark"] .log-entry {
            transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.3s ease;
        }
        
        [data-theme="dark"] .log-entry:hover {
            background-color: #21262d;
        }
        
        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .log-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .filter-buttons {
                flex-direction: column;
            }
            
            .filter-buttons button {
                width: 100%;
            }
        }
        .page-footer {
            z-index: 2000 !important;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    
    <div class="container">
        <!-- رأس الصفحة المحسن -->
        <div class="page-header">
            <h2><i class="fas fa-clipboard-list"></i> سجلات النظام</h2>
            <div class="subtitle">مراقبة وتتبع جميع العمليات والأنشطة في النظام</div>
        </div>
        
        <!-- إحصائيات سريعة محسنة -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-list-alt"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['total_logs']) ?></div>
                <div class="stat-label">إجمالي السجلات<br><small>(آخر 30 يوم)</small></div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['unique_users']) ?></div>
                <div class="stat-label">المستخدمين النشطين</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['unique_actions']) ?></div>
                <div class="stat-label">أنواع العمليات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['unique_tables']) ?></div>
                <div class="stat-label">الأقسام المتأثرة</div>
            </div>
        </div>
        
        <!-- فلاتر البحث المحسنة -->
        <div class="filters-container">
            <div class="filters-header">
                <i class="fas fa-filter filter-icon"></i>
                <h3>فلاتر البحث والتصفية المتقدمة</h3>
            </div>
            <form method="GET" action="">
                <?php if (!empty($encrypted_store_id)): ?>
                    <input type="hidden" name="store_id" value="<?= htmlspecialchars($encrypted_store_id) ?>">
                <?php endif; ?>
                
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="action_type">
                            <i class="fas fa-cog"></i>
                            نوع العملية:
                        </label>
                        <select name="action_type" id="action_type">
                            <option value="">جميع العمليات</option>
                            <?php while ($action_row = $action_types_result->fetch_assoc()): ?>
                                <option value="<?= htmlspecialchars($action_row['action_type']) ?>" 
                                        <?= $action_filter === $action_row['action_type'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars(getActionTypeInArabic($action_row['action_type'], $action_type_mapping)) ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="table_name">
                            <i class="fas fa-layer-group"></i>
                            القسم:
                        </label>
                        <select name="table_name" id="table_name">
                            <option value="">جميع الأقسام</option>
                            <?php while ($table_row = $table_names_result->fetch_assoc()): ?>
                                <?php 
                                $display_name = getFileNameFromTable($table_row['table_name'], $table_to_file_mapping, $hidden_tables);
                                if ($display_name !== null): 
                                ?>
                                <option value="<?= htmlspecialchars($table_row['table_name']) ?>" 
                                        <?= $table_filter === $table_row['table_name'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($display_name) ?>
                                </option>
                                <?php endif; ?>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="date_from">
                            <i class="fas fa-calendar-alt"></i>
                            من تاريخ:
                        </label>
                        <input type="date" name="date_from" id="date_from" value="<?= htmlspecialchars($date_from) ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="date_to">
                            <i class="fas fa-calendar-check"></i>
                            إلى تاريخ:
                        </label>
                        <input type="date" name="date_to" id="date_to" value="<?= htmlspecialchars($date_to) ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="search">
                            <i class="fas fa-search"></i>
                            البحث النصي:
                        </label>
                        <input type="text" name="search" id="search" placeholder="ابحث في الوصف أو اسم المستخدم..." value="<?= htmlspecialchars($search_term) ?>">
                    </div>
                </div>
                
                <div class="filter-buttons">
                    <button type="submit" class="action-btn">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                    <button type="button" class="clear-btn" onclick="clearFilters()">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                    <button type="button" class="export-btn" onclick="exportLogs()">
                        <i class="fas fa-download"></i> تصدير النتائج
                    </button>
                </div>
            </form>
        </div>
        
        <!-- عرض السجلات المحسن -->
        <div class="logs-section">
            <?php if ($result->num_rows > 0): ?>
                <div class="logs-header">
                    <div class="logs-count">
                        <i class="fas fa-check-circle"></i>
                        تم العثور على <?= $result->num_rows ?> سجل من أصل آلاف السجلات
                    </div>
                </div>
                
                <div class="logs-container">
                    <?php while ($log = $result->fetch_assoc()): ?>
                        <?php 
                        $table_display_name = getFileNameFromTable($log['table_name'] ?? 'غير محدد', $table_to_file_mapping, $hidden_tables);
                        if ($table_display_name !== null): 
                        ?>
                        <div class="log-entry">
                            <div class="log-header">
                                <div class="log-badges">
                                    <span class="log-action">
                                        <i class="fas fa-bolt"></i>
                                        <?= htmlspecialchars(getActionTypeInArabic($log['action_type'] ?? 'غير محدد', $action_type_mapping)) ?>
                                    </span>
                                    <span class="log-table">
                                        <i class="fas fa-layer-group"></i>
                                        <?= htmlspecialchars($table_display_name) ?>
                                    </span>
                                    <?php if ($log['record_id']): ?>
                                    <span class="record-id-badge">
                                        <i class="fas fa-hashtag"></i>
                                        ID: <?= htmlspecialchars($log['record_id']) ?>
                                    </span>
                                    <?php endif; ?>
                                </div>
                                <div class="log-meta">
                                    <span class="log-time">
                                        <i class="fas fa-clock"></i> 
                                        <?= formatArabicDateTime($log['created_at']) ?>
                                    </span>
                                    <?php if ($log['username'] || $log['name']): ?>
                                        <span class="log-user">
                                            <i class="fas fa-user-circle"></i> 
                                            <?= htmlspecialchars($log['name'] ?? $log['username'] ?? 'مستخدم غير معروف') ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if ($log['description']): ?>
                                <div class="log-description">
                                    <strong>
                                        <i class="fas fa-info-circle"></i>
                                        تفاصيل العملية:
                                    </strong>
                                    <?= nl2br(htmlspecialchars($log['description'])) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    <?php endwhile; ?>
                </div>
            <?php else: ?>
                <div class="no-logs">
                    <div class="empty-icon">
                        <i class="fas fa-search-minus"></i>
                    </div>
                    <div class="empty-title">لا توجد سجلات</div>
                    <div class="empty-subtitle">لا توجد سجلات تطابق معايير البحث المحددة<br>جرب تعديل الفلاتر أو البحث بكلمات مختلفة</div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function clearFilters() {
            const url = new URL(window.location.href);
            const storeId = url.searchParams.get('store_id');
            
            // إنشاء URL جديد مع الاحتفاظ بـ store_id فقط
            let newUrl = window.location.pathname;
            if (storeId) {
                newUrl += '?store_id=' + encodeURIComponent(storeId);
            }
            
            window.location.href = newUrl;
        }
        
        function exportLogs() {
            Swal.fire({
                title: 'تصدير السجلات',
                text: 'هل تريد تصدير السجلات الحالية إلى ملف CSV؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'نعم، تصدير',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // إنشاء رابط التصدير مع نفس المعاملات الحالية
                    const currentParams = new URLSearchParams(window.location.search);
                    currentParams.set('export', 'csv');
                    
                    window.location.href = 'export_system_logs.php?' + currentParams.toString();
                }
            });
        }
        
        // تحسين تجربة المستخدم - تطبيق الفلاتر تلقائياً عند التغيير
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة مؤشر التحميل
            function showLoadingIndicator() {
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-indicator';
                loadingDiv.innerHTML = `
                    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                                background: rgba(0,0,0,0.5); z-index: 9999; display: flex; 
                                align-items: center; justify-content: center;">
                        <div style="background: white; padding: 30px; border-radius: 15px; 
                                    text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                            <i class="fas fa-spinner fa-spin" style="font-size: 2em; color: var(--color-primary); margin-bottom: 15px;"></i>
                            <div style="font-weight: bold; color: var(--color-primary);">جاري تحميل السجلات...</div>
                        </div>
                    </div>
                `;
                document.body.appendChild(loadingDiv);
            }
            
            const dateInputs = document.querySelectorAll('input[type="date"]');
            const selectInputs = document.querySelectorAll('select');
            
            // تطبيق الفلاتر تلقائياً عند تغيير التواريخ أو القوائم المنسدلة
            [...dateInputs, ...selectInputs].forEach(input => {
                input.addEventListener('change', function() {
                    showLoadingIndicator();
                    setTimeout(() => {
                        document.querySelector('form').submit();
                    }, 300);
                });
            });
            
            // البحث النصي مع تأخير
            const searchInput = document.getElementById('search');
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        showLoadingIndicator();
                        document.querySelector('form').submit();
                    }
                }, 800);
            });
        });
        
        // إضافة تأثيرات بصرية محسنة للسجلات
        document.addEventListener('DOMContentLoaded', function() {
            const logEntries = document.querySelectorAll('.log-entry');
            
            // استخدام Intersection Observer لتحسين الأداء
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0) scale(1)';
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.05 });
            
            logEntries.forEach((entry) => {
                entry.style.opacity = '1';
                entry.style.transform = 'none';
                entry.style.transition = 'none';
                observer.observe(entry);
            });
            
            // تحسين الإحصائيات مع تأثير العد التصاعدي
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent.replace(/,/g, ''));
                if (finalValue > 0) {
                    let currentValue = 0;
                    const increment = finalValue / 30;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        stat.textContent = Math.floor(currentValue).toLocaleString('en-US');
                    }, 50);
                }
            });
            
            // تحسين تجربة الأزرار مع تأثير الموجة
            const buttons = document.querySelectorAll('.action-btn, .export-btn, .clear-btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.5);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => ripple.remove(), 600);
                });
            });
        });
        
        // إضافة CSS للتأثيرات الإضافية
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            .filter-group input:focus,
            .filter-group select:focus {
                animation: focusPulse 2s infinite;
            }
            
            @keyframes focusPulse {
                0% { box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1); }
                50% { box-shadow: 0 0 0 6px rgba(63, 81, 181, 0.2); }
                100% { box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

<?php
$stmt->close();
$conn->close();
?>