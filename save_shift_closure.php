<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
$response = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $account_id = decrypt($_POST['account_id'], $key);
    $store_id = decrypt($_POST['store_id'], $key);
    $shift_date = $_POST['shift_date'];
    $shift_type = $_POST['shift_type'];
    $shift_amount = $_POST['shift_amount'];
    $notes = $_POST['notes'];
    $purchases = $_POST['purchases'] ?? 0;

    if (!$account_id || !$store_id) {
        $response['success'] = false;
        $response['message'] = 'Invalid account or store ID.';
        echo json_encode($response);
        exit();
    }

    $stmt = $conn->prepare("INSERT INTO shift_closures (store_id, account_id, shift_date, shift_type, shift_amount, purchases, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("iissdds", $store_id, $account_id, $shift_date, $shift_type, $shift_amount, $purchases, $notes);

    if ($stmt->execute()) {
        $shift_closure_id = $stmt->insert_id; // Get the ID of the newly created shift closure

        // Log the shift closure addition action
        session_start();
        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'add', 'shift_closures', ?, ?)";
        $description = "تم إضافة وردية جديدة بتاريخ $shift_date من النوع $shift_type بمبلغ $shift_amount";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $account_id, $shift_closure_id, $description);
        $log_stmt->execute();
        $log_stmt->close();

        $response['success'] = true;
        $response['message'] = 'Shift closure saved successfully.';
    } else {
        $response['success'] = false;
        $response['message'] = 'Error saving shift closure: ' . $stmt->error;
    }
    $stmt->close();
} else {
    $response['success'] = false;
    $response['message'] = 'Invalid request.';
}

header('Content-Type: application/json');
echo json_encode($response);
exit();
?>
