<?php
/**
 * Firebase Scripts Integration
 * This file includes all necessary Firebase scripts and initialization for notifications
 */
?>

<script>
// Check if Firebase is already loaded to prevent duplicate loading
if (typeof firebase === 'undefined') {
    console.log('Loading Firebase libraries...');

    // Load Firebase App
    const firebaseAppScript = document.createElement('script');
    firebaseAppScript.src = 'https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js';
    firebaseAppScript.onload = function() {
        // Load Firebase Messaging after App is loaded
        const firebaseMessagingScript = document.createElement('script');
        firebaseMessagingScript.src = 'https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js';
        firebaseMessagingScript.onload = function() {
            // Load Firebase initialization after Messaging is loaded
            const firebaseInitScript = document.createElement('script');
            firebaseInitScript.src = '/js/firebase-init.js';
            document.head.appendChild(firebaseInitScript);
        };
        document.head.appendChild(firebaseMessagingScript);
    };
    document.head.appendChild(firebaseAppScript);
} else {
    console.log('Firebase already loaded, skipping library loading');

    // Just load the initialization script if Firebase is already available
    if (typeof firebase.messaging !== 'undefined') {
        const firebaseInitScript = document.createElement('script');
        firebaseInitScript.src = '/js/firebase-init.js';
        document.head.appendChild(firebaseInitScript);
    }
}
</script>

<!-- Notification Sound -->
<audio id="notificationSound" src="/sounds/notification.mp3" preload="auto"></audio>

<script>
// Global notification functions
function showNotifications() {
    // Get current store_id from URL
    const urlParams = new URLSearchParams(window.location.search);
    const storeId = urlParams.get('store_id');

    // Navigate to notifications page with store_id if available
    if (storeId) {
        window.location.href = `notifications_page.php?store_id=${storeId}`;
    } else {
        window.location.href = 'notifications_page.php';
    }
}

// Function to update unread notifications count
function updateUnreadCount() {
    // Get current store_id from URL
    const urlParams = new URLSearchParams(window.location.search);
    const storeId = urlParams.get('store_id');

    // Fetch unread count
    fetch(`get_unread_count.php${storeId ? `?store_id=${storeId}` : ''}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const countElement = document.querySelector('.notification-count');
                if (countElement) {
                    countElement.textContent = data.count;

                    // Show/hide count badge based on count
                    if (data.count > 0) {
                        countElement.style.display = 'inline-block';
                    } else {
                        countElement.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error updating unread count:', error);
        });
}

// Update unread count when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateUnreadCount();

    // Update count every 30 seconds
    setInterval(updateUnreadCount, 30000);
});

// Override the updateUnreadCount function from firebase-init.js
window.updateUnreadCount = updateUnreadCount;
</script>

<style>
/* Notification icon styles */
.notification-icon-container {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--color-bg);
    transition: all 0.3s ease;
}

.notification-icon-container:hover {
    background-color: var(--color-primary);
    color: var(--color-bg);
    transform: scale(1.1);
}

.notification-icon-container i {
    font-size: 20px;
    color: var(--color-primary);
    transition: color 0.3s ease;
}

.notification-icon-container:hover i {
    color: var(--color-bg);
}

.notification-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    min-width: 20px;
    padding: 0 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
}

.notification-count:empty,
.notification-count[data-count="0"] {
    display: none !important;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Notification bell animation */
.notification-icon-container:hover i {
    animation: bellRing 0.5s ease-in-out;
}

@keyframes bellRing {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(15deg); }
    75% { transform: rotate(-15deg); }
}

/* Dark mode support for notifications */
[data-theme='dark'] .notification-icon-container {
    background-color: var(--color-bg-dark);
}

[data-theme='dark'] .notification-icon-container i {
    color: var(--color-primary-dark);
}

[data-theme='dark'] .notification-icon-container:hover {
    background-color: var(--color-primary-dark);
}

[data-theme='dark'] .notification-icon-container:hover i {
    color: var(--color-bg-dark);
}

/* Notification badge in sidebar */
.notification-badge {
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 8px;
    display: inline-block;
    min-width: 18px;
    text-align: center;
    animation: pulse 2s infinite;
}

.notification-badge:empty {
    display: none !important;
}

/* Sidebar notification styles */
#sidebar .notification-badge {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 16px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#sidebar li {
    position: relative;
}

/* High z-index for all notification elements */
.notification-icon-container {
    z-index: 9999 !important;
    position: relative;
}

/* Ensure notification count is visible */
.notification-count {
    position: absolute !important;
    top: -8px !important;
    right: -8px !important;
    background-color: #ff4757 !important;
    color: white !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    font-weight: bold !important;
    min-width: 20px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    animation: pulse 2s infinite !important;
    z-index: 10000 !important;
    line-height: 1 !important;
    text-align: center !important;
}

.notification-count:empty {
    display: none !important;
}

/* Notification badge in sidebar */
.notification-badge {
    background-color: #ff4757 !important;
    color: white !important;
    border-radius: 50% !important;
    padding: 2px 6px !important;
    font-size: 11px !important;
    font-weight: bold !important;
    margin-right: 8px !important;
    display: inline-block !important;
    min-width: 18px !important;
    text-align: center !important;
    animation: pulse 2s infinite !important;
    line-height: 1.2 !important;
}

.notification-badge:empty {
    display: none !important;
}

/* Force visibility for testing */
.notification-icon-container .notification-count,
#sidebar .notification-badge {
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure SweetAlert notifications appear above everything */
.swal2-container {
    z-index: 99999 !important;
}

.swal2-popup {
    z-index: 99999 !important;
}

/* Firebase notification styling */
.firebase-notification {
    z-index: 99999 !important;
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: #fff !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    padding: 15px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    max-width: 350px !important;
}

/* Firebase notification container */
.firebase-notification-container {
    z-index: 99999 !important;
}

.swal2-container.firebase-notification-container {
    z-index: 99999 !important;
}

/* Toast notifications should appear above everything */
.swal2-toast {
    z-index: 99999 !important;
}

.swal2-toast .swal2-container {
    z-index: 99999 !important;
}
</style>

<script>
// Enhanced notification update functions
window.updateUnreadCount = function() {
    const urlParams = new URLSearchParams(window.location.search);
    const storeId = urlParams.get('store_id');

    let url = 'get_unread_count.php';
    if (storeId) {
        url += '?store_id=' + encodeURIComponent(storeId);
    }

    console.log('Updating unread count from:', url); // Debug log

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Unread count response:', data); // Debug log

            if (data.success) {
                const unreadCount = parseInt(data.unread_count) || 0;

                // Update notification badges in sidebar
                const notificationBadges = document.querySelectorAll('.notification-badge');
                notificationBadges.forEach(badge => {
                    badge.textContent = unreadCount;
                    if (unreadCount > 0) {
                        badge.style.display = 'inline-block';
                        badge.style.visibility = 'visible';
                        badge.style.opacity = '1';
                        badge.classList.add('show');
                    } else {
                        badge.style.display = 'none';
                        badge.style.visibility = 'hidden';
                        badge.style.opacity = '0';
                        badge.classList.remove('show');
                    }
                });

                // Update notification counts in header (main counter)
                const notificationCounts = document.querySelectorAll('.notification-count');
                notificationCounts.forEach(count => {
                    const previousCount = parseInt(count.textContent) || 0;
                    count.textContent = unreadCount;
                    if (unreadCount > 0) {
                        count.style.display = 'flex';
                        count.style.visibility = 'visible';

                        // Trigger bell animation if count increased
                        if (unreadCount > previousCount && typeof window.triggerNotificationAlert === 'function') {
                            window.triggerNotificationAlert();
                        }
                    } else {
                        count.style.display = 'none';
                        count.style.visibility = 'hidden';
                    }
                });

                // Update any other unread count elements
                const unreadCounts = document.querySelectorAll('.unread-count');
                unreadCounts.forEach(count => {
                    count.textContent = unreadCount;
                    if (unreadCount > 0) {
                        count.style.display = 'inline-block';
                    } else {
                        count.style.display = 'none';
                    }
                });

                // Update notification icon with count
                const notificationIcons = document.querySelectorAll('.notification-icon');
                notificationIcons.forEach(icon => {
                    let countElement = icon.querySelector('.notification-count');
                    if (!countElement) {
                        countElement = icon.querySelector('.notification-badge');
                    }

                    if (countElement) {
                        if (unreadCount > 0) {
                            countElement.textContent = unreadCount;
                            countElement.style.display = 'inline-block';
                        } else {
                            countElement.style.display = 'none';
                        }
                    }
                });

                // Update page title if there are unread notifications
                const originalTitle = document.title.replace(/^\(\d+\)\s*/, '');
                if (unreadCount > 0) {
                    document.title = `(${unreadCount}) ${originalTitle}`;
                } else {
                    document.title = originalTitle;
                }

                console.log(`Updated unread count to: ${unreadCount}`); // Debug log
            } else {
                console.error('Failed to get unread count:', data.message);
            }
        })
        .catch(error => {
            console.error('Error updating unread count:', error);
        });
}

// Function to refresh notifications when new one arrives
window.refreshNotificationsOnNewMessage = function() {
    // Update unread count
    updateUnreadCount();

    // If notifications popup is open, refresh it
    const swalContainer = document.querySelector('.swal2-container');
    if (swalContainer && swalContainer.style.display !== 'none') {
        if (typeof refreshNotificationsInPopup === 'function') {
            refreshNotificationsInPopup();
        }
    }
}

// Auto-refresh notifications every 10 seconds (more frequent)
setInterval(() => {
    updateUnreadCount();
}, 10000);

// Initial load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing notification count...');

    // Update immediately
    updateUnreadCount();

    // Update again after a short delay to ensure everything is loaded
    setTimeout(() => {
        console.log('First delayed update...');
        updateUnreadCount();
    }, 500);

    // Update again after 2 seconds for good measure
    setTimeout(() => {
        console.log('Second delayed update...');
        updateUnreadCount();
    }, 2000);

    // Final update after 5 seconds
    setTimeout(() => {
        console.log('Final delayed update...');
        updateUnreadCount();
    }, 5000);
});

// Update when page becomes visible again
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        updateUnreadCount();
    }
});

// Update when window gains focus
window.addEventListener('focus', function() {
    updateUnreadCount();
});

// Force update function for manual calls
window.forceUpdateUnreadCount = function() {
    console.log('Force updating unread count...');
    updateUnreadCount();
};

// Test function to show counter with fake data
window.testNotificationCounter = function(count = 5) {
    console.log(`Testing notification counter with count: ${count}`);

    const notificationCounts = document.querySelectorAll('.notification-count');
    const notificationBadges = document.querySelectorAll('.notification-badge');

    notificationCounts.forEach(counter => {
        counter.textContent = count;
        counter.style.display = 'flex';
        counter.style.visibility = 'visible';
    });

    notificationBadges.forEach(badge => {
        badge.textContent = count;
        badge.style.display = 'inline-block';
    });

    console.log(`Updated ${notificationCounts.length} counters and ${notificationBadges.length} badges`);
};

// Set custom notification icon for background notifications
window.customNotificationIcon = '/uploads/img/logo2.png';

// Override default notification icon in service worker
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then(function(registration) {
        // Send the custom icon path to the service worker
        if (registration.active) {
            registration.active.postMessage({
                type: 'SET_NOTIFICATION_ICON',
                icon: window.customNotificationIcon
            });
        }
    });
}

// Function to show custom notification with logo
window.showCustomNotification = function(title, body, icon = null) {
    if ('Notification' in window && Notification.permission === 'granted') {
        const notificationIcon = icon || window.customNotificationIcon;

        const notification = new Notification(title, {
            body: body,
            icon: notificationIcon,
            badge: notificationIcon,
            tag: 'elwaled-notification',
            requireInteraction: false,
            silent: false
        });

        // Auto close after 5 seconds
        setTimeout(() => {
            notification.close();
        }, 5000);

        // Handle click event
        notification.onclick = function() {
            window.focus();
            notification.close();
        };

        return notification;
    }
    return null;
};
</script>
