<?php
// تفعيل عرض جميع أنواع الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include 'db_connection.php';
include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');

$store_id = isset($_GET['store_id']) ? decrypt($_GET['store_id'], $key) : null;
$search_name = isset($_GET['search_name']) ? $_GET['search_name'] : '';
$search_phone = isset($_GET['search_phone']) ? $_GET['search_phone'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$search_barcode = isset($_GET['search_barcode']) ? $_GET['search_barcode'] : '';
$display_mode = isset($_GET['display_mode']) ? $_GET['display_mode'] : 'by_date';
$data_type = isset($_GET['data_type']) ? $_GET['data_type'] : 'sales'; // نوع البيانات (مبيعات أو مرتجعات)

// تحديد الجدول المطلوب بناءً على نوع البيانات
$table_name = ($data_type === 'returns') ? 'returns' : 'sales';

// دالة لتجميع المبيعات في جلسات بناءً على تسلسل المستخدمين فقط
function groupSalesBySessions($sales_data, $data_type) {
    $sessions = [];
    if (empty($sales_data)) {
        return $sessions;
    }

    $current_session_sales = [];
    $last_account_id = null;

    foreach ($sales_data as $sale) {
        $current_account_id = $sale['account_id'];

        // إنشاء جلسة جديدة فقط عند تغيير المستخدم
        if ($last_account_id !== null && $current_account_id !== $last_account_id) {
            if (!empty($current_session_sales)) {
                $sessions[] = process_session($current_session_sales, $data_type);
            }
            $current_session_sales = [];
        }

        $current_session_sales[] = $sale;
        $last_account_id = $current_account_id;
    }

    // إضافة الجلسة الأخيرة
    if (!empty($current_session_sales)) {
        $sessions[] = process_session($current_session_sales, $data_type);
    }

    return $sessions;
}

// دالة مساعدة لمعالجة بيانات الجلسة
function process_session($session_sales, $data_type) {
    $first_sale = $session_sales[0];
    $last_sale = end($session_sales);

    $total_quantity = 0;
    $total_amount = 0;
    $total_profit = 0;
    $collected = 0;
    $statuses = [];

    // العثور على أول وآخر توقيت في الجلسة
    $min_time = $first_sale['time'];
    $max_time = $last_sale['time'];
    
    foreach ($session_sales as $sale) {
        $total_quantity += isset($sale['quantity']) ? $sale['quantity'] : 0;
        
        if ($data_type === 'returns') {
            $total_amount += isset($sale['refund_amount']) ? $sale['refund_amount'] : 0;
            $collected += isset($sale['refunded']) ? $sale['refunded'] : 0;
        } else {
            $price = isset($sale['price']) ? $sale['price'] : 0;
            $cost = isset($sale['cost']) ? $sale['cost'] : 0;
            $quantity = isset($sale['quantity']) ? $sale['quantity'] : 0;

            $total_amount += $price * $quantity;
            $total_profit += ($price - $cost) * $quantity;
            $collected += isset($sale['collected']) ? $sale['collected'] : 0;
            if (isset($sale['status'])) {
                $statuses[] = $sale['status'];
            }
        }
        
        if ($sale['time'] < $min_time) {
            $min_time = $sale['time'];
        }
        if ($sale['time'] > $max_time) {
            $max_time = $sale['time'];
        }
    }

    // تحديد الحالة المجمعة
    $status = 'confirmed';
    if ($data_type !== 'returns') {
        if (in_array('pending', $statuses)) {
            $status = 'pending';
        } elseif (in_array('delayed', $statuses)) {
            $status = 'delayed';
        }
    }

    // إنشاء عرض الت��ريخ
    $min_date = date('Y-m-d', strtotime($min_time));
    $max_date = date('Y-m-d', strtotime($max_time));
    $order_date_display = $min_date;
    if ($min_date !== $max_date) {
        $order_date_display = $min_date . ' إلى ' . $max_date;
    }

    $result = [
        'order_date' => $min_date, // للتصنيف
        'order_date_display' => $order_date_display, // للعرض
        'account_id' => $first_sale['account_id'],
        'username' => $first_sale['username'],
        'phone' => $first_sale['phone'],
        'account_buyer_id' => $first_sale['account_buyer_id'],
        'min_time' => $min_time,
        'max_time' => $max_time,
        'status' => $status,
        'item_count' => count($session_sales),
        'total_quantity' => $total_quantity,
        'total_amount' => $total_amount,
        'collected' => $collected,
    ];

    if ($data_type !== 'returns') {
        $result['total_profit'] = $total_profit;
    }

    return $result;
}


if ($display_mode === 'by_account') {
    // ... الكود الحالي للعرض حسب الحساب يبقى كما هو
    $sql = "SELECT s.account_id, a.username, a.phone, 
                   s.account_buyer_id, ";
    
    if ($table_name === 'returns') {
        // في حالة المرتجعات، نستخدم 'confirmed' كقيمة ثابتة للحالة
        $sql .= "'confirmed' AS status, ";
    } else {
        // في حالة المبيعات، نستخدم الحقل الفعلي
        $sql .= "CASE 
                   WHEN SUM(CASE WHEN s.status = 'pending' THEN 1 ELSE 0 END) > 0 THEN 'pending'
                   WHEN SUM(CASE WHEN s.status = 'delayed' THEN 1 ELSE 0 END) > 0 THEN 'delayed'
                   ELSE 'confirmed'
                END AS status, ";
    }
    
    $sql .= "COUNT(s.item_id) AS item_count, 
                   SUM(s.quantity) AS total_quantity, ";
    
    if ($table_name === 'returns') {
        $sql .= "SUM(s.refund_amount) AS total_amount, ";
        $sql .= "COALESCE(SUM(s.refunded), 0) AS collected";
    } else {
        $sql .= "SUM(s.price * s.quantity) AS total_amount, ";
        $sql .= "SUM((s.price - s.cost) * s.quantity) AS total_profit, ";
        $sql .= "COALESCE(SUM(s.collected), 0) AS collected";
    }
    
    $sql .= " FROM $table_name s
            JOIN accounts a ON s.account_id = a.account_id
            JOIN items i ON s.item_id = i.item_id
            WHERE 1=1 ";

    // شروط التصفية يجب إضافتها ضمن WHERE
    $params = [];
    if ($store_id) {
        $sql .= " AND s.store_id = ?";
        $params[] = $store_id;
    }
    if ($search_name) {
        $sql .= " AND a.username LIKE ?";
        $params[] = '%' . $search_name . '%';
    }
    if ($search_phone) {
        $sql .= " AND a.phone LIKE ?";
        $params[] = '%' . $search_phone . '%';
    }
    
    // فلترة حسب نوع الصنف
    if ($data_type === 'services') {
        $sql .= " AND i.type = 'service'";
    } elseif ($data_type === 'sales') {
        $sql .= " AND i.type != 'service'";
    }

    // بعد جمع شروط WHERE يتم إضافة GROUP BY 
    $sql .= " GROUP BY s.account_id, a.username, a.phone ";
    
    // إضافة HAVING فقط إذا كنا نستعلم من جدول المبيعات
    if ($table_name !== 'returns' && $status) {
        $sql .= " HAVING status = ? COLLATE utf8mb4_unicode_ci ";
        $params[] = $status;
    }
        $stmt = $conn->prepare($sql);
    if ($params) {
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $orders = [];
    while ($row = $result->fetch_assoc()) {
        $orders[] = $row;
    }

} elseif ($display_mode === 'by_daily') {
    // العرض اليومي الشامل - تجميع جميع المعاملات حسب التاريخ فقط
    $sql = "SELECT DATE(s.time) AS order_date, 
                   'يومي شامل' AS username,
                   '' AS phone,
                   '' AS account_buyer_id,
                   0 AS account_id, ";
    
    if ($table_name === 'returns') {
        $sql .= "'confirmed' AS status, ";
    } else {
        $sql .= "CASE 
                   WHEN SUM(CASE WHEN s.status = 'pending' THEN 1 ELSE 0 END) > 0 THEN 'pending'
                   WHEN SUM(CASE WHEN s.status = 'delayed' THEN 1 ELSE 0 END) > 0 THEN 'delayed'
                   ELSE 'confirmed'
                END AS status, ";
    }
    
    $sql .= "COUNT(s.item_id) AS item_count, 
                   SUM(s.quantity) AS total_quantity, ";
    
    if ($table_name === 'returns') {
        $sql .= "SUM(s.refund_amount) AS total_amount, ";
        $sql .= "COALESCE(SUM(s.refunded), 0) AS collected";
    } else {
        $sql .= "SUM(s.price * s.quantity) AS total_amount, ";
        $sql .= "SUM((s.price - s.cost) * s.quantity) AS total_profit, ";
        $sql .= "COALESCE(SUM(s.collected), 0) AS collected";
    }
    
    $sql .= " FROM $table_name s
            JOIN accounts a ON s.account_id = a.account_id
            JOIN items i ON s.item_id = i.item_id
            WHERE 1=1 ";

    $params = [];
    if ($store_id) {
        $sql .= " AND s.store_id = ?";
        $params[] = $store_id;
    }
    if ($start_date) {
        $sql .= " AND DATE(s.time) >= ?";
        $params[] = $start_date;
    }
    if ($end_date) {
        $sql .= " AND DATE(s.time) <= ?";
        $params[] = $end_date;
    }
    
    // فلترة حسب نوع الصنف
    if ($data_type === 'services') {
        $sql .= " AND i.type = 'service'";
    } elseif ($data_type === 'sales') {
        $sql .= " AND i.type != 'service'";
    }

    $sql .= " GROUP BY DATE(s.time) ";
    
    // إضافة HAVING فقط إذا كنا نستعلم من جدول المبيعات
    if ($table_name !== 'returns' && $status) {
        $sql .= " HAVING status = ? COLLATE utf8mb4_unicode_ci ";
        $params[] = $status;
    }
    
    $sql .= " ORDER BY DATE(s.time) DESC";

    $stmt = $conn->prepare($sql);
    if ($params) {
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $orders = [];
    while ($row = $result->fetch_assoc()) {
        $orders[] = $row;
    }

} else { // by_date
    // جلب جميع المبيعات مرتبة زمنياً لتطبيق منطق الجلسات
    $sql = "SELECT s.*, a.username, a.phone 
            FROM $table_name s
            JOIN accounts a ON s.account_id = a.account_id
            JOIN items i ON s.item_id = i.item_id
            WHERE 1=1";
    
    $params = [];
    if ($store_id) {
        $sql .= " AND s.store_id = ?";
        $params[] = $store_id;
    }
    if ($search_name) {
        $sql .= " AND a.username LIKE ?";
        $params[] = '%' . $search_name . '%';
    }
    if ($search_phone) {
        $sql .= " AND a.phone LIKE ?";
        $params[] = '%' . $search_phone . '%';
    }
    if ($start_date) {
        $sql .= " AND DATE(s.time) >= ?";
        $params[] = $start_date;
    }
    if ($end_date) {
        $sql .= " AND DATE(s.time) <= ?";
        $params[] = $end_date;
    }
    
    // فلترة حسب نوع الصنف
    if ($data_type === 'services') {
        $sql .= " AND i.type = 'service'";
    } elseif ($data_type === 'sales') {
        $sql .= " AND i.type != 'service'";
    }
    
    $sql .= " ORDER BY s.time ASC";

    $stmt = $conn->prepare($sql);
    if ($params) {
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
    }
    $stmt->execute();
    $all_sales_result = $stmt->get_result();
    $all_sales = [];
    while ($row = $all_sales_result->fetch_assoc()) {
        $all_sales[] = $row;
    }

    $sessions = groupSalesBySessions($all_sales, $data_type);

    // تطبيق الفلاتر المتبقية بعد التجميع
    $orders = array_filter($sessions, function($session) use ($status, $search_barcode) {
        if ($status && $session['status'] !== $status) {
            return false;
        }
        if ($search_barcode) {
            $session_barcode = $session['account_id'] . str_replace('-', '', $session['order_date']);
            if (strpos($session_barcode, $search_barcode) === false) {
                return false;
            }
        }
        return true;
    });

    // ترتيب النتائج النهائية
    usort($orders, function($a, $b) {
        if ($a['order_date'] == $b['order_date']) {
            return strtotime($b['max_time']) - strtotime($a['max_time']);
        }
        return strtotime($b['order_date']) - strtotime($a['order_date']);
    });
}


// دالة لتنسيق الأرقام بشكل ذكي
function formatNumber($number) {
    $num = floatval($number);
    if ($num == intval($num)) {
        return intval($num);
    }
    return rtrim(rtrim(number_format($num, 2, '.', ''), '0'), '.');
}

$final_orders = [];
// The $orders array is populated either by the 'by_account' or 'by_date' logic.
// Now, we iterate over it to format and add extra data.
foreach ($orders as $row) {
    $row['encrypted_account_id'] = encrypt($row['account_id'], $key);
    if ($table_name === 'returns') {
        $row['remaining'] = $row['total_amount'] - $row['collected']; // الباقي في المرتجعات
    } else {
        $row['remaining'] = $row['total_amount'] - $row['collected']; // الباقي في المبيعات
    }

    // تنسيق الكمية الإجمالية بشكل ذكي
    $total_quantity = floatval($row['total_quantity']);
    if ($total_quantity == intval($total_quantity)) {
        $row['total_quantity'] = intval($total_quantity);
    } else {
        $row['total_quantity'] = rtrim(rtrim(number_format($total_quantity, 3, '.', ''), '0'), '.');
    }

    // تنسيق المبالغ المالية
    $row['total_amount'] = formatNumber($row['total_amount']);
    $row['collected'] = formatNumber($row['collected']);
    $row['remaining'] = formatNumber($row['remaining']);

    // تنسيق إجمالي المكسب إذا كان موجوداً
    if (isset($row['total_profit'])) {
        $row['total_profit'] = formatNumber($row['total_profit']);
    }

    // جلب اسم العميل إذا كان account_buyer_id موجوداً
    if (!empty($row['account_buyer_id'])) {
        $stmt2 = $conn->prepare("SELECT name FROM accounts WHERE account_id = ?");
        $stmt2->bind_param("i", $row['account_buyer_id']);
        $stmt2->execute();
        $stmt2->bind_result($customer_name);
        $stmt2->fetch();
        $stmt2->close();
        $row['customer_name'] = $customer_name;
    } else {
        $row['customer_name'] = null;
    }
    $final_orders[] = $row;
}

if (isset($stmt)) {
    $stmt->close();
}
$conn->close();

header('Content-Type: application/json');
// For by_date mode, the array is already sorted. For by_account, it's grouped but not sorted by time.
// The original sort was `ORDER BY order_date DESC, max_time DESC`.
// The new by_date sort is handled in PHP. The by_account sort is implicit by GROUP BY.
// If a specific order is needed for by_account, it should be added.
// For now, we assume the DB order is sufficient for by_account.
echo json_encode($final_orders);
?>

