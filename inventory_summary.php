<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// استقبال inventory_id مشفرًا
$encrypted_inventory_id = isset($_GET['inventory_id']) ? $_GET['inventory_id'] : null;
$inventory_id = decrypt($encrypted_inventory_id, $key);

// التحقق من صحة inventory_id
if (!$inventory_id || !is_numeric($inventory_id)) {
    die("خطأ: رقم الجرد غير صالح.");
}

// جلب بيانات الجرد
$inventory_sql = "SELECT * FROM monthly_inventory WHERE inventory_id = ?";
$stmt = $conn->prepare($inventory_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$inventory_result = $stmt->get_result();
$inventory = $inventory_result->fetch_assoc();
$stmt->close();

// التحقق من أن الجرد موجود
if (!$inventory) {
    die("خطأ: لم يتم العثور على بيانات الجرد.");
}

// Set the encrypted store ID
$encrypted_store_id = encrypt($inventory['store_id'], $key);

// جلب بيانات المخزن
$store_sql = "SELECT * FROM stores WHERE store_id = ?";
$stmt = $conn->prepare($store_sql);
$stmt->bind_param("i", $inventory['store_id']);
$stmt->execute();
$store_result = $stmt->get_result();
$store = $store_result->fetch_assoc();
$stmt->close();

// التحقق من أن المخزن موجود
if (!$store) {
    die("خطأ: لم يتم العثور على بيانات المخزن.");
}

// جلب بيانات الأصناف داخل الجرد
$items_sql = "SELECT items.name, monthly_inventory_items.total_recorded_quantity, monthly_inventory_items.closing_quantity, 
              monthly_inventory_items.sold_quantity, monthly_inventory_items.cost, monthly_inventory_items.price, 
              monthly_inventory_items.total_cost, monthly_inventory_items.total_sales, monthly_inventory_items.profit 
              FROM monthly_inventory_items 
              JOIN items ON monthly_inventory_items.item_id = items.item_id 
              WHERE monthly_inventory_items.inventory_id = ?";
$stmt = $conn->prepare($items_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$items_result = $stmt->get_result();
$items = $items_result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// حساب الإجماليات
$total_recorded_quantity = 0;
$total_closing_quantity = 0;
$total_sold_quantity = 0;
$total_cost = 0;
$total_sales = 0;
$total_profit = 0;
$total_remaining_cost = 0;

foreach ($items as $item) {
    $total_recorded_quantity += $item['total_recorded_quantity'];
    $total_closing_quantity += $item['closing_quantity'];
    $total_sold_quantity += $item['sold_quantity'];
    $total_cost += $item['total_cost'];
    $total_sales += $item['total_sales'];
    $total_profit += $item['profit'];
    $total_remaining_cost += $item['closing_quantity'] * $item['cost'];
}

// Fetch balance transfer data
$balance_transfers_sql = "SELECT 
    SUM(value) AS total_transfer_units, 
    SUM((sale_price - cost) * value) AS total_transfer_profit 
FROM inventory_balance_transfers 
WHERE inventory_id = ?";
$stmt = $conn->prepare($balance_transfers_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$balance_transfers_result = $stmt->get_result();
$balance_transfers = $balance_transfers_result->fetch_assoc();
$stmt->close();

$total_transfer_units = $balance_transfers['total_transfer_units'] ?? 0;
$total_transfer_profit = $balance_transfers['total_transfer_profit'] ?? 0;
$combined_profit = $total_profit + $total_transfer_profit;

// Fetch expense data
$expenses_sql = "SELECT 
    SUM(CASE WHEN expense_type = 'Expenses and Damages' THEN amount ELSE 0 END) AS total_expenses_and_damages,
    SUM(CASE WHEN expense_type = 'Credit on Store' THEN amount ELSE 0 END) AS total_credit_on_store,
    SUM(CASE WHEN expense_type = 'Credit to Store' THEN amount ELSE 0 END) AS total_credit_to_store
FROM inventory_expenses 
WHERE inventory_id = ?";
$stmt = $conn->prepare($expenses_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$expenses_result = $stmt->get_result();
$expenses = $expenses_result->fetch_assoc();
$stmt->close();

$total_expenses_and_damages = $expenses['total_expenses_and_damages'] ?? 0;
$total_credit_on_store = $expenses['total_credit_on_store'] ?? 0;
$total_credit_to_store = $expenses['total_credit_to_store'] ?? 0;

// Calculate adjusted profit
$adjusted_profit = $combined_profit - $total_expenses_and_damages - $total_credit_on_store + $total_credit_to_store;

// Fetch shift closure data
$shift_closures_sql = "SELECT 
    SUM(shift_amount) AS total_cash,
    SUM(CASE WHEN shift_type = 'morning' THEN shift_amount ELSE 0 END) AS total_morning_cash,
    SUM(CASE WHEN shift_type = 'night' THEN shift_amount ELSE 0 END) AS total_night_cash,
    SUM(purchases) AS total_purchases
FROM inventory_shift_closures 
WHERE inventory_id = ?";
$stmt = $conn->prepare($shift_closures_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$shift_closures_result = $stmt->get_result();
$shift_closures = $shift_closures_result->fetch_assoc();
$stmt->close();

$total_cash = $shift_closures['total_cash'] ?? 0;
$total_morning_cash = $shift_closures['total_morning_cash'] ?? 0;
$total_night_cash = $shift_closures['total_night_cash'] ?? 0;
$total_purchases = $shift_closures['total_purchases'] ?? 0;

// Fetch total purchase invoice value
$purchase_invoices_sql = "SELECT 
    SUM(total_amount) AS total_purchase_invoice_value 
FROM inventory_purchase_invoices 
WHERE inventory_id = ?";
$stmt = $conn->prepare($purchase_invoices_sql);
$stmt->bind_param("i", $inventory_id);
$stmt->execute();
$purchase_invoices_result = $stmt->get_result();
$purchase_invoices = $purchase_invoices_result->fetch_assoc();
$stmt->close();

$total_purchase_invoice_value = $purchase_invoices['total_purchase_invoice_value'] ?? 0;

// Use the store name from the open inventory
$store_name = $store['name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملخص الجرد</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        /* Enhanced Inventory Summary Styles */
        .inventory-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
             padding: 80px 20px 0 20px; /* إضافة مساحة علوية للهيدر */
        }

        .inventory-header {
            background: linear-gradient(135deg, var(--color-primary), #667eea);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 0;
        }

        .inventory-header h1 {
            font-size: 2.5rem;
            margin: 0 0 15px 0;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .inventory-info {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        .info-item i {
            font-size: 1.2rem;
        }

        .export-section {
            background: var(--color-secondary);
            padding: 25px;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .export-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .summary-card {
            background: var(--color-secondary);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(63, 81, 181, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), #667eea);
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--color-primary);
            margin: 0;
        }

        .card-icon {
            font-size: 1.5rem;
            color: var(--color-primary);
            opacity: 0.7;
        }

        .view-details-btn {
            background: linear-gradient(135deg, var(--color-primary), #667eea);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .view-details-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(63, 81, 181, 0.3);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(63, 81, 181, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(63, 81, 181, 0.1);
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--color-fg);
            opacity: 0.8;
        }

        .items-section {
            background: var(--color-secondary);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary), #667eea);
            border-radius: 2px;
        }

        .enhanced-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .enhanced-table thead th {
            background: linear-gradient(135deg, var(--color-primary), #667eea);
            color: white;
            padding: 18px 15px;
            font-weight: 600;
            text-align: center;
            font-size: 0.95rem;
            border: none;
        }

        .enhanced-table tbody td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
        }

        .enhanced-table tbody tr:hover {
            background: rgba(63, 81, 181, 0.05);
        }

        .enhanced-table tbody tr:last-child td {
            border-bottom: none;
        }

        .finish-section {
            background: var(--color-secondary);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
        }

        .finish-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .finish-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        /* Enhanced Modal Styles */
        .enhanced-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .enhanced-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .enhanced-modal-content {
            background: var(--color-secondary);
            border-radius: 20px;
            padding: 30px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(63, 81, 181, 0.1);
        }

        .modal-title {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--color-primary);
            margin: 0;
        }

        .modal-close {
            background: #e74c3c;
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            background: #c0392b;
            transform: scale(1.1);
        }

        .modal-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .modal-table thead th {
            background: var(--color-primary);
            color: white;
            padding: 15px 12px;
            font-weight: 600;
            text-align: center;
            font-size: 0.9rem;
        }

        .modal-table tbody td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.9rem;
        }

        .modal-table tbody tr:hover {
            background: rgba(63, 81, 181, 0.05);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .inventory-container {
                padding: 0 15px;
            }

            .inventory-header {
                padding: 20px;
                border-radius: 15px 15px 0 0;
            }

            .inventory-header h1 {
                font-size: 2rem;
            }

            .inventory-info {
                gap: 20px;
            }

            .info-item {
                padding: 8px 15px;
                font-size: 0.9rem;
            }

            .summary-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .summary-card {
                padding: 20px;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .enhanced-modal-content {
                margin: 20px;
                padding: 20px;
                max-width: calc(100vw - 40px);
            }

            .modal-table {
                font-size: 0.8rem;
            }

            .modal-table thead th,
            .modal-table tbody td {
                padding: 8px 6px;
            }
        }

        @media (max-width: 480px) {
            .inventory-header h1 {
                font-size: 1.6rem;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .metric-value {
                font-size: 1.2rem;
            }

            .enhanced-modal-content {
                margin: 10px;
                padding: 15px;
            }
        }

        /* Dark Mode Enhancements */
        [data-theme="dark"] .summary-card {
            background: #1c2128;
            border-color: rgba(88, 166, 255, 0.2);
        }

        [data-theme="dark"] .metric-item {
            background: rgba(88, 166, 255, 0.1);
            border-color: rgba(88, 166, 255, 0.2);
        }

        [data-theme="dark"] .enhanced-table {
            background: #1c2128;
        }

        [data-theme="dark"] .enhanced-table tbody td {
            border-color: #30363d;
        }

        [data-theme="dark"] .modal-table {
            background: #1c2128;
        }

        [data-theme="dark"] .modal-table tbody td {
            border-color: #30363d;
        }

        /* Additional UX Enhancements */
        
        /* KPI Progress Bars */
        .kpi-progress {
            width: 100%;
            height: 8px;
            background: rgba(63, 81, 181, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .kpi-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), #667eea);
            border-radius: 4px;
            transition: width 1s ease-in-out;
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-indicator.positive {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-indicator.negative {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .status-indicator.neutral {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        /* Quick Actions Bar */
        .quick-actions {
            background: var(--color-secondary);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quick-action-btn {
            background: linear-gradient(135deg, var(--color-primary), #667eea);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(63, 81, 181, 0.3);
        }

        .quick-action-btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .quick-action-btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .quick-action-btn.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        /* Search and Filter Bar */
        .search-filter-bar {
            background: var(--color-secondary);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px;
            border: 2px solid rgba(63, 81, 181, 0.2);
            border-radius: 25px;
            font-size: 1rem;
            background: rgba(63, 81, 181, 0.05);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
        }

        /* Charts Container */
        .charts-section {
            background: var(--color-secondary);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            background: rgba(63, 81, 181, 0.02);
            border-radius: 10px;
            padding: 20px;
        }

        /* Loading Spinner */
        .loading-spinner {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(63, 81, 181, 0.1);
            border-left: 5px solid var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notification Toast */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-secondary);
            color: var(--color-fg);
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            border-left: 4px solid var(--color-primary);
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-notification.success {
            border-left-color: #28a745;
        }

        .toast-notification.error {
            border-left-color: #dc3545;
        }

        .toast-notification.warning {
            border-left-color: #ffc107;
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--color-primary), #667eea);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(63, 81, 181, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(63, 81, 181, 0.5);
        }

        /* Comparison Cards */
        .comparison-section {
            background: var(--color-secondary);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .comparison-card {
            background: rgba(63, 81, 181, 0.05);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(63, 81, 181, 0.1);
        }

        .comparison-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 10px;
        }

        .comparison-label {
            font-size: 0.9rem;
            color: var(--color-fg);
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .comparison-change {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .comparison-change.positive {
            color: #28a745;
        }

        .comparison-change.negative {
            color: #dc3545;
        }

        /* Print Styles */
        @media print {
            .quick-actions,
            .search-filter-bar,
            .fab,
            .view-details-btn,
            .export-btn {
                display: none !important;
            }

            .inventory-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }

            .summary-card {
                break-inside: avoid;
                margin-bottom: 20px;
            }
        }

        /* Enhanced Item Status Styles */
        .status-indicator-enhanced {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            border: 1px solid;
            font-size: 0.85rem;
            white-space: nowrap;
            min-width: 120px;
            justify-content: center;
        }

        .status-indicator-enhanced i {
            font-size: 1rem;
        }

        .quantity-display,
        .price-display,
        .total-display,
        .profit-display,
        .margin-display {
            display: flex;
            align-items: center;
            gap: 6px;
            justify-content: center;
        }

        .quantity-display i,
        .price-display i,
        .total-display i,
        .profit-display i,
        .margin-display i {
            font-size: 0.9rem;
        }

        .progress-container {
            width: 100%;
            height: 25px;
            background-color: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 1px solid #e9ecef;
        }

        .progress-bar {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            transition: width 0.6s ease;
            position: relative;
            min-width: 30px;
        }

        .progress-text {
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        /* Item Row Status Classes */
        .item-row.status-out-of-stock {
            background-color: rgba(220, 53, 69, 0.05);
            border-left: 4px solid #dc3545;
        }

        .item-row.status-excellent {
            background-color: rgba(40, 167, 69, 0.05);
            border-left: 4px solid #28a745;
        }

        .item-row.status-good {
            background-color: rgba(32, 201, 151, 0.05);
            border-left: 4px solid #20c997;
        }

        .item-row.status-average {
            background-color: rgba(255, 193, 7, 0.05);
            border-left: 4px solid #ffc107;
        }

        .item-row.status-weak {
            background-color: rgba(253, 126, 20, 0.05);
            border-left: 4px solid #fd7e14;
        }

        .item-row.status-stagnant {
            background-color: rgba(108, 117, 125, 0.05);
            border-left: 4px solid #6c757d;
        }

        .item-row:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .item-name {
            font-weight: 600;
            color: var(--color-primary);
        }

        /* Enhanced Table Cells */
        .status-cell {
            min-width: 140px;
        }

        .turnover-cell {
            min-width: 120px;
        }

        .margin-cell {
            min-width: 100px;
        }

        .quantity-cell,
        .price-cell,
        .total-cell,
        .profit-cell {
            min-width: 90px;
        }

        /* Responsive adjustments for new columns */
        @media (max-width: 1200px) {
            .enhanced-table {
                font-size: 0.85rem;
            }
            
            .status-indicator-enhanced {
                min-width: 100px;
                font-size: 0.75rem;
                padding: 6px 10px;
            }
            
            .progress-container {
                height: 20px;
            }
            
            .progress-text {
                font-size: 0.7rem;
            }
        }

        @media (max-width: 768px) {
            .enhanced-table {
                font-size: 0.8rem;
            }
            
            .status-indicator-enhanced {
                flex-direction: column;
                gap: 4px;
                min-width: 80px;
                padding: 4px 8px;
            }
            
            .quantity-display,
            .price-display,
            .total-display,
            .profit-display,
            .margin-display {
                flex-direction: column;
                gap: 2px;
            }
            
            .progress-container {
                height: 18px;
            }
            
            .progress-text {
                font-size: 0.65rem;
            }
        }

        /* Animation for status indicators */
        .status-indicator-enhanced {
            animation: fadeInScale 0.5s ease-out;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Pulse animation for critical items */
        .item-row.status-out-of-stock .status-indicator-enhanced {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
            }
        }

        /* Glow effect for excellent items */
        .item-row.status-excellent .status-indicator-enhanced {
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
        }

        /* Dark mode adjustments for new elements */
        [data-theme="dark"] .progress-container {
            background-color: #30363d;
            border-color: #21262d;
        }

        [data-theme="dark"] .item-row.status-out-of-stock {
            background-color: rgba(220, 53, 69, 0.1);
        }

        [data-theme="dark"] .item-row.status-excellent {
            background-color: rgba(40, 167, 69, 0.1);
        }

        [data-theme="dark"] .item-row.status-good {
            background-color: rgba(32, 201, 151, 0.1);
        }

        [data-theme="dark"] .item-row.status-average {
            background-color: rgba(255, 193, 7, 0.1);
        }

        [data-theme="dark"] .item-row.status-weak {
            background-color: rgba(253, 126, 20, 0.1);
        }

        [data-theme="dark"] .item-row.status-stagnant {
            background-color: rgba(108, 117, 125, 0.1);
        }

        /* Quick Filter Buttons */
        .quick-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background: var(--color-secondary);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .quick-filter-btn {
            padding: 8px 16px;
            border: 2px solid;
            border-radius: 20px;
            background: transparent;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .quick-filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .quick-filter-btn.status-out-of-stock {
            border-color: #dc3545;
            color: #dc3545;
        }

        .quick-filter-btn.status-out-of-stock:hover {
            background-color: #dc3545;
            color: white;
        }

        .quick-filter-btn.status-excellent {
            border-color: #28a745;
            color: #28a745;
        }

        .quick-filter-btn.status-excellent:hover {
            background-color: #28a745;
            color: white;
        }

        .quick-filter-btn.status-good {
            border-color: #20c997;
            color: #20c997;
        }

        .quick-filter-btn.status-good:hover {
            background-color: #20c997;
            color: white;
        }

        .quick-filter-btn.status-average {
            border-color: #ffc107;
            color: #ffc107;
        }

        .quick-filter-btn.status-average:hover {
            background-color: #ffc107;
            color: white;
        }

        .quick-filter-btn.status-weak {
            border-color: #fd7e14;
            color: #fd7e14;
        }

        .quick-filter-btn.status-weak:hover {
            background-color: #fd7e14;
            color: white;
        }

        .quick-filter-btn.status-stagnant {
            border-color: #6c757d;
            color: #6c757d;
        }

        .quick-filter-btn.status-stagnant:hover {
            background-color: #6c757d;
            color: white;
        }

        .quick-filter-btn:not([class*="status-"]) {
            border-color: var(--color-primary);
            color: var(--color-primary);
        }

        .quick-filter-btn:not([class*="status-"]):hover {
            background-color: var(--color-primary);
            color: white;
        }

        /* Active filter button */
        .quick-filter-btn.active {
            background-color: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        /* Responsive quick filters */
        @media (max-width: 768px) {
            .quick-filters {
                padding: 15px;
            }
            
            .quick-filter-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }

        /* Accessibility Improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus Indicators */
        button:focus,
        a:focus,
        input:focus {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 576px) {
            .inventory-header h1 {
                font-size: 1.4rem;
            }

            .info-item {
                font-size: 0.8rem;
                padding: 6px 12px;
            }

            .quick-actions {
                padding: 15px;
            }

            .quick-action-btn {
                padding: 10px 15px;
                font-size: 0.8rem;
            }

            .fab {
                width: 50px;
                height: 50px;
                bottom: 20px;
                right: 20px;
                font-size: 1.2rem;
            }
        }

        /* Explanatory Notes Styles */
        .explanatory-notes {
            background: var(--color-secondary);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border-left: 5px solid var(--color-primary);
        }

        .note-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(63, 81, 181, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(63, 81, 181, 0.1);
        }

        .note-icon {
            font-size: 1.5rem;
            color: var(--color-primary);
            margin-top: 2px;
            min-width: 30px;
        }

        .note-content {
            flex: 1;
        }

        .note-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 8px;
        }

        .note-description {
            font-size: 0.95rem;
            color: var(--color-fg);
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .note-formula {
            background: rgba(40, 167, 69, 0.1);
            padding: 8px 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
            margin-top: 8px;
        }

        .note-example {
            background: rgba(255, 193, 7, 0.1);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            color: #856404;
            border: 1px solid rgba(255, 193, 7, 0.2);
            margin-top: 8px;
        }

        .toggle-notes-btn {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px auto;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }

        .toggle-notes-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
        }

        .help-tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .help-tooltip .tooltip-text {
            visibility: hidden;
            width: 300px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 8px;
            padding: 10px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .help-tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }

        .help-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        .metric-item-enhanced {
            position: relative;
        }

        .metric-help-icon {
            position: absolute;
            top: 5px;
            right: 5px;
            font-size: 0.8rem;
            color: var(--color-primary);
            opacity: 0.7;
            cursor: help;
        }

        /* Dark mode for notes */
        [data-theme="dark"] .explanatory-notes {
            background: #1c2128;
            border-left-color: var(--color-primary);
        }

        [data-theme="dark"] .note-item {
            background: rgba(88, 166, 255, 0.1);
            border-color: rgba(88, 166, 255, 0.2);
        }

        [data-theme="dark"] .note-formula {
            background: rgba(40, 167, 69, 0.2);
            border-color: rgba(40, 167, 69, 0.3);
        }

        [data-theme="dark"] .note-example {
            background: rgba(255, 193, 7, 0.2);
            border-color: rgba(255, 193, 7, 0.3);
        }

        /* RTL Support for SweetAlert */
        .rtl-popup {
            direction: rtl !important;
            text-align: right !important;
        }

        .rtl-popup .swal2-title {
            text-align: center !important;
        }

        .rtl-popup .swal2-html-container {
            text-align: right !important;
        }

        /* Enhanced metric items with better spacing */
        .metric-item {
            position: relative;
            transition: all 0.3s ease;
        }

        .metric-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(63, 81, 181, 0.2);
        }

        /* Improved note items animation */
        .note-item {
            opacity: 0;
            transform: translateY(20px);
            animation: slideInUp 0.6s ease forwards;
        }

        .note-item:nth-child(1) { animation-delay: 0.1s; }
        .note-item:nth-child(2) { animation-delay: 0.2s; }
        .note-item:nth-child(3) { animation-delay: 0.3s; }
        .note-item:nth-child(4) { animation-delay: 0.4s; }
        .note-item:nth-child(5) { animation-delay: 0.5s; }
        .note-item:nth-child(6) { animation-delay: 0.6s; }
        .note-item:nth-child(7) { animation-delay: 0.7s; }
        .note-item:nth-child(8) { animation-delay: 0.8s; }
        .note-item:nth-child(9) { animation-delay: 0.9s; }
        .note-item:nth-child(10) { animation-delay: 1.0s; }
        .note-item:nth-child(11) { animation-delay: 1.1s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Quick info badges */
        .info-badge {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background: rgba(63, 81, 181, 0.1);
            color: var(--color-primary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-top: 5px;
        }

        .info-badge.positive {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .info-badge.negative {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .info-badge.warning {
            background: rgba(255, 193, 7, 0.1);
            color: #856404;
        }
    </style>
</head>
<body>
   
    <?php include 'sidebar.php'; ?>

    <main class="inventory-container">
        <!-- Enhanced Header Section -->
        <div class="inventory-header">
            <h1><i class="fas fa-clipboard-list"></i> ملخص الجرد</h1>
            <div class="inventory-info">
                <div class="info-item">
                    <i class="fas fa-store"></i>
                    <span><strong>الفرع:</strong> <?php echo htmlspecialchars($store['name']); ?></span>
                </div>
                <div class="info-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span><strong>تاريخ الجرد:</strong> <?php echo htmlspecialchars($inventory['inventory_date']); ?></span>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="export-section">
            <a href="export_inventory_summary.php?inventory_id=<?php echo urlencode($encrypted_inventory_id); ?>" class="export-btn">
                <i class="fas fa-file-excel"></i>
                تصدير إلى Excel
            </a>
        </div>

        <!-- Quick Actions Bar -->
        <div class="quick-actions">
            <button class="quick-action-btn" onclick="printSummary()">
                <i class="fas fa-print"></i>
                طباعة التقرير
            </button>
            <button class="quick-action-btn secondary" onclick="shareSummary()">
                <i class="fas fa-share-alt"></i>
                مشاركة
            </button>
            <button class="quick-action-btn success" onclick="downloadPDF()">
                <i class="fas fa-file-pdf"></i>
                تحميل PDF
            </button>
            <button class="quick-action-btn warning" onclick="showComparison()">
                <i class="fas fa-chart-line"></i>
                مقارنة الأشهر
            </button>
        </div>

        <!-- Search and Filter Bar -->
        <div class="search-filter-bar">
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 250px;">
                    <input type="text" id="itemSearch" class="search-input" placeholder="🔍 البحث في الأصناف..." onkeyup="filterItems()">
                </div>
                <button class="quick-action-btn" onclick="toggleCharts()">
                    <i class="fas fa-chart-bar"></i>
                    عرض الرسوم البيانية
                </button>
            </div>
        </div>

        <!-- KPI Overview Section -->
        <div class="comparison-section">
            <h2 class="section-title">مؤشرات الأداء الرئيسية</h2>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <div class="comparison-value"><?php echo number_format(($total_profit / max($total_cost, 1)) * 100, 1); ?>%</div>
                    <div class="comparison-label">هامش الربح</div>
                    <div class="kpi-progress">
                        <div class="kpi-progress-bar" style="width: <?php echo min(($total_profit / max($total_cost, 1)) * 100, 100); ?>%"></div>
                    </div>
                    <div class="comparison-change <?php echo ($total_profit > 0) ? 'positive' : 'negative'; ?>">
                        <i class="fas fa-<?php echo ($total_profit > 0) ? 'arrow-up' : 'arrow-down'; ?>"></i>
                        <?php echo ($total_profit > 0) ? 'ربح' : 'خسارة'; ?>
                    </div>
                    <div class="info-badge <?php echo ($total_profit / max($total_cost, 1)) * 100 > 20 ? 'positive' : (($total_profit / max($total_cost, 1)) * 100 > 10 ? 'warning' : 'negative'); ?>">
                        <i class="fas fa-info-circle"></i>
                        <?php 
                        $margin = ($total_profit / max($total_cost, 1)) * 100;
                        if ($margin > 20) echo 'هامش ممتاز';
                        elseif ($margin > 10) echo 'هامش جيد';
                        else echo 'هامش ضعيف';
                        ?>
                    </div>
                </div>
                
                <div class="comparison-card">
                    <div class="comparison-value"><?php echo number_format(($total_sold_quantity / max($total_recorded_quantity, 1)) * 100, 1); ?>%</div>
                    <div class="comparison-label">معدل دوران المخزون</div>
                    <div class="kpi-progress">
                        <div class="kpi-progress-bar" style="width: <?php echo min(($total_sold_quantity / max($total_recorded_quantity, 1)) * 100, 100); ?>%"></div>
                    </div>
                    <div class="comparison-change <?php echo ($total_sold_quantity > $total_closing_quantity) ? 'positive' : 'neutral'; ?>">
                        <i class="fas fa-sync-alt"></i>
                        دوران جيد
                    </div>
                    <div class="info-badge <?php echo ($total_sold_quantity / max($total_recorded_quantity, 1)) * 100 > 70 ? 'positive' : (($total_sold_quantity / max($total_recorded_quantity, 1)) * 100 > 50 ? 'warning' : 'negative'); ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <?php 
                        $turnover = ($total_sold_quantity / max($total_recorded_quantity, 1)) * 100;
                        if ($turnover > 70) echo 'دوران سريع';
                        elseif ($turnover > 50) echo 'دوران متوسط';
                        else echo 'دوران بطيء';
                        ?>
                    </div>
                </div>

                <div class="comparison-card">
                    <div class="comparison-value"><?php echo number_format($total_remaining_cost); ?></div>
                    <div class="comparison-label">قيمة المخزون المتبقي</div>
                    <div class="kpi-progress">
                        <div class="kpi-progress-bar" style="width: <?php echo min(($total_remaining_cost / max($total_cost, 1)) * 100, 100); ?>%"></div>
                    </div>
                    <div class="comparison-change neutral">
                        <i class="fas fa-warehouse"></i>
                        مخزون متاح
                    </div>
                    <div class="info-badge <?php echo ($total_remaining_cost / max($total_cost, 1)) * 100 > 50 ? 'warning' : 'positive'; ?>">
                        <i class="fas fa-chart-pie"></i>
                        <?php 
                        $remaining_ratio = ($total_remaining_cost / max($total_cost, 1)) * 100;
                        if ($remaining_ratio > 70) echo 'مخزون عالي';
                        elseif ($remaining_ratio > 30) echo 'مخزون متوسط';
                        else echo 'مخزون من��فض';
                        ?>
                    </div>
                </div>

                <div class="comparison-card">
                    <div class="comparison-value"><?php echo number_format($adjusted_profit); ?></div>
                    <div class="comparison-label">صافي الربح</div>
                    <div class="kpi-progress">
                        <div class="kpi-progress-bar" style="width: <?php echo min(abs($adjusted_profit) / max($total_sales, 1) * 100, 100); ?>%"></div>
                    </div>
                    <div class="comparison-change <?php echo ($adjusted_profit > 0) ? 'positive' : 'negative'; ?>">
                        <i class="fas fa-<?php echo ($adjusted_profit > 0) ? 'thumbs-up' : 'thumbs-down'; ?>"></i>
                        <?php echo ($adjusted_profit > 0) ? 'ربح صافي' : 'خسارة صافية'; ?>
                    </div>
                    <div class="info-badge <?php echo ($adjusted_profit > $total_profit * 0.8) ? 'positive' : (($adjusted_profit > $total_profit * 0.5) ? 'warning' : 'negative'); ?>">
                        <i class="fas fa-calculator"></i>
                        <?php 
                        if ($adjusted_profit > $total_profit * 0.8) echo 'مصروفات قليلة';
                        elseif ($adjusted_profit > $total_profit * 0.5) echo 'مصروفات متوسطة';
                        else echo 'مصروفات عالية';
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section (Initially Hidden) -->
        <div class="charts-section" id="chartsSection" style="display: none;">
            <h2 class="section-title">الرسوم البيانية التفاعلية</h2>
            <div class="charts-grid">
                <div class="chart-container">
                    <canvas id="profitChart"></canvas>
                </div>
                <div class="chart-container">
                    <canvas id="inventoryChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Toggle Button for Explanatory Notes -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="toggle-notes-btn" onclick="toggleExplanatoryNotes()">
                <i class="fas fa-info-circle"></i>
                <span id="notesToggleText">عرض شرح الأرقام والمصطلحات</span>
            </button>
        </div>

        <!-- Explanatory Notes Section (Initially Hidden) -->
        <div class="explanatory-notes" id="explanatoryNotes" style="display: none;">
            <h2 class="section-title">شرح الأرقام والمصطلحات</h2>
            
            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">الكمية المسجلة</div>
                    <div class="note-description">
                        هي إجمالي كمية جميع الأصناف التي تم تسجيلها في بداية الشهر أو عند إضافة أصناف جديدة للمخزن.
                    </div>
                    <div class="note-example">
                        <strong>مثال:</strong> إذا كان لديك 100 قطعة من صنف A و 50 قطعة من صنف B، فإن الكمية المسجلة = 150 قطعة
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">الكمية المتبقية</div>
                    <div class="note-description">
                        هي الكمية الموجودة فعلياً في المخزن في نهاية الشهر بعد عمليات البيع والإضافة.
                    </div>
                    <div class="note-formula">
                        الكمية المتبقية = الكمية المسجلة + الكمية المضافة - الكمية المباعة
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">الكمية المباعة</div>
                    <div class="note-description">
                        هي إجمالي الكمية التي تم بيعها من جميع الأصناف خلال فترة الجرد.
                    </div>
                    <div class="note-formula">
                        الكمية المباعة = الكمية المسجلة - الكمية المتبقية
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">إجمالي التكلفة</div>
                    <div class="note-description">
                        هي إجمالي تكلفة شراء جميع الأصناف المسجلة في المخزن.
                    </div>
                    <div class="note-formula">
                        إجمالي التكلفة = مجموع (سعر التكلفة × الكمية المسجلة) لكل صنف
                    </div>
                    <div class="note-example">
                        <strong>مثال:</strong> صنف بتكلفة 10 جنيه وكمية 100 قطعة = 1000 جنيه
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">إجمالي المبيعات</div>
                    <div class="note-description">
                        هي إجمالي قيمة المبيعات بسعر البيع للعملاء.
                    </div>
                    <div class="note-formula">
                        إجمالي المبيعات = مجموع (سعر البيع × الكمية المباعة) لكل صنف
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">إجمالي المكسب</div>
                    <div class="note-description">
                        هو صافي الربح من المبيعات قبل خصم المصروفات والتوالف.
                    </div>
                    <div class="note-formula">
                        إجمالي المكسب = إجمالي المبيعات - تكلفة البضاعة المباعة
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">قيمة المخزون المتبقي</div>
                    <div class="note-description">
                        هي قيمة البضاعة المتبقية في المخزن محسوبة بسعر التكلفة.
                    </div>
                    <div class="note-formula">
                        قيمة المتبقي = مجموع (الكمية المتبقية × سعر التكلفة) لكل صنف
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">هامش الربح</div>
                    <div class="note-description">
                        هو نسبة الربح إلى التكلفة، ويوضح مدى ربحية العمليات التجارية.
                    </div>
                    <div class="note-formula">
                        هامش الربح % = (إجمالي المكسب ÷ إجمالي التكلفة) × 100
                    </div>
                    <div class="note-example">
                        <strong>مثال:</strong> مكسب 500 جنيه من تكلفة 2000 جنيه = 25% هامش ربح
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">معدل دوران المخزون</div>
                    <div class="note-description">
                        يوضح كفاءة إدارة المخزون ومدى سرعة بيع البضاعة.
                    </div>
                    <div class="note-formula">
                        معدل الدوران % = (الكمية المباعة ÷ الكمية المسجلة) × 100
                    </div>
                    <div class="note-example">
                        <strong>تفسير:</strong> كلما زاد المعدل، كلما كان دوران المخزون أفضل
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="note-content">
    <div class="note-title">تحويلات الرصيد</div>
    <div class="note-description">
        هي عمليات تحويل رصيد الهاتف بين مزودي الخدمة وأجهزة الشحن، وتشمل التعامل مع مزودين مثل ضامن، فوري، وبساطة.
    </div>
    <div class="note-example">
        <strong>ملاحظة:</strong> تؤثر على حساب الأرباح النهائية للمخزن
    </div>
</div>

            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">المصروفات والمعادلات</div>
                    <div class="note-description">
                        تشمل المصروفات والتوالف والديون (أجل على المحل وأجل للمحل).
                    </div>
                    <div class="note-formula">
                        المكسب بعد المعادلة = المكسب الإجمالي - المصروفات - أجل على المحل + أجل للمحل
                    </div>
                </div>
            </div>

            <div class="note-item">
                <div class="note-icon">
                    <i class="fas fa-cash-register"></i>
                </div>
                <div class="note-content">
                    <div class="note-title">الورديات والنقدي</div>
                    <div class="note-description">
                        تتضمن إجمالي النقدية المحصلة من الورديات الصباحية والمسائية والمشتريات.
                    </div>
                    <div class="note-example">
                        <strong>ملاحظة:</strong> يجب أن تتطابق مع إجمالي المبيعات النقدية
                    </div>
                </div>
            </div>

        </div>

            <!-- Enhanced Summary Cards Grid -->
        <div class="summary-grid">
            <!-- Main Inventory Summary Card -->
            <div class="summary-card">
                <div class="card-header">
                    <h3 class="card-title">ملخص الأصناف الرئيسي</h3>
                    <i class="fas fa-boxes card-icon"></i>
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_recorded_quantity); ?></div>
                        <div class="metric-label">الكمية المسجلة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_closing_quantity); ?></div>
                        <div class="metric-label">الكمية المتبقية</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_sold_quantity); ?></div>
                        <div class="metric-label">الكمية المباعة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_cost, 2); ?></div>
                        <div class="metric-label">إجمالي التكلفة</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_sales, 2); ?></div>
                        <div class="metric-label">إجمالي المبيعات</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_profit, 2); ?></div>
                        <div class="metric-label">إجمالي المكسب</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_remaining_cost, 2); ?></div>
                        <div class="metric-label">قيمة المتبقي</div>
                    </div>
                </div>
            </div>

            <!-- Balance Transfers Card -->
            <div class="summary-card">
                <div class="card-header">
                    <h3 class="card-title">تحويلات الرصيد</h3>
                    <i class="fas fa-exchange-alt card-icon"></i>
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_transfer_units, 2); ?></div>
                        <div class="metric-label">إجمالي الوحدات</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_transfer_profit, 2); ?></div>
                        <div class="metric-label">مكسب التحويلات</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($combined_profit, 2); ?></div>
                        <div class="metric-label">المكسب الإجمالي</div>
                    </div>
                </div>
                <button class="view-details-btn" onclick="showBalanceTransfers(<?php echo $inventory_id; ?>)">
                    <i class="fas fa-eye"></i>
                    عرض التفاصيل
                </button>
            </div>

            <!-- Enhanced Modal for Balance Transfers -->
            <div id="balanceTransfersModal" class="enhanced-modal">
                <div class="enhanced-modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">تفاصيل تحويلات الرصيد</h3>
                        <button class="modal-close" onclick="closeModal('balanceTransfersModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <table class="modal-table">
                            <thead>
                                <tr>
                                    <th>اسم الحساب</th>
                                    <th>المزود</th>
                                    <th>التكلفة</th>
                                    <th>سعر البيع</th>
                                    <th>القيمة</th>
                                    <th>تاريخ الإنشاء</th>
                                </tr>
                            </thead>
                            <tbody id="balanceTransfersTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <script>
                function showBalanceTransfers(inventoryId) {
                    fetch(`fetch_balance_transfers.php?inventory_id=${inventoryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('balanceTransfersTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(transfer => {
                                const createdAt = new Date(transfer.created_at);
                                const options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: true };
                                const formattedDateTime = createdAt.toLocaleString('ar-EG', options);

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${transfer.account_name}</td>
                                    <td>${transfer.provider}</td>
                                    <td>${parseFloat(transfer.cost).toFixed(2)}</td>
                                    <td>${parseFloat(transfer.sale_price).toFixed(2)}</td>
                                    <td>${parseFloat(transfer.value).toFixed(2)}</td>
                                    <td>${formattedDateTime}</td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('balanceTransfersModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching balance transfers:', error));
                }

                function closeModal(modalId) {
                    document.getElementById(modalId).classList.remove('active');
                }
            </script>

            <!-- Expenses Card -->
            <div class="summary-card">
                <div class="card-header">
                    <h3 class="card-title">المصروفات والمعادلات</h3>
                    <i class="fas fa-receipt card-icon"></i>
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_expenses_and_damages, 2); ?></div>
                        <div class="metric-label">المصاريف والتوالف</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_credit_on_store, 2); ?></div>
                        <div class="metric-label">أجل على المحل</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_credit_to_store, 2); ?></div>
                        <div class="metric-label">أجل للمحل</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($adjusted_profit, 2); ?></div>
                        <div class="metric-label">المكسب بعد المعادلة</div>
                    </div>
                </div>
                <button class="view-details-btn" onclick="showExpenseDetails(<?php echo $inventory_id; ?>)">
                    <i class="fas fa-eye"></i>
                    عرض التفاصيل
                </button>
            </div>

            <!-- Enhanced Modal for Expense Details -->
            <div id="expenseDetailsModal" class="enhanced-modal">
                <div class="enhanced-modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">تفاصيل المصروفات</h3>
                        <button class="modal-close" onclick="closeModal('expenseDetailsModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <table class="modal-table">
                            <thead>
                                <tr>
                                    <th>اسم المصروف</th>
                                    <th>نوع المصروف</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ المصروف</th>
                                </tr>
                            </thead>
                            <tbody id="expenseDetailsTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <script>
                function showExpenseDetails(inventoryId) {
                    fetch(`fetch_expenses.php?inventory_id=${inventoryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('expenseDetailsTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(expense => {
                                const expenseDate = new Date(expense.expense_date);
                                const formattedDate = expenseDate.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                                const expenseTypeMap = {
                                    'Expenses and Damages': 'مصروفات وتوالف',
                                    'Credit to Store': 'أجل للمحل',
                                    'Credit on Store': 'أجل على المحل'
                                };
                                const expenseTypeArabic = expenseTypeMap[expense.expense_type] || expense.expense_type;

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${expense.expense_name}</td>
                                    <td>${expenseTypeArabic}</td>
                                    <td>${parseFloat(expense.amount).toFixed(2)}</td>
                                    <td>${formattedDate}</td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('expenseDetailsModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching expense details:', error));
                }
            </script>

            <!-- Shift Closures Card -->
            <div class="summary-card">
                <div class="card-header">
                    <h3 class="card-title">الورديات والنقدي</h3>
                    <i class="fas fa-cash-register card-icon"></i>
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_cash, 2); ?></div>
                        <div class="metric-label">إجمالي النقدي</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_morning_cash, 2); ?></div>
                        <div class="metric-label">النقدي الصباحي</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_night_cash, 2); ?></div>
                        <div class="metric-label">النقدي المسائي</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_purchases, 2); ?></div>
                        <div class="metric-label">إجمالي المشتريات</div>
                    </div>
                </div>
                <button class="view-details-btn" onclick="showShiftClosures(<?php echo $inventory_id; ?>)">
                    <i class="fas fa-eye"></i>
                    عرض التفاصيل
                </button>
            </div>

            <!-- Enhanced Modal for Shift Closures -->
            <div id="shiftClosuresModal" class="enhanced-modal">
                <div class="enhanced-modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">تفاصيل الورديات</h3>
                        <button class="modal-close" onclick="closeModal('shiftClosuresModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <table class="modal-table">
                            <thead>
                                <tr>
                                    <th>اسم الحساب</th>
                                    <th>تاريخ الوردية</th>
                                    <th>نوع الوردية</th>
                                    <th>المبلغ</th>
                                    <th>المشتريات</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="shiftClosuresTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <script>
                function showShiftClosures(inventoryId) {
                    fetch(`fetch_shift_closures.php?inventory_id=${inventoryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('shiftClosuresTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(shift => {
                                const shiftDate = new Date(shift.shift_date);
                                const formattedDate = shiftDate.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${shift.account_name}</td>
                                    <td>${formattedDate}</td>
                                    <td>${shift.shift_type === 'morning' ? 'صباحية' : 'مسائية'}</td>
                                    <td>${parseFloat(shift.shift_amount).toFixed(2)}</td>
                                    <td>${parseFloat(shift.purchases).toFixed(2)}</td>
                                    <td>${shift.notes || 'لا توجد ملاحظات'}</td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('shiftClosuresModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching shift closures:', error));
                }
            </script>

            <!-- Purchase Invoices Card -->
            <div class="summary-card">
                <div class="card-header">
                    <h3 class="card-title">فواتير الشراء</h3>
                    <i class="fas fa-file-invoice card-icon"></i>
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($total_purchase_invoice_value, 2); ?></div>
                        <div class="metric-label">إجمالي قيمة الفواتير</div>
                    </div>
                </div>
                <button class="view-details-btn" onclick="showPurchaseInvoices(<?php echo $inventory_id; ?>)">
                    <i class="fas fa-eye"></i>
                    عرض التفاصيل
                </button>
            </div>
        </div>

            <!-- Enhanced Modal for Purchase Invoices -->
            <div id="purchaseInvoicesModal" class="enhanced-modal">
                <div class="enhanced-modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">تفاصيل فواتير الشراء</h3>
                        <button class="modal-close" onclick="closeModal('purchaseInvoicesModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <table class="modal-table">
                            <thead>
                                <tr>
                                    <th>اسم الحساب</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>عرض الأصناف</th>
                                </tr>
                            </thead>
                            <tbody id="purchaseInvoicesTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Enhanced Modal for Invoice Items -->
            <div id="invoiceItemsModal" class="enhanced-modal">
                <div class="enhanced-modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">تفاصيل أصناف الفاتورة</h3>
                        <button class="modal-close" onclick="closeModal('invoiceItemsModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <table class="modal-table">
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>الكمية</th>
                                    <th>جملة الصنف</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody id="invoiceItemsTableBody">
                                <!-- Data will be dynamically loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <script>
                function showPurchaseInvoices(inventoryId) {
                    fetch(`fetch_purchase_invoices.php?inventory_id=${inventoryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('purchaseInvoicesTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(invoice => {
                                const createdAt = new Date(invoice.created_at);
                                const formattedDate = createdAt.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${invoice.account_name}</td>
                                    <td>${parseFloat(invoice.total_amount).toFixed(2)}</td>
                                    <td>${formattedDate}</td>
                                    <td>
                                        <button onclick="showInvoiceItems(${invoice.inventory_invoice_id})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('purchaseInvoicesModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching purchase invoices:', error));
                }

                function showInvoiceItems(invoiceId) {
                    fetch(`fetch_inventory_invoice_items.php?invoice_id=${invoiceId}`)
                        .then(response => response.json())
                        .then(data => {
                            const tableBody = document.getElementById('invoiceItemsTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows

                            data.forEach(item => {
                                const totalCost = (parseFloat(item.cost) * parseFloat(item.quantity)).toFixed(2);

                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${item.item_name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${parseFloat(item.cost).toFixed(2)}</td>
                                    <td>${totalCost}</td>
                                `;
                                tableBody.appendChild(row);
                            });

                            document.getElementById('invoiceItemsModal').classList.add('active');
                        })
                        .catch(error => console.error('Error fetching invoice items:', error));
                }
            </script>

            <!-- Items Details Section -->
        <div class="items-section">
            <h2 class="section-title">تفاصيل الأصناف</h2>
            
            <!-- Quick Status Filters -->
            <div class="quick-filters">
                <button class="quick-filter-btn active" onclick="filterByStatus(''); setActiveFilter(this);">
                    <i class="fas fa-list"></i>
                    جميع الأصناف
                </button>
                <button class="quick-filter-btn status-out-of-stock" onclick="filterByStatus('نفد المخزون'); setActiveFilter(this);">
                    <i class="fas fa-exclamation-triangle"></i>
                    نفد المخزون
                </button>
                <button class="quick-filter-btn status-excellent" onclick="filterByStatus('مبيعات ممتازة'); setActiveFilter(this);">
                    <i class="fas fa-fire"></i>
                    مبيعات ممتازة
                </button>
                <button class="quick-filter-btn status-good" onclick="filterByStatus('مبيعات جيدة'); setActiveFilter(this);">
                    <i class="fas fa-thumbs-up"></i>
                    مبيعات جيدة
                </button>
                <button class="quick-filter-btn status-average" onclick="filterByStatus('مبيعات متوسطة'); setActiveFilter(this);">
                    <i class="fas fa-minus-circle"></i>
                    مبيعات متوسطة
                </button>
                <button class="quick-filter-btn status-weak" onclick="filterByStatus('مبيعات ضعيفة'); setActiveFilter(this);">
                    <i class="fas fa-arrow-down"></i>
                    مبيعات ضعيفة
                </button>
                <button class="quick-filter-btn status-stagnant" onclick="filterByStatus('راكد'); setActiveFilter(this);">
                    <i class="fas fa-times-circle"></i>
                    راكد
                </button>
            </div>
            
            <div class="table-responsive">
                <table id="itemsTable" class="enhanced-table">
                    <thead>
                        <tr>
                            <th>حالة الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الكمية المسجلة</th>
                            <th>الكمية المتبقية</th>
                            <th>الكمية المباعة</th>
                            <th>معدل الدوران</th>
                            <th>التكلفة</th>
                            <th>السعر</th>
                            <th>هامش الربح</th>
                            <th>إجمالي التكلفة</th>
                            <th>إجمالي المبيعات</th>
                            <th>المكسب</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $item): 
                            // حساب معدل الدوران
                            $turnover_rate = ($item['total_recorded_quantity'] > 0) ? 
                                ($item['sold_quantity'] / $item['total_recorded_quantity']) * 100 : 0;
                            
                            // حساب هامش الربح
                            $profit_margin = ($item['cost'] > 0) ? 
                                (($item['price'] - $item['cost']) / $item['cost']) * 100 : 0;
                            
                            // تحديد حالة الصنف
                            $status_class = '';
                            $status_icon = '';
                            $status_text = '';
                            $status_color = '';
                            
                            if ($item['closing_quantity'] == 0) {
                                // نفد المخزون
                                $status_class = 'status-out-of-stock';
                                $status_icon = 'fas fa-exclamation-triangle';
                                $status_text = 'نفد المخزون';
                                $status_color = '#dc3545';
                            } elseif ($turnover_rate >= 80) {
                                // مبيعات ممتازة
                                $status_class = 'status-excellent';
                                $status_icon = 'fas fa-fire';
                                $status_text = 'مبيعات ممتازة';
                                $status_color = '#28a745';
                            } elseif ($turnover_rate >= 60) {
                                // مبيعات جيدة
                                $status_class = 'status-good';
                                $status_icon = 'fas fa-thumbs-up';
                                $status_text = 'مبيعات جيدة';
                                $status_color = '#20c997';
                            } elseif ($turnover_rate >= 40) {
                                // مبيعات متوسطة
                                $status_class = 'status-average';
                                $status_icon = 'fas fa-minus-circle';
                                $status_text = 'مبيعات متوسطة';
                                $status_color = '#ffc107';
                            } elseif ($turnover_rate >= 20) {
                                // مبيعات ضعيفة
                                $status_class = 'status-weak';
                                $status_icon = 'fas fa-arrow-down';
                                $status_text = 'مبيعات ضعيفة';
                                $status_color = '#fd7e14';
                            } else {
                                // راكد
                                $status_class = 'status-stagnant';
                                $status_icon = 'fas fa-times-circle';
                                $status_text = 'راكد';
                                $status_color = '#6c757d';
                            }
                            
                            // تحديد اتجاه السهم للمبيعات
                            $sales_arrow = '';
                            $sales_arrow_color = '';
                            if ($turnover_rate >= 70) {
                                $sales_arrow = 'fas fa-arrow-up';
                                $sales_arrow_color = '#28a745';
                            } elseif ($turnover_rate >= 40) {
                                $sales_arrow = 'fas fa-arrow-right';
                                $sales_arrow_color = '#ffc107';
                            } else {
                                $sales_arrow = 'fas fa-arrow-down';
                                $sales_arrow_color = '#dc3545';
                            }
                            
                            // تحديد لون هامش الربح
                            $profit_color = '';
                            if ($profit_margin >= 30) {
                                $profit_color = '#28a745';
                            } elseif ($profit_margin >= 15) {
                                $profit_color = '#20c997';
                            } elseif ($profit_margin >= 5) {
                                $profit_color = '#ffc107';
                            } else {
                                $profit_color = '#dc3545';
                            }
                        ?>
                            <tr class="item-row <?php echo $status_class; ?>">
                                <td class="status-cell">
                                    <div class="status-indicator-enhanced" style="background-color: <?php echo $status_color; ?>10; border-color: <?php echo $status_color; ?>;">
                                        <i class="<?php echo $status_icon; ?>" style="color: <?php echo $status_color; ?>;"></i>
                                        <span style="color: <?php echo $status_color; ?>; font-weight: 600;"><?php echo $status_text; ?></span>
                                    </div>
                                </td>
                                <td class="item-name">
                                    <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                                </td>
                                <td class="quantity-cell">
                                    <div class="quantity-display">
                                        <i class="fas fa-boxes" style="color: #6c757d;"></i>
                                        <span><?php echo number_format($item['total_recorded_quantity']); ?></span>
                                    </div>
                                </td>
                                <td class="quantity-cell">
                                    <div class="quantity-display">
                                        <i class="fas fa-warehouse" style="color: <?php echo $item['closing_quantity'] > 0 ? '#28a745' : '#dc3545'; ?>;"></i>
                                        <span style="color: <?php echo $item['closing_quantity'] > 0 ? '#28a745' : '#dc3545'; ?>; font-weight: 600;">
                                            <?php echo number_format($item['closing_quantity']); ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="quantity-cell">
                                    <div class="quantity-display">
                                        <i class="<?php echo $sales_arrow; ?>" style="color: <?php echo $sales_arrow_color; ?>;"></i>
                                        <span style="color: <?php echo $sales_arrow_color; ?>; font-weight: 600;">
                                            <?php echo number_format($item['sold_quantity']); ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="turnover-cell">
                                    <div class="progress-container">
                                        <div class="progress-bar" style="width: <?php echo min($turnover_rate, 100); ?>%; background-color: <?php echo $sales_arrow_color; ?>;">
                                            <span class="progress-text"><?php echo number_format($turnover_rate, 1); ?>%</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="price-cell">
                                    <div class="price-display">
                                        <i class="fas fa-tag" style="color: #6c757d;"></i>
                                        <span><?php echo number_format($item['cost'], 2); ?></span>
                                    </div>
                                </td>
                                <td class="price-cell">
                                    <div class="price-display">
                                        <i class="fas fa-money-bill-wave" style="color: #28a745;"></i>
                                        <span><?php echo number_format($item['price'], 2); ?></span>
                                    </div>
                                </td>
                                <td class="margin-cell">
                                    <div class="margin-display" style="color: <?php echo $profit_color; ?>;">
                                        <i class="fas fa-percentage" style="color: <?php echo $profit_color; ?>;"></i>
                                        <span style="font-weight: 600;"><?php echo number_format($profit_margin, 1); ?>%</span>
                                    </div>
                                </td>
                                <td class="total-cell">
                                    <div class="total-display">
                                        <i class="fas fa-calculator" style="color: #6c757d;"></i>
                                        <span><?php echo number_format($item['total_cost'], 2); ?></span>
                                    </div>
                                </td>
                                <td class="total-cell">
                                    <div class="total-display">
                                        <i class="fas fa-chart-line" style="color: #28a745;"></i>
                                        <span style="color: #28a745; font-weight: 600;"><?php echo number_format($item['total_sales'], 2); ?></span>
                                    </div>
                                </td>
                                <td class="profit-cell">
                                    <div class="profit-display" style="color: <?php echo $item['profit'] >= 0 ? '#28a745' : '#dc3545'; ?>;">
                                        <i class="fas fa-<?php echo $item['profit'] >= 0 ? 'plus-circle' : 'minus-circle'; ?>" 
                                           style="color: <?php echo $item['profit'] >= 0 ? '#28a745' : '#dc3545'; ?>;"></i>
                                        <span style="font-weight: 700;"><?php echo number_format($item['profit'], 2); ?></span>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Finish Inventory Section -->
        <?php if ($inventory['status'] === 'pending'): ?>
            <div class="finish-section">
                <h3 style="color: var(--color-primary); margin-bottom: 20px;">إنهاء الجرد</h3>
                <p style="margin-bottom: 25px; color: var(--color-fg); opacity: 0.8;">
                    بعد مراجعة جميع البيانات، يمكنك إنهاء الجرد الحالي وبدء شهر جديد
                </p>
                <form method="POST" action="finish_inventory.php">
                    <input type="hidden" name="inventory_id" value="<?php echo htmlspecialchars($encrypted_inventory_id); ?>">
                    <button type="submit" name="finish_inventory" class="finish-btn">
                        <i class="fas fa-calendar-plus"></i>
                        بدء شهر جديد
                    </button>
                </form>
            </div>
        <?php endif; ?>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner"></div>
        </div>

        <!-- Toast Notification -->
        <div class="toast-notification" id="toastNotification">
            <div id="toastMessage"></div>
        </div>

        <!-- Floating Action Button -->
        <button class="fab" onclick="scrollToTop()" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script>
        // Enhanced DataTable with better features
        let itemsTable;
        $(document).ready(function() {
            itemsTable = $('#itemsTable').DataTable({
                language: {
                    url: '/elwaled_market/datatables/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[11, 'desc']], // Sort by profit column (now column 11)
                columnDefs: [
                    { targets: [0], orderable: false }, // Status column not sortable
                    { targets: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11], className: 'text-center' },
                    { targets: [5], type: 'num' }, // Turnover rate column
                    { targets: [8], type: 'num' } // Profit margin column
                ],
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> طباعة',
                        className: 'btn btn-info'
                    }
                ]
            });

            // Initialize charts
            initializeCharts();
            
            // Add loading animation to buttons
            addLoadingAnimations();
        });

        // Enhanced Functions
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toastNotification');
            const toastMessage = document.getElementById('toastMessage');
            
            toast.className = `toast-notification ${type}`;
            toastMessage.textContent = message;
            toast.classList.add('show');
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        function filterItems() {
            const searchValue = document.getElementById('itemSearch').value;
            if (itemsTable) {
                itemsTable.search(searchValue).draw();
            }
        }

        // Add status-based filtering
        function filterByStatus(status) {
            if (itemsTable) {
                itemsTable.column(0).search(status).draw();
            }
        }

        // Set active filter button
        function setActiveFilter(button) {
            // Remove active class from all filter buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            // Add active class to clicked button
            button.classList.add('active');
        }

        // Show item details in modal
        function showItemDetails(itemName) {
            Swal.fire({
                title: 'تفاصيل الصنف',
                text: 'تفاصيل ' + itemName,
                icon: 'info',
                confirmButtonText: 'موافق',
                customClass: {
                    popup: 'rtl-popup'
                }
            });
        }

        // Export filtered data
        function exportFilteredData() {
            if (itemsTable) {
                var filteredData = itemsTable.rows({ search: 'applied' }).data().toArray();
                console.log('Filtered data:', filteredData);
                showToast('تم تصدير البيانات المفلترة', 'success');
            }
        }

        // Show toast notification
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toastNotification');
            const toastMessage = document.getElementById('toastMessage');
            
            toastMessage.textContent = message;
            toast.className = `toast-notification ${type} show`;
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // Add click handlers for item rows
        $(document).ready(function() {
            $('#itemsTable tbody').on('click', 'tr', function() {
                const itemName = $(this).find('.item-name strong').text();
                if (itemName) {
                    showItemDetails(itemName);
                }
            });
        });

        function toggleCharts() {
            const chartsSection = document.getElementById('chartsSection');
            if (chartsSection.style.display === 'none') {
                chartsSection.style.display = 'block';
                showToast('تم عرض الرسوم البيانية', 'success');
                // Trigger chart resize
                setTimeout(() => {
                    if (window.profitChart) window.profitChart.resize();
                    if (window.inventoryChart) window.inventoryChart.resize();
                }, 300);
            } else {
                chartsSection.style.display = 'none';
                showToast('تم إخفاء الرسوم البيانية', 'neutral');
            }
        }

        function printSummary() {
            showLoading();
            setTimeout(() => {
                window.print();
                hideLoading();
                showToast('تم إعداد التقرير للطباعة', 'success');
            }, 500);
        }

        function shareSummary() {
            if (navigator.share) {
                navigator.share({
                    title: 'ملخص الجرد - <?php echo htmlspecialchars($store['name']); ?>',
                    text: 'ملخص جرد المخزون لتاريخ <?php echo htmlspecialchars($inventory['inventory_date']); ?>',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showToast('تم نسخ رابط التقرير', 'success');
                });
            }
        }

        function downloadPDF() {
            showLoading();
            // Simulate PDF generation
            setTimeout(() => {
                hideLoading();
                showToast('سيتم إضافة تحميل PDF قريباً', 'warning');
            }, 1000);
        }

        function showComparison() {
            Swal.fire({
                title: 'مقارنة الأشهر',
                text: 'سيتم إضافة ميزة مقارنة الأشهر قريباً',
                icon: 'info',
                confirmButtonText: 'حسناً',
                confirmButtonColor: '#3f51b5'
            });
        }

        function initializeCharts() {
            // Profit Chart
            const profitCtx = document.getElementById('profitChart');
            if (profitCtx) {
                window.profitChart = new Chart(profitCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['المبيعات', 'التكلفة', 'المكسب'],
                        datasets: [{
                            data: [
                                <?php echo $total_sales; ?>,
                                <?php echo $total_cost; ?>,
                                <?php echo $total_profit; ?>
                            ],
                            backgroundColor: [
                                '#28a745',
                                '#dc3545',
                                '#3f51b5'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'توزيع المبيعات والأرباح'
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // Inventory Chart
            const inventoryCtx = document.getElementById('inventoryChart');
            if (inventoryCtx) {
                window.inventoryChart = new Chart(inventoryCtx, {
                    type: 'bar',
                    data: {
                        labels: ['الكمية المسجلة', 'الكمية المباعة', 'الكمية المتبقية'],
                        datasets: [{
                            label: 'الكميات',
                            data: [
                                <?php echo $total_recorded_quantity; ?>,
                                <?php echo $total_sold_quantity; ?>,
                                <?php echo $total_closing_quantity; ?>
                            ],
                            backgroundColor: [
                                'rgba(63, 81, 181, 0.8)',
                                'rgba(40, 167, 69, 0.8)',
                                'rgba(255, 193, 7, 0.8)'
                            ],
                            borderColor: [
                                '#3f51b5',
                                '#28a745',
                                '#ffc107'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'حالة المخزون'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        function addLoadingAnimations() {
            // Add loading animation to all buttons
            document.querySelectorAll('.view-details-btn, .quick-action-btn').forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.classList.contains('loading')) {
                        this.classList.add('loading');
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                        
                        setTimeout(() => {
                            this.classList.remove('loading');
                            this.innerHTML = originalText;
                        }, 1000);
                    }
                });
            });
        }

        // Enhanced modal functions with animations
        function showBalanceTransfers(inventoryId) {
            showLoading();
            fetch(`fetch_balance_transfers.php?inventory_id=${inventoryId}`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    const tableBody = document.getElementById('balanceTransfersTableBody');
                    tableBody.innerHTML = '';

                    if (data.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد تحويلات رصيد</td></tr>';
                    } else {
                        data.forEach(transfer => {
                            const createdAt = new Date(transfer.created_at);
                            const options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: true };
                            const formattedDateTime = createdAt.toLocaleString('ar-EG', options);

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${transfer.account_name}</td>
                                <td>${transfer.provider}</td>
                                <td>${parseFloat(transfer.cost).toFixed(2)}</td>
                                <td>${parseFloat(transfer.sale_price).toFixed(2)}</td>
                                <td>${parseFloat(transfer.value).toFixed(2)}</td>
                                <td>${formattedDateTime}</td>
                            `;
                            tableBody.appendChild(row);
                        });
                    }

                    document.getElementById('balanceTransfersModal').classList.add('active');
                    showToast('تم تحميل تفاصيل تحويلات الرصيد', 'success');
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error fetching balance transfers:', error);
                    showToast('حدث خطأ في تحميل البيانات', 'error');
                });
        }

        function showExpenseDetails(inventoryId) {
            showLoading();
            fetch(`fetch_expenses.php?inventory_id=${inventoryId}`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    const tableBody = document.getElementById('expenseDetailsTableBody');
                    tableBody.innerHTML = '';

                    if (data.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 20px; color: #666;">لا توجد مصروفات</td></tr>';
                    } else {
                        data.forEach(expense => {
                            const expenseDate = new Date(expense.expense_date);
                            const formattedDate = expenseDate.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                            const expenseTypeMap = {
                                'Expenses and Damages': 'مصروفات وتوالف',
                                'Credit to Store': 'أجل للمحل',
                                'Credit on Store': 'أجل على المحل'
                            };
                            const expenseTypeArabic = expenseTypeMap[expense.expense_type] || expense.expense_type;

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${expense.expense_name}</td>
                                <td>${expenseTypeArabic}</td>
                                <td>${parseFloat(expense.amount).toFixed(2)}</td>
                                <td>${formattedDate}</td>
                            `;
                            tableBody.appendChild(row);
                        });
                    }

                    document.getElementById('expenseDetailsModal').classList.add('active');
                    showToast('تم تحميل تفاصيل المصروفات', 'success');
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error fetching expense details:', error);
                    showToast('حدث خطأ في تحميل البيانات', 'error');
                });
        }

        function showShiftClosures(inventoryId) {
            showLoading();
            fetch(`fetch_shift_closures.php?inventory_id=${inventoryId}`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    const tableBody = document.getElementById('shiftClosuresTableBody');
                    tableBody.innerHTML = '';

                    if (data.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">لا توجد ورديات</td></tr>';
                    } else {
                        data.forEach(shift => {
                            const shiftDate = new Date(shift.shift_date);
                            const formattedDate = shiftDate.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${shift.account_name}</td>
                                <td>${formattedDate}</td>
                                <td>${shift.shift_type === 'morning' ? 'صباحية' : 'مسائية'}</td>
                                <td>${parseFloat(shift.shift_amount).toFixed(2)}</td>
                                <td>${parseFloat(shift.purchases).toFixed(2)}</td>
                                <td>${shift.notes || 'لا توجد ملاحظات'}</td>
                            `;
                            tableBody.appendChild(row);
                        });
                    }

                    document.getElementById('shiftClosuresModal').classList.add('active');
                    showToast('تم تحميل تفاصيل الورديات', 'success');
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error fetching shift closures:', error);
                    showToast('حدث خطأ في تحميل البيانات', 'error');
                });
        }

        function showPurchaseInvoices(inventoryId) {
            showLoading();
            fetch(`fetch_purchase_invoices.php?inventory_id=${inventoryId}`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    const tableBody = document.getElementById('purchaseInvoicesTableBody');
                    tableBody.innerHTML = '';

                    if (data.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 20px; color: #666;">لا توجد فواتير شراء</td></tr>';
                    } else {
                        data.forEach(invoice => {
                            const createdAt = new Date(invoice.created_at);
                            const formattedDate = createdAt.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${invoice.account_name}</td>
                                <td>${parseFloat(invoice.total_amount).toFixed(2)}</td>
                                <td>${formattedDate}</td>
                                <td>
                                    <button class="view-details-btn" onclick="showInvoiceItems(${invoice.inventory_invoice_id})">
                                        <i class="fas fa-eye"></i> عرض
                                    </button>
                                </td>
                            `;
                            tableBody.appendChild(row);
                        });
                    }

                    document.getElementById('purchaseInvoicesModal').classList.add('active');
                    showToast('تم تحميل فواتير الشراء', 'success');
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error fetching purchase invoices:', error);
                    showToast('حدث خطأ في تحميل البيانات', 'error');
                });
        }

        function showInvoiceItems(invoiceId) {
            showLoading();
            fetch(`fetch_inventory_invoice_items.php?invoice_id=${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    const tableBody = document.getElementById('invoiceItemsTableBody');
                    tableBody.innerHTML = '';

                    if (data.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 20px; color: #666;">لا توجد أصناف</td></tr>';
                    } else {
                        data.forEach(item => {
                            const totalCost = (parseFloat(item.cost) * parseFloat(item.quantity)).toFixed(2);

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${item.item_name}</td>
                                <td>${item.quantity}</td>
                                <td>${parseFloat(item.cost).toFixed(2)}</td>
                                <td>${totalCost}</td>
                            `;
                            tableBody.appendChild(row);
                        });
                    }

                    document.getElementById('invoiceItemsModal').classList.add('active');
                    showToast('تم تحميل أصناف الفاتورة', 'success');
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error fetching invoice items:', error);
                    showToast('حدث خطأ في تحميل البيانات', 'error');
                });
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('enhanced-modal')) {
                event.target.classList.remove('active');
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                document.querySelectorAll('.enhanced-modal.active').forEach(modal => {
                    modal.classList.remove('active');
                });
            }
        });

        // Show welcome message
        window.addEventListener('load', function() {
            setTimeout(() => {
                showToast('مرحباً بك في ملخص الجرد المحسن!', 'success');
            }, 1000);
        });

        // Toggle Explanatory Notes
        function toggleExplanatoryNotes() {
            const notesSection = document.getElementById('explanatoryNotes');
            const toggleText = document.getElementById('notesToggleText');
            const toggleBtn = document.querySelector('.toggle-notes-btn i');
            
            if (notesSection.style.display === 'none') {
                notesSection.style.display = 'block';
                toggleText.textContent = 'إخفاء شرح الأرقام والمصطلحات';
                toggleBtn.className = 'fas fa-eye-slash';
                showToast('تم عرض الشرح التفصيلي للأرقام', 'success');
                
                // Smooth scroll to notes section
                setTimeout(() => {
                    notesSection.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start' 
                    });
                }, 100);
            } else {
                notesSection.style.display = 'none';
                toggleText.textContent = 'عرض شرح الأرقام والمصطلحات';
                toggleBtn.className = 'fas fa-info-circle';
                showToast('تم إخفاء الشرح التفصيلي', 'neutral');
            }
        }

        // Add help tooltips to KPI cards
        function addKPITooltips() {
            // Add tooltips to comparison cards
            const comparisonCards = document.querySelectorAll('.comparison-card');
            
            const tooltips = [
                'هامش الربح يوضح نسبة الربح من التكلفة. كلما زاد الهامش، كلما كانت الربحية أفضل.',
                'معدل دوران المخزون يوضح كفاءة البيع. المعدل العالي يعني بيع سريع للبضاعة.',
                'قيمة المخزون المتبقي تمثل رأس المال المجمد في البضاعة غير المباعة.',
                'صافي الربح هو الربح النهائي بعد خصم جميع المصروفات والتعديلات.'
            ];

            comparisonCards.forEach((card, index) => {
                if (tooltips[index]) {
                    card.setAttribute('title', tooltips[index]);
                    card.style.cursor = 'help';
                }
            });
        }

        // Initialize tooltips when page loads
        document.addEventListener('DOMContentLoaded', function() {
            addKPITooltips();
        });

        // Add contextual help for different sections
        function showContextualHelp(section) {
            let title, content;
            
            switch(section) {
                case 'inventory':
                    title = 'ملخص الأصناف الرئيسي';
                    content = 'يعرض هذا القسم إجمالي الكميات والقيم المالية لجميع أصناف المخزن، ويساعد في فهم حالة المخزون العامة.';
                    break;
                case 'balance':
                    title = 'تحويلات الرصيد';
                    content = 'هي عمليات تحويل رصيد الهاتف بين مزودي الخدمة وأجهزة الشحن، وتشمل التعامل مع مزودين مثل ضامن، فوري، وبساطة';
                    break;
                case 'expenses':
                    title = 'المصروفات والمعادلات';
                    content = 'يشمل جميع المصروفات والتوالف والديون التي تؤثر على صافي الربح النهائي للمخزن.';
                    break;
                case 'shifts':
                    title = 'الورديات والنقدي';
                    content = 'يعرض إجمالي النقدية المحصلة من الورديات المختلفة ويجب أن تتطابق مع إجمالي المبيعات النقدية.';
                    break;
                case 'purchases':
                    title = 'فواتير الشراء';
                    content = 'تعرض إجمالي قيمة فواتير الشراء التي تم إدخالها خلال فترة الجرد.';
                    break;
                default:
                    return;
            }
            
            Swal.fire({
                title: title,
                text: content,
                icon: 'info',
                confirmButtonText: 'فهمت',
                confirmButtonColor: '#3f51b5',
                customClass: {
                    popup: 'rtl-popup'
                }
            });
        }

        // Add help buttons to section headers
        function addSectionHelpButtons() {
            const sections = [
                { selector: '.summary-card:nth-child(1) .card-title', section: 'inventory' },
                { selector: '.summary-card:nth-child(2) .card-title', section: 'balance' },
                { selector: '.summary-card:nth-child(3) .card-title', section: 'expenses' },
                { selector: '.summary-card:nth-child(4) .card-title', section: 'shifts' },
                { selector: '.summary-card:nth-child(5) .card-title', section: 'purchases' }
            ];

            sections.forEach(({ selector, section }) => {
                const titleElement = document.querySelector(selector);
                if (titleElement) {
                    const helpButton = document.createElement('button');
                    helpButton.innerHTML = '<i class="fas fa-question-circle"></i>';
                    helpButton.className = 'metric-help-icon';
                    helpButton.style.cssText = 'background: none; border: none; color: var(--color-primary); cursor: help; margin-left: 10px;';
                    helpButton.onclick = () => showContextualHelp(section);
                    titleElement.appendChild(helpButton);
                }
            });
        }

        // Initialize section help buttons
        setTimeout(addSectionHelpButtons, 1000);
    </script>
        <?php include 'notifications.php'; ?>
    <!-- Add CSS override to ensure global footer appears above content -->
    <style>
        .page-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 3000 !important;
        }
    </style>
</body>
</html>

<?php $conn->close(); ?>
