<?php
include 'db_connection.php';

$item_ids = isset($_GET['item_ids']) ? explode(',', $_GET['item_ids']) : [];

if (empty($item_ids)) {
    echo json_encode([]);
    exit();
}

// Prepare the query to fetch item details
$placeholders = implode(',', array_fill(0, count($item_ids), '?'));
$sql = "SELECT item_id, name, barcode FROM items WHERE item_id IN ($placeholders)";
$stmt = $conn->prepare($sql);

// Bind parameters dynamically
$stmt->bind_param(str_repeat('i', count($item_ids)), ...$item_ids);
$stmt->execute();
$result = $stmt->get_result();

$itemDetails = [];
while ($row = $result->fetch_assoc()) {
    $itemDetails[] = $row;
}

$stmt->close();
$conn->close();

echo json_encode($itemDetails);
?>
