# Security Policy

[![OpenSSF Vulnerability Disclosure](https://img.shields.io/badge/OpenSSF-Vulnerability_Disclosure-green)](https://github.com/ossf/oss-vulnerability-guide/blob/main/finder-guide.md)
[![GitHub Report](https://img.shields.io/badge/GitHub-Security_Advisories-blue)](https://github.com/maennchen/ZipStream-PHP/security/advisories/new)
[![Email Report](https://img.shields.io/badge/Email-jonatan%40maennchen.ch-blue)](mailto:<EMAIL>)

This repository follows the
[OpenSSF Vulnerability Disclosure guide](https://github.com/ossf/oss-vulnerability-guide/tree/main).
You can learn more about it in the
[Finders Guide](https://github.com/ossf/oss-vulnerability-guide/blob/main/finder-guide.md).

Please report vulnerabilities via the
[GitHub Security Vulnerability Reporting](https://github.com/maennchen/ZipStream-PHP/security/advisories/new)
or via email to [`<EMAIL>`](mailto:<EMAIL>) if this does
not work for you.

Our vulnerability management team will respond within 3 working days of your
report. If the issue is confirmed as a vulnerability, we will open a Security
Advisory. This project follows a 90 day disclosure timeline.

If you have questions about reporting security issues, email the vulnerability
management team: [`<EMAIL>`](mailto:<EMAIL>)
