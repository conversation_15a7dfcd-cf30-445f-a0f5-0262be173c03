on:
  workflow_call:

name: "Test"

permissions:
  contents: read

jobs:
  phpunit:
    name: P<PERSON>Unit (PHP ${{ matrix.php }} on ${{ matrix.os }})

    runs-on: ${{ matrix.os }}

    continue-on-error: ${{ matrix.experimental }}

    strategy:
      fail-fast: false
      matrix:
        php: ["8.2", "8.3", "8.4"]
        os: [ubuntu-latest]
        experimental: [false]
        include:
          - php: nightly
            os: ubuntu-latest
            experimental: true
          - php: "8.4"
            os: windows-latest
            experimental: false
          - php: "8.4"
            os: macos-latest
            experimental: false

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: SetUp PHP
        id: setup-php
        uses: shivammathur/setup-php@9e72090525849c5e82e596468b86eb55e9cc5401 # v2
        with:
          php-version: "${{ matrix.php }}"
          tools: phpunit
          coverage: xdebug
          extensions: xdebug,zip
      - name: Get composer cache directory
        id: composer-cache-common
        if: "${{ runner.os != 'Windows' }}"
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      - name: Get composer cache directory
        id: composer-cache-windows
        if: "${{ runner.os == 'Windows' }}"
        run: echo "dir=$(composer config cache-files-dir)" >> $env:GITHUB_OUTPUT
      - name: Cache Deps
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        id: cache
        with:
          path: ${{ steps.composer-cache-common.outputs.dir }}${{ steps.composer-cache-windows.outputs.dir }}
          key: deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-composer-
            deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-
            deps-${{ steps.setup-php.outputs.php-version }}-
            deps-
      - name: Install Deps
        if: matrix.php != 'nightly'
        run: composer install --prefer-dist
      - name: Install Deps (ignore PHP requirement)
        if: matrix.php == 'nightly'
        run: composer install --prefer-dist --ignore-platform-req=php+
      - name: Run PHPUnit
        run: composer run test:unit:cov
      - name: Upload coverage results to Coveralls
        env:
          COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COVERALLS_PARALLEL: true
          COVERALLS_FLAG_NAME: ${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}
        run: composer run coverage:report
        continue-on-error: ${{ matrix.experimental }}

  mark_coverage_done:
    needs: ["phpunit"]

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Coveralls Finished
        uses: coverallsapp/github-action@648a8eb78e6d50909eff900e4ec85cab4524a45b # v2.3.6
        with:
          github-token: ${{ secrets.github_token }}
          parallel-finished: true

  psalm:
    name: Run Psalm

    runs-on: "ubuntu-latest"

    permissions:
      security-events: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: SetUp PHP
        id: setup-php
        uses: shivammathur/setup-php@9e72090525849c5e82e596468b86eb55e9cc5401 # v2
        with:
          php-version: "8.3"
      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      - name: Cache Deps
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        id: cache
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-composer-
            deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-
            deps-${{ steps.setup-php.outputs.php-version }}-
            deps-
      - name: Install Deps
        run: composer install --prefer-dist
      - name: Run Psalm
        run: composer run test:lint -- --report=results.sarif
      - name: "Upload SARIF"
        uses: github/codeql-action/upload-sarif@f6091c0113d1dcf9b98e269ee48e8a7e51b7bdd4 # v3
        with:
          sarif_file: results.sarif

  php-cs:
    name: Run PHP-CS

    runs-on: "ubuntu-latest"

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: SetUp PHP
        id: setup-php
        uses: shivammathur/setup-php@9e72090525849c5e82e596468b86eb55e9cc5401 # v2
        with:
          php-version: "8.3"
      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      - name: Cache Deps
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        id: cache
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-composer-
            deps-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-
            deps-${{ steps.setup-php.outputs.php-version }}-
            deps-
      - name: Install Deps
        run: composer install --prefer-dist
      - name: Run PHP-CS
        run: composer run test:formatted
