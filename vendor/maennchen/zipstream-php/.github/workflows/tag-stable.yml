on:
  push:
    tags:
      - "[0-9]+.[0-9]+.[0-9]+"

name: "Stable Tag"

permissions:
  contents: read

jobs:
  docs:
    name: "<PERSON><PERSON>"

    uses: ./.github/workflows/part_docs.yml

  release:
    name: "Release"

    needs: ["docs"]

    permissions:
      id-token: write
      contents: write
      attestations: write

    uses: ./.github/workflows/part_release.yml
    with:
      releaseName: "${{ github.ref_name }}"
      stable: true

  deploy_pages:
    name: "Deploy to GitHub Pages"

    needs: ["release", "docs"]

    runs-on: ubuntu-latest

    permissions:
      pages: write
      id-token: write

    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@d6db90164ac5ed86f2b6aed7e0febac5b3c0c03e # v4.0.5
