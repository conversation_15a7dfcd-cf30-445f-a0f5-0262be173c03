<?php
require_once 'db_connection.php';
require_once 'security.php';
require_once 'encryption_functions.php';

// فحص صلاحية الوصول للوحدة
checkPagePermission('inventory', 'access');

$key = getenv('ENCRYPTION_KEY');

// تحديد الفرع إذا كان محدداً
$store_id = null;
$where_clause = '';

if (isset($_GET['store_id'])) {
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);
    $where_clause = "WHERE store_id = ?";
}

// استعلام الإحصائيات
$stats_sql = "SELECT 
    COUNT(*) as total_inventories,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_inventories,
    SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as completed_inventories
    FROM monthly_inventory 
    $where_clause";

$stmt = $conn->prepare($stats_sql);

if ($where_clause) {
    $stmt->bind_param("i", $store_id);
}

$stmt->execute();
$result = $stmt->get_result();
$stats = $result->fetch_assoc();
$stmt->close();

// إرجاع النتائج كـ JSON
header('Content-Type: application/json; charset=utf-8');
echo json_encode([
    'success' => true,
    'total' => (int)$stats['total_inventories'],
    'pending' => (int)$stats['pending_inventories'],
    'completed' => (int)$stats['completed_inventories']
]);

$conn->close();
?>