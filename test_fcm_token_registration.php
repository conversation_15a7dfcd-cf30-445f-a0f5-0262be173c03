<?php
/**
 * Test FCM Token Registration
 * 
 * This file is used to test if FCM token registration is working properly
 */

session_start();
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// Check if user is logged in
if (!isset($_SESSION['account_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'User not logged in'
    ]);
    exit;
}

$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';

$store_id = decrypt($encrypted_store_id, $key);
$account_id = decrypt($encrypted_account_id, $key);

// Get user info
$user_query = "SELECT username, role FROM accounts WHERE account_id = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("i", $account_id);
$user_stmt->execute();
$user_result = $user_stmt->get_result();
$user_data = $user_result->fetch_assoc();

// Get store info
$store_query = "SELECT name FROM stores WHERE store_id = ?";
$store_stmt = $conn->prepare($store_query);
$store_stmt->bind_param("i", $store_id);
$store_stmt->execute();
$store_result = $store_stmt->get_result();
$store_data = $store_result->fetch_assoc();

// Get FCM tokens for this user
$tokens_query = "SELECT token, created_at, last_updated FROM fcm_tokens WHERE account_id = ? AND store_id = ?";
$tokens_stmt = $conn->prepare($tokens_query);
$tokens_stmt->bind_param("ii", $account_id, $store_id);
$tokens_stmt->execute();
$tokens_result = $tokens_stmt->get_result();

$tokens = [];
while ($row = $tokens_result->fetch_assoc()) {
    $tokens[] = $row;
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل رموز FCM</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="web_css/style_web.css">
    <style>
        .test-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .token-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            word-break: break-all;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 5px;
        }

        .status-indicator.success {
            background-color: #28a745;
        }

        .status-indicator.warning {
            background-color: #ffc107;
        }

        .status-indicator.danger {
            background-color: #dc3545;
        }

        [data-theme='dark'] .test-card {
            background-color: #2d3748;
        }

        [data-theme='dark'] .token-item {
            background-color: #1a202c;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <div class="content">
        <div class="container mt-4">
            <h1 class="mb-4">اختبار تسجيل رموز FCM</h1>

            <div class="row">
                <div class="col-md-12">
                    <div class="test-card">
                        <h3 class="mb-3">معلومات المستخدم</h3>
                        <p><strong>اسم المستخدم:</strong> <?= htmlspecialchars($user_data['username']) ?></p>
                        <p><strong>الدور:</strong> <?= htmlspecialchars($user_data['role']) ?></p>
                        <p><strong>المتجر:</strong> <?= htmlspecialchars($store_data['name']) ?></p>
                        <p><strong>معرف الحساب:</strong> <?= $account_id ?></p>
                        <p><strong>معرف المتجر:</strong> <?= $store_id ?></p>
                    </div>

                    <div class="test-card">
                        <h3 class="mb-3">حالة Firebase</h3>
                        <div id="firebaseStatus">
                            <p><span class="status-indicator warning"></span> جاري التحقق من Firebase...</p>
                        </div>
                        <div id="permissionStatus">
                            <p><span class="status-indicator warning"></span> جاري التحقق من أذونات الإشعارات...</p>
                        </div>
                        <div id="tokenStatus">
                            <p><span class="status-indicator warning"></span> جاري التحقق من رمز FCM...</p>
                        </div>
                    </div>

                    <div class="test-card">
                        <h3 class="mb-3">الرموز المسجلة في قاعدة البيانات</h3>
                        <?php if (count($tokens) > 0): ?>
                            <?php foreach ($tokens as $token): ?>
                                <div class="token-item">
                                    <p><strong>الرمز:</strong> <code><?= htmlspecialchars(substr($token['token'], 0, 50)) ?>...</code></p>
                                    <p><strong>تاريخ الإنشاء:</strong> <?= htmlspecialchars($token['created_at']) ?></p>
                                    <p><strong>آخر تحديث:</strong> <?= htmlspecialchars($token['last_updated']) ?></p>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-warning">لا توجد رموز FCM مسجلة لهذا المستخدم.</p>
                        <?php endif; ?>
                    </div>

                    <div class="test-card">
                        <h3 class="mb-3">اختبارات</h3>
                        <button id="testPermissionBtn" class="btn btn-primary me-2">اختبار طلب الإذن</button>
                        <button id="testTokenBtn" class="btn btn-success me-2">اختبار الحصول على الرمز</button>
                        <button id="testSaveBtn" class="btn btn-warning me-2">اختبار حفظ الرمز</button>
                        <button id="refreshPageBtn" class="btn btn-info">تحديث الصفحة</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <!-- Firebase Messaging -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
            authDomain: "macm-84114.firebaseapp.com",
            projectId: "macm-84114",
            storageBucket: "macm-84114.firebasestorage.app",
            messagingSenderId: "860043675105",
            appId: "1:860043675105:web:72586005d5bd035ff8bea0"
        };

        const vapidKey = 'BMVJO7rt5hONuPb0UzJm2B9T52CuXtcjsWDmHKXf8ass2zyctrBrjXWncazpezhWSdBbcrr8pPcegRixWaTiSBI';

        let messaging = null;

        $(document).ready(function() {
            // Initialize Firebase
            try {
                firebase.initializeApp(firebaseConfig);
                messaging = firebase.messaging();
                updateStatus('firebaseStatus', 'success', 'Firebase تم تهيئته بنجاح');
            } catch (error) {
                updateStatus('firebaseStatus', 'danger', 'خطأ في تهيئة Firebase: ' + error.message);
            }

            // Check notification permission
            checkPermissionStatus();

            // Check FCM token
            checkTokenStatus();

            // Button event handlers
            $('#testPermissionBtn').click(testPermission);
            $('#testTokenBtn').click(testToken);
            $('#testSaveBtn').click(testSaveToken);
            $('#refreshPageBtn').click(() => location.reload());
        });

        function updateStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            const indicator = element.querySelector('.status-indicator');
            const text = element.querySelector('p');
            
            indicator.className = `status-indicator ${type}`;
            text.innerHTML = `<span class="status-indicator ${type}"></span> ${message}`;
        }

        function checkPermissionStatus() {
            if (!('Notification' in window)) {
                updateStatus('permissionStatus', 'danger', 'الإشعارات غير مدعومة في هذا المتصفح');
                return;
            }

            const permission = Notification.permission;
            if (permission === 'granted') {
                updateStatus('permissionStatus', 'success', 'تم منح إذن الإشعارات');
            } else if (permission === 'denied') {
                updateStatus('permissionStatus', 'danger', 'تم رفض إذن الإشعارات');
            } else {
                updateStatus('permissionStatus', 'warning', 'لم يتم طلب إذن الإشعارات بعد');
            }
        }

        function checkTokenStatus() {
            if (!messaging) {
                updateStatus('tokenStatus', 'danger', 'Firebase Messaging غير متاح');
                return;
            }

            messaging.getToken({ vapidKey: vapidKey })
                .then((currentToken) => {
                    if (currentToken) {
                        updateStatus('tokenStatus', 'success', `تم الحصول على رمز FCM: ${currentToken.substring(0, 20)}...`);
                    } else {
                        updateStatus('tokenStatus', 'warning', 'لم يتم الحصول على رمز FCM');
                    }
                })
                .catch((err) => {
                    updateStatus('tokenStatus', 'danger', 'خطأ في الحصول على رمز FCM: ' + err.message);
                });
        }

        function testPermission() {
            Notification.requestPermission().then((permission) => {
                checkPermissionStatus();
                Swal.fire({
                    icon: permission === 'granted' ? 'success' : 'error',
                    title: permission === 'granted' ? 'تم منح الإذن' : 'لم يتم منح الإذن',
                    timer: 2000
                });
            });
        }

        function testToken() {
            if (!messaging) {
                Swal.fire('خطأ', 'Firebase Messaging غير متاح', 'error');
                return;
            }

            messaging.getToken({ vapidKey: vapidKey })
                .then((currentToken) => {
                    if (currentToken) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الحصول على الرمز',
                            text: currentToken.substring(0, 50) + '...',
                            timer: 3000
                        });
                        checkTokenStatus();
                    } else {
                        Swal.fire('تحذير', 'لم يتم الحصول على رمز FCM', 'warning');
                    }
                })
                .catch((err) => {
                    Swal.fire('خطأ', 'خطأ في الحصول على الرمز: ' + err.message, 'error');
                });
        }

        function testSaveToken() {
            if (!messaging) {
                Swal.fire('خطأ', 'Firebase Messaging غير متاح', 'error');
                return;
            }

            messaging.getToken({ vapidKey: vapidKey })
                .then((currentToken) => {
                    if (currentToken) {
                        return fetch('register_fcm_token_after_login.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ token: currentToken }),
                        });
                    } else {
                        throw new Error('لم يتم الحصول على رمز FCM');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('نجح', 'تم حفظ الرمز بنجاح: ' + data.action, 'success');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        Swal.fire('خطأ', 'فشل حفظ الرمز: ' + data.message, 'error');
                    }
                })
                .catch((error) => {
                    Swal.fire('خطأ', 'خطأ في حفظ الرمز: ' + error.message, 'error');
                });
        }
    </script>
</body>
</html>
