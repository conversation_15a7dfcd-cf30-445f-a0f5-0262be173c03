<!-- تطبيق الثيم فوراً قبل تحميل أي شيء آخر لتجنب الوميض -->
<script>
(function() {
    'use strict';
    
    // التحقق من وجود الثيم مطبق مسبقاً لتجنب التكرار
    if (document.documentElement.hasAttribute('data-theme-applied')) {
        return;
    }
    
    // تطبيق الثيم فوراً من localStorage أو من قاعدة البيانات
    let appliedTheme = 'light'; // القيمة الافتراضية
    
    // محاولة الحصول على الثيم من localStorage أولاً (الأسرع)
    const savedTheme = localStorage.getItem('userTheme');
    
    if (savedTheme) {
        appliedTheme = savedTheme.toLowerCase();
    } else {
        // إذا لم يكن محفوظ في localStorage، استخدم القيمة من PHP
        const userThemeFromDB = '<?= $userTheme ?? "Light" ?>';
        appliedTheme = userThemeFromDB.toLowerCase();
        // حفظ في localStorage للمرات القادمة
        localStorage.setItem('userTheme', appliedTheme);
    }
    
    // تطبيق الثيم فوراً على HTML
    document.documentElement.setAttribute('data-theme', appliedTheme);
    document.documentElement.setAttribute('data-theme-applied', 'true');
    
    if (appliedTheme === 'dark') {
        document.documentElement.classList.add('dark-mode');
        // تطبيق على الجسم إذا كان موجوداً، وإلا انتظار تحميله
        if (document.body) {
            document.body.classList.add('dark-mode');
        } else {
            // إضافة مستمع لتطبيق الثيم على الجسم عند تحميله
            document.addEventListener('DOMContentLoaded', function() {
                if (!document.body.classList.contains('dark-mode')) {
                    document.body.classList.add('dark-mode');
                }
            });
        }
    } else {
        document.documentElement.classList.remove('dark-mode');
        if (document.body) {
            document.body.classList.remove('dark-mode');
        } else {
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.remove('dark-mode');
            });
        }
    }
    
    // منع الانتقالات أثناء التحميل الأولي
    document.documentElement.classList.add('theme-loading');
    
    // إزالة كلاس التحميل بعد فترة قصيرة
    setTimeout(function() {
        document.documentElement.classList.remove('theme-loading');
    }, 50);
})();
</script>

<!-- CSS فوري لتطبيق الثيم على اللودر -->
<style>
/* تطبيق الثيم فوراً لتجنب الوميض */
.theme-loading * {
    transition: none !important;
}

/* تطبيق الثيم على اللودر فوراً */
html[data-theme="dark"] #page-loader,
html.dark-mode #page-loader {
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #242b3d 100%) !important;
}

html[data-theme="dark"] .brand-name,
html.dark-mode .brand-name {
    color: #5b9bd5 !important;
    text-shadow: 0 4px 20px rgba(91, 155, 213, 0.3) !important;
}

html[data-theme="dark"] .dot,
html.dark-mode .dot {
    background: #5b9bd5 !important;
    box-shadow: 0 2px 10px rgba(91, 155, 213, 0.3) !important;
}

html[data-theme="dark"] .loading-text,
html.dark-mode .loading-text {
    color: #e1e8f0 !important;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5) !important;
}

html[data-theme="dark"] .loading-subtitle,
html.dark-mode .loading-subtitle {
    color: rgba(225, 232, 240, 0.7) !important;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5) !important;
}

html[data-theme="dark"] .particle,
html.dark-mode .particle {
    background: rgba(91, 155, 213, 0.15) !important;
}
</style>

<!-- CSS صفحة التحميل - يجب أن يكون في المقدمة -->
<style>
/* Page Loader Styles - Critical CSS */
#page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999999;
    transition: opacity 0.8s ease, visibility 0.8s ease;
    font-family: 'Cairo', Arial, sans-serif;
    overflow: hidden;
    pointer-events: auto;
}

.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

.loader-brand {
    position: relative;
    z-index: 2;
    text-align: center;
    margin-bottom: 30px;
}

.brand-name {
    font-size: 3rem;
    font-weight: 700;
    color: #007bff;
    text-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
    margin-bottom: 20px;
    letter-spacing: 2px;
    animation: brandPulse 2s ease-in-out infinite;
}

@keyframes brandPulse {
    0%, 100% {
        transform: scale(1);
        text-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        text-shadow: 0 6px 30px rgba(0, 123, 255, 0.5);
    }
}

.dots-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.dot {
    width: 12px;
    height: 12px;
    background: #007bff;
    border-radius: 50%;
    animation: dotBounce 1.4s ease-in-out infinite both;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.loading-text {
    position: relative;
    z-index: 2;
    font-size: 1.5rem;
    color: #333;
    margin-top: 20px;
    font-weight: 600;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: textFade 2s ease-in-out infinite;
}

.loading-subtitle {
    position: relative;
    z-index: 2;
    font-size: 1rem;
    color: rgba(51, 51, 51, 0.8);
    margin-top: 10px;
    font-weight: 400;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

@keyframes textFade {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

/* Dark Mode Loader Styles */
.dark-mode #page-loader {
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #242b3d 100%);
}

.dark-mode .particle {
    background: rgba(91, 155, 213, 0.15);
}

.dark-mode .brand-name {
    color: #5b9bd5;
    text-shadow: 0 4px 20px rgba(91, 155, 213, 0.3);
}

.dark-mode .dot {
    background: #5b9bd5;
    box-shadow: 0 2px 10px rgba(91, 155, 213, 0.3);
}

.dark-mode .loading-text {
    color: #e1e8f0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.dark-mode .loading-subtitle {
    color: rgba(225, 232, 240, 0.7);
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
}
</style>

<!-- JavaScript فوري لإظهار صفحة التحميل -->
<script>
// إظهار صفحة التحميل فوراً
document.addEventListener("DOMContentLoaded", function() {
    const loader = document.getElementById('page-loader');
    if (loader) {
        loader.style.display = 'flex';
        loader.style.visibility = 'visible';
        loader.style.opacity = '1';
    }
});

// إنشاء الجسيمات فوراً
function createParticles() {
    const particlesContainer = document.getElementById('particles-container');
    if (!particlesContainer) return;

    const particleCount = 15;
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (6 + Math.random() * 4) + 's';
        const size = 3 + Math.random() * 3;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particlesContainer.appendChild(particle);
    }
}

// تشغيل الجسيمات فوراً عند تحميل DOM
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', createParticles);
} else {
    createParticles();
}
</script>

<?php
// تحميل نظام الحماية والصلاحيات
if (!defined('SECURITY_MIDDLEWARE_LOADED')) {
    require_once 'security.php';
}

// Fetch unread notifications count for the specific user
$unreadQuery = "SELECT COUNT(*) as unread_count
                FROM notifications n
                LEFT JOIN notification_reads nr ON n.id = nr.notification_id AND nr.account_id = ?
                WHERE n.store_id = ? AND nr.id IS NULL AND n.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
$unreadStmt = $conn->prepare($unreadQuery);
$unreadStmt->bind_param("ii", $account_id, $store_id);
$unreadStmt->execute();
$unreadResult = $unreadStmt->get_result();
$unreadCount = $unreadResult->fetch_assoc()['unread_count'];

// Fetch the user's profile image path and access type
$query = "SELECT img_path, access_type FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$imgPath = $user['img_path'] ?? null;
$access_type = $user['access_type'] ?? 'cashier_system';

// Determine the current page
$current_page = basename($_SERVER['PHP_SELF']);

// دوال مساعدة للتحقق من الصلاحيات في bottom_nav
function hasBottomNavPermission($module, $permission = 'access') {
    global $permissions_system, $role;
    
    if (!$permissions_system) {
        // في حالة عدم وجود نظام الصلاحيات، استخدم النظام القديم
        return true;
    }
    
    return $permissions_system->hasPermission($module, $permission);
}

function isBottomNavAdmin() {
    global $permissions_system, $role;
    
    if (!$permissions_system) {
        return $role === 'admin';
    }
    
    return $permissions_system->isAdmin();
}
?>

<!-- Page Loader -->
<div id="page-loader">
    <!-- Particles Background -->
    <div class="particles-container" id="particles-container">
        <!-- Particles will be generated by JavaScript -->
    </div>
    
    <!-- Brand Logo Container -->
    <div class="loader-brand">
        <div class="brand-name">Elwaled Market</div>
        
        <!-- Simple Animated Dots Loader -->
        <div class="dots-loader">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
    </div>
    
    <div class="loading-text">جاري التحميل...</div>
    <div class="loading-subtitle">يرجى الانتظار قليلاً</div>
</div>

<div class="bottom-nav">
    <?php
    // تحديد الروابط حسب نوع الوصول ونظام الصلاحيات
    $nav_links = [];
    
    if ($access_type === 'cashier_system') {
        // روابط نظام الكاشير
        if (hasBottomNavPermission('cashier_home', 'access')) {
            $nav_links[] = [
                'url' => "users.php?account_id=" . urlencode($encrypted_account_id),
                'icon' => 'fas fa-home',
                'text' => 'الرئيسية',
                'page' => 'users.php'
            ];
        }
        
        if (hasBottomNavPermission('cashier_shift_closure', 'access')) {
            $nav_links[] = [
                'url' => "shift_closure.php?account_id=" . urlencode($encrypted_account_id),
                'icon' => 'fas fa-cash-register',
                'text' => 'الوردية',
                'page' => 'shift_closure.php'
            ];
        }
        
        if (hasBottomNavPermission('cashier_invoices', 'access')) {
            $nav_links[] = [
                'url' => "add_invoice.php?account_id=" . urlencode($encrypted_account_id) . "&multi_customer=true",
                'icon' => 'fas fa-file-invoice',
                'text' => 'فاتورة',
                'page' => 'add_invoice.php'
            ];
        }
        
        // الإشعارات متاحة دائماً
        $nav_links[] = [
            'url' => "javascript:void(0);",
            'icon' => 'fas fa-bell',
            'text' => 'الإشعارات',
            'page' => 'notifications.php',
            'onclick' => 'showNotifications()',
            'id' => 'notifications-icon',
            'extra' => '<span class="notification-count">' . $unreadCount . '</span>'
        ];
        
        if (hasBottomNavPermission('cashier_account', 'access')) {
            $profile_img = '';
            if ($imgPath) {
                $profile_img = '<img src="' . htmlspecialchars($imgPath) . '" alt="Profile" class="nav-profile-img">';
            } else {
                $profile_img = '<div class="nav-default-profile"><i class="fas fa-user"></i></div>';
            }
            
            $nav_links[] = [
                'url' => "user_account.php?account_id=" . urlencode($encrypted_account_id),
                'icon' => $profile_img,
                'text' => 'الحساب',
                'page' => 'user_account.php',
                'is_profile' => true
            ];
        }
        
    } else {
        // روابط النظام الإداري
        if (hasBottomNavPermission('dashboard', 'view') || isBottomNavAdmin()) {
            $nav_links[] = [
                'url' => "stores.php",
                'icon' => 'fas fa-tachometer-alt',
                'text' => 'لوحة التحكم',
                'page' => 'stores.php'
            ];
        }
        
        if (hasBottomNavPermission('invoices', 'view')) {
            $nav_links[] = [
                'url' => "invoices.php",
                'icon' => 'fas fa-file-invoice-dollar',
                'text' => 'الفواتير',
                'page' => 'invoices.php'
            ];
        }
        
        if (hasBottomNavPermission('products', 'view')) {
            $nav_links[] = [
                'url' => "items.php",
                'icon' => 'fas fa-box',
                'text' => 'المنتجات',
                'page' => 'items.php'
            ];
        }
        
        if (hasBottomNavPermission('reports', 'view')) {
            $nav_links[] = [
                'url' => "reports.php",
                'icon' => 'fas fa-chart-bar',
                'text' => 'التقارير',
                'page' => 'reports.php'
            ];
        }
        
        // الإشعارات متاحة دائماً
        $nav_links[] = [
            'url' => "javascript:void(0);",
            'icon' => 'fas fa-bell',
            'text' => 'الإشعارات',
            'page' => 'notifications.php',
            'onclick' => 'showNotifications()',
            'id' => 'notifications-icon',
            'extra' => '<span class="notification-count">' . $unreadCount . '</span>'
        ];
        
        // الحساب متاح دائماً
        $profile_img = '';
        if ($imgPath) {
            $profile_img = '<img src="' . htmlspecialchars($imgPath) . '" alt="Profile" class="nav-profile-img">';
        } else {
            $profile_img = '<div class="nav-default-profile"><i class="fas fa-user"></i></div>';
        }
        
        $nav_links[] = [
            'url' => "account_settings.php",
            'icon' => $profile_img,
            'text' => 'الحساب',
            'page' => 'account_settings.php',
            'is_profile' => true
        ];
    }
    
    // عرض الروابط
    foreach ($nav_links as $link) {
        $active_class = ($current_page === $link['page']) ? 'active' : '';
        $onclick = isset($link['onclick']) ? 'onclick="' . $link['onclick'] . '"' : '';
        $id = isset($link['id']) ? 'id="' . $link['id'] . '"' : '';
        $extra = isset($link['extra']) ? $link['extra'] : '';
        
        echo '<a href="' . $link['url'] . '" class="' . $active_class . '" ' . $onclick . ' ' . $id . '>';
        
        if (isset($link['is_profile']) && $link['is_profile']) {
            echo $link['icon'];
        } else {
            echo '<i class="' . $link['icon'] . '"></i>';
        }
        
        echo '<span>' . $link['text'] . '</span>';
        echo $extra;
        echo '</a>';
    }
    
    // إضافة رابط إضافي للمديرين للوصول للنظام الإداري من نظام الكاشير
    if ($access_type === 'cashier_system' && isBottomNavAdmin()) {
        echo '<a href="stores.php" class="admin-panel-link" title="الوصول للنظام الإداري">';
        echo '<i class="fas fa-cogs"></i>';
        echo '<span>الإدارة</span>';
        echo '</a>';
    }
    ?>
</div>

<style>
/* Bottom Navigation Bar Styles */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 65px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    border-top: 1px solid rgba(0, 123, 255, 0.1);
    backdrop-filter: blur(10px);
}

.bottom-nav a {
    text-decoration: none;
    color: #333;
    font-size: 0.85rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    position: relative;
    border-radius: 12px;
    transition: all 0.3s ease;
    min-width: 50px;
}

.bottom-nav a:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateY(-2px);
}

.bottom-nav a.active {
    color: #007bff;
    font-weight: bold;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.bottom-nav a i {
    font-size: 20px;
    margin-bottom: 3px;
    transition: all 0.3s ease;
}

.bottom-nav a:hover i {
    transform: scale(1.1);
}

.bottom-nav a span {
    font-weight: 500;
    font-family: 'Cairo', Arial, sans-serif;
}

.notification-count {
    position: absolute;
    top: -2px;
    right: 8px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.nav-profile-img {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 3px;
    border: 2px solid rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
}

.bottom-nav a:hover .nav-profile-img {
    border-color: #007bff;
    transform: scale(1.05);
}

.nav-default-profile {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 3px;
    border: 2px solid rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
}

.bottom-nav a:hover .nav-default-profile {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    transform: scale(1.05);
}

/* Dark Mode Styles */
.dark-mode .bottom-nav {
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #242b3d 100%);
    border-top: 1px solid #3a4553;
    box-shadow: 0 -4px 25px rgba(26, 35, 50, 0.4);
}

.dark-mode .bottom-nav a {
    color: #e1e8f0;
}

.dark-mode .bottom-nav a:hover {
    background-color: rgba(91, 155, 213, 0.12);
}

.dark-mode .bottom-nav a.active {
    color: #5b9bd5;
    background: linear-gradient(135deg, rgba(91, 155, 213, 0.15) 0%, rgba(74, 144, 194, 0.08) 100%);
    border: 1px solid rgba(91, 155, 213, 0.25);
}

.dark-mode .bottom-nav a.active i {
    color: #5b9bd5;
}

.dark-mode .nav-profile-img {
    border-color: rgba(91, 155, 213, 0.25);
}

.dark-mode .bottom-nav a:hover .nav-profile-img {
    border-color: #5b9bd5;
}

.dark-mode .nav-default-profile {
    background: linear-gradient(135deg, #2a3441 0%, #242b3d 100%);
    color: #e1e8f0;
    border-color: rgba(91, 155, 213, 0.25);
}

.dark-mode .bottom-nav a:hover .nav-default-profile {
    border-color: #5b9bd5;
    background: linear-gradient(135deg, #5b9bd5 0%, #4a90c2 100%);
    color: white;
}

/* Admin Panel Link Styles */
.admin-panel-link {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    border-radius: 8px !important;
    margin: 0 2px !important;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3) !important;
}

.admin-panel-link:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4) !important;
}

.admin-panel-link i,
.admin-panel-link span {
    color: white !important;
}

.dark-mode .admin-panel-link {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4) !important;
}

.dark-mode .admin-panel-link:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.5) !important;
}
.notification-container {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
        }

        .notification-item {
            background-color: #f1f1f1;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 123, 255, 0.1);
        }

        .notification-item:hover {
            background-color: #e9ecef;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .notification-message {
            font-size: 16px;
            margin: 0;
            color: #333;
            font-weight: 500;
        }

        .notification-time {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        /* Dark Mode Notification Styles */
        .dark-mode .notification-container {
            background: var(--dark-surface-light);
            border: 1px solid var(--dark-border-light);
            border-radius: 12px;
        }

        .dark-mode .notification-container::-webkit-scrollbar {
            width: 8px;
        }

        .dark-mode .notification-container::-webkit-scrollbar-track {
            background: var(--dark-surface);
            border-radius: 4px;
        }

        .dark-mode .notification-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            border-radius: 4px;
        }

        .dark-mode .notification-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--blue-accent) 0%, var(--blue-soft) 100%);
        }

        .dark-mode .notification-item {
            background: linear-gradient(135deg, var(--dark-surface) 0%, var(--dark-surface-light) 100%);
            border: 1px solid var(--dark-border-light);
            box-shadow: 0 2px 8px rgba(26, 35, 50, 0.3);
            color: var(--dark-text);
        }

        .dark-mode .notification-item:hover {
            background: linear-gradient(135deg, var(--dark-surface-hover) 0%, var(--dark-surface-light) 100%);
            border-color: var(--blue-soft);
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.2);
            transform: translateY(-2px);
        }

        .dark-mode .notification-item.read {
            background: linear-gradient(135deg, var(--dark-surface) 0%, rgba(42, 52, 65, 0.7) 100%);
            opacity: 0.8;
            border-color: var(--dark-border);
        }

        .dark-mode .notification-item.read:hover {
            opacity: 1;
            background: linear-gradient(135deg, var(--dark-surface-hover) 0%, rgba(42, 52, 65, 0.9) 100%);
        }

        .dark-mode .notification-message {
            color: var(--dark-text);
            font-weight: 500;
        }

        .dark-mode .notification-time {
            color: var(--dark-text-muted);
        }

        /* تحسين نافذة الإشعارات في SweetAlert */
        .dark-mode .swal2-popup .notification-container {
            background: var(--dark-surface-light) !important;
            border: 1px solid var(--dark-border-light) !important;
            border-radius: 12px !important;
            max-height: 400px !important;
            overflow-y: auto !important;
        }

        .dark-mode .swal2-popup .notification-item {
            background: linear-gradient(135deg, var(--dark-surface) 0%, var(--dark-surface-light) 100%) !important;
            border: 1px solid var(--dark-border-light) !important;
            color: var(--dark-text) !important;
            margin-bottom: 10px !important;
            padding: 12px !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }

        .dark-mode .swal2-popup .notification-item:hover {
            background: linear-gradient(135deg, var(--dark-surface-hover) 0%, var(--dark-surface-light) 100%) !important;
            border-color: var(--blue-soft) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(91, 155, 213, 0.15) !important;
        }

        .dark-mode .swal2-popup .notification-message {
            color: var(--dark-text) !important;
            font-weight: 500 !important;
            margin: 0 !important;
        }

        .dark-mode .swal2-popup .notification-time {
            color: var(--dark-text-muted) !important;
            font-size: 12px !important;
            margin-top: 5px !important;
        }

        /* تحسين زر "عرض كل الإشعارات" في الثيم الداكن */
        .dark-mode .swal2-popup .btn-primary {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
            border: none !important;
            color: var(--white) !important;
            font-family: 'Cairo', Arial, sans-serif !important;
            font-weight: 600 !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 3px 10px rgba(91, 155, 213, 0.3) !important;
        }

        .dark-mode .swal2-popup .btn-primary:hover {
            background: linear-gradient(135deg, var(--blue-accent) 0%, var(--blue-soft) 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 5px 15px rgba(91, 155, 213, 0.4) !important;
        }

        /* تحسين حالة "لا توجد إشعارات" */
        .dark-mode .swal2-popup .notification-item:only-child {
            text-align: center !important;
            font-style: italic !important;
            color: var(--dark-text-muted) !important;
            background: rgba(91, 155, 213, 0.05) !important;
            border: 2px dashed var(--dark-border-light) !important;
        }

        /* Dark Mode SweetAlert2 Styles */
        .dark-mode .swal2-popup {
            background: linear-gradient(135deg, var(--dark-surface-light) 0%, var(--dark-surface) 100%) !important;
            color: var(--dark-text) !important;
            border: 2px solid var(--dark-border-light) !important;
            border-radius: 15px !important;
            box-shadow: 0 15px 50px rgba(26, 35, 50, 0.4) !important;
        }

        .dark-mode .swal2-title {
            color: var(--blue-soft) !important;
        }

        .dark-mode .swal2-content {
            color: var(--dark-text-secondary) !important;
        }

        .dark-mode .swal2-confirm {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%) !important;
            border: none !important;
            border-radius: 10px !important;
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3) !important;
            font-family: 'Cairo', Arial, sans-serif !important;
            font-weight: 600 !important;
        }

        .dark-mode .swal2-confirm:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4) !important;
        }

        .dark-mode .swal2-cancel {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #5a6268 100%) !important;
            border: none !important;
            border-radius: 10px !important;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
            font-family: 'Cairo', Arial, sans-serif !important;
            font-weight: 600 !important;
            color: var(--white) !important;
        }

        .dark-mode .swal2-cancel:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
        }

        .dark-mode .swal2-icon.swal2-success [class^='swal2-success-line'] {
            background-color: var(--blue-soft) !important;
        }

        .dark-mode .swal2-icon.swal2-success .swal2-success-ring {
            border-color: var(--blue-soft) !important;
        }

        .dark-mode .swal2-icon.swal2-error {
            border-color: #ff6b6b !important;
            color: #ff6b6b !important;
        }

        .dark-mode .swal2-icon.swal2-warning {
            border-color: #ffc107 !important;
            color: #ffc107 !important;
        }

        .dark-mode .swal2-icon.swal2-info {
            border-color: var(--blue-soft) !important;
            color: var(--blue-soft) !important;
        }

        .dark-mode .swal2-backdrop {
            background: rgba(0, 0, 0, 0.7) !important;
        }

        /* Dark mode variables for consistency */
        :root {
            --dark-bg: #0f1419;
            --dark-surface: #1a2332;
            --dark-surface-light: #242b3d;
            --dark-surface-hover: #2a3441;
            --dark-text: #e1e8f0;
            --dark-text-secondary: #b8c5d1;
            --dark-text-muted: #8a9ba8;
            --dark-border: #2d3748;
            --dark-border-light: #3a4553;
            --blue-gradient-start: #1e3a8a;
            --blue-gradient-end: #3b82f6;
            --blue-accent: #5b9bd5;
            --blue-hover: #4a90c2;
            --blue-soft: #6ba3d6;
            --blue-muted: #4a7ba7;
            --white: #ffffff;
            --secondary-color: #6c757d;
        }

        /* تحسين الخطوط في نوافذ SweetAlert */
        .swal2-popup {
            font-family: 'Cairo', Arial, sans-serif !important;
            direction: rtl !important;
            text-align: right !important;
        }

        .swal2-title {
            font-family: 'Cairo', Arial, sans-serif !important;
            font-weight: 700 !important;
        }

        .swal2-content {
            font-family: 'Cairo', Arial, sans-serif !important;
            font-weight: 500 !important;
        }

        .swal2-confirm,
        .swal2-cancel {
            font-family: 'Cairo', Arial, sans-serif !important;
            font-weight: 600 !important;
            border-radius: 10px !important;
            transition: all 0.3s ease !important;
        }

        .swal2-confirm:hover,
        .swal2-cancel:hover {
            transform: translateY(-2px) !important;
        }
</style>


<audio id="notificationSound" src="/sound.mp3" preload="auto"></audio>

    <!-- Firebase SDK for notifications -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let previousUnreadCount = <?= $unreadCount ?>;
        let audioEnabled = false;
        let messaging = null;
        let fcmToken = null;
        let notificationsModalOpen = false; // متغير لتتبع حالة نافذة الإشعارات
        let lastNotificationTime = 0; // لمنع التكرار
        let isUpdatingCount = false; // لمنع التحديث المتزامن
        let notificationQueue = []; // قائمة انتظار للإشعارات

        // Firebase Configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
            authDomain: "macm-84114.firebaseapp.com",
            projectId: "macm-84114",
            storageBucket: "macm-84114.firebasestorage.app",
            messagingSenderId: "860043675105",
            appId: "1:860043675105:web:72586005d5bd035ff8bea0"
        };

        const vapidKey = "BMVJO7rt5hONuPb0UzJm2B9T52CuXtcjsWDmHKXf8ass2zyctrBrjXWncazpezhWSdBbcrr8pPcegRixWaTiSBI";

        // Initialize Firebase
        try {
            if (!firebase.apps.length) {
                firebase.initializeApp(firebaseConfig);
            }
            messaging = firebase.messaging();
            console.log('✅ Firebase initialized in bottom_nav.php');
        } catch (error) {
            console.error('❌ Firebase initialization error:', error);
        }

        // Add Firebase notification styles
        const firebaseStyles = `
            <style>
                .firebase-notification {
                    position: fixed !important;
                    top: 20px !important;
                    right: 20px !important;
                    z-index: 999999 !important;
                    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
                    color: #333 !important;
                    border-radius: 12px !important;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                    min-width: 320px !important;
                    max-width: 400px !important;
                    animation: slideInRight 0.5s ease-out !important;
                    backdrop-filter: blur(10px) !important;
                    border: 1px solid rgba(0, 123, 255, 0.2) !important;
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    pointer-events: auto !important;
                    transition: all 0.3s ease !important;
                }

                /* Dark Mode Firebase Notification Styles */
                .dark-mode .firebase-notification,
                html[data-theme="dark"] .firebase-notification {
                    background: linear-gradient(135deg, #1a2332 0%, #242b3d 100%) !important;
                    color: #e1e8f0 !important;
                    border: 1px solid rgba(91, 155, 213, 0.3) !important;
                    box-shadow: 0 8px 32px rgba(26, 35, 50, 0.4) !important;
                }

                /* Firebase notification bell icon - Light Mode */
                .firebase-notification .notification-bell-icon {
                    background: rgba(0, 123, 255, 0.1) !important;
                    color: #007bff !important;
                }

                /* Firebase notification bell icon - Dark Mode */
                .dark-mode .firebase-notification .notification-bell-icon,
                html[data-theme="dark"] .firebase-notification .notification-bell-icon {
                    background: rgba(91, 155, 213, 0.15) !important;
                    color: #5b9bd5 !important;
                }

                /* Firebase notification close button - Light Mode */
                .firebase-notification .notification-close-btn {
                    background: rgba(0, 0, 0, 0.1) !important;
                    color: #333 !important;
                }

                /* Firebase notification close button - Dark Mode */
                .dark-mode .firebase-notification .notification-close-btn,
                html[data-theme="dark"] .firebase-notification .notification-close-btn {
                    background: rgba(225, 232, 240, 0.1) !important;
                    color: #e1e8f0 !important;
                }

                /* Firebase notification close button hover - Light Mode */
                .firebase-notification .notification-close-btn:hover {
                    background: rgba(0, 0, 0, 0.2) !important;
                }

                /* Firebase notification close button hover - Dark Mode */
                .dark-mode .firebase-notification .notification-close-btn:hover,
                html[data-theme="dark"] .firebase-notification .notification-close-btn:hover {
                    background: rgba(225, 232, 240, 0.2) !important;
                }

                /* Firebase notification title - Dark Mode */
                .dark-mode .firebase-notification .notification-title,
                html[data-theme="dark"] .firebase-notification .notification-title {
                    color: #e1e8f0 !important;
                }

                /* Firebase notification body - Dark Mode */
                .dark-mode .firebase-notification .notification-body,
                html[data-theme="dark"] .firebase-notification .notification-body {
                    color: rgba(225, 232, 240, 0.9) !important;
                }

                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }

                /* Slide out animation for removal */
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }

                .firebase-notification.removing {
                    animation: slideOutRight 0.3s ease-in !important;
                }
            </style>
        `;

        // Add styles to head
        if (document.head) {
            document.head.insertAdjacentHTML('beforeend', firebaseStyles);
        } else {
            document.addEventListener('DOMContentLoaded', function() {
                document.head.insertAdjacentHTML('beforeend', firebaseStyles);
            });
        }

        // Enable audio after first user interaction
        function enableAudio() {
            if (!audioEnabled) {
                const notificationSound = document.getElementById('notificationSound');
                if (notificationSound) {
                    // Try to play and immediately pause to enable audio context
                    notificationSound.play().then(() => {
                        notificationSound.pause();
                        notificationSound.currentTime = 0;
                        audioEnabled = true;
                        console.log('Audio enabled after user interaction');
                    }).catch(e => {
                        console.log('Audio enable failed:', e.message);
                    });
                }
            }
        }

        // Add event listeners for user interaction
        document.addEventListener('click', enableAudio, { once: true });
        document.addEventListener('touchstart', enableAudio, { once: true });
        document.addEventListener('keydown', enableAudio, { once: true });

        // Show Firebase notification popup
        function showFirebaseNotification(notification, data) {
            console.log('🔔 Showing Firebase notification in bottom_nav.php:', notification);

            // Remove any existing notifications first
            const existingNotifications = document.querySelectorAll('.firebase-notification');
            existingNotifications.forEach(notif => {
                notif.classList.add('removing');
                setTimeout(() => notif.remove(), 300);
            });

            // Create notification element
            const notificationElement = document.createElement('div');
            notificationElement.className = 'firebase-notification';

            notificationElement.innerHTML = `
                <div style="display: flex; align-items: flex-start; padding: 16px; gap: 12px;">
                    <div class="notification-bell-icon" style="border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; transition: all 0.3s ease;">
                        <i class="fas fa-bell" style="font-size: 18px; transition: all 0.3s ease;"></i>
                    </div>
                    <div style="flex: 1; min-width: 0;">
                        <div class="notification-title" style="font-weight: bold; font-size: 16px; margin-bottom: 4px; line-height: 1.3; font-family: 'Cairo', Arial, sans-serif;">${notification.title || 'إشعار جديد'}</div>
                        <div class="notification-body" style="font-size: 14px; opacity: 0.9; line-height: 1.4; word-wrap: break-word; font-family: 'Cairo', Arial, sans-serif;">${notification.body || ''}</div>
                    </div>
                    <div class="notification-close-btn" onclick="removeFirebaseNotification(this)" style="cursor: pointer; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; border-radius: 50%; transition: all 0.3s ease; flex-shrink: 0;">
                        <i class="fas fa-times" style="font-size: 12px; transition: all 0.3s ease;"></i>
                    </div>
                </div>
            `;

            // Add to page
            document.body.appendChild(notificationElement);
            console.log('✅ Notification element added to bottom_nav.php body');

            // Auto remove after 5 seconds with smooth animation
            setTimeout(() => {
                removeFirebaseNotification(notificationElement.querySelector('.notification-close-btn'));
            }, 5000);

            return notificationElement;
        }

        // Function to remove Firebase notification with animation
        function removeFirebaseNotification(closeBtn) {
            const notificationElement = closeBtn.closest('.firebase-notification');
            if (notificationElement && notificationElement.parentNode) {
                notificationElement.classList.add('removing');
                setTimeout(() => {
                    if (notificationElement.parentNode) {
                        notificationElement.remove();
                    }
                }, 300);
            }
        }

        // Handle foreground messages
        function handleForegroundMessage(payload) {
            console.log('📨 Handling foreground message in bottom_nav.php:', payload);
            const { notification, data } = payload;
            const currentTime = Date.now();

            // منع التكرار - لا تظهر نفس الإشعار أكثر من مرة كل 2 ثانية
            if (currentTime - lastNotificationTime < 2000) {
                console.log('⏸️ Skipping duplicate notification - too soon');
                return;
            }

            // Show notification popup
            showFirebaseNotification(notification, data);

            // Play notification sound (مع منع التكرار)
            if (currentTime - lastNotificationTime > 3000) {
                const notificationSound = document.getElementById('notificationSound');
                if (notificationSound && audioEnabled) {
                    notificationSound.play().catch(e => {
                        console.log('Sound play failed:', e.message);
                    });
                } else if (!audioEnabled) {
                    console.log('Audio not enabled yet - waiting for user interaction');
                }
            }

            lastNotificationTime = currentTime;

            // Update unread count (مع تأخير لتجنب التحديث المتزامن)
            setTimeout(() => {
                updateUnreadCount();
            }, 1000);
        }

        window.updateUnreadCount = function() {
            // تخطي التحديث إذا كانت نافذة الإشعارات مفتوحة أو إذا كان هناك تحديث جاري
            if (notificationsModalOpen || isUpdatingCount) {
                console.log('⏸️ Skipping update - Modal open or update in progress');
                return;
            }

            isUpdatingCount = true;

            fetch('fetch_account_notifications.php')
                .then(response => response.json())
                .then(data => {
                    const currentUnreadCount = data.unreadCount;
                    const notificationCountElement = document.querySelector('.notification-count');
                    
                    if (notificationCountElement) {
                        notificationCountElement.textContent = currentUnreadCount;
                    }

                    // Play notification sound if unread count increases (منع التكرار)
                    if (currentUnreadCount > previousUnreadCount) {
                        const currentTime = Date.now();
                        
                        // منع تشغيل الصوت أكثر من مرة كل 3 ثوانٍ
                        if (currentTime - lastNotificationTime > 3000) {
                            const notificationSound = document.getElementById('notificationSound');
                            if (notificationSound && audioEnabled) {
                                notificationSound.play().catch(e => {
                                    console.log('Sound play failed:', e.message);
                                });
                            } else if (!audioEnabled) {
                                console.log('Audio not enabled yet - waiting for user interaction');
                            }
                            lastNotificationTime = currentTime;
                        }
                    }

                    previousUnreadCount = currentUnreadCount;
                    console.log('���� Unread count updated:', currentUnreadCount);
                })
                .catch(error => {
                    console.error('Error updating unread count:', error);
                })
                .finally(() => {
                    isUpdatingCount = false;
                });
        }

        window.showNotifications = function() {
            // تعيين حالة النافذة كمفتوحة
            notificationsModalOpen = true;
            console.log('🔔 Notifications modal opened - Auto-refresh paused');

            fetch('fetch_account_notifications.php')
                .then(response => response.json())
                .then(data => {
                    Swal.fire({
                        title: 'الإشعارات',
                        html: `
                            <div id="notifications-container" style="max-height: 400px; overflow-y: auto; text-align: right; direction: rtl;">
                                ${data.notifications}
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <a href="view_all_notifications.php?account_id=<?= urlencode($encrypted_account_id) ?>" class="btn btn-primary" style="background-color: #007bff; color: #fff; text-decoration: none; padding: 10px 20px; border-radius: 5px;">
                                    عرض كل الإشعارات
                                </a>
                            </div>
                        `,
                        width: '600px',
                        padding: '3em',
                        background: '#fff',
                        showCloseButton: true,
                        focusConfirm: false,
                        confirmButtonText: 'إغلاق',
                        confirmButtonColor: '#3B82F6',
                        didOpen: () => {
                            console.log('✅ Notifications modal fully opened');
                        },
                        willClose: () => {
                            // إعادة تعيين حالة النافذة كمغلقة
                            notificationsModalOpen = false;
                            console.log('🔔 Notifications modal closed - Auto-refresh resumed');

                            // تحديث فوري للإشعارات بعد إغلاق النافذة
                            setTimeout(() => {
                                updateUnreadCount();
                            }, 500);
                        }
                    });

                    // Log the notification view in the notification_reads table
                    data.notificationData.forEach(notification => {
                        fetch('log_notification_view.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ notification_id: notification.id, account_id: <?= $account_id ?> })
                        });
                    });
                })
                .catch(error => {
                    console.error('Error fetching notifications:', error);
                    notificationsModalOpen = false; // إعادة تعيين الحالة في حالة الخطأ
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ في تحميل الإشعارات',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                });
        }

        // Initialize Firebase Messaging
        async function initializeFirebaseMessaging() {
            try {
                console.log('🔔 Initializing Firebase Messaging in bottom_nav.php...');

                if (!messaging) {
                    console.error('❌ Firebase messaging not available');
                    return;
                }

                // Request notification permission
                const permission = await Notification.requestPermission();
                console.log('🔐 Permission result:', permission);

                if (permission === 'granted') {
                    console.log('✅ Notification permission granted');

                    // Get FCM token
                    fcmToken = await messaging.getToken({ vapidKey: vapidKey });
                    console.log('🎫 FCM Token:', fcmToken);

                    // Save token to server
                    if (fcmToken) {
                        try {
                            const response = await fetch('save_fcm_token.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    token: fcmToken,
                                    account_id: <?= $account_id ?>,
                                    store_id: <?= $store_id ?>
                                })
                            });
                            const result = await response.json();
                            if (result.success) {
                                console.log('✅ FCM token saved successfully');
                            }
                        } catch (error) {
                            console.error('❌ Error saving FCM token:', error);
                        }
                    }

                    // Listen for foreground messages
                    messaging.onMessage((payload) => {
                        console.log('📨 Message received in foreground (bottom_nav.php):', payload);
                        handleForegroundMessage(payload);
                    });

                    console.log('✅ Firebase Messaging initialization completed in bottom_nav.php');
                }
            } catch (error) {
                console.error('❌ Error initializing Firebase messaging in bottom_nav.php:', error);
            }
        }

        // Register Service Worker
        async function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    let registration;
                    try {
                        registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                        console.log('✅ Service Worker registered at root');
                    } catch (e) {
                        registration = await navigator.serviceWorker.register('/elwaled_market/firebase-messaging-sw.js');
                        console.log('✅ Service Worker registered at elwaled_market');
                    }

                    // Note: useServiceWorker is deprecated in Firebase v9+
                    // The service worker will be used automatically
                    console.log('✅ Service Worker registration completed');

                } catch (error) {
                    console.error('❌ Service Worker registration failed:', error);
                }
            }
        }

        // Test function for debugging
        window.testFirebaseNotificationBottomNav = function() {
            console.log('🧪 Testing Firebase notification in bottom_nav.php...');
            const testNotification = {
                title: 'إشعار تجريبي - Bottom Nav',
                body: 'هذا إشعار تجريبي من Bottom Navigation'
            };
            showFirebaseNotification(testNotification, {});
        };

        // Initialize Firebase when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 DOM loaded in bottom_nav.php, initializing Firebase...');
                registerServiceWorker().then(() => {
                    initializeFirebaseMessaging();
                });
            });
        } else {
            console.log('🚀 DOM already loaded in bottom_nav.php, initializing Firebase...');
            registerServiceWorker().then(() => {
                initializeFirebaseMessaging();
            });
        }

        setInterval(updateUnreadCount, 5000); // Refresh unread count every 5 seconds

        // Page Loader Management - Fast & Smart
        document.addEventListener("DOMContentLoaded", () => {
            const loader = document.getElementById('page-loader');
            let isLoaderHidden = false;
            let loaderStartTime = Date.now();
            let minDisplayTime = 600; // حد أدنى لعرض اللودر (600ms)
            let maxWaitTime = 3000; // حد أقصى للانتظار (3 ثوانٍ)

            // Function to hide the loader
            const hideLoader = () => {
                if (isLoaderHidden) return;
                
                const timeElapsed = Date.now() - loaderStartTime;
                
                // التأكد من عرض اللودر للحد الأدنى من الوقت
                if (timeElapsed < minDisplayTime) {
                    setTimeout(hideLoader, minDisplayTime - timeElapsed);
                    return;
                }
                
                isLoaderHidden = true;
                console.log('🔄 Hiding page loader...');
                
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.style.visibility = 'hidden';
                    loader.style.display = 'none';
                    console.log('✅ Page loader hidden');
                }, 600);
            };

            // Show the loader initially
            if (loader) {
                loader.style.display = 'flex';
                loader.style.visibility = 'visible';
                loader.style.opacity = '1';
                console.log('🔄 Page loader shown');
            }

            // Create particles animation
            createParticles();

            // Smart loading detection - فقط الموارد الحرجة
            let loadingChecks = {
                domReady: true, // DOM جاهز بالفعل
                criticalResourcesLoaded: false,
                pageInteractive: false
            };

            console.log('✅ DOM ready');

            // فحص الموارد الحرجة فقط
            const checkCriticalResources = () => {
                let criticalResourcesCount = 0;
                let loadedCriticalResources = 0;

                // فحص الصور المرئية فقط (above the fold)
                const visibleImages = Array.from(document.querySelectorAll('img')).filter(img => {
                    const rect = img.getBoundingClientRect();
                    return rect.top < window.innerHeight + 100; // الصور في المنطقة المرئية + 100px
                });

                criticalResourcesCount += visibleImages.length;

                if (visibleImages.length === 0) {
                    console.log('✅ No critical images to load');
                } else {
                    visibleImages.forEach(img => {
                        if (img.complete && img.naturalHeight !== 0) {
                            loadedCriticalResources++;
                        } else {
                            img.addEventListener('load', checkCriticalResourcesComplete);
                            img.addEventListener('error', checkCriticalResourcesComplete);
                        }
                    });
                }

                // فحص السكريبتات المهمة فقط
                const criticalScripts = Array.from(document.querySelectorAll('script[src]')).filter(script => {
                    const src = script.src.toLowerCase();
                    return src.includes('bootstrap') || src.includes('jquery') || 
                           script.hasAttribute('data-critical') || script.classList.contains('critical');
                });

                criticalResourcesCount += criticalScripts.length;

                if (criticalScripts.length === 0) {
                    console.log('✅ No critical scripts to load');
                } else {
                    criticalScripts.forEach(script => {
                        if (script.readyState === 'loaded' || script.readyState === 'complete') {
                            loadedCriticalResources++;
                        } else {
                            script.addEventListener('load', checkCriticalResourcesComplete);
                            script.addEventListener('error', checkCriticalResourcesComplete);
                        }
                    });
                }

                function checkCriticalResourcesComplete() {
                    loadedCriticalResources++;
                    if (loadedCriticalResources >= criticalResourcesCount) {
                        console.log('✅ All critical resources loaded');
                        loadingChecks.criticalResourcesLoaded = true;
                        checkAllLoaded();
                    }
                }

                // إذا لم تكن هناك موارد حرجة
                if (criticalResourcesCount === 0) {
                    console.log('✅ No critical resources to load');
                    loadingChecks.criticalResourcesLoaded = true;
                    checkAllLoaded();
                } else {
                    checkCriticalResourcesComplete();
                }
            };

            // فحص تفاعلية الصفحة
            const checkPageInteractive = () => {
                if (document.readyState === 'interactive' || document.readyState === 'complete') {
                    loadingChecks.pageInteractive = true;
                    console.log('✅ Page interactive');
                    checkAllLoaded();
                } else {
                    document.addEventListener('readystatechange', () => {
                        if (document.readyState === 'interactive' || document.readyState === 'complete') {
                            loadingChecks.pageInteractive = true;
                            console.log('✅ Page interactive');
                            checkAllLoaded();
                        }
                    });
                }
            };

            // Function to check if everything is loaded
            const checkAllLoaded = () => {
                const allLoaded = Object.values(loadingChecks).every(check => check === true);
                
                if (allLoaded) {
                    console.log('🎉 All critical resources loaded, hiding loader...');
                    hideLoader();
                } else {
                    console.log('⏳ Still loading critical resources...', loadingChecks);
                }
            };

            // بدء فحص الموارد
            setTimeout(() => {
                checkCriticalResources();
                checkPageInteractive();
            }, 100);

            // فحص سريع للخطوط إذا كانت متاحة
            if (document.fonts && document.fonts.status === 'loaded') {
                console.log('✅ Fonts already loaded');
            } else if (document.fonts) {
                document.fonts.ready.then(() => {
                    console.log('✅ Fonts loaded');
                }).catch(() => {
                    console.log('⚠️ Font loading timeout');
                });
            }

            // إخفاء سريع إذا كانت الصفحة محملة بالفعل
            if (document.readyState === 'complete') {
                setTimeout(() => {
                    if (!isLoaderHidden) {
                        console.log('🚀 Page already complete, quick hide');
                        loadingChecks.criticalResourcesLoaded = true;
                        loadingChecks.pageInteractive = true;
                        checkAllLoaded();
                    }
                }, 200);
            }

            // حد أقصى للانتظار (مقلل إلى 3 ثوانٍ)
            setTimeout(() => {
                if (!isLoaderHidden) {
                    console.log('⏰ Maximum wait time reached, hiding loader');
                    hideLoader();
                }
            }, maxWaitTime);

            // إخفاء فوري عند تحميل النافذة
            window.addEventListener('load', () => {
                console.log('✅ Window fully loaded');
                setTimeout(() => {
                    if (!isLoaderHidden) {
                        console.log('🚀 Window loaded, hiding loader');
                        hideLoader();
                    }
                }, 300);
            });
        });

        // Function to create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles-container');
            if (!particlesContainer) return;

            const particleCount = 15; // Number of particles

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // Random starting position
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (6 + Math.random() * 4) + 's';
                
                // Random size
                const size = 3 + Math.random() * 3;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                particlesContainer.appendChild(particle);
            }
        }
    </script>

