<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php';

// Check if user is logged in
if (!isset($_SESSION['account_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Get store_id and account_id from session
$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '';
$encrypted_account_id = isset($_SESSION['account_id']) ? $_SESSION['account_id'] : '';

if (!$encrypted_store_id || !$encrypted_account_id) {
    echo json_encode(['success' => false, 'message' => 'Session data missing']);
    exit;
}

$store_id = decrypt($encrypted_store_id, $key);
$account_id = decrypt($encrypted_account_id, $key);

// Insert test notification into database
$message = "هذا إشعار تجريبي تم إرساله في " . date('Y-m-d H:i:s');
$insert_query = "INSERT INTO notifications (message, store_id, created_by) VALUES (?, ?, ?)";
$insert_stmt = $conn->prepare($insert_query);
$insert_stmt->bind_param("sii", $message, $store_id, $account_id);

if (!$insert_stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Failed to save notification to database']);
    exit;
}

$notification_id = $insert_stmt->insert_id;

// Get FCM tokens for this account only
$tokens_query = "SELECT token FROM fcm_tokens WHERE account_id = ? AND store_id = ?";
$tokens_stmt = $conn->prepare($tokens_query);
$tokens_stmt->bind_param("ii", $account_id, $store_id);
$tokens_stmt->execute();
$tokens_result = $tokens_stmt->get_result();

$tokens = [];
while ($row = $tokens_result->fetch_assoc()) {
    $tokens[] = $row['token'];
}

if (empty($tokens)) {
    echo json_encode([
        'success' => false,
        'message' => 'لا توجد رموز FCM مسجلة لهذا الحساب. يرجى تحديث الصفحة وقبول إذن الإشعارات.'
    ]);
    exit;
}

// Use Firebase Admin SDK with service account
// Path to service account key file
$serviceAccountPath = __DIR__ . '/firebase-adminsdk.json';

// Check if service account key file exists
if (!file_exists($serviceAccountPath)) {
    echo json_encode([
        'success' => false,
        'message' => 'Service account key file not found at: ' . $serviceAccountPath
    ]);
    exit;
}

// Include the OAuth 2.0 access token helper
require_once 'get_access_token.php';

try {
    // Get OAuth 2.0 access token
    $accessTokenInfo = getAccessToken($serviceAccountPath);
    $accessToken = $accessTokenInfo['token'];

    // Get project ID from service account file
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

    // Check if service account file is valid
    if (!isset($serviceAccount['project_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid service account file. Missing project_id field.',
            'debug' => [
                'file_exists' => file_exists($serviceAccountPath),
                'file_size' => filesize($serviceAccountPath),
                'file_content_sample' => substr(file_get_contents($serviceAccountPath), 0, 100) . '...',
                'json_last_error' => json_last_error_msg()
            ]
        ]);
        exit;
    }

    // Prepare FCM message
    $fcmMessage = [
        'message' => [
            'token' => $tokens[0], // Send to first token
            'notification' => [
                'title' => 'إشعار تجريبي',
                'body' => $message
            ],
            'webpush' => [
                'headers' => [
                    'Urgency' => 'high'
                ],
                'notification' => [
                    'title' => 'إشعار تجريبي',
                    'body' => $message,
                    'icon' => '/icon.png',
                    'click_action' => 'https://' . $_SERVER['HTTP_HOST'] . '/notifications.php'
                ],
                'fcm_options' => [
                    'link' => 'https://' . $_SERVER['HTTP_HOST'] . '/notifications.php'
                ]
            ],
            'data' => [
                'notification_id' => (string)$notification_id
            ]
        ]
    ];

    $url = 'https://fcm.googleapis.com/v1/projects/' . $serviceAccount['project_id'] . '/messages:send';
    $headers = [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ];

    // Send request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmMessage));

    // Enable verbose debugging
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    // Execute the request
    $result = curl_exec($ch);

    // Get verbose information
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);

    // Get HTTP status code
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if ($result === false) {
        echo json_encode([
            'success' => false,
            'message' => 'cURL error: ' . curl_error($ch),
            'debug' => [
                'verbose_log' => $verboseLog,
                'http_code' => $httpCode,
                'url' => $url,
                'access_token_sample' => substr($accessToken, 0, 20) . '...'
            ]
        ]);
    } else {
        $response = json_decode($result, true);
        if (isset($response['name'])) {
            echo json_encode([
                'success' => true,
                'message' => 'تم إرسال الإشعار التجريبي بنجاح',
                'response' => $response
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => isset($response['error']) ? $response['error']['message'] : 'Unknown error',
                'debug' => [
                    'verbose_log' => $verboseLog,
                    'http_code' => $httpCode,
                    'response' => $response,
                    'request' => [
                        'url' => $url,
                        'headers' => [
                            'Authorization: Bearer ' . substr($accessToken, 0, 20) . '...',
                            'Content-Type: application/json'
                        ],
                        'payload' => $fcmMessage
                    ],
                    'access_token_info' => [
                        'expires_in' => $accessTokenInfo['expires_in'],
                        'expires_at' => date('Y-m-d H:i:s', $accessTokenInfo['expires_at'])
                    ]
                ]
            ]);
        }
    }

    curl_close($ch);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

// JWT function is now in get_access_token.php

$insert_stmt->close();
$tokens_stmt->close();
$conn->close();
?>
