<?php
// تفعيل عرض جميع أنواع الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include 'db_connection.php';
include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');

$order_date = isset($_GET['order_date']) ? $_GET['order_date'] : '';
$data_type = isset($_GET['data_type']) ? $_GET['data_type'] : 'sales';
$store_id = isset($_GET['store_id']) ? decrypt($_GET['store_id'], $key) : null;

if (empty($order_date)) {
    echo json_encode([]);
    exit;
}

// تحديد الجدول المطلوب بناءً على نوع البيانات
$table_name = ($data_type === 'returns') ? 'returns' : 'sales';

// بناء الاستعلام لجلب جميع المعاملات في التاريخ المحدد
$sql = "SELECT s.sale_id, s.item_id, s.quantity, s.price, s.cost, 
               (s.price * s.quantity) AS total_amount,
               ((s.price - s.cost) * s.quantity) AS profit,
               TIME(s.time) AS sale_time,
               s.status,
               i.name,
               a.username
        FROM $table_name s
        JOIN items i ON s.item_id = i.item_id
        JOIN accounts a ON s.account_id = a.account_id
        WHERE DATE(s.time) = ?";

$params = [$order_date];

// إضافة شرط المتجر إذا كان محدداً
if ($store_id) {
    $sql .= " AND s.store_id = ?";
    $params[] = $store_id;
}

// فلترة حسب نوع الصنف
if ($data_type === 'services') {
    $sql .= " AND i.type = 'service'";
} elseif ($data_type === 'sales') {
    $sql .= " AND i.type != 'service'";
}

$sql .= " ORDER BY s.time ASC";

$stmt = $conn->prepare($sql);
if ($params) {
    $stmt->bind_param(str_repeat('s', count($params)), ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

$daily_details = [];
while ($row = $result->fetch_assoc()) {
    // تنسيق البيانات
    $row['quantity'] = floatval($row['quantity']);
    $row['price'] = floatval($row['price']);
    $row['cost'] = floatval($row['cost']);
    $row['total_amount'] = floatval($row['total_amount']);
    $row['profit'] = floatval($row['profit']);
    
    $daily_details[] = $row;
}

$stmt->close();
$conn->close();

header('Content-Type: application/json');
echo json_encode($daily_details);
?>