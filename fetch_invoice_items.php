<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['encrypted_invoice_id'])) {
    $invoice_id = decrypt($_POST['encrypted_invoice_id'], $key);

    // Fetch invoice items
    $stmt = $conn->prepare("SELECT items.name, purchases.quantity, items.cost AS price
                            FROM purchases
                            JOIN items ON purchases.item_id = items.item_id
                            WHERE purchases.invoice_id = ?");
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $items = [];
    while ($row = $result->fetch_assoc()) {
        $items[] = $row;
    }
    $stmt->close();

    // Fetch invoice images
    $stmt = $conn->prepare("SELECT img_path FROM invoice_images WHERE purchase_invoice_id = ?");
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $images = [];
    while ($row = $result->fetch_assoc()) {
        $images[] = $row['img_path'];
    }
    $stmt->close();

    echo json_encode(['success' => true, 'items' => $items, 'images' => $images]);
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}

$conn->close();
?>
