<?php
include 'db_connection.php';

include 'encryption_functions.php';
$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['item_id'])) {
    $encrypted_item_id = $_GET['item_id'];
    $item_id = decrypt($encrypted_item_id, $key);

    if (!is_numeric($item_id)) {
        echo json_encode(['success' => false, 'message' => 'Invalid item ID']);
        exit;
    }

    $sql = "SELECT * FROM items WHERE item_id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        exit;
    }
    $stmt->bind_param("i", $item_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();

    if ($result->num_rows > 0) {
        $item = $result->fetch_assoc();

        // Get item images
        $images_sql = "SELECT img_id, img_path FROM item_images WHERE item_id = ?";
        $images_stmt = $conn->prepare($images_sql);
        $images_stmt->bind_param("i", $item_id);
        $images_stmt->execute();
        $images_result = $images_stmt->get_result();
        
        $images = [];
        while ($image_row = $images_result->fetch_assoc()) {
            $images[] = $image_row;
        }
        $images_stmt->close();
        
        $item['images'] = $images;

        // Adjust item data based on type
        if ($item['type'] == 'piece' || $item['type'] == 'other') {
            $item['name_piece'] = $item['name'];
            $item['cost_piece'] = $item['cost'];
            $item['price_piece'] = $item['price'];
            $item['quantity_piece'] = $item['quantity'];
        } elseif ($item['type'] == 'box') {
            $item['name_box'] = $item['name'];
            $item['cost_box'] = $item['cost'];
            $item['price1_box'] = $item['price'];
            $item['quantity_box'] = $item['quantity'];
            $item['pieces_per_box'] = $item['pieces_per_box'];
        } elseif ($item['type'] == 'fridge') {
            $item['name_fridge'] = $item['name'];
            $item['cost_fridge'] = $item['cost'];
            $item['price_fridge'] = $item['price'];
            $item['quantity_fridge'] = $item['quantity'];
        }

        echo json_encode(['success' => true, 'item' => $item]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Item not found']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}

$conn->close();
?>
