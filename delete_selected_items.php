<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'db_connection.php';

include 'encryption_functions.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['selected_items']) && isset($_POST['category_id'])) {
    $selected_items = $_POST['selected_items'];
    $encrypted_category_id = $_POST['category_id'];

    foreach ($selected_items as $item_id) {
        // Delete images from the folder
       
        // Get all related tables
        $related_tables = [];
        $result = $conn->query("SELECT TABLE_NAME, COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE REFERENCED_TABLE_NAME = 'items' AND REFERENCED_COLUMN_NAME = 'item_id'");
        while ($row = $result->fetch_assoc()) {
            $related_tables[] = $row;
        }

        // Delete related data from other tables
        foreach ($related_tables as $table) {
            $stmt = $conn->prepare("DELETE FROM {$table['TABLE_NAME']} WHERE {$table['COLUMN_NAME']} = ?");
            $stmt->bind_param("i", $item_id);
            $stmt->execute();
            $stmt->close();
        }

        $stmt = $conn->prepare("DELETE FROM items WHERE item_id = ?");
        $stmt->bind_param("i", $item_id);
        $stmt->execute();
        $stmt->close();
    }

    echo "<script>alert('تم حذف الأصناف المحددة بنجاح.'); window.location.href = 'items.php?category_id=" . htmlspecialchars($encrypted_category_id) . "';</script>";
} else {
    echo "<script>alert('لم يتم تحديد أي أصناف للحذف.'); window.location.href = 'items.php?category_id=" . htmlspecialchars($_POST['category_id']) . "';</script>";
}

$conn->close();
?>
