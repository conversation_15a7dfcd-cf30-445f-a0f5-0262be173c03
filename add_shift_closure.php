<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

// Retrieve and validate input fields
$shift_date = isset($_POST['shift_date']) ? trim($_POST['shift_date']) : null;
$shift_type = isset($_POST['shift_type']) ? strtolower(trim($_POST['shift_type'])) : null;
$shift_amount = isset($_POST['shift_amount']) ? floatval($_POST['shift_amount']) : null;
$notes = isset($_POST['notes']) ? trim($_POST['notes']) : null;
$store_id = isset($_POST['store_id']) ? intval($_POST['store_id']) : null;
$encrypted_account_id = isset($_POST['account_id']) ? $_POST['account_id'] : null;
$purchases = isset($_POST['purchases']) ? floatval($_POST['purchases']) : 0;

// Decrypt the account_id
$key = getenv('ENCRYPTION_KEY');
$account_id = $encrypted_account_id ? decrypt($encrypted_account_id, $key) : null;

// Check for missing fields
if (!$shift_date || !$shift_type || !$shift_amount || !$store_id || !$account_id) {
    echo json_encode(['success' => false, 'error' => 'Missing required fields. Please ensure all fields are filled.']);
    exit();
}

// Ensure valid shift type
if (!in_array($shift_type, ['morning', 'night'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid shift type.']);
    exit();
}

// Insert the new shift with status 'Active'
$stmt = $conn->prepare("INSERT INTO shift_closures (shift_date, shift_type, shift_amount, notes, purchases, status, store_id, account_id) 
                        VALUES (?, ?, ?, ?, ?, 'Active', ?, ?)");
$stmt->bind_param("ssddssi", $shift_date, $shift_type, $shift_amount, $notes, $purchases, $store_id, $account_id);

if ($stmt->execute()) {
    $shift_closure_id = $stmt->insert_id; // Get the ID of the newly created shift closure

    // Log the shift closure addition action
    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                VALUES (?, 'add', 'shift_closures', ?, ?)";
    $description = "تم إضافة وردية جديدة بتاريخ $shift_date من النوع $shift_type بمبلغ $shift_amount";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("iis", $account_id, $shift_closure_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    echo json_encode(['success' => true, 'message' => 'تم إضافة الوردية بنجاح.']);
} else {
    echo json_encode(['success' => false, 'error' => 'فشل في إضافة الوردية.']);
}

$stmt->close();
$conn->close();
?>
