<?php
include 'db_connection.php';

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$registration_success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['username']) && isset($_POST['password']) && isset($_POST['name']) && isset($_POST['phone'])) {
    $username = $_POST['username'];
    $password = password_hash($_POST['password'], PASSWORD_BCRYPT);
    $name = $_POST['name'];
    $phone = $_POST['phone'];
    $role = 'user'; // Default role for new registrations
    $status = 'requested'; // Default status for new registrations

    $stmt = $conn->prepare("INSERT INTO accounts (username, password, role, name, phone, status) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssss", $username, $password, $role, $name, $phone, $status);
    if ($stmt->execute()) {
        $registration_success = true;
        echo "<script>
                document.addEventListener('DOMContentLoaded', function() {
                    Swal.fire({
                        title: 'تم تسجيل طلبك بنجاح',
                        icon: 'success',
                        confirmButtonText: 'موافق'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = 'index.php';
                        }
                    });
                });
              </script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء تسجيل الحساب: " . $stmt->error . "');</script>";
    }
    $stmt->close();
}

$registration_success = isset($_GET['success']);

?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل مستخدم جديد | code_wars_official</title>
    <!-- Custom CSS Link -->
    <link rel="stylesheet" href="web_css/style_login9.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        .box {
    position: relative;
    width: 380px;
    min-height: 500px;
    /* Ensure minimum height for the form */
    background: #1c1c1c;
    border-radius: 10px;
    overflow: hidden;
}
    </style>
</head>
<body>
    <div class="box">
        <div class="border-line"></div>
        <form method="POST" action="">
            <h2>تسجيل مستخدم جديد</h2>
            <?php if ($registration_success): ?>
                <div class="success-message">
                    <div class="checkmark">✔</div>
                    تم تسجيل طلبك بنجاح
                </div>
            <?php else: ?>
                <div class="input-box">
                    <input type="text" name="username" required="required">
                    <span>اسم المستخدم</span>
                    <i></i>
                </div>
                <div class="input-box">
                    <input type="password" name="password" required="required">
                    <span>كلمة المرور</span>
                    <i></i>
                </div>
                <div class="input-box">
                    <input type="text" name="name" required="required">
                    <span>الاسم كامل</span>
                    <i></i>
                </div>
                <div class="input-box">
                    <input type="text" name="phone" required="required">
                    <span>رقم الهاتف</span>
                    <i></i>
                </div>
                <input type="submit" value="تسجيل" class="btn">
            <?php endif; ?>
        </form>
    </div>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</body>
</html>
