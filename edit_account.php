<?php
include 'db_connection.php';
include 'encryption_functions.php';

session_start(); // Ensure session_start() is called before using $_SESSION

$key = getenv('ENCRYPTION_KEY');

$response = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_account_id'])) {
    $account_id = decrypt($_POST['edit_account_id'], $key);
    $username = $_POST['username'];
    $role = $_POST['role'];
    $name = $_POST['name'];
    $phone = $_POST['phone'];
    $status = isset($_POST['status']) ? 'active' : 'inactive';
    $store_id = $_POST['store_id']; // Assuming store_id is passed in the form

    // Fetch the current account details for comparison
    $fetchStmt = $conn->prepare("SELECT username, role, name, phone, status, store_id FROM accounts WHERE account_id = ?");
    $fetchStmt->bind_param("i", $account_id);
    $fetchStmt->execute();
    $fetchStmt->bind_result($current_username, $current_role, $current_name, $current_phone, $current_status, $current_store_id);
    $fetchStmt->fetch();
    $fetchStmt->close();

    // Fetch the current password from the database
    $passwordStmt = $conn->prepare("SELECT password FROM accounts WHERE account_id = ?");
    $passwordStmt->bind_param("i", $account_id);
    $passwordStmt->execute();
    $passwordStmt->bind_result($current_password);
    $passwordStmt->fetch();
    $passwordStmt->close();

    // Check if password is provided and has changed
    if (!empty($_POST['password']) && $_POST['password'] !== $current_password) {
        $password = password_hash($_POST['password'], PASSWORD_BCRYPT);
    } else {
        $password = $current_password;
    }

    // Update the account details
    $updateStmt = $conn->prepare("UPDATE accounts SET username = ?, password = ?, role = ?, name = ?, phone = ?, status = ?, store_id = ? WHERE account_id = ?");
    $updateStmt->bind_param("ssssssii", $username, $password, $role, $name, $phone, $status, $store_id, $account_id);

    // Fetch the current store name for comparison
    $storeStmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $storeStmt->bind_param("i", $current_store_id);
    $storeStmt->execute();
    $storeStmt->bind_result($current_store_name);
    $storeStmt->fetch();
    $storeStmt->close();

    // Fetch the new store name
    $newStoreStmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $newStoreStmt->bind_param("i", $store_id);
    $newStoreStmt->execute();
    $newStoreStmt->bind_result($new_store_name);
    $newStoreStmt->fetch();
    $newStoreStmt->close();

    if ($updateStmt->execute()) {
        $updateStmt->close();

        // Log the account edit action
        $logged_in_account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        // Prepare a description of what was changed
        $changes = [];
        if ($username !== $current_username) $changes[] = "اسم المستخدم من $current_username إلى $username";
        if ($role !== $current_role) $changes[] = "الدور من $current_role إلى $role";
        if ($name !== $current_name) $changes[] = "الاسم من $current_name إلى $name";
        if ($phone !== $current_phone) $changes[] = "رقم الهاتف من $current_phone إلى $phone";
        if ($status !== $current_status) $changes[] = "الحالة من $current_status إلى $status";
        if ($store_id !== $current_store_id) $changes[] = "الفرع من $current_store_name إلى $new_store_name";

        if (!empty($changes)) {
            $description = "تم تعديل الحساب رقم $account_id: " . implode(", ", $changes);

            $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                        VALUES (?, 'update', 'accounts', ?, ?)";
            $log_stmt = $conn->prepare($log_sql);
            $log_stmt->bind_param("iis", $logged_in_account_id, $account_id, $description);
            $log_stmt->execute();
            $log_stmt->close();
        }

        $response['success'] = true;
        $response['message'] = 'تم تعديل الحساب بنجاح.';
    } else {
        $response['success'] = false;
        $response['message'] = 'حدث خطأ أثناء تعديل الحساب: ' . $updateStmt->error;
        $updateStmt->close();
    }
} else {
    $response['success'] = false;
    $response['message'] = 'طلب غير صالح.';
}

header('Content-Type: application/json');
echo json_encode($response);
exit();
?>