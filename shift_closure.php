<?php
// تحميل نظام الحماية والصلاحيات
require_once 'security.php';

// التحقق من صلاحية الوصول لتقفيل الوردية
checkPagePermission('cashier_shift_closure', 'access');

// فحص الصلاحيات الفرعية
$canView = hasPermission('cashier_shift_closure', 'view');
$canCloseShift = hasPermission('cashier_shift_closure', 'close_shift'); // هذه الصلاحية قد تكون في وحدة أخرى أو غير موجودة
// إذا لم تكن موجودة، سنعتبر أن المستخدم يمكنه تقفيل الوردية إذا كا�� لديه صلاحية العرض
$canCloseShift = $canCloseShift || $canView; // إذا كان لديه صلاحية العرض، يمكنه التقفيل أيضاً

$encrypted_account_id = $_GET['account_id'] ?? null;
$account_id = $encrypted_account_id ? decrypt($encrypted_account_id, $key) : null;

if (!$account_id) {
    die("Account ID not found. Please log in again.");
}

// Fetch the store_id using the account_id
$query = "SELECT store_id FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$account = $result->fetch_assoc();
$store_id = $account['store_id'] ?? null;

if (!$store_id) {
    die("Store ID not found for the given Account ID.");
}

// Encrypt the store_id for session storage
$encrypted_store_id = encrypt($store_id, $key);

// Fetch the store name using the store ID
$query = "SELECT name FROM stores WHERE store_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
$store = $result->fetch_assoc();
$storeName = $store['name'] ?? 'اسم الفرع غير متوفر';

// Fetch the user's theme preference
$query = "SELECT theme FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$userTheme = $user['theme'] ?? 'Light';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <title>ELWALED MARKET - تقفيل وردية</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
            --primary-light: #4dabf7;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white: #ffffff;
            --light-bg: #f0f2f5;
            
            /* ألوان محسنة للثيم الداكن - أكثر راحة للعين */
            --dark-bg: #0f1419;
            --dark-surface: #1a2332;
            --dark-surface-light: #242b3d;
            --dark-surface-hover: #2a3441;
            --dark-text: #e1e8f0;
            --dark-text-secondary: #b8c5d1;
            --dark-text-muted: #8a9ba8;
            --border-color: #dee2e6;
            --dark-border: #2d3748;
            --dark-border-light: #3a4553;
            
            /* ألوان زرقاء ناعمة ومريحة */
            --blue-gradient-start: #1e3a8a;
            --blue-gradient-end: #3b82f6;
            --blue-accent: #5b9bd5;
            --blue-hover: #4a90c2;
            --blue-soft: #6ba3d6;
            --blue-muted: #4a7ba7;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: var(--light-bg);
            color: #333;
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            direction: rtl;
            text-align: right;
            min-height: 100vh;
        }

        .dark-mode {
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-surface) 40%, var(--dark-surface-light) 100%);
            color: var(--dark-text);
            min-height: 100vh;
        }

        /* Main Content */
        .main-content {
            padding: 30px 20px 100px;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Form Styles */
        .form-container {
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            margin-top: 20px;
            border: 1px solid rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
            padding: 30px;
        }

        .form-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .dark-mode .form-container {
            background: var(--dark-surface-light);
            box-shadow: 0 8px 30px rgba(26, 35, 50, 0.4);
            border: 1px solid var(--dark-border-light);
        }

        .dark-mode .form-container:hover {
            box-shadow: 0 12px 40px rgba(26, 35, 50, 0.5);
            border-color: var(--blue-soft);
        }

        .form-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .dark-mode .form-title {
            color: var(--blue-soft);
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
            font-size: 1rem;
        }

        .dark-mode .form-label {
            color: var(--dark-text);
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background-color: var(--white);
            color: #333;
            transition: all 0.3s ease;
            outline: none;
            margin-bottom: 20px;
            font-family: 'Cairo', Arial, sans-serif;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .dark-mode .form-control {
            background-color: var(--dark-surface-hover);
            color: var(--dark-text);
            border-color: var(--dark-border-light);
        }

        .dark-mode .form-control:focus {
            border-color: var(--blue-soft);
            box-shadow: 0 0 0 3px rgba(91, 155, 213, 0.2);
        }

        .btn-primary {
            width: 100%;
            padding: 15px;
            border-radius: 12px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            color: var(--white);
            text-align: center;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            font-family: 'Cairo', Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .dark-mode .btn-primary {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3);
        }

        .dark-mode .btn-primary:hover {
            box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4);
            background: linear-gradient(135deg, var(--blue-hover) 0%, var(--blue-soft) 100%);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                padding: 20px 15px 100px;
            }

            .form-container {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 15px 10px 100px;
            }

            .form-container {
                padding: 15px;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body>
    <!-- Main Content -->
    <div class="main-content">
        <div class="form-container fade-in">
            <div class="form-title">
                <i class="fas fa-cash-register"></i>
                <?= $canCloseShift ? 'تقفيل الوردية' : 'عرض الوردية' ?>
            </div>
            <?php if (!$canCloseShift): ?>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 20px; text-align: center; color: #1976d2;">
                <i class="fas fa-info-circle"></i>
                <strong>وضع العرض فقط</strong> - يمكنك عرض بيانات الوردية بدون إمكانية التعديل
            </div>
            <?php endif; ?>
            <form id="shift-closure-form" action="save_shift_closure.php" method="POST">
                <input type="hidden" name="account_id" value="<?= htmlspecialchars($encrypted_account_id) ?>">
                <input type="hidden" name="store_id" value="<?= htmlspecialchars(encrypt($store_id, $key)) ?>">

                <label for="shift_date" class="form-label">
                    <i class="fas fa-calendar-alt" style="margin-left: 8px;"></i>
                    تاريخ اليوم
                </label>
                <input type="date" class="form-control" id="shift_date" name="shift_date" value="<?= date('Y-m-d') ?>" <?= !$canCloseShift ? 'readonly' : 'required' ?>>

                <label for="shift_type" class="form-label">
                    <i class="fas fa-clock" style="margin-left: 8px;"></i>
                    نشاط الوردية
                </label>
                <select class="form-control" id="shift_type" name="shift_type" <?= !$canCloseShift ? 'disabled' : 'required' ?>>
                    <option value="morning">صباحية</option>
                    <option value="night">مسائية</option>
                </select>

                <label for="shift_amount" class="form-label">
                    <i class="fas fa-money-bill-wave" style="margin-left: 8px;"></i>
                    نقدي الوردية
                </label>
                <input type="number" class="form-control" id="shift_amount" name="shift_amount" step="0.01" placeholder="0.00" <?= !$canCloseShift ? 'readonly' : 'required' ?>>

                <label for="purchases" class="form-label">
                    <i class="fas fa-shopping-cart" style="margin-left: 8px;"></i>
                    المشتريات
                </label>
                <input type="number" class="form-control" id="purchases" name="purchases" step="0.01" placeholder="0.00" <?= !$canCloseShift ? 'readonly' : 'required' ?>>

                <label for="notes" class="form-label">
                    <i class="fas fa-sticky-note" style="margin-left: 8px;"></i>
                    ملاحظات
                </label>
                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="اكتب أي ملاحظات إضافية..." <?= !$canCloseShift ? 'readonly' : '' ?>></textarea>

                <?php if ($canCloseShift): ?>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save"></i>
                    تسجيل الوردية
                </button>
                <?php else: ?>
                <div style="text-align: center; padding: 15px; color: #666;">
                    <i class="fas fa-eye"></i>
                    وضع العرض فقط - لا يمكن حفظ التغييرات
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Initialize theme
        const body = document.body;
        const userTheme = '<?= $userTheme ?>';

        // Set initial theme
        if (userTheme === 'Dark') {
            body.classList.add('dark-mode');
        }

        // Form submission
        document.getElementById('shift-closure-form').addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = new FormData(this);

            // Add loading state to button
            const submitBtn = this.querySelector('.btn-primary');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التسجيل...';
            submitBtn.disabled = true;

            fetch('save_shift_closure.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم تسجيل الوردية بنجاح',
                        text: 'تم حفظ بيانات الوردية بنجاح',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true
                    }).then(() => {
                        // Reset form
                        this.reset();
                        document.getElementById('shift_date').value = '<?= date('Y-m-d') ?>';
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في التسجيل',
                        text: data.message || 'حدث خطأ أثناء تسجيل الوردية',
                        showConfirmButton: true,
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.',
                    showConfirmButton: true,
                    confirmButtonText: 'موافق'
                });
            });
        });

        // Add animation to form elements
        document.addEventListener('DOMContentLoaded', function() {
            const formElements = document.querySelectorAll('.form-control');
            formElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
                element.classList.add('fade-in');
            });
        });
    </script>

    <!-- Include Bottom Navigation -->
    <?php include 'bottom_nav.php'; ?>
</body>
</html>
