<?php
// Include required files first
include 'db_connection.php';
include 'encryption_functions.php';
include 'session_manager.php';

// Determine environment for consistent URL handling
$isProduction = ($_SERVER['HTTP_HOST'] === 'elwaled.shop');
$baseUrl = $isProduction ? 'https://elwaled.shop/' : '';

// Get encryption key
$key = getenv('ENCRYPTION_KEY');

/**
 * دالة لضمان حفظ معرف الحساب مشفراً في الجلسة
 * Function to ensure account ID is encrypted and saved in session
 */
function ensureAccountIdInSession($account_id, $key) {
    if (!empty($account_id) && !empty($key)) {
        $encrypted_account_id = encrypt($account_id, $key);
        $_SESSION['account_id'] = $encrypted_account_id;
        error_log("Account ID encrypted and saved to session: " . $account_id);
        return $encrypted_account_id;
    }
    return false;
}

// Validate encryption key
if (empty($key)) {
    error_log("Encryption key not found in environment variables");
    die("System configuration error. Please contact administrator.");
}

// Start session with security settings
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', $isProduction ? 1 : 0);
    ini_set('session.use_strict_mode', 1);
    session_start();
}

// Initialize session manager
$sessionManager = new SessionManager($conn, $key, $isProduction);

// Prevent infinite redirect loops
if (!isset($_SESSION['redirect_count'])) {
    $_SESSION['redirect_count'] = 0;
}

// Reset redirect count if more than 5 minutes have passed
if (isset($_SESSION['last_redirect_time']) && (time() - $_SESSION['last_redirect_time']) > 300) {
    $_SESSION['redirect_count'] = 0;
}

// If too many redirects, clear session and show error
if ($_SESSION['redirect_count'] > 5) {
    $debug_info = [
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'session_id' => session_id(),
        'account_id' => $_SESSION['account_id'] ?? 'none',
        'role' => $_SESSION['role'] ?? 'none'
    ];
    error_log("Too many redirects detected: " . json_encode($debug_info));

    session_unset();
    session_destroy();
    // Clear remember me cookie
    if (isset($_COOKIE['remember_me'])) {
        setcookie('remember_me', '', time() - 3600, '/');
    }
    // Start fresh session
    session_start();
    $error_message = "حدث خطأ في النظام. يرجى تسجيل الدخول مرة أخرى.";
}

if (!empty($_SESSION['account_id'])) {
    // Prevent redirect loops by checking if we're already on index.php
    $current_page = basename($_SERVER['PHP_SELF']);
    if ($current_page === 'index.php') {
        // Validate session using session manager
        if ($sessionManager->validateSession()) {
            $role = $_SESSION['role'];
            $access_type = $_SESSION['access_type'] ?? 'cashier_system';
            $encrypted_account_id = $_SESSION['account_id'];
            
            $_SESSION['redirect_count']++;
            $_SESSION['last_redirect_time'] = time();
            
            // Redirect based on access type first, then role for compatibility
            if ($access_type === 'admin_panel') {
                // Administrative system - redirect to stores
                header("Location: {$baseUrl}stores.php");
                exit();
            } elseif ($access_type === 'cashier_system') {
                // Cashier system - redirect to users
                header("Location: {$baseUrl}users.php?account_id=" . urlencode($encrypted_account_id));
                exit();
            } else {
                // Fallback to role-based system for compatibility
                switch ($role) {
                    case 'admin':
                        header("Location: {$baseUrl}stores.php");
                        exit();
                    case 'dealer':
                        header("Location: {$baseUrl}index_dealer.php");
                        exit();
                    case 'user':
                        header("Location: {$baseUrl}users.php?account_id=" . urlencode($encrypted_account_id));
                        exit();
                    default:
                        // If role is invalid, clear session and show login form
                        $sessionManager->clearSession();
                        $error_message = "دور المستخدم غير صالح. يرجى الاتصال بالإدارة.";
                        break;
                }
            }
        } else {
            // Session validation failed, error message will be set by session manager
            $error_message = "انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.";
        }
    }
}

// Check for "Remember Me" cookie using session manager
if (empty($_SESSION['account_id']) && !empty($_COOKIE['remember_me'])) {
    $user_role = $sessionManager->validateRememberMeToken();
    
    if ($user_role) {
        // Reset redirect count on successful token validation
        $_SESSION['redirect_count'] = 0;
        $_SESSION['last_redirect_time'] = time();
        
        // Redirect based on access type first, then role for compatibility
        $encrypted_account_id = $_SESSION['account_id'];
        $access_type = $_SESSION['access_type'] ?? 'cashier_system';
        
        if ($access_type === 'admin_panel') {
            // Administrative system - redirect to stores
            header("Location: {$baseUrl}stores.php");
            exit();
        } elseif ($access_type === 'cashier_system') {
            // Cashier system - redirect to users
            header("Location: {$baseUrl}users.php?account_id=" . urlencode($encrypted_account_id));
            exit();
        } else {
            // Fallback to role-based system for compatibility
            switch ($user_role) {
                case 'admin':
                    header("Location: {$baseUrl}stores.php");
                    exit();
                case 'dealer':
                    header("Location: {$baseUrl}index_dealer.php");
                    exit();
                case 'user':
                    header("Location: {$baseUrl}users.php?account_id=" . urlencode($encrypted_account_id));
                    exit();
                default:
                    // Invalid role
                    $sessionManager->clearSession();
                    $error_message = "دور المستخدم غير صالح. يرجى الاتصال بالإدارة.";
                    break;
            }
        }
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['username']) && isset($_POST['password'])) {
    // Sanitize and validate input
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    // Basic validation
    if (empty($username) || empty($password)) {
        $error_message = "يرجى إدخال اسم المستخدم وكلمة المرور.";
    } elseif (strlen($username) > 50) {
        $error_message = "اسم المستخدم طويل جداً.";
    } elseif (strlen($password) > 255) {
        $error_message = "كلمة المرور طويلة جداً.";
    } else {

    $stmt = $conn->prepare("SELECT account_id, username, password, role, store_id, name, status, access_type FROM accounts WHERE username = ? LIMIT 1");
    if (!$stmt) {
        error_log("Database prepare failed: " . $conn->error);
        $error_message = "خطأ في النظام. يرجى المحاولة لاحقاً.";
    } else {
        $stmt->bind_param("s", $username);
        if (!$stmt->execute()) {
            error_log("Database execute failed: " . $stmt->error);
            $error_message = "خطأ في النظام. يرجى المحاولة لاحقاً.";
            $stmt->close();
        } else {
            $result = $stmt->get_result();
            $stmt->close();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        if ($user['status'] !== 'active') {
            $error_message = "حسابك غير مفعل.";
        } elseif (password_verify($password, $user['password'])) {
            $account_id = $user['account_id'];
            $role = $user['role'];
            $store_id = $user['store_id'];
            $name = $user['name'];
            $access_type = $user['access_type'] ?? 'cashier_system'; // Default to cashier system

            // Set session variables
            $encrypted_account_id = ensureAccountIdInSession($account_id, $key);
            $_SESSION['username'] = $username;
            $_SESSION['role'] = $role;
            $_SESSION['store_id'] = encrypt($store_id, $key); // Add encrypted store_id to session
            $_SESSION['access_type'] = $access_type; // Add access type to session

            // Handle "Remember Me" functionality using session manager
            if (!empty($_POST['remember_me'])) {
                $sessionManager->createRememberMeToken($account_id, $username);
            } else {
                $sessionManager->clearUserTokens($account_id);
            }

            // Log the login action with the user's name in Arabic
            $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description)
                        VALUES (?, 'login', 'accounts', ?)";
            $description = "قام المستخدم $name بتسجيل الدخول";
            $log_stmt = $conn->prepare($log_sql);
            $log_stmt->bind_param("is", $account_id, $description);
            $log_stmt->execute();
            $log_stmt->close();

            // Reset redirect count on successful login
            $_SESSION['redirect_count'] = 0;
            $_SESSION['last_redirect_time'] = time();

            // Redirect based on access type and role
            if ($access_type === 'admin_panel') {
                // Administrative system - redirect to stores
                header("Location: {$baseUrl}stores.php");
                exit();
            } elseif ($access_type === 'cashier_system') {
                // Cashier system - redirect to users
                $encrypted_store_id = encrypt($store_id, $key);
                header("Location: {$baseUrl}users.php?store_id=$encrypted_store_id&account_id=" . urlencode($encrypted_account_id));
                exit();
            } else {
                // Fallback to old role-based system for compatibility
                if ($role == 'admin') {
                    header("Location: {$baseUrl}stores.php");
                    exit();
                } elseif ($role == 'dealer') {
                    $encrypted_store_id = encrypt($store_id, $key);
                    header("Location: {$baseUrl}index_dealer.php?store_id=$encrypted_store_id&account_id=" . urlencode($_SESSION['account_id']));
                    exit();
                } elseif ($role == 'user') {
                    $encrypted_store_id = encrypt($store_id, $key);
                    header("Location: {$baseUrl}users.php?store_id=$encrypted_store_id&account_id=" . urlencode($encrypted_account_id));
                    exit();
                } else {
                    // Invalid role - clear session and show error
                    error_log("Invalid role during login: " . $role . " for user: " . $username);
                    session_unset();
                    session_destroy();
                    $error_message = "خطأ في صلاحيات الحساب. يرجى الاتصال بالإدارة.";
                }
            }
        } else {
            $error_message = "كلمة المرور غير صحيحة.";
        }
    } else {
        $error_message = "اسم المستخدم غير موجود.";
    }
        } // Close the database execute else block
    } // Close the database prepare else block
    } // Close the validation else block
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <title>Elwaled Market</title>
    <link href="uploads/img/logo2.png" rel="icon">
    <link rel="stylesheet" href="web_css/style_login9.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .box {
            height: 450px; /* Increased height to ensure the login button is visible */
        }

    </style>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#2E3AA1">

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <!-- Firebase Messaging -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>
</head>
<body>
    <div class="box">
        <div class="border-line"></div>
        <form method="POST" action="">
            <h2>تسجيل الدخول</h2>
            <div class="input-box">
                <input type="text" name="username" required="required">
                <span>اسم المستخدم</span>
                <i></i>
            </div>
            <div class="input-box">
                <input type="password" name="password" required="required">
                <span>كلمة المرور</span>
                <i></i>
            </div><br>
            <div class="remember-me">
                <input type="checkbox" name="remember_me" id="remember_me">
                <label for="remember_me">تذكرني</label>
            </div>
            <div class="imp-links">
                <!-- Remove "نسيت كلمة المرور" link -->
                <a href="register.php">تسجيل مستخدم جديد</a>
            </div>
            <input type="submit" value="تسجيل الدخول" class="btn">
        </form>
    </div>

    <!-- Hidden form to trigger FCM token registration after successful login -->
    <div id="fcmTokenRegistration" style="display: none; text-align: center; padding: 10px; background-color: #e3f2fd; border-radius: 5px; margin-top: 10px;">
        <p style="margin: 0; color: #1976d2;">
            <i class="fas fa-bell" style="margin-left: 5px;"></i>
            جاري تهيئة الإشعارات...
        </p>
    </div>

    <!-- Notification Sound -->
    <audio id="notificationSound" src="/sounds/notification.mp3" preload="auto"></audio>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Firebase Login Integration -->
    <script src="/js/firebase-login.js"></script>

    <script>
        // Set user login status for Firebase script
        window.isUserLoggedIn = <?php echo !empty($_SESSION['account_id']) ? 'true' : 'false'; ?>;

        function showPopupMessage(message, type) {
            Swal.fire({
                icon: type,
                title: message,
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        }

        <?php if (isset($error_message)): ?>
            showPopupMessage(<?php echo json_encode($error_message, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP); ?>, 'error');
        <?php endif; ?>

        // تم تحسين الملف لضمان حفظ معرف الحساب مشفراً في الجلسة في جميع الحالات
        // File enhanced to ensure account ID is encrypted and saved in session in all cases
    </script>


  <script>
  let deferredPrompt;

  window.addEventListener('beforeinstallprompt', (e) => {
    // يمنع العرض الأوتوماتيكي
    e.preventDefault();
    // خزن الحدث عشان تستخدمه بعدين
    deferredPrompt = e;

    // دلوقتي ممكن تظهر زر أو مودال للمستخدم "حمل التطبيق"
    showInstallButton();
  });

  function showInstallButton() {
    const btn = document.createElement('button');
    btn.textContent = 'نزل التطبيق';
    btn.style = 'position:fixed; bottom:20px; right:20px; padding:10px;';
    document.body.append(btn);

    btn.addEventListener('click', async () => {
      // عرض البروبمت
      deferredPrompt.prompt();
      // استنى اختيار المستخدم
      const { outcome } = await deferredPrompt.userChoice;
      if (outcome === 'accepted') {
        console.log('User accepted the install');
      } else {
        console.log('User dismissed the install');
      }
      // مش محتاجه تاني
      deferredPrompt = null;
      btn.remove();
    });
  }
</script>

</body>
</html>