<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';
include 'permissions_system.php';

$permissions_system = new PermissionsSystem($conn, getenv('ENCRYPTION_KEY'));
$permissions_system->checkPageAccess('sales', 'view_page');

$key = getenv('ENCRYPTION_KEY');

$store_id = null;
if (isset($_GET['store_id'])) {
    $encrypted_store_id = $_GET['store_id'];
    $store_id = decrypt($encrypted_store_id, $key);
}

$sql = "SELECT DATE(s.time) AS order_date, s.account_id, a.username, a.phone, 
               CASE 
                   WHEN SUM(CASE WHEN s.status = 'pending' THEN 1 ELSE 0 END) > 0 THEN 'pending'
                   WHEN SUM(CASE WHEN s.status = 'delayed' THEN 1 ELSE 0 END) > 0 THEN 'delayed'
                   ELSE 'confirmed'
               END AS status, 
               COUNT(s.item_id) AS item_count, 
               SUM(s.quantity) AS total_quantity, 
               SUM(s.price * s.quantity) AS total_amount, 
               COALESCE(SUM(s.collected), 0) AS collected
        FROM sales s
        JOIN accounts a ON s.account_id = a.account_id
        JOIN items i ON s.item_id = i.item_id";

$conditions = [];
$params = [];

if ($store_id) {
    $conditions[] = "s.store_id = ?";
    $params[] = $store_id;
}

// استبعاد الخدمات من المبيعات العادية
$conditions[] = "i.type != 'service'";

if (!empty($conditions)) {
    $sql .= " WHERE " . implode(" AND ", $conditions);
}

$sql .= " GROUP BY DATE(s.time), s.account_id, a.username, a.phone
          ORDER BY DATE(s.time) DESC"; // ترتيب الطلبات حسب التاريخ من الأحدث إلى الأقدم

$stmt = $conn->prepare($sql);
if ($store_id) {
    $stmt->bind_param("i", $store_id);
}
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}

?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <link href="uploads\img\logo.png" rel="icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات التجار</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="web_css/sales.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

   

</head>
<?php include 'sidebar.php'; ?>

<div class="container">
    <!-- Header Section with Enhanced Design -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-chart-line"></i>
            إدارة المبيعات والمرتجعات
        </h1>
        <p class="page-subtitle">تتبع وإدارة جميع عمليات البيع والإرجاع بسهولة</p>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-content">
                <h3 id="total-orders">0</h3>
                <p>إجمالي الطلبات</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-content">
                <h3 id="total-amount">0 ج.م</h3>
                <p>إجمالي المبيعات</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3 id="pending-orders">0</h3>
                <p>طلبات معلقة</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3 id="confirmed-orders">0</h3>
                <p>طلبات مؤكدة</p>
            </div>
        </div>
    </div>

    <!-- Enhanced Search Container -->
    <div class="search-container">
        <div class="search-header">
            <h3><i class="fas fa-filter"></i> فلترة البيانات</h3>
            <button type="button" class="toggle-search-btn" onclick="toggleSearchForm()">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
    <form id="searchForm" class="search-form">
            <input type="hidden" id="store-id" name="store_id" value="<?php echo htmlspecialchars($encrypted_store_id); ?>">
            
            <!-- Search Fields Row 1 -->
            <div class="search-row">
                <div class="search-field">
                    <label for="search-name">
                        <i class="fas fa-user"></i>
                        اسم الشخص
                    </label>
                    <input type="text" id="search-name" placeholder="ابحث عن اسم الشخص">
                </div>
                <div class="search-field">
                    <label for="search-phone">
                        <i class="fas fa-phone"></i>
                        رقم الهاتف
                    </label>
                    <input type="text" id="search-phone" placeholder="ابحث عن رقم الهاتف">
                </div>
                <div class="search-field">
                    <label for="search-barcode">
                        <i class="fas fa-barcode"></i>
                        البـاركود
                    </label>
                    <input type="text" id="search-barcode" placeholder="ابحث بالباركود">
                </div>
            </div>

            <!-- Search Fields Row 2 -->
            <div class="search-row">
                <div class="search-field">
                    <label for="start-date">
                        <i class="fas fa-calendar-alt"></i>
                        من تاريخ
                    </label>
                    <input type="date" id="start-date" value="<?php echo date('Y-m-01'); ?>">
                </div>
                <div class="search-field">
                    <label for="end-date">
                        <i class="fas fa-calendar-alt"></i>
                        إلى تاريخ
                    </label>
                    <input type="date" id="end-date">
                </div>
                <div class="search-field">
                    <label for="status-filter">
                        <i class="fas fa-flag"></i>
                        حالة الطلب
                    </label>
                    <select id="status-filter">
                        <option value="">كل الحالات</option>
                        <option value="pending">انتظار</option>
                        <option value="delayed">مؤجل</option>
                        <option value="confirmed">مؤكد</option>
                    </select>
                </div>
            </div>

            <!-- Search Fields Row 3 -->
            <div class="search-row">
                <div class="search-field">
                    <label for="display-mode">
                        <i class="fas fa-eye"></i>
                        طريقة العرض
                    </label>
                    <select id="display-mode" onchange="fetchOrders()">
                        <option value="by_date">عرض حسب التاريخ</option>
                        <option value="by_account">عرض حسب الحساب</option>
                        <option value="by_daily">عرض يومي شامل</option>
                    </select>
                </div>
                <div class="search-actions">
                    <button type="button" class="search-btn" onclick="searchOrders()">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <button type="button" class="clear-btn" onclick="clearDates()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="button" class="refresh-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Enhanced Tabs Container -->
    <div class="tabs-container">
        <div class="tabs">
            <?php if ($permissions_system->hasPermission('sales', 'view_sales_tab')): ?>
                <button class="tab-btn active" data-tab="sales">
                    <i class="fas fa-shopping-bag"></i>
                    المبيعات
                    <span class="tab-count" id="sales-count">0</span>
                </button>
            <?php endif; ?>
            <?php if ($permissions_system->hasPermission('sales', 'view_services_tab')): ?>
                <button class="tab-btn" data-tab="services">
                    <i class="fas fa-concierge-bell"></i>
                    مبيعات الخدمات
                    <span class="tab-count" id="services-count">0</span>
                </button>
            <?php endif; ?>
            <?php if ($permissions_system->hasPermission('sales', 'view_returns_tab')): ?>
                <button class="tab-btn" data-tab="returns">
                    <i class="fas fa-undo"></i>
                    المرتجعات
                    <span class="tab-count" id="returns-count">0</span>
                </button>
            <?php endif; ?>
        </div>
    </div>

    
    <!-- Enhanced Table Section -->
    <div class="table-section">
        <div class="table-header">
            <div class="table-title">
                <h3 id="table-title">قائمة المبيعات</h3>
                <span class="table-subtitle" id="table-subtitle">عرض جميع المعاملات</span>
            </div>
            <div class="table-controls">
                <button class="control-btn" onclick="selectAllOrders()" title="تحديد الكل">
                    <i class="fas fa-check-square"></i>
                </button>
                <button class="control-btn" id="toggle-profit-btn" onclick="toggleProfitColumns()" title="إخفاء/إظهار خانات المكسب والتكلفة">
                    <i class="fas fa-eye" id="eye-icon"></i>
                </button>
                <?php if ($permissions_system->hasPermission('sales', 'export_data')): ?>
                    <button class="control-btn" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="fas fa-file-excel"></i>
                    </button>
                <?php endif; ?>
                <?php if ($permissions_system->hasPermission('sales', 'print_invoices')): ?>
                    <button class="control-btn" onclick="printTable()" title="طباعة الجدول">
                        <i class="fas fa-print"></i>
                    </button>
                <?php endif; ?>
                <?php if ($permissions_system->hasPermission('sales', 'view_reports')): ?>
                    <button class="control-btn" onclick="showSalesReport()" title="عرض تقرير المبيعات الشامل">
                        <i class="fas fa-chart-pie"></i>
                    </button>
                    <button class="control-btn" onclick="showServicesReport()" title="عرض تقرير الخدمات الشامل">
                        <i class="fas fa-concierge-bell"></i>
                    </button>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="table-wrapper">
            <table class="enhanced-table">
                <thead>
                    <tr>
                        <th class="checkbox-column">
                            <input type="checkbox" id="select-all-checkbox" onchange="toggleSelectAll()">
                        </th>
                        <th class="action-column">
                            <i class="fas fa-print"></i>
                            فاتورة
                        </th>
                        <th class="sortable" onclick="sortTable('username')">
                            <i class="fas fa-user"></i>
                            اسم الشخص
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th>
                            <i class="fas fa-user-tag"></i>
                            العميل
                        </th>
                        <th class="sortable" onclick="sortTable('phone')">
                            <i class="fas fa-phone"></i>
                            رقم الهاتف
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('status')">
                            <i class="fas fa-flag"></i>
                            حالة الفاتورة
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('quantity')">
                            <i class="fas fa-boxes"></i>
                            كمية الأصناف
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('amount')">
                            <i class="fas fa-money-bill-wave"></i>
                            مجموع الفاتورة
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <?php if ($permissions_system->hasPermission('sales', 'view_profit')): ?>
                            <th class="profit-column sortable" onclick="sortTable('profit')">
                                <i class="fas fa-chart-line"></i>
                                مكسب الفاتورة
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                        <?php endif; ?>
                        <th class="sortable" onclick="sortTable('date')">
                            <i class="fas fa-calendar-alt"></i>
                            التاريخ
                            <i class="fas fa-sort sort-icon"></i>
                        </th>
                        <th class="action-column">
                            <i class="fas fa-cogs"></i>
                            إجراءات
                        </th>
                    </tr>
                </thead>
                <tbody id="orders-table-body">
                    <!-- سيتم ملء القائمة باستخدام JavaScript -->
                </tbody>
            </table>
            
            <!-- Enhanced Loading Spinner -->
            <div id="loading-spinner" class="loading-spinner">
                <div class="spinner-container">
                    <div class="spinner"></div>
                    <div class="loading-text">
                        <p>جاري تحميل البيانات...</p>
                        <span class="loading-dots">...</span>
                    </div>
                </div>
            </div>
            
            <!-- Empty State -->
            <div id="empty-state" class="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-inbox"></i>
                </div>
                <h3>لا توجد بيانات</h3>
                <p>لم يتم العثور على أي طلبات تطابق معايير البحث</p>
                <button class="refresh-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث البيانات
                </button>
            </div>
        </div>
    </div>
    
    <!-- Enhanced Fixed Navbar -->
    <div class="fixed-navbar">
        <div class="navbar-content">
            <div class="selection-info">
                <span id="selected-count">0</span>
                <span>عنصر محدد</span>
            </div>
            <div class="navbar-buttons">
                <?php if ($permissions_system->hasPermission('sales', 'print_invoices')): ?>
                    <button onclick="printSelectedReceipts()" class="action-btn print-btn" disabled>
                        <i class="fas fa-print"></i>
                        <span class="btn-text">طباعة الفواتير</span>
                    </button>
                <?php endif; ?>
                <button onclick="viewSelectedOrders()" class="action-btn view-btn" disabled>
                    <i class="fas fa-eye"></i>
                    <span class="btn-text">عرض التفاصيل</span>
                </button>
                <?php if ($permissions_system->hasPermission('sales', 'manage_sales') || $permissions_system->hasPermission('sales', 'manage_returns')): ?>
                    <button onclick="deleteSelectedOrders()" class="action-btn delete-btn" disabled>
                        <i class="fas fa-trash-alt"></i>
                        <span class="btn-text">حذف المحدد</span>
                    </button>
                <?php endif; ?>
                <?php if ($permissions_system->hasPermission('sales', 'export_data')): ?>
                    <button onclick="exportSelectedToExcel()" class="action-btn export-btn" disabled>
                        <i class="fas fa-file-excel"></i>
                        <span class="btn-text">تصدير المحدد</span>
                    </button>
                <?php endif; ?>
            </div>
            <div class="quick-actions">
                <button onclick="refreshData()" class="quick-btn" title="تحديث البيانات">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button onclick="toggleFullscreen()" class="quick-btn" title="ملء الشاشة">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div id="orderDetailsModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h2>تفاصيل الفاتورة</h2><br>
        <div class="table-wrapper">
        <table>
            <thead>
                <tr>
                    <th>اختيار</th>
                    <th>معرف البيع</th>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>تكلفة القطعة</th>
                    <th>سعر القطعة</th>
                    <th>مجموع السعر</th>
                    <th>مكسب الصنف</th>
                    <th>تاريخ ووقت البيع</th>
                    <th>الحالة</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody id="order-details-body">
                <!-- سيتم ملء هذه القائمة باستخدام JavaScript -->
            </tbody>
        </table>
        </div>
        <button class="action-btn" style="background-color:#dc3545;color:white;" onclick="deleteSelectedItems()">حذف الأصناف المحددة</button>
    </div>
</div>

<!-- Orders Modal -->
<div id="userOrdersModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeUserOrdersModal()">&times;</span>
        <h2>طلبات المستخدم</h2>
        <div id="userOrdersSummary" class="summary-bar">
            <div class="summary-item frame">
                <span id="userTotalQuantity">إجمالي الكميات: 0</span>
            </div>
            <div class="summary-item frame">
                <span id="userTotalAmount">إجمالي مجموع سعر الأصناف: 0</span>
            </div>
            <div class="summary-item frame">
                <span id="userTotalPaid">إجمالي المدفوع: 0</span>
            </div>
            <div class="summary-item frame">
                <span id="userTotalRemaining">الباقي: 0</span>
            </div>
        </div>
        <table id="userOrdersTable" class="display">
            <thead>
                <tr>
                    <th>رقم الطلب</th>
                    <th>اسم الصنف</th>
                    <th>حالة الطلب</th>
                    <th>عدد الأصناف</th>
                    <th>المجموع</th>
                    <th>تاريخ الطلب</th>
                </tr>
            </thead>
            <tbody id="userOrdersTableBody">
                <!-- Orders will be populated here -->
            </tbody>
        </table>
    </div>
</div>

<audio id="newOrderSound" src="Ques.wav" preload="auto"></audio>
<input type="hidden" id="store-id" value="<?php echo htmlspecialchars($encrypted_store_id); ?>">

<script src="js/sales.js" defer></script>
<script src="view_selected_orders.js" defer></script>

<?php
$conn->close();
?>
<script>
        // تمرير معرف المتجر المشفر واسم المتجر إلى JavaScript
        window.encryptedStoreId = '<?php echo $encrypted_store_id; ?>';
        window.storeName = '<?php echo htmlspecialchars($store_name); ?>';
    </script>
</body>
</html>
