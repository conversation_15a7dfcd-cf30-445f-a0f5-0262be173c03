<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');
session_start();

$encrypted_account_id = $_GET['account_id'] ?? null;
$account_id = $encrypted_account_id ? decrypt($encrypted_account_id, $key) : null;

if (!$account_id) {
    die("Account ID not found. Please log in again.");
}

// Fetch the store_id using the account_id
$query = "SELECT store_id FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$account = $result->fetch_assoc();
$store_id = $account['store_id'] ?? null;

if (!$store_id) {
    die("Store ID not found for the given Account ID.");
}

// Fetch the user's theme preference
$query = "SELECT theme FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$userTheme = $user['theme'] ?? 'Light';

// Fetch all notifications for the store
$query = "SELECT message, created_at FROM notifications WHERE store_id = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
$notifications = $result->fetch_all(MYSQLI_ASSOC);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <title>كل الإشعارات</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
  
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
            --primary-light: #4dabf7;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white: #ffffff;
            --light-bg: #f0f2f5;
            
            /* ألوان محسنة للثيم الداكن - أكثر راحة للعين */
            --dark-bg: #0f1419;
            --dark-surface: #1a2332;
            --dark-surface-light: #242b3d;
            --dark-surface-hover: #2a3441;
            --dark-text: #e1e8f0;
            --dark-text-secondary: #b8c5d1;
            --dark-text-muted: #8a9ba8;
            --border-color: #dee2e6;
            --dark-border: #2d3748;
            --dark-border-light: #3a4553;
            
            /* ألوان زرقاء ناعمة ومريحة */
            --blue-gradient-start: #1e3a8a;
            --blue-gradient-end: #3b82f6;
            --blue-accent: #5b9bd5;
            --blue-hover: #4a90c2;
            --blue-soft: #6ba3d6;
            --blue-muted: #4a7ba7;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: var(--light-bg);
            color: #333;
            transition: all 0.3s ease;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
            direction: rtl;
            text-align: right;
            margin-bottom: 80px;
        }

        .dark-mode {
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-surface) 40%, var(--dark-surface-light) 100%);
            color: var(--dark-text);
            min-height: 100vh;
        }

        .notification-page-container {
            max-width: 700px;
            margin: 0 auto;
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            border: 1px solid rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
            padding: 30px;
        }

        .notification-page-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .dark-mode .notification-page-container {
            background: var(--dark-surface-light);
            box-shadow: 0 8px 30px rgba(26, 35, 50, 0.4);
            border: 1px solid var(--dark-border-light);
        }

        .dark-mode .notification-page-container:hover {
            box-shadow: 0 12px 40px rgba(26, 35, 50, 0.5);
            border-color: var(--blue-soft);
        }

        .notification-page-header {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 700;
        }

        .dark-mode .notification-page-header {
            color: var(--blue-soft);
        }

        .notification-page-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(0, 123, 255, 0.01) 100%);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .notification-page-item:hover {
            background: linear-gradient(90deg, rgba(0, 123, 255, 0.08) 0%, rgba(0, 123, 255, 0.04) 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.15);
            border-color: var(--primary-color);
        }

        .dark-mode .notification-page-item {
            background: linear-gradient(135deg, var(--dark-surface) 0%, var(--dark-surface-light) 100%);
            border: 1px solid var(--dark-border-light);
            box-shadow: 0 4px 15px rgba(26, 35, 50, 0.3);
        }

        .dark-mode .notification-page-item:hover {
            background: linear-gradient(90deg, rgba(91, 155, 213, 0.12) 0%, rgba(74, 144, 194, 0.06) 100%);
            border-color: var(--blue-soft);
            box-shadow: 0 6px 20px rgba(91, 155, 213, 0.2);
        }

        .notification-page-message {
            font-size: 16px;
            color: #333;
            margin: 0;
            flex: 1;
            font-weight: 500;
            line-height: 1.5;
        }

        .dark-mode .notification-page-message {
            color: var(--dark-text);
        }

        .notification-page-time {
            font-size: 12px;
            color: var(--secondary-color);
            margin-left: 15px;
            white-space: nowrap;
            font-weight: 500;
        }

        .dark-mode .notification-page-time {
            color: var(--dark-text-muted);
        }

        .no-notifications {
            text-align: center;
            padding: 60px 20px;
            color: var(--secondary-color);
        }

        .dark-mode .no-notifications {
            color: var(--dark-text-secondary);
        }

        .no-notifications-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .no-notifications-text {
            font-size: 1.3rem;
            font-weight: 600;
        }

        .back-button {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            text-decoration: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            border: none;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
            color: var(--white);
            text-decoration: none;
        }

        .dark-mode .back-button {
            background: linear-gradient(135deg, var(--blue-muted) 0%, var(--blue-accent) 100%);
            box-shadow: 0 4px 15px rgba(91, 155, 213, 0.3);
        }

        .dark-mode .back-button:hover {
            box-shadow: 0 6px 20px rgba(91, 155, 213, 0.4);
        }

        /* Ensure bottom navigation bar styles do not interfere */
        .bottom-nav {
            z-index: 9999;
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .notification-page-container {
                padding: 20px;
            }

            .notification-page-header {
                font-size: 2rem;
            }

            .notification-page-item {
                padding: 15px;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .notification-page-time {
                margin-left: 0;
                align-self: flex-end;
            }
        }

        @media (max-width: 480px) {
            .notification-page-header {
                font-size: 1.8rem;
            }

            .notification-page-item {
                padding: 12px;
            }

            .notification-page-message {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="notification-page-container">
        <h2 class="notification-page-header">كل الإشعارات</h2>
        <?php if (!empty($notifications)): ?>
            <?php foreach ($notifications as $notification): ?>
                <div class="notification-page-item">
                    <p class="notification-page-message"><?= htmlspecialchars($notification['message']) ?></p>
                    <p class="notification-page-time"><?= htmlspecialchars($notification['created_at']) ?></p>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="no-notifications">
                <i class="fas fa-bell-slash no-notifications-icon"></i>
                <div class="no-notifications-text">لا توجد إشعارات.</div>
            </div>
        <?php endif; ?>
    </div>
    <?php include 'bottom_nav.php'; ?>
    <script>
        // Initialize theme
        const body = document.body;
        const userTheme = '<?= $userTheme ?>';

        // Set initial theme
        if (userTheme === 'Dark') {
            body.classList.add('dark-mode');
        }

        document.addEventListener('DOMContentLoaded', function () {
            const notificationsIcon = document.querySelector('#notifications-icon');
            if (notificationsIcon) {
                notificationsIcon.classList.add('active');
            }

            // Add fade-in animation to notification items
            const notificationItems = document.querySelectorAll('.notification-page-item');
            notificationItems.forEach((item, index) => {
                item.classList.add('fade-in');
                item.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
