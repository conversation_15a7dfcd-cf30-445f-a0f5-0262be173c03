<?php
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['current_store_id'])) {
    $encrypted_current_store_id = $_GET['current_store_id'];
    $current_store_id = decrypt($encrypted_current_store_id, $key);
    
    if ($current_store_id === false) {
        echo json_encode(['success' => false, 'message' => 'خطأ في فك التشفير']);
        exit();
    }
    
    // الحصول على التصنيف المحدد إذا كان متوفراً
    $category_name = null;
    $category_id = null;
    
    if (isset($_GET['category_id']) && !empty($_GET['category_id'])) {
        $encrypted_category_id = $_GET['category_id'];
        $category_id = decrypt($encrypted_category_id, $key);
        
        if ($category_id !== false && $category_id > 0) {
            // جلب اسم التصنيف من الفرع الحالي
            $category_sql = "SELECT name FROM categories WHERE category_id = ? AND store_id = ?";
            $category_stmt = $conn->prepare($category_sql);
            
            if ($category_stmt) {
                $category_stmt->bind_param("ii", $category_id, $current_store_id);
                $category_stmt->execute();
                $category_result = $category_stmt->get_result();
                
                if ($category_result->num_rows > 0) {
                    $category_name = $category_result->fetch_assoc()['name'];
                } else {
                    // التصنيف غير موجود في الفرع الحالي
                    echo json_encode([
                        'success' => false, 
                        'message' => 'لا يمكن الإضافة في هذا الفرع لعدم وجود التصنيف المحدد'
                    ]);
                    exit();
                }
                $category_stmt->close();
            }
        }
    }
    
    if (!empty($category_name)) {
        // جلب الفروع التي تحتوي على نفس التصنيف (بالاسم)
        $sql = "SELECT DISTINCT s.store_id, s.name, 
                (SELECT COUNT(*) FROM categories WHERE store_id = s.store_id) as categories_count,
                c.category_id, c.name as category_name
                FROM stores s 
                JOIN categories c ON s.store_id = c.store_id 
                WHERE s.store_id != ? AND c.name = ?
                ORDER BY s.name ASC";
        
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("is", $current_store_id, $category_name);
        } else {
            echo json_encode(['success' => false, 'message' => 'خطأ في إعداد الاستعلام: ' . $conn->error]);
            exit();
        }
    } else {
        // جلب جميع الفروع عدا الفرع الحالي (للحالات التي لا يوجد فيها تصنيف محدد)
        $sql = "SELECT store_id, name, 
                (SELECT COUNT(*) FROM categories WHERE store_id = stores.store_id) as categories_count 
                FROM stores 
                WHERE store_id != ?
                ORDER BY name ASC";
        
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("i", $current_store_id);
        } else {
            echo json_encode(['success' => false, 'message' => 'خطأ في إعداد الاستعلام: ' . $conn->error]);
            exit();
        }
    }
    
    if ($stmt) {
        $stmt->execute();
        $result = $stmt->get_result();
        
        $stores = [];
        while ($row = $result->fetch_assoc()) {
            $store_data = [
                'store_id' => $row['store_id'],
                'encrypted_store_id' => encrypt($row['store_id'], $key),
                'name' => $row['name'],
                'categories_count' => $row['categories_count']
            ];
            
            // إضافة معلومات التصنيف إذا كان متوفراً
            if (isset($row['category_id'])) {
                $store_data['category_id'] = $row['category_id'];
                $store_data['encrypted_category_id'] = encrypt($row['category_id'], $key);
                $store_data['category_name'] = $row['category_name'];
            }
            
            $stores[] = $store_data;
        }
        
        $stmt->close();
        
        $message = '';
        if ($category_name && count($stores) == 0) {
            $message = "لا توجد فروع أخرى تحتوي على تصنيف '$category_name'";
        } elseif ($category_name) {
            $message = "الفروع التي تحتوي على تصنيف '$category_name'";
        }
        
        echo json_encode([
            'success' => true,
            'stores' => $stores,
            'category_name' => $category_name,
            'message' => $message
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في تحضير الاستعلام']);
    }
    
} else {
    echo json_encode(['success' => false, 'message' => 'معرف الفرع مفقود']);
}

$conn->close();
?>