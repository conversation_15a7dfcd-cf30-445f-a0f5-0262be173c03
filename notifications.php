<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

include 'db_connection.php';
require_once 'encryption_functions.php';

// Get store_id from URL parameter or session
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : (isset($_SESSION['store_id']) ? $_SESSION['store_id'] : '');
$store_id = null;

if (!empty($encrypted_store_id)) {
    $key = getenv('ENCRYPTION_KEY');
    $store_id = decrypt($encrypted_store_id, $key);
}

// Get current user ID from encrypted account_id in session
$user_id = null;
if (isset($_SESSION['account_id']) && !empty($_SESSION['account_id'])) {
    $key = getenv('ENCRYPTION_KEY');
    $user_id = decrypt($_SESSION['account_id'], $key);
}

// Fetch unread notifications from the last week based on store_id
$query = "
    SELECT n.id,
           SUBSTRING(n.message, 1, 50) as title,
           n.message, n.created_at, n.created_by, n.status,
           0 as is_read,
           NULL as viewed_at,
           u.username as sender_name
    FROM notifications n
    LEFT JOIN accounts u ON n.created_by = u.account_id
    WHERE n.status = 'notread' AND n.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
";

$params = [];

// Add store filter if store_id is provided
if ($store_id) {
    $query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
    $params[] = $store_id;
}

$query .= " ORDER BY n.created_at DESC";

if (count($params) > 0) {
    $stmt = $conn->prepare($query);
    $stmt->bind_param(str_repeat('i', count($params)), ...$params);
} else {
    $stmt = $conn->prepare($query);
}
$stmt->execute();
$result = $stmt->get_result();

// Fetch unread notifications count
$unread_query = "
    SELECT COUNT(*) as unread_count
    FROM notifications n
    WHERE n.status = 'notread' AND n.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
";

$unread_params = [];

if ($store_id) {
    $unread_query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
    $unread_params[] = $store_id;
}

$unread_stmt = $conn->prepare($unread_query);
if (count($unread_params) > 0) {
    $unread_stmt->bind_param(str_repeat('i', count($unread_params)), ...$unread_params);
}
$unread_stmt->execute();
$unread_result = $unread_stmt->get_result();
$unread_assoc = $unread_result->fetch_assoc();
$unreadCount = isset($unread_assoc['unread_count']) ? $unread_assoc['unread_count'] : 0;
?>

<style>
    .notification-icon {
        font-size: 30px;
        color: #fdb813;
        cursor: pointer;
        position: absolute;
        top: 40px;
        left: 40px;
    }

    .notification-icon .notification-count {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: red;
        color: white;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 12px;
        font-weight: bold;
    }

    .notification-container {
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;
    }

    .notification-item {
        background-color: #f1f1f1;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .notification-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .notification-item.unread {
        border-left: 4px solid #3f51b5;
        background-color: #f8f9ff;
    }

    .notification-item.read {
        opacity: 0.7;
    }

    .notification-message {
        font-size: 16px;
        margin: 0 0 8px 0;
        line-height: 1.4;
    }

    .notification-time {
        font-size: 12px;
        color: #888;
        margin-bottom: 8px;
    }

    .notification-sender {
        font-size: 11px;
        color: #666;
        font-style: italic;
    }

    .mark-read-btn {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 5px 10px;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        font-size: 12px;
        margin: 4px 2px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.3s ease;
    }

    .mark-read-btn:hover {
        background-color: #45a049;
    }

    /* Dark theme styles */
    [data-theme="dark"] .notification-icon {
        color: #fdb813;
    }

    [data-theme="dark"] .notification-icon .notification-count {
        background-color: #e74c3c;
        color: #ffffff;
    }

    [data-theme="dark"] .notification-container {
        background-color: #161b22;
        color: #c9d1d9;
        border: 1px solid #3f51b5;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
    }

    [data-theme="dark"] .notification-item {
        background-color: #1c1e22;
        color: #e6edf3;
        border: 1px solid #333;
    }

    [data-theme="dark"] .notification-item.unread {
        background-color: #1a1d29;
        border-left-color: #5865f2;
    }

    [data-theme="dark"] .notification-item.read {
        background-color: #2c2f33;
        color: #888;
    }

    [data-theme="dark"] .mark-read-btn {
        background-color: #4caf50;
        color: #ffffff;
    }

    [data-theme="dark"] .mark-read-btn:hover {
        background-color: #45a049;
    }

    /* High z-index for notification popup */
    .high-z-index-swal {
        z-index: 99999 !important;
    }

    .swal2-container.high-z-index-swal {
        z-index: 99999 !important;
    }

    /* Ensure notification icon is also high z-index */
    .notification-icon {
        z-index: 9999;
        position: relative;
    }
</style>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    let previousUnreadCount = <?= $unreadCount ?>;

    window.markAsRead = function(notificationId) {
        fetch('mark_notification_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                notification_id: notificationId
            })
        }).then(response => response.json())
          .then(data => {
              if (data.success) {
                  // Update the specific notification element
                  let notificationElement = document.getElementById('notification-' + notificationId);
                  if (notificationElement) {
                      notificationElement.classList.remove('unread');
                      notificationElement.classList.add('read');
                      const markReadBtn = notificationElement.querySelector('.mark-read-btn');
                      if (markReadBtn) {
                          markReadBtn.remove();
                      }
                  }

                  // Update unread count immediately and with delay
                  updateUnreadCount();
                  setTimeout(() => {
                      updateUnreadCount();
                  }, 1000);

                  // Force update if available
                  if (typeof forceUpdateUnreadCount === 'function') {
                      setTimeout(() => {
                          forceUpdateUnreadCount();
                      }, 1500);
                  }

                  // Refresh notifications in popup if it's open
                  const swalContainer = document.querySelector('.swal2-container');
                  if (swalContainer && swalContainer.style.display !== 'none') {
                      setTimeout(() => {
                          refreshNotificationsInPopup();
                      }, 500); // Small delay to ensure database is updated
                  }
              } else {
                  console.error('Error marking notification as read:', data.message);
              }
          })
          .catch(error => {
              console.error('Error:', error);
          });
    }

    window.updateUnreadCount = function() {
        // This function will be overridden by firebase_scripts.php
    }

    window.showNotifications = function() {
        // Fetch latest notifications from server
        fetchLatestNotifications();
    }

    function fetchLatestNotifications() {
        const urlParams = new URLSearchParams(window.location.search);
        const storeId = urlParams.get('store_id');

        let url = 'get_notifications.php';
        if (storeId) {
            url += '?store_id=' + encodeURIComponent(storeId);
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayNotifications(data.notifications, data.unread_count);
                } else {
                    console.error('Error fetching notifications:', data.message);
                    displayNotifications([], 0);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                displayNotifications([], 0);
            });
    }

    function displayNotifications(notifications, unreadCount) {
        let notificationsHtml = '';

        if (notifications.length > 0) {
            notifications.forEach(notification => {
                const createdAt = new Date(notification.created_at);
                const formattedDate = createdAt.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });

                notificationsHtml += `
                    <div class="notification-item ${notification.is_read ? 'read' : 'unread'}" id="notification-${notification.id}">
                        <div class="notification-time">
                            <i class="fas fa-clock"></i> ${formattedDate}
                        </div>
                        <div class="notification-message">
                            ${notification.title}: ${notification.message.replace(/\n/g, '<br>')}
                        </div>
                        <div class="notification-sender">
                            من: ${notification.sender_name}
                        </div>
                        ${!notification.is_read ? `
                            <button class="mark-read-btn" onclick="markAsRead(${notification.id})">
                                <i class="fas fa-check"></i> تحديد كمقروء
                            </button>
                        ` : ''}
                    </div>
                `;
            });
        } else {
            notificationsHtml = '<div class="notification-item"><div class="notification-message">لا توجد إشعارات جديدة</div></div>';
        }

        Swal.fire({
            title: 'الإشعارات',
            html: `<div class="notification-container">${notificationsHtml}</div>`,
            width: '600px',
            padding: '3em',
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: 'إغلاق',
            confirmButtonColor: '#3B82F6',
            customClass: {
                container: 'high-z-index-swal'
            },
            didOpen: () => {
                // Set high z-index for the notification popup
                const swalContainer = document.querySelector('.swal2-container');
                if (swalContainer) {
                    swalContainer.style.zIndex = '99999';
                }

                // Auto-refresh notifications every 5 seconds while popup is open
                const intervalId = setInterval(() => {
                    refreshNotificationsInPopup();
                }, 5000);

                // Clear interval when popup is closed
                Swal.getCloseButton().addEventListener('click', () => {
                    clearInterval(intervalId);
                });

                // Also clear interval if popup is closed by clicking outside
                const swalPopup = document.querySelector('.swal2-popup');
                if (swalPopup) {
                    swalPopup.addEventListener('click', (e) => {
                        e.stopPropagation();
                    });

                    swalContainer.addEventListener('click', () => {
                        clearInterval(intervalId);
                    });
                }
            }
        });
    }

    function refreshNotificationsInPopup() {
        const urlParams = new URLSearchParams(window.location.search);
        const storeId = urlParams.get('store_id');

        let url = 'get_notifications.php';
        if (storeId) {
            url += '?store_id=' + encodeURIComponent(storeId);
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the notification container content
                    const container = document.querySelector('.notification-container');
                    if (container) {
                        let notificationsHtml = '';

                        if (data.notifications.length > 0) {
                            data.notifications.forEach(notification => {
                                const createdAt = new Date(notification.created_at);
                                const formattedDate = createdAt.toLocaleString('ar-EG', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                });

                                notificationsHtml += `
                                    <div class="notification-item ${notification.is_read ? 'read' : 'unread'}" id="notification-${notification.id}">
                                        <div class="notification-time">
                                            <i class="fas fa-clock"></i> ${formattedDate}
                                        </div>
                                        <div class="notification-message">
                                            ${notification.title}: ${notification.message.replace(/\n/g, '<br>')}
                                        </div>
                                        <div class="notification-sender">
                                            من: ${notification.sender_name}
                                        </div>
                                        ${!notification.is_read ? `
                                            <button class="mark-read-btn" onclick="markAsRead(${notification.id})">
                                                <i class="fas fa-check"></i> تحديد كمقروء
                                            </button>
                                        ` : ''}
                                    </div>
                                `;
                            });
                        } else {
                            notificationsHtml = '<div class="notification-item"><div class="notification-message">لا توجد إشعارات جديدة</div></div>';
                        }

                        container.innerHTML = notificationsHtml;
                    }

                    // Update unread count
                    updateUnreadCount();
                }
            })
            .catch(error => {
                console.error('Error refreshing notifications:', error);
            });
    }

    function refreshNotifications() {
        updateUnreadCount();
    }

    setInterval(refreshNotifications, 5000); // Refresh notifications every 5 seconds
</script>



