<?php
include 'db_connection.php';
include 'encryption_functions.php';

header('Content-Type: application/json');

$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : null;

if (!$encrypted_store_id || !$start_date || !$end_date) {
    echo json_encode(['success' => false, 'error' => 'Missing required parameters.']);
    exit();
}

$store_id = decrypt($encrypted_store_id, $key);

$query = "SELECT shift_type, status, shift_amount, purchases 
          FROM shift_closures 
          WHERE store_id = ? AND shift_date BETWEEN ? AND ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("iss", $store_id, $start_date, $end_date);
$stmt->execute();
$result = $stmt->get_result();

$morningShifts = 0;
$nightShifts = 0;
$confirmedShifts = 0;
$pendingShifts = 0; // Add this variable to track pending shifts
$totalMorningAmount = 0;
$totalNightAmount = 0;
$totalAmount = 0;
$totalPurchases = 0; // Add this variable to track total purchases
$totalShifts = 0; // Add this variable to track the total number of shifts

while ($row = $result->fetch_assoc()) {
    $shift_type = strtolower(trim($row['shift_type']));
    $status = $row['status'];
    $amount = floatval($row['shift_amount']);
    $purchases = floatval($row['purchases']); // Fetch purchases

    $totalShifts++; // Increment total shifts for every row

    if ($status === 'Active') {
        $totalAmount += $amount;
        $totalPurchases += $purchases; // Add purchases to total

        if ($shift_type === 'morning') {
            $morningShifts++;
            $totalMorningAmount += $amount;
        } elseif ($shift_type === 'night') {
            $nightShifts++;
            $totalNightAmount += $amount;
        }

        $confirmedShifts++;
    } elseif ($status === 'Inactive') {
        $pendingShifts++; // Count pending shifts
    }
}

$stmt->close();
$conn->close();

echo json_encode([
    'success' => true,
    'total_amount' => $totalAmount,
    'total_purchases' => $totalPurchases, // Include total purchases in the response
    'total_shifts' => $totalShifts, // Include total shifts in the response
    'morning_shifts' => $morningShifts,
    'morning_amount' => $totalMorningAmount,
    'night_shifts' => $nightShifts,
    'night_amount' => $totalNightAmount,
    'confirmed_shifts' => $confirmedShifts,
    'pending_shifts' => $pendingShifts // Include pending shifts in the response
]);
?>
