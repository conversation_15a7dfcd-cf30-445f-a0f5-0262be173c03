<?php
// منع التخزين في المتصفح
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');

// تفريغ OPcache وذاكرة الحالة
if (function_exists('opcache_reset')) {
    opcache_reset();
}
clearstatcache();

session_start();
include 'db_connection.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

// Log the logout action if the user is logged in
if (isset($_SESSION['account_id'])) {
    $account_id = decrypt($_SESSION['account_id'], $key);

    // Fetch the user's name from the database
    $stmt = $conn->prepare("SELECT name FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $stmt->bind_result($name);
    $stmt->fetch();
    $stmt->close();

    // Log the logout action
    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
                VALUES (?, 'logout', 'accounts', ?)";
    $description = "قام المستخدم $name بتسجيل الخروج";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("is", $account_id, $description);
    $log_stmt->execute();
    $log_stmt->close();

    // Delete the "Remember Me" token from the database
    if (!empty($_COOKIE['remember_me'])) {
        $stmt = $conn->prepare("DELETE FROM user_tokens WHERE account_id = ?");
        $stmt->bind_param("i", $account_id);
        $stmt->execute();
        $stmt->close();
    }
}

// Clear all session data
$_SESSION = [];

// If session cookies are used, delete the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Clear all cookies
foreach ($_COOKIE as $key => $value) {
    setcookie($key, '', time() - 42000, '/');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>تسجيل الخروج</title>
    <script>
        // Clear offline data before redirecting
        if (typeof clearOfflineDataForLogout === 'function') {
            clearOfflineDataForLogout().then(() => {
                window.location.href = 'index.php';
            }).catch(() => {
                // Redirect anyway if cleanup fails
                window.location.href = 'index.php';
            });
        } else {
            // Fallback: try to clear IndexedDB manually
            try {
                // Clear all possible database names
                const possibleDBNames = ['invoiceOfflineDB'];

                // Try to get user-specific database names from storage
                if (sessionStorage.getItem('current_user_id')) {
                    possibleDBNames.push('invoiceOfflineDB_' + sessionStorage.getItem('current_user_id'));
                }

                let clearPromises = possibleDBNames.map(dbName => {
                    return new Promise((resolve) => {
                        const deleteRequest = indexedDB.deleteDatabase(dbName);
                        deleteRequest.onsuccess = () => resolve();
                        deleteRequest.onerror = () => resolve(); // Continue even if fails
                        deleteRequest.onblocked = () => resolve(); // Continue even if blocked
                    });
                });

                Promise.all(clearPromises).then(() => {
                    window.location.href = 'index.php';
                });

            } catch (error) {
                console.error('Error clearing offline data:', error);
                window.location.href = 'index.php';
            }
        }
    </script>
</head>
<body>
    <p>جاري تسجيل الخروج وتنظيف البيانات...</p>
</body>
</html>
