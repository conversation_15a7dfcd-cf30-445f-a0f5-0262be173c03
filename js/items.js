// @ts-nocheck
// متغيرات الصلاحيات - سيتم تعيينها من PHP
var permissions = window.itemsPermissions || {
    canCreate: false,
    canEdit: false,
    canDelete: false,
    canChangeStatus: false
};

var editItemModal = document.getElementById("editItemModal");
var addItemModal = document.getElementById("addItemModal");
var editItemSpan = document.getElementsByClassName("close")[0];

/* Updated modal display using "active" class */
editItemSpan.onclick = function() {
    editItemModal.classList.remove("active"); // instead of .style.display = "none"
}

// Consolidated window click handler to avoid conflicts
window.onclick = function(event) {
    // Handle edit modal
    if (event.target == editItemModal) {
        editItemModal.classList.remove("active");
    }
    
    // Handle add modal
    if (event.target == addItemModal) {
        addItemModal.classList.remove("active");
    }
    
    // Handle view images modal
    const viewImagesModal = document.getElementById('viewImagesModal');
    if (event.target == viewImagesModal) {
        viewImagesModal.classList.remove('active');
    }
    
    // Handle image preview modal
    const imagePreviewModal = document.getElementById('imagePreviewModal');
    if (event.target == imagePreviewModal) {
        closeImagePreview();
    }
}

function editItem(encryptedItemId) {
    // فحص الصلاحية
    if (!permissions.canEdit) {
        Swal.fire({
            icon: 'warning',
            title: 'غير مصرح',
            text: 'ليس لديك صلاحية لتعديل الأصناف'
        });
        return;
    }

    // Disable SSE connection
    if (typeof eventSource !== "undefined") {
        eventSource.close();
    }

    // Fetch data for the selected item using AJAX
    fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('edit_item_id').value = encryptedItemId;
                document.getElementById('edit_item_type').value = data.item.type;
                document.getElementById('edit_barcode').value = data.item.barcode;
                
                // Reset arrays for tracking changes
                imagesToDelete = [];
                currentItemImages = data.item.images || [];
                similarItemsData = [];
                selectedSimilarItems = [];
                
                // إعادة تعيين خيار التعديل في فروع أخرى
                document.getElementById('edit_in_other_stores').checked = false;
                document.getElementById('other_stores_items').style.display = 'none';
                
                // Display current images
                const currentImagesDiv = document.getElementById('current_images');
                currentImagesDiv.innerHTML = '';
                if (data.item.images && data.item.images.length > 0) {
                    const imagesTitle = document.createElement('h4');
                    imagesTitle.textContent = 'الصور الحالية:';
                    currentImagesDiv.appendChild(imagesTitle);
                    
                    data.item.images.forEach(image => {
                        const imageContainer = document.createElement('div');
                        imageContainer.style.display = 'inline-block';
                        imageContainer.style.margin = '5px';
                        imageContainer.style.position = 'relative';
                        
                        const img = document.createElement('img');
                        img.src = image.img_path;
                        img.style.width = '100px';
                        img.style.height = '100px';
                        img.style.objectFit = 'cover';
                        img.style.border = '1px solid #ddd';
                        img.style.borderRadius = '5px';
                        
                        const deleteBtn = document.createElement('button');
                        deleteBtn.innerHTML = '×';
                        deleteBtn.style.position = 'absolute';
                        deleteBtn.style.top = '-5px';
                        deleteBtn.style.right = '-5px';
                        deleteBtn.style.background = 'red';
                        deleteBtn.style.color = 'white';
                        deleteBtn.style.border = 'none';
                        deleteBtn.style.borderRadius = '50%';
                        deleteBtn.style.width = '20px';
                        deleteBtn.style.height = '20px';
                        deleteBtn.style.cursor = 'pointer';
                        deleteBtn.onclick = function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            markImageForDeletion(image.img_id, imageContainer);
                        };
                        
                        imageContainer.appendChild(img);
                        imageContainer.appendChild(deleteBtn);
                        currentImagesDiv.appendChild(imageContainer);
                    });
                }
                
                toggleEditItemFields();

                if (data.item.type === 'piece' || data.item.type === 'other') {
                    document.getElementById('edit_item_name_piece').value = data.item.name;
                    document.getElementById('edit_cost_piece').value = data.item.cost;
                    document.getElementById('edit_price1_piece').value = data.item.price;
                    document.getElementById('edit_quantity_piece').value = data.item.quantity;
                } else if (data.item.type === 'box') {
                    document.getElementById('edit_item_name_box').value = data.item.name;
                    document.getElementById('edit_cost_box').value = data.item.cost;
                    document.getElementById('edit_price1_box').value = data.item.price;
                    document.getElementById('edit_quantity_box').value = data.item.quantity;
                    document.getElementById('edit_pieces_per_box').value = data.item.pieces_per_box;
                } else if (data.item.type === 'fridge') {
                    document.getElementById('edit_item_name_fridge').value = data.item.name;
                    document.getElementById('edit_cost_fridge').value = data.item.cost;
                    document.getElementById('edit_price1_fridge').value = data.item.price;
                    document.getElementById('edit_quantity_fridge').value = data.item.quantity;
                } else if (data.item.type === 'service') {
                    document.getElementById('edit_item_name_service').value = data.item.name;
                    document.getElementById('edit_cost_service').value = data.item.cost;
                    document.getElementById('edit_price_service').value = data.item.price;
                    document.getElementById('edit_quantity_service').value = data.item.quantity;
                    document.getElementById('edit_service_provider').value = data.item.service_provider;
                    document.getElementById('edit_service_type').value = data.item.service_type;
                    document.getElementById('edit_commission_threshold').value = data.item.commission_threshold;
                    document.getElementById('edit_commission_fixed').value = data.item.commission_fixed;
                    document.getElementById('edit_commission_per_thousand').value = data.item.commission_per_thousand;
                    
                    // تعيين قيمة checkbox السعر المخصص
                    const editIsCustomPriced = document.getElementById('edit_is_custom_priced');
                    if (editIsCustomPriced) {
                        editIsCustomPriced.checked = data.item.is_custom_priced == 1;
                    }
                    
                    toggleCommissionFields('edit');
                }

                // Display modal using active class
                editItemModal.classList.add("active"); // instead of .style.display = "block"
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'فشل في جلب تفاصيل الصنف.'
                });
            }
        })
        .catch(error => {
            console.error("Error fetching item:", error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء جلب تفاصيل الصنف.'
            });
        });
}

function deleteItem(itemId) {
    // فحص الصلاحية
    if (!permissions.canDelete) {
        Swal.fire({
            icon: 'warning',
            title: 'غير مصرح',
            text: 'ليس لديك صلاحية لحذف الأصناف'
        });
        return;
    }

    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، احذفه!'
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new FormData();
            formData.append('delete_item_id', itemId);

            fetch('delete_item.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(`item-${itemId}`).remove();
                    Swal.fire(
                        'تم الحذف!',
                        'تم حذف الصنف بنجاح.',
                        'success'
                    );
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الحذف.'
                });
            });
        }
    });
}

function setupDynamicTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tableViews = document.querySelectorAll('.table-view');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tab = button.dataset.tab;

            // Update button states
            tabButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Update table visibility
            if (tab === 'all') {
                tableViews.forEach(view => view.classList.add('active'));
            } else {
                tableViews.forEach(view => view.classList.remove('active'));
                document.getElementById(`${tab}-view`).classList.add('active');
            }
        });
    });
}

document.getElementById('searchField').onkeyup = function() {
    var filter = this.value.toLowerCase();
    var rows = document.querySelectorAll('#itemsTable tr, #servicesTable tr');
    rows.forEach(row => {
        var name = row.cells[1].textContent.toLowerCase();
        var barcode = row.cells[5] ? row.cells[5].textContent.toLowerCase() : '';
        var type = row.dataset.type;

        if (name.includes(filter) || barcode.startsWith(filter) || type.includes(filter)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function toggleCheckbox(row) {
    var checkbox = row.querySelector('input[type="checkbox"]');
    if (checkbox) {
        checkbox.checked = !checkbox.checked;
    }
}

// Improved event handling to prevent conflicts between row clicks and button clicks
function handleRowClick(event, row) {
    // Check if the click was on a button or its child elements (including favorite button)
    if (event.target.closest('button') || 
        event.target.closest('.status-cell') || 
        event.target.closest('.favorite-btn') ||
        event.target.classList.contains('fas')) {
        return; // Don't toggle checkbox if clicking on buttons or status
    }
    toggleCheckbox(row);
}

function toggleEditItemFields() {
    var itemType = document.getElementById("edit_item_type").value;
    var pieceFields = document.getElementById("editPieceFields");
    var boxFields = document.getElementById("editBoxFields");
    var fridgeFields = document.getElementById("editFridgeFields");
    var serviceFields = document.getElementById("editServiceFields");

    pieceFields.style.display = "none";
    boxFields.style.display = "none";
    fridgeFields.style.display = "none";
    serviceFields.style.display = "none";

    if (itemType === "piece" || itemType === "other") {
        pieceFields.style.display = "block";
    } else if (itemType === "box") {
        boxFields.style.display = "block";
    } else if (itemType === "fridge") {
        fridgeFields.style.display = "block";
    } else if (itemType === "service") {
        serviceFields.style.display = "block";
        toggleCommissionFields('edit');
    }
}

function showAddItemForm() {
    // فحص الصلاحية
    if (!permissions.canCreate) {
        Swal.fire({
            icon: 'warning',
            title: 'غير مصرح',
            text: 'ليس لديك صلاحية لإنشاء أصناف جديدة',
            confirmButtonText: 'حسناً',
            customClass: {
                confirmButton: 'swal2-confirm-custom'
            }
        });
        return;
    }

    // إعادة تعيين النموذج
    resetAddItemForm();
    
    // إضافة تأثير الظهور
    addItemModal.classList.add("active");
    
    // التركيز على أول حقل
    setTimeout(() => {
        const firstInput = addItemModal.querySelector('.input-field:not([disabled])');
        if (firstInput) {
            firstInput.focus();
        }
    }, 300);
}

function resetAddItemForm() {
    const form = document.querySelector('form[action="add_item_from_items.php"]');
    if (form) {
        form.reset();
        
        // إعادة تعيين المتغيرات
        selectedStoresForAdd = [];
        
        // إخفاء قسم الفروع الأخرى
        document.getElementById('add_to_other_stores').checked = false;
        document.getElementById('other_stores_add_list').style.display = 'none';
        
        // إعادة تعيين حقول النوع
        toggleAddItemFields();
        
        // إعادة تعيين الباركود
        toggleBarcode();
        
        // إخفاء رسائل الخطأ
        const errorMessages = form.querySelectorAll('.error-message');
        errorMessages.forEach(msg => msg.style.display = 'none');
    }
}

function closeAddItemForm() {
    addItemModal.classList.remove("active"); // instead of .style.display = "none"
}

function toggleAddItemFields() {
    var itemType = document.getElementById("item_type").value;
    var pieceFields = document.getElementById("addPieceFields");
    var boxFields = document.getElementById("addBoxFields");
    var fridgeFields = document.getElementById("addFridgeFields");
    var serviceFields = document.getElementById("addServiceFields");

    pieceFields.style.display = "none";
    boxFields.style.display = "none";
    fridgeFields.style.display = "none";
    serviceFields.style.display = "none";

    if (itemType === "piece" || itemType === "other") {
        pieceFields.style.display = "block";
    } else if (itemType === "box") {
        boxFields.style.display = "block";
    } else if (itemType === "fridge") {
        fridgeFields.style.display = "block";
    } else if (itemType === "service") {
        serviceFields.style.display = "block";
        toggleCommissionFields('add');
    }
}

function generateBarcode(inputId) {
    var inputField = document.getElementById(inputId);
    var randomBarcode = Math.floor(Math.random() * 1000000); // Generate a random 6-digit number
    inputField.value = randomBarcode;
}

function toggleBarcodeField(inputId, isChecked) {
    var inputField = document.getElementById(inputId);
    if (isChecked) {
        inputField.value = '';
        inputField.readOnly = true;
    } else {
        inputField.value = '';
        inputField.readOnly = false;
    }
}

function toggleBarcode() {
    var barcodeField = document.getElementById("barcode");
    var autoGenerateCheckbox = document.getElementById("auto_generate_barcode");
    if (autoGenerateCheckbox.checked) {
        barcodeField.disabled = true;
    } else {
        barcodeField.disabled = false;
    }
}

function toggleCommissionFields(prefix) {
  var serviceTypeElement = document.getElementById(prefix + '_service_type');
  if (!serviceTypeElement) return;

  var serviceType = serviceTypeElement.value;
  var commissionFields = document.getElementById('commissionFields_' + prefix);
  var quantityLabel = document.getElementById('quantity_label_' + prefix);
  var costLabel = document.querySelector('label[for="' + prefix + '_cost_service"]');
  var costInput = document.getElementById(prefix + '_cost_service');
  var priceLabel = document.querySelector('label[for="' + prefix + '_price_service"]');
  var priceInput = document.getElementById(prefix + '_price_service');

  if (serviceType === 'cash_withdraw' || serviceType === 'cash_deposit') {
    if (commissionFields) commissionFields.style.display = 'block';
    if (costLabel) costLabel.style.display = 'none';
    if (costInput) costInput.style.display = 'none';
    if (priceLabel) priceLabel.style.display = 'none';
    if (priceInput) priceInput.style.display = 'none';
  } else {
    if (commissionFields) commissionFields.style.display = 'none';
    if (costLabel) costLabel.style.display = 'block';
    if (costInput) costInput.style.display = 'block';
    if (priceLabel) priceLabel.style.display = 'block';
    if (priceInput) priceInput.style.display = 'block';
  }

  if (quantityLabel) {
      if (serviceType === 'topup_card' || serviceType === 'balance' || serviceType === 'bundle' || serviceType === 'cash_withdraw' || serviceType === 'cash_deposit') {
        quantityLabel.textContent = 'رصيد:';
      } else {
        quantityLabel.textContent = 'الكمية:';
      }
  }
}

document.addEventListener('DOMContentLoaded', function() {
  const editServiceType = document.getElementById('edit_service_type');
  if (editServiceType) {
    editServiceType.addEventListener('change', function() {
      toggleCommissionFields('edit');
    });
  }

  const addServiceType = document.getElementById('add_service_type');
  if (addServiceType) {
    addServiceType.addEventListener('change', function() {
      toggleCommissionFields('add');
    });
  }
});


document.querySelector('form[action="edit_item.php"]').addEventListener('submit', function(event) {
    event.preventDefault();
    
    const editInOtherStores = document.getElementById('edit_in_other_stores').checked;
    
    if (editInOtherStores && selectedSimilarItems.length > 0) {
        // التحديث المتعدد
        handleMultipleItemsUpdate();
    } else {
        // التحديث العادي
        handleSingleItemUpdate();
    }
});

function handleSingleItemUpdate() {
    const formData = new FormData(document.querySelector('form[action="edit_item.php"]'));
    
    // Add images to delete to form data
    if (imagesToDelete.length > 0) {
        formData.append('images_to_delete', JSON.stringify(imagesToDelete));
    }

    fetch('edit_item.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'تم تعديل الصنف بنجاح',
                showConfirmButton: false,
                timer: 3000
            });
            editItemModal.classList.remove("active");
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: data.message || 'حدث خطأ أثناء تعديل الصنف.'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'حدث خطأ أثناء تعديل الصنف.'
        });
    });
}

function handleMultipleItemsUpdate() {
    const form = document.querySelector('form[action="edit_item.php"]');
    const formData = new FormData(form);
    
    // جمع البيانات المطلوبة ل��تحديث المتعدد
    const itemType = document.getElementById('edit_item_type').value;
    let itemName, cost, price, quantity, piecesPerBox = 0;
    
    if (itemType === 'piece' || itemType === 'other') {
        itemName = document.getElementById('edit_item_name_piece').value;
        cost = document.getElementById('edit_cost_piece').value;
        price = document.getElementById('edit_price1_piece').value;
        quantity = document.getElementById('edit_quantity_piece').value;
    } else if (itemType === 'box') {
        itemName = document.getElementById('edit_item_name_box').value;
        cost = document.getElementById('edit_cost_box').value;
        price = document.getElementById('edit_price1_box').value;
        quantity = document.getElementById('edit_quantity_box').value;
        piecesPerBox = document.getElementById('edit_pieces_per_box').value;
    } else if (itemType === 'fridge') {
        itemName = document.getElementById('edit_item_name_fridge').value;
        cost = document.getElementById('edit_cost_fridge').value;
        price = document.getElementById('edit_price1_fridge').value;
        quantity = document.getElementById('edit_quantity_fridge').value;
    }
    
    const barcode = document.getElementById('edit_barcode').value;
    
    // إعداد البيانات للإرسال
    const updateData = new FormData();
    updateData.append('main_item_id', document.getElementById('edit_item_id').value);
    updateData.append('selected_items', JSON.stringify(selectedSimilarItems));
    updateData.append('item_name', itemName);
    updateData.append('cost', cost);
    updateData.append('price', price);
    updateData.append('barcode', barcode);
    updateData.append('pieces_per_box', piecesPerBox);
    
    // عرض تأكيد للمستخدم
    const selectedCount = selectedSimilarItems.length + 1; // +1 للصنف الحالي
    Swal.fire({
        title: 'تأكيد التحديث',
        text: `سيتم تحديث ${selectedCount} صنف في فروع مختلفة. هل أنت متأكد؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، حدث الكل',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('edit_item.php', {
                method: 'POST',
                body: updateData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = `تم تحديث ${data.updated_count} صنف بنجاح`;
                    
                    // إضافة معلومات الإشعارات إذا وجدت
                    if (data.price_change_notifications) {
                        message += `\nتم إرسال ${data.price_change_notifications} إشعار تغيير سعر`;
                    }
                    
                    Swal.fire({
                        icon: 'success',
                        title: 'تم التحديث بنجاح',
                        text: message,
                        showConfirmButton: false,
                        timer: 4000
                    });
                    editItemModal.classList.remove("active");
                    setTimeout(() => {
                        location.reload();
                    }, 3000);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء التحديث المتعدد.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء التحديث المتعدد.'
                });
            });
        }
    });
}

function toggleItemStatus(itemId, currentStatus) {
    // فحص الصلاحية
    if (!permissions.canChangeStatus) {
        Swal.fire({
            icon: 'warning',
            title: 'غير مصرح',
            text: 'ليس لديك صلاحية لتغيير حالة الأصناف'
        });
        return;
    }

    const formData = new FormData();
    formData.append('toggle_item_id', itemId);
    formData.append('current_status', currentStatus);

    fetch('toggle_item_status.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const statusCell = document.querySelector(`#item-${itemId} .status-cell`);
            const newStatus = data.new_status;
            const newStatusText = newStatus === 'active' ? 'نشط' : 'متوقف';
            const newStatusClass = newStatus === 'active' ? 'confirmed' : 'pending';

            statusCell.innerHTML = `<span class='status-frame ${newStatusClass}'>${newStatusText}</span>`;
            statusCell.classList.toggle('confirmed', newStatus === 'active');
            statusCell.classList.toggle('pending', newStatus !== 'active');
            statusCell.setAttribute('onclick', `event.stopPropagation(); toggleItemStatus(${itemId}, "${newStatus}")`);
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء تغيير حالة الصنف.'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'حدث خطأ أثناء تغيير حالة الصنف.'
        });
    });
}

// تعريف متغيرات سيتم تعيينها من PHP
var encrypted_store_id = window.encrypted_store_id || "";
var encrypted_account_id = window.encrypted_account_id || "";

// متغيرات الفلترة
let currentFilter = 'all';
let userFavorites = new Set();

// تحميل المفضلة من البيانات الأولية
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('#itemsTable tr');
    rows.forEach(row => {
        const favoriteBtn = row.querySelector('.favorite-btn');
        if (favoriteBtn && favoriteBtn.classList.contains('is-favorite')) {
            const itemId = favoriteBtn.getAttribute('data-item-id');
            if (itemId) {
                userFavorites.add(parseInt(itemId));
            }
        }
    });
});

// وظيفة إدارة المفضلة
async function toggleFavorite(itemId, button, event) {
    // منع انتشار الحدث
    if (event) {
        event.stopPropagation();
        event.preventDefault();
    }
    
    if (!encrypted_account_id) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'يجب تسجيل الدخول لإضافة الأصناف إلى المفضلة'
        });
        return;
    }

    const isFavorite = button.classList.contains('is-favorite');
    const action = isFavorite ? 'remove' : 'add';
    
    try {
        const response = await fetch('manage_favorites.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                account_id: encrypted_account_id,
                item_id: itemId,
                action: action
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            if (action === 'add') {
                button.classList.remove('not-favorite');
                button.classList.add('is-favorite');
                button.title = 'إزالة من المفضلة';
                userFavorites.add(parseInt(itemId));
            } else {
                button.classList.remove('is-favorite');
                button.classList.add('not-favorite');
                button.title = 'إضافة إل�� المفضلة';
                userFavorites.delete(parseInt(itemId));
            }
            
            // إعادة تطبيق الفلتر الحالي
            applyCurrentFilter();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: result.message || 'حدث خطأ في إدارة المفضلة'
            });
        }
        
    } catch (error) {
        console.error('خطأ في الشبكة:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'حدث خطأ في الاتصال بالخادم'
        });
    }
}

// وظائف الفلترة
function applyFilter(filterType) {
    currentFilter = filterType;
    
    // تحديث أزرار الفلتر باستخدام الكلاسات
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`[data-filter="${filterType}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
    
    applyCurrentFilter();
}

function applyCurrentFilter() {
    const rows = document.querySelectorAll('#itemsTable tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const favoriteBtn = row.querySelector('.favorite-btn');
        const imageBtn = row.querySelector('.action-btn[title*="صور"]');
        const statusCell = row.querySelector('.status-cell');
        
        if (!favoriteBtn) return; // تخطي الصفوف التي لا تحتوي على أزرار
        
        const itemId = parseInt(favoriteBtn.getAttribute('data-item-id'));
        const isFavorite = userFavorites.has(itemId);
        const hasImages = imageBtn && imageBtn.classList.contains('has-images-icon');
        
        // تحديد حالة الصنف (نشط أم متوقف)
        let isActive = true;
        if (statusCell) {
            const statusFrame = statusCell.querySelector('.status-frame');
            if (statusFrame) {
                isActive = statusFrame.classList.contains('confirmed');
            }
        }
        
        let shouldShow = true;
        
        switch (currentFilter) {
            case 'favorites':
                shouldShow = isFavorite;
                break;
            case 'active':
                shouldShow = isActive;
                break;
            case 'inactive':
                shouldShow = !isActive;
                break;
            case 'with-images':
                shouldShow = hasImages;
                break;
            case 'no-images':
                shouldShow = !hasImages;
                break;
            case 'all':
            default:
                shouldShow = true;
                break;
        }
        
        if (shouldShow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // إظهار/إخفاء رسالة عدم وجود نتائج
    const noResultsMessage = document.querySelector('.table-responsive');
    if (visibleCount === 0) {
        let message = '';
        switch (currentFilter) {
            case 'favorites':
                message = 'لا توجد أصناف في المفضلة';
                break;
            case 'active':
                message = 'لا توجد أصناف نشطة';
                break;
            case 'inactive':
                message = 'لا توجد أصناف متوقفة';
                break;
            case 'with-images':
                message = 'لا توجد أصناف مع ص��ر';
                break;
            case 'no-images':
                message = 'لا توجد أصناف بدون صور';
                break;
            default:
                message = 'لا توجد أصناف';
                break;
        }
        
        // إضافة رسالة إذا لم تكن موجودة
        let noResultsDiv = document.getElementById('no-results-filter');
        if (!noResultsDiv) {
            noResultsDiv = document.createElement('div');
            noResultsDiv.id = 'no-results-filter';
            noResultsDiv.style.cssText = 'text-align: center; padding: 40px; color: #666; font-style: italic;';
            noResultsMessage.appendChild(noResultsDiv);
        }
        noResultsDiv.innerHTML = `<i class="fas fa-search" style="font-size: 48px; color: #ccc; margin-bottom: 15px; display: block;"></i>${message}`;
        noResultsDiv.style.display = 'block';
    } else {
        const noResultsDiv = document.getElementById('no-results-filter');
        if (noResultsDiv) {
            noResultsDiv.style.display = 'none';
        }
    }
}

let currentItemId = '';
let imagesToDelete = []; // Array to store image IDs marked for deletion
let currentItemImages = []; // Array to store current item images
let similarItemsData = []; // Array to store similar items data
let selectedSimilarItems = []; // Array to store selected similar items

function viewItemImages(encryptedItemId) {
    currentItemId = encryptedItemId;
    
    // Fetch item details and images
    fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Set item name
                document.getElementById('modalItemName').textContent = `صور الصنف: ${data.item.name}`;
                
                // Clear previous images
                const imagesContainer = document.getElementById('modalImagesContainer');
                imagesContainer.innerHTML = '';
                
                if (data.item.images && data.item.images.length > 0) {
                    // Display images
                    data.item.images.forEach(image => {
                        const imageItem = document.createElement('div');
                        imageItem.className = 'modal-image-item';
                        
                        const img = document.createElement('img');
                        img.src = image.img_path;
                        img.alt = 'صورة الصنف';
                        img.onclick = function() {
                            openImagePreview(image.img_path);
                        };
                        
                        imageItem.appendChild(img);
                        imagesContainer.appendChild(imageItem);
                    });
                } else {
                    // No images message
                    const noImagesDiv = document.createElement('div');
                    noImagesDiv.className = 'no-images-message';
                    noImagesDiv.innerHTML = '<i class="fas fa-image" style="font-size: 48px; color: #ccc;"></i><br>لا توجد صور لهذا الصنف';
                    imagesContainer.appendChild(noImagesDiv);
                }
                
                // Show modal
                document.getElementById('viewImagesModal').classList.add('active');
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'فشل في جلب صور الصنف.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء جلب صور الصنف.'
            });
        });
}

function closeViewImagesModal() {
    document.getElementById('viewImagesModal').classList.remove('active');
}

function openFullImagePage() {
    window.open('view_item_images.php?item_id=' + encodeURIComponent(currentItemId), '_blank');
}

function openImagePreview(imageSrc) {
    document.getElementById('previewImage').src = imageSrc;
    document.getElementById('imagePreviewModal').style.display = 'block';
}

function closeImagePreview() {
    document.getElementById('imagePreviewModal').style.display = 'none';
}

// Image preview click handler is now consolidated in the main window.onclick handler above


function markImageForDeletion(imgId, imageContainer) {
    // Add image ID to deletion array if not already there
    if (!imagesToDelete.includes(imgId)) {
        imagesToDelete.push(imgId);
    }
    
    // Mark the image container as deleted (visual feedback)
    imageContainer.style.opacity = '0.5';
    imageContainer.style.border = '2px solid #dc3545';
    imageContainer.style.position = 'relative';
    
    // Add "محذوف" overlay
    const deletedOverlay = document.createElement('div');
    deletedOverlay.innerHTML = 'محذوف';
    deletedOverlay.style.position = 'absolute';
    deletedOverlay.style.top = '50%';
    deletedOverlay.style.left = '50%';
    deletedOverlay.style.transform = 'translate(-50%, -50%)';
    deletedOverlay.style.background = 'rgba(220, 53, 69, 0.9)';
    deletedOverlay.style.color = 'white';
    deletedOverlay.style.padding = '5px 10px';
    deletedOverlay.style.borderRadius = '3px';
    deletedOverlay.style.fontSize = '12px';
    deletedOverlay.style.fontWeight = 'bold';
    
    imageContainer.appendChild(deletedOverlay);
    
    // Change delete button to restore button
    const deleteBtn = imageContainer.querySelector('button');
    deleteBtn.innerHTML = '↶';
    deleteBtn.style.background = '#28a745';
    deleteBtn.title = 'استعادة الصورة';
    deleteBtn.onclick = function() {
        restoreImageFromDeletion(imgId, imageContainer);
    };
}

function restoreImageFromDeletion(imgId, imageContainer) {
    // Remove image ID from deletion array
    const index = imagesToDelete.indexOf(imgId);
    if (index > -1) {
        imagesToDelete.splice(index, 1);
    }
    
    // Restore visual appearance
    imageContainer.style.opacity = '1';
    imageContainer.style.border = '1px solid #ddd';
    
    // Remove "محذوف" overlay
    const overlay = imageContainer.querySelector('div:last-child');
    if (overlay && overlay.innerHTML === 'محذوف') {
        overlay.remove();
    }
    
    // Change restore button back to delete button
    const restoreBtn = imageContainer.querySelector('button');
    restoreBtn.innerHTML = '×';
    restoreBtn.style.background = '#dc3545';
    restoreBtn.title = 'حذف الصورة';
    restoreBtn.onclick = function() {
        markImageForDeletion(imgId, imageContainer);
    };
}

function updateImageIconColor(encryptedItemId, hasImages) {
    // Find the image button for this item
    const buttons = document.querySelectorAll('button[onclick*="' + encryptedItemId + '"]');
    buttons.forEach(button => {
        if (button.innerHTML.includes('fa-images')) {
            if (hasImages) {
                button.classList.remove('no-images-icon');
                button.classList.add('has-images-icon');
                button.title = 'يحتوي على صور';
            } else {
                button.classList.remove('has-images-icon');
                button.classList.add('no-images-icon');
                button.title = 'لا يحتوي على صور';
            }
        }
    });
}

// وظائف التعديل في فروع أخرى
function toggleOtherStoresEdit() {
    const checkbox = document.getElementById('edit_in_other_stores');
    const container = document.getElementById('other_stores_items');
    
    if (checkbox.checked) {
        container.style.display = 'block';
        loadSimilarItems();
    } else {
        container.style.display = 'none';
        similarItemsData = [];
        selectedSimilarItems = [];
    }
}

function loadSimilarItems() {
    const similarItemsList = document.getElementById('similar_items_list');
    const currentItemId = document.getElementById('edit_item_id').value;
    
    if (!currentItemId) {
        similarItemsList.innerHTML = '<div class="no-similar-items">لا يوجد صنف محدد</div>';
        return;
    }
    
    similarItemsList.innerHTML = '<div class="loading-similar-items"><i class="fas fa-spinner fa-spin"></i> جاري البحث عن الأصناف المشابهة...</div>';
    
    fetch(`get_similar_items_in_other_stores.php?item_id=${encodeURIComponent(currentItemId)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                similarItemsData = data.similar_items;
                displaySimilarItems(data.similar_items, data.current_barcode);
            } else {
                similarItemsList.innerHTML = `<div class="no-similar-items">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error loading similar items:', error);
            similarItemsList.innerHTML = '<div class="no-similar-items">حدث خطأ أثناء البحث عن الأصناف المشابهة</div>';
        });
}

function displaySimilarItems(items, barcode) {
    const similarItemsList = document.getElementById('similar_items_list');
    
    if (items.length === 0) {
        similarItemsList.innerHTML = `<div class="no-similar-items">لا توجد أصناف أخرى بنفس الباركود (${barcode}) في فروع أخرى</div>`;
        return;
    }
    
    let html = '';
    items.forEach(item => {
        const quantityUnit = getQuantityUnit(item.type);
        html += `
            <div class="similar-item" data-item-id="${item.encrypted_item_id}">
                <input type="checkbox" class="similar-item-checkbox" 
                       onchange="toggleSimilarItemSelection('${item.encrypted_item_id}', this)">
                <div class="similar-item-info">
                    <div class="similar-item-name">${item.name}</div>
                    <div class="similar-item-store">الفرع: ${item.store_name}</div>
                    <div class="similar-item-barcode">الباركود: ${item.barcode || 'غير محدد'}</div>
                    <div class="similar-item-details">
                        <span>التكلفة: ${item.cost}</span>
                        <span>السعر: ${item.price}</span>
                        <span>الكمية: ${item.quantity}${quantityUnit}</span>
                        <span>الحالة: ${item.status === 'active' ? 'نشط' : 'متوقف'}</span>
                    </div>
                </div>
            </div>
        `;
    });
    
    similarItemsList.innerHTML = html;
}

function toggleSimilarItemSelection(encryptedItemId, checkbox) {
    const itemDiv = checkbox.closest('.similar-item');
    
    if (checkbox.checked) {
        selectedSimilarItems.push(encryptedItemId);
        itemDiv.classList.add('selected');
    } else {
        const index = selectedSimilarItems.indexOf(encryptedItemId);
        if (index > -1) {
            selectedSimilarItems.splice(index, 1);
        }
        itemDiv.classList.remove('selected');
    }
}

function getQuantityUnit(type) {
    switch(type) {
        case 'box': return ' كرتونة';
        case 'fridge': return ' كيلو';
        case 'piece': return ' قطعة';
        default: return ' قطعة';
    }
}

// وظائف الإضافة في فروع أخرى
let selectedStoresForAdd = []; // Array to store selected stores for adding

function toggleOtherStoresAdd() {
    const checkbox = document.getElementById('add_to_other_stores');
    const container = document.getElementById('other_stores_add_list');
    
    if (checkbox.checked) {
        container.style.display = 'block';
        loadAvailableStores();
    } else {
        container.style.display = 'none';
        selectedStoresForAdd = [];
    }
}

function loadAvailableStores() {
    const availableStoresList = document.getElementById('available_stores_list');
    const currentStoreId = window.encrypted_store_id;
    
    if (!currentStoreId) {
        availableStoresList.innerHTML = '<div class="no-stores">لا يوجد فرع محدد</div>';
        return;
    }
    
    availableStoresList.innerHTML = '<div class="loading-stores"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الفروع المتاحة...</div>';
    
    let url = `get_available_stores_for_add.php?current_store_id=${encodeURIComponent(currentStoreId)}`;
    const categoryId = window.encrypted_category_id; // إضافة معرف التصنيف
    if (categoryId) {
        url += `&category_id=${encodeURIComponent(categoryId)}`;
    }
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAvailableStores(data.stores);
                // عرض رسالة توضيحية إذا كان هناك تصنيف محدد
                if (data.category_name) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'category-filter-message';
                    messageDiv.style.cssText = 'background: #e3f2fd; padding: 10px; margin-bottom: 15px; border-radius: 5px; color: #1976d2; font-size: 14px;';
                    messageDiv.innerHTML = `<i class="fas fa-info-circle"></i> الفروع المعروضة تحتوي على تصنيف "${data.category_name}"`;
                    availableStoresList.insertBefore(messageDiv, availableStoresList.firstChild);
                }
            } else {
                availableStoresList.innerHTML = `<div class="no-stores">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error loading available stores:', error);
            availableStoresList.innerHTML = '<div class="no-stores">حدث خطأ أثناء تحميل الفروع المتاحة</div>';
        });
}

function displayAvailableStores(stores) {
    const availableStoresList = document.getElementById('available_stores_list');
    
    if (stores.length === 0) {
        availableStoresList.innerHTML = '<div class="no-stores">لا توجد فروع أخرى تحتوي على نفس التصنيف</div>';
        return;
    }
    
    let html = '';
    stores.forEach(store => {
        html += `
            <div class="available-store" 
                 data-store-id="${store.encrypted_store_id}"
                 data-category-id="${store.encrypted_category_id || ''}">
                <input type="checkbox" class="store-checkbox" 
                       onchange="toggleStoreSelection('${store.encrypted_store_id}', this)">
                <div class="store-info">
                    <div class="store-name">${store.name}</div>
                    <div class="store-categories">التصنيفات: ${store.categories_count}</div>
                    ${store.category_name ? `<div class="store-category">التصنيف: ${store.category_name}</div>` : ''}
                </div>
            </div>
        `;
    });
    
    availableStoresList.innerHTML = html;
}

function toggleStoreSelection(encryptedStoreId, checkbox) {
    const storeDiv = checkbox.closest('.available-store');
    const categoryId = storeDiv.getAttribute('data-category-id'); // إضافة هذا
    
    if (checkbox.checked) {
        // حفظ كل من معرف الفرع ومعرف التصنيف
        selectedStoresForAdd.push({
            store_id: encryptedStoreId,
            category_id: categoryId
        });
        storeDiv.classList.add('selected');
    } else {
        // البحث والحذف بناءً على معرف الفرع
        const index = selectedStoresForAdd.findIndex(item => 
            (typeof item === 'object' && item.store_id === encryptedStoreId) || 
            (typeof item === 'string' && item === encryptedStoreId)
        );
        if (index > -1) {
            selectedStoresForAdd.splice(index, 1);
        }
        storeDiv.classList.remove('selected');
    }
}

// تعديل وظيفة إرسال النموذج لتشمل الإضافة في فروع أخرى
document.querySelector('form[action="add_item_from_items.php"]').addEventListener('submit', function(event) {
    event.preventDefault();
    const formData = new FormData(this);
    
    // إضافة الفروع المحددة للإضافة
    const addToOtherStores = document.getElementById('add_to_other_stores').checked;
    if (addToOtherStores && selectedStoresForAdd.length > 0) {
        formData.append('add_to_other_stores', 'true');
        formData.append('selected_stores', JSON.stringify(selectedStoresForAdd));
        
        // التحقق من الباركود في الفروع المحددة
        checkBarcodeInSelectedStores(formData);
    } else {
        // الإضافة العادية
        submitAddItemForm(formData);
    }
});

function checkBarcodeInSelectedStores(formData) {
    const barcode = formData.get('barcode');
    const autoGenerate = formData.get('auto_generate_barcode');
    
    if (autoGenerate) {
        // إذا كان الباركود تلقائي، لا نحتاج للتحقق
        submitAddItemForm(formData);
        return;
    }
    
    if (!barcode) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'يجب إدخال باركود أو تفعيل الباركود التلقائي'
        });
        return;
    }
    
    // التحقق من الباركود في الفروع المحددة
    const checkData = new FormData();
    checkData.append('barcode', barcode);
    checkData.append('selected_stores', JSON.stringify(selectedStoresForAdd));
    
    fetch('check_barcode_in_stores.php', {
        method: 'POST',
        body: checkData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.conflicts.length > 0) {
                // عرض تحذير بالفروع التي تحتوي على نفس الباركود
                let conflictMessage = 'تم العثور على أصناف بنفس الباركود في الفروع التالية:\n';
                data.conflicts.forEach(conflict => {
                    conflictMessage += `- ${conflict.store_name}: ${conflict.item_name}\n`;
                });
                conflictMessage += '\nهل تريد المتابعة والإضافة في الفروع الأخرى فقط؟';
                
                Swal.fire({
                    title: 'تعارض في الباركود',
                    text: conflictMessage,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، أضف في الفروع المتاحة',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // إزالة الفروع المتعارضة من القائمة
                        const conflictStoreIds = data.conflicts.map(c => c.encrypted_store_id);
                        const filteredStores = selectedStoresForAdd.filter(storeId => !conflictStoreIds.includes(storeId));
                        
                        if (filteredStores.length > 0) {
                            formData.set('selected_stores', JSON.stringify(filteredStores));
                            submitAddItemForm(formData);
                        } else {
                            Swal.fire({
                                icon: 'info',
                                title: 'لا توجد فروع متاحة',
                                text: 'جميع الفروع المحددة تحتوي على أصناف بنفس الباركود'
                            });
                        }
                    }
                });
            } else {
                // لا توجد تعارضات، يمكن المتابعة
                submitAddItemForm(formData);
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: data.message || 'حدث خطأ أثناء التحقق من الباركود'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'حدث خطأ أثناء التحقق من الباركود'
        });
    });
}

function submitAddItemForm(formData) {
    fetch('add_item_from_items.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let message = data.message;
            if (data.added_stores_count) {
                message += `\nتم إضافة الصنف في ${data.added_stores_count} فرع`;
            }
            
            Swal.fire({
                icon: 'success',
                title: message,
                showConfirmButton: false,
                timer: 3000
            });
            addItemModal.classList.remove("active");
            document.querySelector('form[action="add_item_from_items.php"]').reset();
            
            // إعادة تعيين المتغيرات
            selectedStoresForAdd = [];
            document.getElementById('add_to_other_stores').checked = false;
            document.getElementById('other_stores_add_list').style.display = 'none';

            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: data.message
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'حدث خطأ أثناء إضافة الصنف.'
        });
    });
}

function initializeCreativeServiceTooltips() {
    // Add CSS for the tooltip
    if (!document.getElementById('service-tooltip-styles')) {
        const style = document.createElement('style');
        style.id = 'service-tooltip-styles';
        style.textContent = `
            .service-tooltip-creative {
                display: none;
                position: absolute;
                z-index: 1080;
                background: linear-gradient(145deg, #ffffff, #f0f0f0);
                border: 1px solid #ddd;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                padding: 15px;
                width: 280px;
                font-family: 'Cairo', sans-serif;
                font-size: 14px;
                color: #333;
                opacity: 0;
                transform: translateY(10px);
                transition: opacity 0.3s ease, transform 0.3s ease;
            }
            .service-tooltip-creative.visible {
                display: block;
                opacity: 1;
                transform: translateY(0);
            }
            .service-tooltip-creative .tooltip-header {
                font-weight: 700;
                font-size: 16px;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid #eee;
                color: #2c3e50;
            }
            .service-tooltip-creative .tooltip-body .tooltip-row {
                margin-bottom: 8px;
                display: flex;
                justify-content: space-between;
            }
            .service-tooltip-creative .tooltip-body .tooltip-row strong {
                color: #34495e;
                font-weight: 600;
            }
        `;
        document.head.appendChild(style);
    }

    // Create a single tooltip element
    let tooltip = document.getElementById('service-tooltip-singleton');
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.id = 'service-tooltip-singleton';
        tooltip.className = 'service-tooltip-creative';
        document.body.appendChild(tooltip);
    }

    const table = document.getElementById('itemsTable');
    if (!table) return;

    table.addEventListener('mouseover', (e) => {
        const cell = e.target.closest('.service-type-cell');
        if (!cell) return;

        // Get data from attributes
        const provider = cell.dataset.provider || 'غير محدد';
        const serviceType = cell.dataset.serviceType || 'غير محدد';
        const threshold = cell.dataset.commissionThreshold;
        const fixed = cell.dataset.commissionFixed;
        const perThousand = cell.dataset.commissionPerThousand;
        const serviceTypeValue = cell.dataset.serviceTypeValue || '';

        let commissionDetails = '';
        if (serviceTypeValue === 'cash_withdraw' || serviceTypeValue === 'cash_deposit') {
            commissionDetails = `
                <div class="tooltip-row">
                    <strong>حد العمولة:</strong> <span>${threshold || 'N/A'}</span>
                </div>
                <div class="tooltip-row">
                    <strong>عمولة ثابتة:</strong> <span>${fixed || 'N/A'}</span>
                </div>
                <div class="tooltip-row">
                    <strong>عمولة لكل ألف:</strong> <span>${perThousand || 'N/A'}</span>
                </div>
            `;
        }

        tooltip.innerHTML = `
            <div class="tooltip-header">تفاصيل الخدمة</div>
            <div class="tooltip-body">
                <div class="tooltip-row">
                    <strong>المزود:</strong> <span>${provider}</span>
                </div>
                <div class="tooltip-row">
                    <strong>نوع الخدمة:</strong> <span>${serviceType}</span>
                </div>
                ${commissionDetails}
            </div>
        `;

        const rect = cell.getBoundingClientRect();
        tooltip.style.left = `${rect.left + window.scrollX}px`;
        tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
        tooltip.classList.add('visible');
    });

    table.addEventListener('mouseout', (e) => {
        const cell = e.target.closest('.service-type-cell');
        if (cell) {
            const tooltip = document.getElementById('service-tooltip-singleton');
            if (tooltip) {
                tooltip.classList.remove('visible');
            }
        }
    });
}

// إضافة event listeners لأزرار الفلترة
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.filter-btn').forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            applyFilter(filterType);
        });
    });
    
    // إضافة تحسينات على تجربة المستخدم
    initializeUserExperienceEnhancements();
    
    // طباعة الصلاحيات للتشخيص
    console.log('Permissions loaded:', permissions);
    
    // Initialize creative tooltips for service items
    initializeCreativeServiceTooltips();
});

// تحسينات تجربة المستخدم
function initializeUserExperienceEnhancements() {
    // إضافة اختصارات لوحة المفاتيح
    addKeyboardShortcuts();
    
    // إضافة تحسينات على النماذج
    enhanceForms();
    
    // إضافة تأثيرات بصرية
    addVisualEnhancements();
    
    // إضافة تحسينات الاستجابة
    addResponsiveEnhancements();
}

// اختصارات لوحة المفاتيح
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N: إضافة صنف جديد
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            if (permissions.canCreate) {
                showAddItemForm();
            }
        }
        
        // Escape: إغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            closeAllModals();
        }
        
        // Ctrl/Cmd + F: التركيز على البحث
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchField = document.getElementById('searchField');
            if (searchField) {
                searchField.focus();
                searchField.select();
            }
        }
        
        // أرقام 1-6: تطبيق الفلاتر
        if (e.key >= '1' && e.key <= '6' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            const target = e.target;
            if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                const filters = ['all', 'favorites', 'active', 'inactive', 'with-images', 'no-images'];
                const filterIndex = parseInt(e.key) - 1;
                if (filters[filterIndex]) {
                    applyFilter(filters[filterIndex]);
                }
            }
        }
    });
}

function closeAllModals() {
    const modals = document.querySelectorAll('.modal.active');
    modals.forEach(modal => {
        modal.classList.remove('active');
    });
    
    // إغلاق معاينة الصور
    const imagePreview = document.getElementById('imagePreviewModal');
    if (imagePreview && imagePreview.style.display === 'block') {
        closeImagePreview();
    }
}

// تحسينات النماذج
function enhanceForms() {
    // إضافة التحقق من صحة البيانات في الوقت الفعلي
    addRealTimeValidation();
    
    // إضافة حفظ تلقائي للمسودات
    addAutoSave();
    
    // إضافة تحسينات على حقول الإدخال
    enhanceInputFields();
}

function addRealTimeValidation() {
    // التحقق من الحقول المطلوبة
    document.querySelectorAll('.input-field[required]').forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });
        
        field.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
    
    // التحقق من الأرقام
    document.querySelectorAll('input[type="number"]').forEach(field => {
        field.addEventListener('input', function() {
            validateNumberField(this);
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    const isRequired = field.hasAttribute('required');
    
    if (isRequired && !value) {
        showFieldError(field, 'هذا الحقل مطلوب');
        return false;
    }
    
    clearFieldError(field);
    return true;
}

function validateNumberField(field) {
    const value = parseFloat(field.value);
    const min = parseFloat(field.getAttribute('min'));
    const max = parseFloat(field.getAttribute('max'));
    
    if (isNaN(value)) {
        showFieldError(field, 'يجب إدخال رقم صحيح');
        return false;
    }
    
    if (!isNaN(min) && value < min) {
        showFieldError(field, `القيمة يجب أن تكون أكبر من أو تساوي ${min}`);
        return false;
    }
    
    if (!isNaN(max) && value > max) {
        showFieldError(field, `القيمة يجب أن تكون أقل من أو تساوي ${max}`);
        return false;
    }
    
    clearFieldError(field);
    return true;
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        color: #dc3545;
        font-size: 0.85rem;
        margin-top: 5px;
        padding: 5px 10px;
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.2);
        border-radius: 5px;
        animation: slideDown 0.3s ease;
    `;
    
    field.style.borderColor = '#dc3545';
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    field.style.borderColor = '';
}

function addAutoSave() {
    // حفظ مسودة نموذج الإضافة
    const addForm = document.querySelector('form[action="add_item_from_items.php"]');
    if (addForm) {
        const inputs = addForm.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', debounce(() => {
                saveFormDraft('add_item_draft', addForm);
            }, 1000));
        });
        
        // استعادة المسودة عند فتح النموذج
        const originalShowAddItemForm = window.showAddItemForm;
        window.showAddItemForm = function() {
            originalShowAddItemForm();
            setTimeout(() => {
                restoreFormDraft('add_item_draft', addForm);
            }, 100);
        };
    }
}

function saveFormDraft(key, form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    localStorage.setItem(key, JSON.stringify(data));
}

function restoreFormDraft(key, form) {
    const savedData = localStorage.getItem(key);
    if (savedData) {
        try {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(fieldName => {
                const field = form.querySelector(`[name="${fieldName}"]`);
                if (field && field.type !== 'file') {
                    field.value = data[fieldName];
                }
            });
        } catch (e) {
            console.warn('خطأ في استعادة المسودة:', e);
        }
    }
}

function clearFormDraft(key) {
    localStorage.removeItem(key);
}

function enhanceInputFields() {
    // إضافة تأثيرات بصرية للحقول
    document.querySelectorAll('.input-field').forEach(field => {
        // إضافة تأثير التركيز
        field.addEventListener('focus', function() {
            this.parentNode.classList.add('field-focused');
        });
        
        field.addEventListener('blur', function() {
            this.parentNode.classList.remove('field-focused');
        });
        
        // إضافة تأثير الملء
        field.addEventListener('input', function() {
            if (this.value.trim()) {
                this.parentNode.classList.add('field-filled');
            } else {
                this.parentNode.classList.remove('field-filled');
            }
        });
        
        // تطبيق الحالة الأولية
        if (field.value.trim()) {
            field.parentNode.classList.add('field-filled');
        }
    });
}

// تأثيرات بصرية
function addVisualEnhancements() {
    // إضافة تأثيرات الحركة للجداول
    addTableAnimations();
    
    // إضافة تأثيرات للأزرار
    addButtonEffects();
    
    // إضافة تأثيرات للبطاقات
    addCardEffects();
}

function addTableAnimations() {
    const tableRows = document.querySelectorAll('#itemsTable tr');
    tableRows.forEach((row, index) => {
        row.style.animationDelay = `${index * 0.05}s`;
        row.classList.add('table-row-animate');
    });
    
    // إضافة CSS للحركة
    if (!document.getElementById('table-animations-style')) {
        const style = document.createElement('style');
        style.id = 'table-animations-style';
        style.textContent = `
            .table-row-animate {
                animation: fadeInUp 0.6s ease forwards;
                opacity: 0;
                transform: translateY(20px);
            }
            
            @keyframes fadeInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

function addButtonEffects() {
    document.querySelectorAll('.action-btn, .filter-btn, .add-btn').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
        
        button.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(0) scale(0.95)';
        });
        
        button.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });
    });
}

function addCardEffects() {
    document.querySelectorAll('.stat-card, .detail-section').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}

// تحسينات الاستجابة
function addResponsiveEnhancements() {
    // إضافة دعم اللمس للأجهزة المحمولة
    addTouchSupport();
    
    // إضافة تحسينات للشاشات الصغيرة
    addMobileOptimizations();
}

function addTouchSupport() {
    // إضافة دعم السحب للفلاتر على الأجهزة المحمولة
    const filterContainer = document.querySelector('.filter-buttons-row');
    if (filterContainer) {
        let isDown = false;
        let startX;
        let scrollLeft;
        
        filterContainer.addEventListener('mousedown', (e) => {
            isDown = true;
            startX = e.pageX - filterContainer.offsetLeft;
            scrollLeft = filterContainer.scrollLeft;
        });
        
        filterContainer.addEventListener('mouseleave', () => {
            isDown = false;
        });
        
        filterContainer.addEventListener('mouseup', () => {
            isDown = false;
        });
        
        filterContainer.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - filterContainer.offsetLeft;
            const walk = (x - startX) * 2;
            filterContainer.scrollLeft = scrollLeft - walk;
        });
    }
}

function addMobileOptimizations() {
    // تحسين حجم النوافذ المنبثقة للأجهزة المحمولة
    function optimizeModalForMobile() {
        const modals = document.querySelectorAll('.modal-content');
        modals.forEach(modal => {
            if (window.innerWidth <= 768) {
                modal.style.width = '95%';
                modal.style.maxHeight = '90vh';
                modal.style.margin = '5vh auto';
            }
        });
    }
    
    // تطبيق التحسينات عند تغيير حجم الشاشة
    window.addEventListener('resize', debounce(optimizeModalForMobile, 250));
    optimizeModalForMobile();
}

// وظائف مساعدة
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// إضافة إشعارات تفاعلية
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="notification-icon fas ${getNotificationIcon(type)}"></i>
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        max-width: 500px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        border-left: 4px solid ${getNotificationColor(type)};
    `;
    
    document.body.appendChild(notification);
    
    // إضافة CSS للإشعارات
    if (!document.getElementById('notifications-style')) {
        const style = document.createElement('style');
        style.id = 'notifications-style';
        style.textContent = `
            .notification-content {
                display: flex;
                align-items: center;
                padding: 15px;
                gap: 10px;
            }
            .notification-icon {
                font-size: 1.2rem;
                color: ${getNotificationColor(type)};
            }
            .notification-message {
                flex: 1;
                color: #333;
                font-weight: 500;
            }
            .notification-close {
                background: none;
                border: none;
                color: #999;
                cursor: pointer;
                padding: 5px;
                border-radius: 3px;
                transition: background 0.2s;
            }
            .notification-close:hover {
                background: #f0f0f0;
            }
        `;
        document.head.appendChild(style);
    }
    
    // إظهار الإشعار
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // إخفاء الإشعار تلقائياً
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, duration);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || colors.info;
}

// تحسين وظائف SweetAlert
const originalSwalFire = Swal.fire;
Swal.fire = function(options) {
    // إضافة تخصيصات افتراضية
    if (typeof options === 'object') {
        options.confirmButtonText = options.confirmButtonText || 'حسناً';
        options.cancelButtonText = options.cancelButtonText || 'إلغاء';
        options.customClass = options.customClass || {};
        options.customClass.confirmButton = 'swal2-confirm-custom';
        options.customClass.cancelButton = 'swal2-cancel-custom';
    }
    
    return originalSwalFire.call(this, options);
};

// إضافة CSS مخصص لـ SweetAlert
if (!document.getElementById('swal-custom-style')) {
    const style = document.createElement('style');
    style.id = 'swal-custom-style';
    style.textContent = `
        .swal2-confirm-custom {
            background: linear-gradient(135deg, var(--color-primary) 0%, #303f9f 100%) !important;
            border: none !important;
            border-radius: 25px !important;
            padding: 10px 25px !important;
            font-weight: 600 !important;
        }
        .swal2-cancel-custom {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
            border: none !important;
            border-radius: 25px !important;
            padding: 10px 25px !important;
            font-weight: 600 !important;
        }
    `;
    document.head.appendChild(style);
}