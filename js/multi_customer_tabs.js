/**
 * Multi Customer Tabs Manager
 * إدارة شريط العملاء المتعددين
 */

class CustomerTabsManager {
    constructor() {
        this.customers = new Map();
        this.activeCustomer = '1';
        this.maxCustomers = 20;
        this.customerCounter = 4; 
        this.init();
    }

    init() {
        // تهيئة العملاء الافتراضيين
        for (let i = 1; i <= 4; i++) {
            this.customers.set(i.toString(), {
                id: i.toString(),
                name: `العميل ${i}`,
                items: [],
                hasItems: false,
                lastActivity: Date.now(),
                invoiceType: 'customer_sale',
                branchId: null,
                accountBuyerId: null
            });
        }

        // إنشاء شريط العملاء
        this.createCustomerTabs();
        
        // ربط الأحداث
        this.bindEvents();
        
        // تحميل العميل الأول
        this.loadCustomer('1');
        
        // ربط أحداث السحب الأولية
        this.rebindOriginalTableEvents();
        this.rebindResponsiveTableEvents();
        
        console.log('Customer Tabs Manager initialized');
    }

    createCustomerTabs() {
        // البحث عن شريط العملاء الموجود أو إنشاؤه
        let tabsContainer = document.getElementById('multi-customer-tabs');
        
        if (!tabsContainer) {
            // إنشاء شريط العملاء
            tabsContainer = document.createElement('div');
            tabsContainer.id = 'multi-customer-tabs';
            tabsContainer.className = 'multi-customer-tabs';
            
            // إدراج الشريط بعد header
            const header = document.querySelector('.header');
            if (header) {
                header.insertAdjacentElement('afterend', tabsContainer);
            }
        }

        // إنشاء محتوى الشريط
        const tabsHTML = `
            <div class="customer-tabs-container">
                ${this.generateTabsHTML()}
                <div class="add-customer-tab" title="إضافة عميل جديد">
                    <i class="fas fa-plus"></i>
                </div>
            </div>
        `;

        tabsContainer.innerHTML = tabsHTML;
        tabsContainer.style.display = 'block';
    }

    generateTabsHTML() {
        let html = '';
        for (let [id, customer] of this.customers) {
            const isActive = id === this.activeCustomer;
            const hasItems = customer.items && customer.items.length > 0;
            const total = customer.items ? customer.items.reduce((sum, item) => {
                if (item.isCashService) {
                    return sum + (parseFloat(item.price) || 0);
                } else {
                    const price = parseFloat(item.price) || 0;
                    const quantity = parseFloat(item.quantity) || 1;
                    return sum + (price * quantity);
                }
            }, 0).toFixed(2) : '0.00';
            
            html += `
                <div class="customer-tab ${isActive ? 'active' : ''}" data-customer="${id}" style="position: relative;">
                    ${parseInt(id) > 4 ? '<button class="remove-customer" title="إزالة العميل" style="position: absolute; top: 50%; left: 8px; transform: translateY(-50%); background: transparent; border: none; color: #ff5b5b; cursor: pointer; font-size: 14px; padding: 5px;"><i class="fas fa-trash-alt"></i></button>' : ''}
                    <i class="fas fa-user" style="margin-left: ${parseInt(id) > 4 ? '30px' : '0px'};"></i>
                    <span class="customer-number">${id}</span>
                    <span class="customer-name">${customer.name}</span>
                    <div class="items-indicator ${hasItems ? 'has-items' : ''}" 
                         style="position: absolute; top: -8px; right: 20px; display: ${hasItems ? 'block' : 'none'}; background: red; color: white; border-radius: 50%; padding: 2px 6px; font-size: 12px; min-width: 20px; text-align: center;">
                        ${hasItems ? customer.items.length : ''}
                    </div>
                    <span class="customer-total">${total}</span>
                </div>
            `;
        }
        return html;
    }

    bindEvents() {
        const container = document.getElementById('multi-customer-tabs');
        if (!container) return;

        // التبديل بين العملاء
        container.addEventListener('click', (e) => {
            const tab = e.target.closest('.customer-tab');
            const addTab = e.target.closest('.add-customer-tab');
            const removeBtn = e.target.closest('.remove-customer');

            if (removeBtn) {
                e.stopPropagation();
                const customerId = removeBtn.closest('.customer-tab').dataset.customer;
                this.removeCustomer(customerId);
            } else if (tab && !removeBtn) {
                const customerId = tab.dataset.customer;
                this.switchCustomer(customerId);
            } else if (addTab) {
                this.addNewCustomer();
            }
        });

        // تخصيص اسم العميل عبر نقر مزدوج مع SweetAlert
        container.addEventListener('dblclick', (e) => {
        const tab = e.target.closest('.customer-tab');
        if (tab) {
        const customerId = tab.dataset.customer;
        const customer = this.customers.get(customerId);
        if (customer) {
        Swal.fire({
        title: 'تغيير اسم العميل',
        input: 'text',
        inputValue: customer.name,
        inputPlaceholder: 'أدخل الاسم الجديد',
        showCancelButton: true,
        confirmButtonText: 'حفظ',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        showDenyButton: true,
        denyButtonText: 'إعادة إلى الافتراضي',
        denyButtonColor: '#ffc107',
        inputValidator: (value) => {
        if (!value || value.trim() === '') {
        return 'يرجى إدخال اسم صالح';
        }
        },
        customClass: {
        popup: 'swal2-arabic',
        title: 'swal2-arabic',
        input: 'swal2-arabic'
        },
        didOpen: () => {
        const input = Swal.getInput();
        if (input) {
        input.style.textAlign = 'right';
        input.style.fontFamily = 'Cairo, sans-serif';
        }
        }
        }).then((result) => {
        if (result.isConfirmed) {
        customer.name = result.value.trim();
        } else if (result.isDenied) {
        customer.name = `العميل ${customer.id}`;
        }
        this.updateTabsDisplay();
        });
        }
        }
        });

        // ربط أحداث السحب والإفلات على التبويبات
        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            const tab = e.target.closest('.customer-tab');
            if (tab) {
                tab.style.border = '2px dashed #007bff';
            }
        });

        container.addEventListener('dragleave', (e) => {
            const tab = e.target.closest('.customer-tab');
            if (tab) {
                tab.style.border = '';
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            const tab = e.target.closest('.customer-tab');
            if (tab) {
                tab.style.border = '';
                const data = e.dataTransfer.getData('text/plain');
                const targetCustomerId = tab.dataset.customer;
                try {
                    const parsed = JSON.parse(data);
                    if (parsed.type === 'move') {
                        // تم تغيير المنطق هنا إلى النسخ بدلاً من النقل
                        this.copyItemToCustomer(parsed.cartId, targetCustomerId);
                    } else if (parsed.type === 'add') {
                        this.addItemToCustomer(targetCustomerId, parsed.item);
                    }
                } catch (error) {
                    console.error('Error parsing drag data:', error);
                }
            }
        });

        // اختصارات الكيبورد للتبديل بين العملاء
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key >= '1' && e.key <= '8') {
                e.preventDefault();
                const customerId = e.key;
                if (this.customers.has(customerId)) {
                    this.switchCustomer(customerId);
                }
            }
            // اختصارات جديدة
            if (e.ctrlKey) {
                if (e.key === 'n' || e.key === 'N') { // Ctrl + N لإضافة عميل جديد
                    e.preventDefault();
                    this.addNewCustomer();
                } else if (e.key === 'd' || e.key === 'D') { // Ctrl + D لحذف العميل النشط
                    e.preventDefault();
                    this.removeCustomer(this.activeCustomer);
                } else if (e.key === 't' || e.key === 'T') { // Ctrl + T للتبديل بين التبويبات (يمكن تخصيصها لدورة)
                    e.preventDefault();
                    const customerIds = Array.from(this.customers.keys());
                    const currentIndex = customerIds.indexOf(this.activeCustomer);
                    const nextIndex = (currentIndex + 1) % customerIds.length;
                    this.switchCustomer(customerIds[nextIndex]);
                }
            }
        });
    }

    switchCustomer(customerId) {
        if (!this.customers.has(customerId) || customerId === this.activeCustomer) {
            return;
        }

        // حفظ العميل الحالي
        this.saveCurrentCustomer();

        // تحديث العميل النشط
        this.activeCustomer = customerId;

        // تحميل بيانات العميل الجديد
        this.loadCustomer(customerId);

        // تحديث واجهة المستخدم
        this.updateUI();

        // إعادة ربط أحداث السحب بعد التبديل
        this.rebindOriginalTableEvents();
        this.rebindResponsiveTableEvents();

        // تحديث آخر نشاط
        this.customers.get(customerId).lastActivity = Date.now();

        console.log(`Switched to customer ${customerId}`);
    }

    saveCurrentCustomer() {
        if (!this.activeCustomer || !window.addedItems) return;

        const customer = this.customers.get(this.activeCustomer);
        if (customer) {
            // حفظ الأصناف
            customer.items = [...window.addedItems];
            customer.hasItems = window.addedItems.length > 0;
            
            // حفظ نوع الفاتورة والبيانات الإضافية
            const invoiceTypeSelect = document.getElementById('invoice-type');
            const branchSelect = document.getElementById('branch-select');
            const accountSelect = document.getElementById('account-select');
            const headerCustomerSelect = document.getElementById('header-customer-select');

            if (invoiceTypeSelect) {
                customer.invoiceType = invoiceTypeSelect.value;
            }
            
            if (branchSelect) {
                customer.branchId = branchSelect.value;
            }
            
            // Reset buyer ID first
            customer.accountBuyerId = null;

            if (invoiceTypeSelect.value === 'sale') {
                if (accountSelect) {
                    customer.accountBuyerId = accountSelect.value;
                }
            } else if (invoiceTypeSelect.value === 'customer_sale' || invoiceTypeSelect.value === 'customer_return') {
                if (headerCustomerSelect) {
                    customer.accountBuyerId = headerCustomerSelect.value;
                }
            }

            console.log(`Saved customer ${this.activeCustomer} data:`, customer);
        }
    }

    loadCustomer(customerId) {
        const customer = this.customers.get(customerId);
        if (!customer) return;

        // تحميل الأصناف
        window.addedItems = customer.items ? [...customer.items] : [];

        // تحديث عداد الأصناف
        this.updateItemCount();

        // تحديث جدول الأصناف المختارة في الوضع المقسم
        this.updateSelectedItemsTable();

        // تحديث الجدول الأصلي
        this.updateOriginalTable();

        // تحميل نوع الفاتورة والبيانات الإضافية
        const invoiceTypeSelect = document.getElementById('invoice-type');
        const branchSelect = document.getElementById('branch-select');
        const accountSelect = document.getElementById('account-select');
        const headerCustomerSelect = document.getElementById('header-customer-select');

        if (invoiceTypeSelect && customer.invoiceType) {
            invoiceTypeSelect.value = customer.invoiceType;
            // تشغيل حدث التغيير لإظهار/إخفاء الخيارات المناسبة
            invoiceTypeSelect.dispatchEvent(new Event('change'));
        }

        // تحميل البيانات بعد تحديث نوع الفاتورة
        setTimeout(() => {
            if (branchSelect && customer.branchId) {
                branchSelect.value = customer.branchId;
            }
            
            if (accountSelect && customer.accountBuyerId) {
                accountSelect.value = customer.accountBuyerId;
            }
            
            if (headerCustomerSelect && customer.accountBuyerId) {
                headerCustomerSelect.value = customer.accountBuyerId;
            }
        }, 100);

        console.log(`Loaded customer ${customerId} data:`, customer);
    }

    updateUI() {
        // تحديث شريط العملاء
        this.updateTabsDisplay();
        
        // تحديث عداد الأصناف
        this.updateItemCount();
        
        // تحديث الجدول إذا كان في الوضع المتجاوب
        if (window.responsiveMode && typeof window.updateResponsiveTable === 'function') {
            window.updateResponsiveTable();
        }
    }

    updateTabsDisplay() {
        const container = document.querySelector('.customer-tabs-container');
        if (!container) return;

        // إعادة إنشاء التبويبات
        const addTab = container.querySelector('.add-customer-tab');
        container.innerHTML = this.generateTabsHTML();
        container.appendChild(addTab);
    }

    updateItemCount() {
        const itemCountElement = document.getElementById('item-count');
        if (itemCountElement) {
            itemCountElement.textContent = window.addedItems ? window.addedItems.length : 0;
        }

        // تحديث عداد الأصناف في الوضع المقسم عند التبديل بين العملاء
        const responsiveItemCountElement = document.getElementById('responsive_item_count');
        if (responsiveItemCountElement) {
            responsiveItemCountElement.textContent = window.addedItems ? window.addedItems.length : 0;
            console.log(`تم تحديث عداد الأصناف في الوضع المقسم عند التبديل للعميل ${this.activeCustomer}: ${window.addedItems ? window.addedItems.length : 0}`);
        }

        // تحديث مؤشر الأصناف في التبويب
        const activeTab = document.querySelector(`.customer-tab[data-customer="${this.activeCustomer}"]`);
        if (activeTab) {
            const indicator = activeTab.querySelector('.items-indicator');
            const hasItems = window.addedItems && window.addedItems.length > 0;
            
            if (indicator) {
                indicator.style.display = hasItems ? 'block' : 'none';
                indicator.textContent = hasItems ? window.addedItems.length : '';
                indicator.className = `items-indicator ${hasItems ? 'has-items' : ''}`;
            }
        }

        // تحديث عرض التبويبات لضمان عرض التغييرات فوراً
        this.updateTabsDisplay();
    }

    addNewCustomer() {
        if (this.customers.size >= this.maxCustomers) {
            if (typeof toastr !== 'undefined') {
                toastr.warning(`الحد الأقصى للعملاء هو ${this.maxCustomers}`);
            }
            return;
        }

        let newId = null;
        for (let i = 1; i <= this.maxCustomers; i++) {
            if (!this.customers.has(i.toString())) {
                newId = i.toString();
                break;
            }
        }

        if (newId === null) {
            toastr.warning(`الحد الأقصى للعملاء هو ${this.maxCustomers}`);
            return;
        }

        // إنشاء عميل جديد
        this.customers.set(newId, {
            id: newId,
            name: `العميل ${newId}`,
            items: [],
            hasItems: false,
            lastActivity: Date.now(),
            invoiceType: 'customer_sale',
            branchId: null,
            accountBuyerId: null
        });

        // تحديث الواجهة
        this.updateTabsDisplay();

        // التبديل للعميل الجديد
        this.switchCustomer(newId);

        if (typeof toastr !== 'undefined') {
            toastr.success(`تم إضافة العميل ${newId}`);
        }

        console.log(`Added new customer ${newId}`);
    }

    removeCustomer(customerId) {
        // لا يمكن حذف العملاء الأساسيين (1-4)
        if (parseInt(customerId) <= 4) {
            if (typeof toastr !== 'undefined') {
                toastr.warning('لا يمكن حذف العملاء الأساسيين');
            }
            return;
        }

        const customer = this.customers.get(customerId);
        if (!customer) return;

        // التحقق من وجود أصناف
        if (customer.items && customer.items.length > 0) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'تأكيد الحذف',
                    text: `العميل ${customerId} يحتوي على ${customer.items.length} صنف. هل تريد حذفه؟`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء',
                    confirmButtonColor: '#d33'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.performRemoveCustomer(customerId);
                    }
                });
            } else {
                if (confirm(`العميل ${customerId} يحتوي على ${customer.items.length} صنف. هل تريد حذفه؟`)) {
                    this.performRemoveCustomer(customerId);
                }
            }
        } else {
            this.performRemoveCustomer(customerId);
        }
    }

    performRemoveCustomer(customerId) {
        // حذف العميل
        this.customers.delete(customerId);

        // إذا كان العميل المحذوف هو النشط، التبديل لعميل آخر
        if (this.activeCustomer === customerId) {
            const firstCustomerId = Array.from(this.customers.keys())[0];
            this.switchCustomer(firstCustomerId);
        }

        // تحديث الواجهة
        this.updateTabsDisplay();

        if (typeof toastr !== 'undefined') {
            toastr.success(`تم حذف العميل ${customerId}`);
        }

        console.log(`Removed customer ${customerId}`);
    }

    // حفظ جلسة العميل النشط
    saveCurrentCustomerSession() {
        this.saveCurrentCustomer();
        
        const customer = this.customers.get(this.activeCustomer);
        if (!customer || !customer.items || customer.items.length === 0) {
            if (typeof toastr !== 'undefined') {
                toastr.warning('لا توجد أصناف للحفظ');
            }
            return false;
        }

        // حفظ العميل النشط فقط - لا نستخدم saveItemsToFile لأنها تحفظ جميع العملاء
        // بدلاً من ذلك، نحفظ العميل النشط مباشرة
        return this.saveActiveCustomerOnly();
    }

    // دالة جديدة لحفظ العميل النشط فقط
    async saveActiveCustomerOnly() {
        const customer = this.customers.get(this.activeCustomer);
        if (!customer || !customer.items || customer.items.length === 0) {
            if (typeof toastr !== 'undefined') {
                toastr.warning('لا توجد أصناف للحفظ للعميل النشط');
            }
            return false;
        }

        // التحقق من اختيار العميل قبل الحفظ
        if ((customer.invoiceType === 'customer_sale' || customer.invoiceType === 'customer_return') && !customer.accountBuyerId) {
            if (typeof toastr !== 'undefined') {
                toastr.error('يرجى اختيار العميل قبل حفظ الفاتورة.');
            }
            return false; // منع الحفظ
        }

        try {
            // إنشاء بيانات الفاتورة للعميل النشط فقط مع معرف فريد للوضع المقسم
            const invoiceData = {
                type: customer.invoiceType || 'purchase',
                invoice_type: customer.invoiceType || 'purchase',
                store_id: window.encryptedStoreId,
                account_id: window.encryptedAccountId,
                account_buyer_id: customer.accountBuyerId ? window.encryptedAccountsMap[customer.accountBuyerId] : null,
                branch_id: customer.branchId,
                items: customer.items,
                images: [],
                // إضافة معرف فريد للوضع المقسم
                multiCustomerMode: true,
                customerId: this.activeCustomer,
                timestamp: Date.now(),
                sessionId: `multi_customer_${this.activeCustomer}_${Date.now()}`
            };

            // استخدام دالة الحفظ مع دعم العمل دون اتصال
            if (typeof saveInvoiceWithOfflineSupport === 'function') {
                const result = await saveInvoiceWithOfflineSupport(invoiceData);
                
                if (result.success) {
                    // تشغيل الصوت المناسب
                    if (window.playSound) {
                        if (customer.invoiceType === 'customer_return') {
                            window.playSound('return');
                        } else {
                            window.playSound('success');
                        }
                    }

                    // مسح بيانات العميل النشط بعد الحفظ الناجح
                    this.clearActiveCustomer();

                    // عرض رسالة النجاح
                    if (typeof toastr !== 'undefined') {
                        toastr.success(`تم حفظ فاتورة العميل ${this.activeCustomer} بنجاح`);
                    }

                    return true;
                } else {
                    // تشغيل صوت الخطأ
                    if (window.playSound) {
                        window.playSound('error');
                    }
                    
                    if (typeof toastr !== 'undefined') {
                        toastr.error(result.message || 'حدث خطأ أثناء حفظ الفاتورة');
                    }
                    return false;
                }
            } else {
                // الطريقة التقليدية للحفظ مع FormData
                const formData = new FormData();
                formData.append('store_id', invoiceData.store_id);
                formData.append('account_id', invoiceData.account_id);
                formData.append('account_buyer_id', invoiceData.account_buyer_id || '');
                formData.append('items', JSON.stringify(invoiceData.items));
                formData.append('invoice_type', invoiceData.type);
                formData.append('images', JSON.stringify(invoiceData.images));
                
                // إضافة معلومات الوضع المقسم
                formData.append('multi_customer_mode', 'true');
                formData.append('customer_id', this.activeCustomer);
                
                if (invoiceData.branch_id) {
                    formData.append('branch_id', invoiceData.branch_id);
                }

                const response = await fetch('save_sale_invoice.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    // تشغيل الصوت المناسب
                    if (window.playSound) {
                        if (customer.invoiceType === 'customer_return') {
                            window.playSound('return');
                        } else {
                            window.playSound('success');
                        }
                    }

                    // مسح بيانات العميل النشط بعد الحفظ الناجح
                    this.clearActiveCustomer();

                    // عرض رسالة النجاح
                    if (typeof toastr !== 'undefined') {
                        toastr.success(`تم حفظ فاتورة العميل ${this.activeCustomer} بنجاح`);
                    }

                    return true;
                } else {
                    // تشغيل صوت الخطأ
                    if (window.playSound) {
                        window.playSound('error');
                    }
                    
                    if (typeof toastr !== 'undefined') {
                        toastr.error(result.message || 'حدث خطأ أثناء حفظ الفاتورة');
                    }
                    return false;
                }
            }
        } catch (error) {
            console.error('Error saving active customer:', error);
            
            // تشغيل صوت الخطأ
            if (window.playSound) {
                window.playSound('error');
            }
            
            if (typeof toastr !== 'undefined') {
                toastr.error('حدث خطأ أثناء حفظ الفاتورة');
            }
            return false;
        }
    }

    // دالة لمسح بيانات العميل النشط بعد الحفظ
    clearActiveCustomer() {
        const customer = this.customers.get(this.activeCustomer);
        if (customer) {
            // مسح الأصناف
            customer.items = [];
            customer.hasItems = false;
            
            // مسح window.addedItems أيضاً
            window.addedItems = [];
            
            // تحديث الواجهة
            this.updateSelectedItemsTable();
            this.updateOriginalTable();
            this.updateItemCount();
            
            // إعادة تعيين عداد الأصناف في الوضع المقسم إلى صفر بعد الحفظ الناجح
            const responsiveItemCountElement = document.getElementById('responsive_item_count');
            if (responsiveItemCountElement) {
                responsiveItemCountElement.textContent = '0';
            }
            
            console.log(`تم مسح بيانات العميل ${this.activeCustomer} بعد الحفظ`);
        }
    }

    // الحصول على بيانات جميع العملاء
    getAllCustomersData() {
        this.saveCurrentCustomer(); // حفظ العميل النشط أولاً
        
        const customersData = {};
        for (let [id, customer] of this.customers) {
            if (customer.items && customer.items.length > 0) {
                customersData[id] = {
                    name: customer.name,
                    items: customer.items,
                    invoiceType: customer.invoiceType,
                    branchId: customer.branchId,
                    accountBuyerId: customer.accountBuyerId,
                    lastActivity: customer.lastActivity
                };
            }
        }
        
        return customersData;
    }

    // تحميل بيانات جميع العملاء
    loadAllCustomersData(customersData) {
        if (!customersData || typeof customersData !== 'object') return;

        for (let [id, data] of Object.entries(customersData)) {
            if (this.customers.has(id)) {
                const customer = this.customers.get(id);
                customer.items = data.items || [];
                customer.hasItems = customer.items.length > 0;
                customer.invoiceType = data.invoiceType || 'purchase';
                customer.branchId = data.branchId || null;
                customer.accountBuyerId = data.accountBuyerId || null;
                customer.lastActivity = data.lastActivity || Date.now();
            }
        }

        // تحديث الواجهة
        this.updateUI();
        
        console.log('Loaded all customers data:', customersData);
    }

    // تنظيف جلسات العملاء الفارغة
    cleanupEmptyCustomers() {
        for (let [id, customer] of this.customers) {
            if (parseInt(id) > 4 && (!customer.items || customer.items.length === 0)) {
                // حذف العملاء الإضافيين الفارغين
                this.customers.delete(id);
            }
        }
        
        this.updateTabsDisplay();
    }

    // الحصول على العميل النشط
    getActiveCustomer() {
        return this.customers.get(this.activeCustomer);
    }

    // الحصول على معرف العميل النشط
    getActiveCustomerId() {
        return this.activeCustomer;
    }

    // تحديث جدول الأصناف المختارة في الوضع المقسم
    updateSelectedItemsTable() {
        const selectedItemsTable = document.getElementById('selectedItemsTable');
        if (!selectedItemsTable) return;

        // مسح الجدول الحالي
        selectedItemsTable.innerHTML = '';

        // إذا لم تكن هناك أصناف، اخرج
        if (!window.addedItems || window.addedItems.length === 0) {
            this.updateInvoiceTotal();
            return;
        }

        // تحقق من وضع الظلام
        const isDarkMode = document.body.classList.contains('dark-mode');
        const rowBgColor = isDarkMode ? '#242b3d' : '#ffffff';
        const textColor = isDarkMode ? '#e1e8f0' : '#333333';
        const borderColor = isDarkMode ? '#3a4553' : '#eee';
        const inputBgColor = isDarkMode ? '#2a3441' : '#ffffff';

        // إضافة كل صنف إلى الجدول
        window.addedItems.forEach(item => {
        const tr = document.createElement('tr');
        tr.dataset.itemId = item.id;
        tr.dataset.cartId = item.cartId; // Add cartId to dataset
        tr.dataset.itemName = item.name;
        tr.dataset.itemPrice = item.price || '0';
        tr.dataset.isCustomBarcode = item.isCustomBarcode || false;
        tr.draggable = true; // جعل الصف قابلاً للسحب
        // إضافة بيانات خدمة الكاش إلى dataset
        if (item.isCashService) {
            tr.dataset.isCashService = 'true';
            tr.dataset.commissionThreshold = item.commission_threshold || '0';
            tr.dataset.commissionFixed = item.commission_fixed || '0';
            tr.dataset.commissionPerThousand = item.commission_per_thousand || '0';
        }
        if (item.customBarcodeData) {
        tr.dataset.customBarcodeData = JSON.stringify(item.customBarcodeData);
        }
        tr.style.borderBottom = `1px solid ${borderColor}`;
        tr.style.backgroundColor = rowBgColor;
        tr.style.color = textColor;
        
        // خلية الاسم
        const nameCell = document.createElement('td');
        nameCell.style.textAlign = 'right';
        nameCell.style.paddingRight = '20px';
        nameCell.style.paddingTop = '8px';
        nameCell.style.paddingBottom = '8px';
        nameCell.style.color = textColor;
        nameCell.innerHTML = `<span style="display: inline-block; vertical-align: middle;">${item.name}</span>`;
        
        // خلية السعر
        const priceCell = document.createElement('td');
        priceCell.style.textAlign = 'center';
        priceCell.style.paddingTop = '8px';
        priceCell.style.paddingBottom = '8px';
        priceCell.style.color = textColor;
        priceCell.style.fontSize = '13px';
        priceCell.style.fontWeight = 'bold';
        
        // تحديد وحدة القياس
        let priceUnit = '';
        if (item.isCustomBarcode) {
        priceUnit = '/كيلو';
        }
        if (item.isCashService) {
            const threshold = parseFloat(item.commission_threshold) || 0;
            const fixed = parseFloat(item.commission_fixed) || 0;
            const perThousand = parseFloat(item.commission_per_thousand) || 0;
            const quantity = parseFloat(item.quantity) || 0;
            
            let commission = 0;
            if (quantity < threshold) {
                commission = fixed;
            } else {
                const thousands = Math.floor(quantity / 1000);
                commission = thousands * perThousand;
                const remainder = quantity % 1000;
                if (remainder > 0) {
                    commission += fixed;
                }
            }
            priceCell.textContent = `${commission.toFixed(2)} (عمولة)`;
        } else {
            priceCell.textContent = `${item.price || '0'}${priceUnit}`;
        }
        
        // خلية الكمية
        const quantityCell = document.createElement('td');
        quantityCell.style.width = '10%';
        quantityCell.style.maxWidth = '60px';
        quantityCell.style.minWidth = '60px';
        quantityCell.style.textAlign = 'center';
        quantityCell.style.paddingTop = '8px';
        quantityCell.style.paddingBottom = '8px';
        quantityCell.style.paddingRight = '5px';
        quantityCell.style.paddingLeft = '5px';
        quantityCell.style.color = textColor;
        
        const displayQuantity = item.quantity || 1;
        quantityCell.innerHTML = `
        <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="any" value="${displayQuantity}" 
        style="width: 50px !important; max-width: 50px !important; min-width: 50px !important; margin: auto; padding: 4px 2px !important; text-align: center; background-color: ${inputBgColor}; color: ${textColor}; border-color: ${borderColor}; font-size: 12px;">
        `;
        
        // خلية الإجمالي
        const totalCell = document.createElement('td');
        totalCell.className = 'item-total';
        totalCell.style.textAlign = 'center';
        totalCell.style.paddingTop = '8px';
        totalCell.style.paddingBottom = '8px';
        totalCell.style.paddingRight = '10px';
        totalCell.style.paddingLeft = '10px';
        totalCell.style.color = textColor;
        totalCell.style.fontWeight = 'bold';
        
        let itemTotal;
        if (item.isCashService) {
        const threshold = parseFloat(item.commission_threshold) || 0;
        const fixed = parseFloat(item.commission_fixed) || 0;
        const perThousand = parseFloat(item.commission_per_thousand) || 0;
        const quantity = parseFloat(item.quantity) || 0;
        
        let commission = 0;
        if (quantity < threshold) {
        commission = fixed;
        } else {
        const thousands = Math.floor(quantity / 1000);
        commission = thousands * perThousand;
        const remainder = quantity % 1000;
        if (remainder > 0) {
        commission += fixed;
        }
        }
        itemTotal = quantity + commission;
        } else {
        itemTotal = (parseFloat(item.quantity) || 1) * parseFloat(item.price || 0);
        }
        totalCell.textContent = itemTotal.toFixed(2);
        
        // خلية الإجراءات
        const actionsCell = document.createElement('td');
        actionsCell.style.textAlign = 'center';
        actionsCell.style.paddingTop = '8px';
        actionsCell.style.paddingBottom = '8px';
        actionsCell.style.color = textColor;
        actionsCell.innerHTML = `
        <button type="button" class="btn btn-danger btn-sm remove-btn" style="padding: 4px 8px;">
        <i class="fas fa-trash"></i>
        </button>
        `;
        
        // إضافة الخلايا إلى الصف
        tr.appendChild(nameCell);
        tr.appendChild(priceCell);
        tr.appendChild(quantityCell);
        tr.appendChild(totalCell);
        tr.appendChild(actionsCell);
        
        // ربط الأحداث
        const quantityInput = quantityCell.querySelector('.quantity-input');
        quantityInput.addEventListener('input', () => {
        const newQuantity = parseFloat(quantityInput.value) || 1;
        
        // تحديث الكمية في window.addedItems
        const itemIndex = window.addedItems.findIndex(addedItem => addedItem.cartId === item.cartId);
        if (itemIndex !== -1) {
        window.addedItems[itemIndex].quantity = newQuantity;
        }
        
        // تحديث الإجمالي
        let newTotal;
        if (tr.dataset.isCashService === 'true') {
        const threshold = parseFloat(tr.dataset.commissionThreshold) || 0;
        const fixed = parseFloat(tr.dataset.commissionFixed) || 0;
        const perThousand = parseFloat(tr.dataset.commissionPerThousand) || 0;
        
        let commission = 0;
        if (newQuantity < threshold) {
        commission = fixed;
        } else {
        const thousands = Math.floor(newQuantity / 1000);
        commission = thousands * perThousand;
        const remainder = newQuantity % 1000;
        if (remainder > 0) {
        commission += fixed;
        }
        }
        newTotal = newQuantity + commission;
        
        // تحديث خانة السعر لعرض العمولة الجديدة
        priceCell.textContent = `${commission.toFixed(2)} (عمولة)`;
        } else {
        newTotal = newQuantity * parseFloat(item.price || 0);
        }
        totalCell.textContent = newTotal.toFixed(2);
        
        // تحديث إجمالي الفاتورة
        this.updateInvoiceTotal();
        
        // حفظ التغييرات
        this.saveCurrentCustomer();
        });
        
        const removeBtn = actionsCell.querySelector('.remove-btn');
        removeBtn.addEventListener('click', () => {
        this.removeItemFromCustomer(item.cartId);
        });
        
        // ربط أحداث السحب والإفلات
        tr.addEventListener('dragstart', (e) => {
        e.dataTransfer.setData('text/plain', JSON.stringify({type: 'move', cartId: item.cartId}));
        e.dataTransfer.effectAllowed = 'copy'; // تغيير شكل المؤشر إلى نسخ
        });
        
        tr.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        });
        
        tr.addEventListener('drop', (e) => {
        e.preventDefault();
        const data = e.dataTransfer.getData('text/plain');
        // هنا يمكن إضافة منطق لإعادة ترتيب الأصنف داخل التبويب نفسه إذا لزم الأمر
        });
        
        selectedItemsTable.appendChild(tr);
        });
        
        // تحديث إجمالي الفاتورة
        this.updateInvoiceTotal();
    }

    // تحديث الجدول الأصلي
    updateOriginalTable() {
        const originalTable = document.getElementById('accountsTable');
        if (!originalTable) return;

        // إزالة التمييز من جميع الصفوف
        const allRows = originalTable.querySelectorAll('tr');
        allRows.forEach(row => {
            if (row.dataset.itemId) {
                row.classList.remove('highlight');
                
                // إعادة تعيين خلية الاسم
                const nameCell = row.querySelector('td:first-child');
                if (nameCell && row.dataset.itemName) {
                    nameCell.innerHTML = `<i class="fas fa-box-open text-blue-500 mr-2"></i> ${row.dataset.itemName}`;
                }

                // إعادة تعيين خلية الإضافة
                const addCell = row.querySelector('.add-cell, td:last-child');
                if (addCell) {
                    addCell.innerHTML = `
                        <button class="btn btn-primary add-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    `;
                    addCell.className = 'add-cell';
                }
            }
        });

        // تمييز الأصناف المضافة
        if (window.addedItems && window.addedItems.length > 0) {
            window.addedItems.forEach(item => {
                const row = originalTable.querySelector(`tr[data-item-id="${item.id}"]`);
                if (row) {
                    row.classList.add('highlight');
                    
                    // تحديث خلية الاسم
                    const nameCell = row.querySelector('td:first-child');
                    if (nameCell) {
                        nameCell.innerHTML = `
                            <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-right: 10px;"></i>
                            ${item.name}
                        `;
                    }

                    // تحديث خلية الكمية
                    const addCell = row.querySelector('.add-cell, td:last-child');
                    if (addCell) {
                        addCell.innerHTML = `
                            <input type="number" class="form-control quantity-input" placeholder="الكمية" min="0.1" step="any" value="${item.quantity || 1}" style="width: 80px; margin: auto;">
                        `;
                    }
                }
            });
        }

        // إعادة ربط الأحداث
        this.rebindOriginalTableEvents();
    }

    // دالة جديدة لإعادة ربط أحداث السحب في الجدول المتجاوب
    rebindResponsiveTableEvents() {
        const itemsList = document.getElementById('itemsList');
        if (!itemsList) return;

        const allRows = itemsList.querySelectorAll('tr');
        allRows.forEach(row => {
            if (row.dataset.itemId) {
                // التقق مما إذا كان الصف مفضل (يحتوي على أيقونة النجمة)
                const isFavorite = row.querySelector('.fas.fa-star') !== null;
                if (!isFavorite) {
                    row.draggable = true;
                    row.ondragstart = (e) => {
                        const item = {
                            id: row.dataset.itemId,
                            name: row.dataset.itemName,
                            price: row.dataset.itemPrice || '0'
                        };
                        e.dataTransfer.setData('text/plain', JSON.stringify({type: 'add', item: item}));
                        e.dataTransfer.effectAllowed = 'copy';
                    };
                }
            }
        });

        console.log('Rebound drag events for responsive table');
    }

    // إعادة ربط أحداث الجدول الأصلي
    rebindOriginalTableEvents() {
        const originalTable = document.getElementById('accountsTable');
        if (!originalTable) return;

        // ربط أحداث أزرار الإضافة
        const addButtons = originalTable.querySelectorAll('.add-btn');
        addButtons.forEach(btn => {
            btn.onclick = (e) => {
                const row = e.target.closest('tr');
                if (row && row.dataset.itemId) {
                    const item = {
                        id: row.dataset.itemId,
                        name: row.dataset.itemName,
                        price: row.dataset.itemPrice || '0'
                    };
                    
                    // إضافة الصنف للعميل النشط
                    this.addItemToCurrentCustomer(item);
                }
            };
        });

        // ربط أحداث أزرار الحذف
        const removeIcons = originalTable.querySelectorAll('.remove-item');
        removeIcons.forEach(icon => {
            icon.onclick = (e) => {
                const row = e.target.closest('tr');
                if (row && row.dataset.cartId) { // Use cartId instead of itemId
                    this.removeItemFromCustomer(row.dataset.cartId);
                }
            };
        });

        // ربط أحداث تغيير الكمية
        const quantityInputs = originalTable.querySelectorAll('.quantity-input');
        quantityInputs.forEach(input => {
            input.oninput = (e) => {
                const row = e.target.closest('tr');
                if (row && row.dataset.cartId) { // Use cartId instead of itemId
                    const newQuantity = parseFloat(e.target.value) || 1;
                    this.updateItemQuantity(row.dataset.cartId, newQuantity);
                }
            };
        });

        // إضافة أحداث السحب لصفوف الجدول الرئيسي
        const allRows = originalTable.querySelectorAll('tr[data-item-id]');
        allRows.forEach(row => {
            row.draggable = true;
            row.ondragstart = (e) => {
                const item = {
                    id: row.dataset.itemId,
                    name: row.dataset.itemName,
                    price: row.dataset.itemPrice || '0'
                };
                e.dataTransfer.setData('text/plain', JSON.stringify({type: 'add', item: item}));
                e.dataTransfer.effectAllowed = 'copy';
            };
        });
    }

    // إضافة صنف للعميل النشط
    addItemToCurrentCustomer(item) {
        if (!window.addedItems) {
            window.addedItems = [];
        }

        const existingIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);

        if (existingIndex !== -1 && !item.isCustomBarcode) {
            // Item exists and is not a custom barcode, update quantity
            const currentQuantity = parseFloat(window.addedItems[existingIndex].quantity) || 1;
            window.addedItems[existingIndex].quantity = currentQuantity + 1;
        } else {
            // Add new item for custom barcodes or if item doesn't exist
            const newItem = { 
                ...item,
                cartId: `cart-item-${Date.now()}-${Math.random()}` // Add unique cartId
            };
            // Set default quantity if not present
            if (typeof newItem.quantity === 'undefined') {
                newItem.quantity = 1;
            }
            window.addedItems.push(newItem);
        }

        // Save changes first to update customer.items
        this.saveCurrentCustomer();

        // Update UI
        this.updateSelectedItemsTable();
        this.updateOriginalTable();
        this.updateItemCount(); // This also calls updateTabsDisplay
    }

    // حذف صنف من العميل النشط
    removeItemFromCustomer(cartId) {
        if (!window.addedItems) return;

        // حذف الصنف من المصفوفة
        const itemIndex = window.addedItems.findIndex(item => item.cartId === cartId);
        if (itemIndex !== -1) {
            window.addedItems.splice(itemIndex, 1);
        }

        // تحديث الواجهة
        this.updateSelectedItemsTable();
        this.updateOriginalTable();
        this.updateItemCount();

        // حفظ التغييرات
        this.saveCurrentCustomer();
    }

    // إضافة صنف إلى عميل محدد
    addItemToCustomer(customerId, item) {
        const customer = this.customers.get(customerId);
        if (!customer) return;

        if (!customer.items) customer.items = [];

        const existingIndex = customer.items.findIndex(i => i.id === item.id);
        if (existingIndex !== -1 && !item.isCustomBarcode) {
            customer.items[existingIndex].quantity = (customer.items[existingIndex].quantity || 1) + 1;
        } else {
            customer.items.push({ ...item, quantity: item.quantity || 1, cartId: `cart-item-${Date.now()}-${Math.random()}` });
        }

        customer.hasItems = customer.items.length > 0;
        customer.lastActivity = Date.now();

        this.updateTabsDisplay();

        if (customerId === this.activeCustomer) {
            window.addedItems = [...customer.items];
            this.updateSelectedItemsTable();
            this.updateOriginalTable();
            this.updateItemCount();
        }

        this.saveCustomer(customerId);

        if (typeof toastr !== 'undefined') {
            toastr.success(`تم إضافة الصنف إلى العميل ${customerId}`);
        }
    }

    // نسخ صنف إلى عميل آخر
    copyItemToCustomer(cartId, targetCustomerId) {
        if (targetCustomerId === this.activeCustomer) return; // لا يمكن النسخ إلى نفس العميل

        const sourceCustomer = this.customers.get(this.activeCustomer);
        const targetCustomer = this.customers.get(targetCustomerId);

        if (!sourceCustomer || !targetCustomer) return;

        // البحث عن الصنف في العميل المصدر
        const itemToCopy = sourceCustomer.items.find(item => item.cartId === cartId);
        if (!itemToCopy) return;

        // إنشاء نسخة جديدة من الصنف مع cartId جديد
        const newItem = { ...itemToCopy, cartId: `cart-item-${Date.now()}-${Math.random()}` };

        // التحقق مما إذا كان صنف مشابه (نفس الـ id وليس باركود مخصص) موجودًا بالفعل
        const existingItemIndex = targetCustomer.items.findIndex(item => item.id === itemToCopy.id && !item.isCustomBarcode);

        if (existingItemIndex !== -1 && !itemToCopy.isCustomBarcode) {
            // إذا كان الصنف موجودًا وليس باركود مخصص، قم بزيادة الكمية
            targetCustomer.items[existingItemIndex].quantity = (targetCustomer.items[existingItemIndex].quantity || 1) + (itemToCopy.quantity || 1);
        } else {
            // إذا لم يكن موجودًا أو كان باركود مخصص، أضف الصنف الجديد
            targetCustomer.items.push(newItem);
        }

        // تحديث حالة العميل الهدف
        targetCustomer.hasItems = true;
        targetCustomer.lastActivity = Date.now();

        // تحديث عرض التبويبات لإظهار التغييرات فوراً
        this.updateTabsDisplay();

        // حفظ بيانات العميل الهدف
        this.saveCustomer(targetCustomerId);

        if (typeof toastr !== 'undefined') {
            toastr.success(`تم نسخ الصنف إلى العميل ${targetCustomerId}`);
        }
    }

    // تحديث كمية صنف
    updateItemQuantity(cartId, newQuantity) {
        if (!window.addedItems) return;

        const itemIndex = window.addedItems.findIndex(item => item.cartId === cartId);
        if (itemIndex !== -1) {
            window.addedItems[itemIndex].quantity = newQuantity;

            // تحديث الواجهة
            this.updateSelectedItemsTable();
            this.updateItemCount();

            // حفظ ��لتغييرات
            this.saveCurrentCustomer();
        }
    }

    // تحديث إجمالي الفاتورة
    updateInvoiceTotal() {
        const totalElement = document.getElementById('total_amount_value');
        if (!totalElement) return;

        let total = 0;
        if (window.addedItems && window.addedItems.length > 0) {
            total = window.addedItems.reduce((sum, item) => {
                if (item.isCashService) {
                    return sum + (parseFloat(item.price) || 0);
                } else {
                    const price = parseFloat(item.price) || 0;
                    const quantity = parseFloat(item.quantity) || 1;
                    return sum + (price * quantity);
                }
            }, 0);
        }

        totalElement.textContent = total.toFixed(2);

        // Update active customer tab total immediately
        const activeTab = document.querySelector(`.customer-tab[data-customer="${this.activeCustomer}"]`);
        if (activeTab) {
            const totalSpan = activeTab.querySelector('.customer-total');
            if (totalSpan) {
                totalSpan.textContent = total.toFixed(2);
            }
        }
    }

    // دالة لحفظ بيانات عميل محدد
    saveCustomer(customerId) {
        const customer = this.customers.get(customerId);
        if (customer) {
            // هنا يمكن إضافة منطق الحفظ إذا لزم الأمر
            console.log(`Saved customer ${customerId} data`);
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.CustomerTabsManager = CustomerTabsManager;
