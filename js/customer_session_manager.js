/**
 * Customer Session Manager
 * إدارة جلسات العملاء المتعددين
 */

class CustomerSessionManager {
    constructor(accountId) {
        this.accountId = accountId;
        this.storageKey = `customer_sessions_${accountId}`;
        this.autoSaveInterval = null;
        this.autoSaveDelay = 2000; // 2 seconds
        this.init();
    }

    init() {
        // تفعيل الحفظ التلقائي
        this.startAutoSave();
        
        // حفظ البيانات عند إغلاق النافذة
        window.addEventListener('beforeunload', () => {
            this.saveAllSessions();
        });

        // حفظ البيانات عند إخفاء الصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveAllSessions();
            }
        });

        console.log('Customer Session Manager initialized');
    }

    /**
     * حفظ جلسة عميل واحد
     */
    saveCustomerSession(customerId, sessionData) {
        try {
            const allSessions = this.loadAllSessions();
            
            // تحديث بيانات العميل
            allSessions[customerId] = {
                ...sessionData,
                lastUpdated: Date.now(),
                version: this.getSessionVersion()
            };

            // حفظ في localStorage
            localStorage.setItem(this.storageKey, JSON.stringify(allSessions));
            
            console.log(`Customer ${customerId} session saved:`, sessionData);
            return true;

        } catch (error) {
            console.error('Error saving customer session:', error);
            return false;
        }
    }

    /**
     * تحميل جلسة عميل واحد
     */
    loadCustomerSession(customerId) {
        try {
            const allSessions = this.loadAllSessions();
            const session = allSessions[customerId];

            if (session) {
                // التحقق من صحة البيانات
                if (this.validateSessionData(session)) {
                    console.log(`Customer ${customerId} session loaded:`, session);
                    return session;
                } else {
                    console.warn(`Invalid session data for customer ${customerId}`);
                    return null;
                }
            }

            return null;

        } catch (error) {
            console.error('Error loading customer session:', error);
            return null;
        }
    }

    /**
     * تحميل جميع الجلسات
     */
    loadAllSessions() {
        try {
            const sessionsData = localStorage.getItem(this.storageKey);
            
            if (sessionsData) {
                const sessions = JSON.parse(sessionsData);
                
                // التحقق من صحة البيانات
                if (typeof sessions === 'object' && sessions !== null) {
                    return sessions;
                }
            }

            return {};

        } catch (error) {
            console.error('Error loading all sessions:', error);
            return {};
        }
    }

    /**
     * حفظ جميع الجلسات
     */
    saveAllSessions() {
        if (window.customerTabsManager) {
            try {
                // حفظ العميل النشط أولاً
                window.customerTabsManager.saveCurrentCustomer();
                
                // الحصول على بيانات جميع العملاء
                const customersData = window.customerTabsManager.getAllCustomersData();
                
                // حف�� كل عميل
                for (const [customerId, customerData] of Object.entries(customersData)) {
                    this.saveCustomerSession(customerId, customerData);
                }

                console.log('All customer sessions saved');
                return true;

            } catch (error) {
                console.error('Error saving all sessions:', error);
                return false;
            }
        }

        return false;
    }

    /**
     * حذف جلسة عميل
     */
    deleteCustomerSession(customerId) {
        try {
            const allSessions = this.loadAllSessions();
            
            if (allSessions[customerId]) {
                delete allSessions[customerId];
                localStorage.setItem(this.storageKey, JSON.stringify(allSessions));
                
                console.log(`Customer ${customerId} session deleted`);
                return true;
            }

            return false;

        } catch (error) {
            console.error('Error deleting customer session:', error);
            return false;
        }
    }

    /**
     * مسح جميع الجلسات
     */
    clearAllSessions() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('All customer sessions cleared');
            return true;

        } catch (error) {
            console.error('Error clearing all sessions:', error);
            return false;
        }
    }

    /**
     * استيراد جلسات من ملف
     */
    importSessions(sessionsData) {
        try {
            if (typeof sessionsData === 'object' && sessionsData !== null) {
                // التحقق من صحة البيانات
                const validSessions = {};
                
                for (const [customerId, sessionData] of Object.entries(sessionsData)) {
                    if (this.validateSessionData(sessionData)) {
                        validSessions[customerId] = {
                            ...sessionData,
                            lastUpdated: Date.now(),
                            version: this.getSessionVersion()
                        };
                    }
                }

                // حفظ الجلسات الصحيحة
                localStorage.setItem(this.storageKey, JSON.stringify(validSessions));
                
                console.log('Sessions imported successfully:', validSessions);
                return true;
            }

            return false;

        } catch (error) {
            console.error('Error importing sessions:', error);
            return false;
        }
    }

    /**
     * تصدير جميع الجلسات
     */
    exportSessions() {
        try {
            const allSessions = this.loadAllSessions();
            
            // إنشاء ملف للتصدير
            const exportData = {
                accountId: this.accountId,
                exportDate: new Date().toISOString(),
                version: this.getSessionVersion(),
                sessions: allSessions
            };

            return exportData;

        } catch (error) {
            console.error('Error exporting sessions:', error);
            return null;
        }
    }

    /**
     * التحقق من صحة بيانات الجلسة
     */
    validateSessionData(sessionData) {
        if (!sessionData || typeof sessionData !== 'object') {
            return false;
        }

        // التحقق من الحقول المطلوبة
        const requiredFields = ['name', 'items', 'lastActivity'];
        
        for (const field of requiredFields) {
            if (!(field in sessionData)) {
                return false;
            }
        }

        // التحقق من صحة الأصناف
        if (!Array.isArray(sessionData.items)) {
            return false;
        }

        // التحقق من صحة كل صنف
        for (const item of sessionData.items) {
            if (!this.validateItemData(item)) {
                return false;
            }
        }

        return true;
    }

    /**
     * التحقق من صحة بيانات الصنف
     */
    validateItemData(itemData) {
        if (!itemData || typeof itemData !== 'object') {
            return false;
        }

        const requiredFields = ['id', 'name', 'price', 'quantity'];
        
        for (const field of requiredFields) {
            if (!(field in itemData)) {
                return false;
            }
        }

        // التحقق من أن الكمية والسعر أرقام صحيحة
        if (isNaN(parseFloat(itemData.quantity)) || isNaN(parseFloat(itemData.price))) {
            return false;
        }

        return true;
    }

    /**
     * الحصول على إصدار الجلسة
     */
    getSessionVersion() {
        return '1.0.0';
    }

    /**
     * تفعيل الحفظ التلقائي
     */
    startAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }

        this.autoSaveInterval = setInterval(() => {
            this.saveAllSessions();
        }, this.autoSaveDelay);

        console.log('Auto-save started');
    }

    /**
     * إيقاف الحفظ التلقائي
     */
    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
            console.log('Auto-save stopped');
        }
    }

    /**
     * الحصول على إحصائيات الجلسات
     */
    getSessionsStats() {
        try {
            const allSessions = this.loadAllSessions();
            const stats = {
                totalSessions: Object.keys(allSessions).length,
                sessionsWithItems: 0,
                totalItems: 0,
                lastActivity: null
            };

            for (const [customerId, sessionData] of Object.entries(allSessions)) {
                if (sessionData.items && sessionData.items.length > 0) {
                    stats.sessionsWithItems++;
                    stats.totalItems += sessionData.items.length;
                }

                if (sessionData.lastActivity) {
                    if (!stats.lastActivity || sessionData.lastActivity > stats.lastActivity) {
                        stats.lastActivity = sessionData.lastActivity;
                    }
                }
            }

            return stats;

        } catch (error) {
            console.error('Error getting sessions stats:', error);
            return null;
        }
    }

    /**
     * تنظيف الجلسات القديمة
     */
    cleanupOldSessions(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days default
        try {
            const allSessions = this.loadAllSessions();
            const currentTime = Date.now();
            let cleanedCount = 0;

            for (const [customerId, sessionData] of Object.entries(allSessions)) {
                const sessionAge = currentTime - (sessionData.lastActivity || 0);
                
                if (sessionAge > maxAge && (!sessionData.items || sessionData.items.length === 0)) {
                    delete allSessions[customerId];
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                localStorage.setItem(this.storageKey, JSON.stringify(allSessions));
                console.log(`Cleaned up ${cleanedCount} old sessions`);
            }

            return cleanedCount;

        } catch (error) {
            console.error('Error cleaning up old sessions:', error);
            return 0;
        }
    }

    /**
     * استعادة جلسة من نسخة احتياطية
     */
    restoreFromBackup(backupData) {
        try {
            if (backupData && backupData.sessions) {
                return this.importSessions(backupData.sessions);
            }

            return false;

        } catch (error) {
            console.error('Error restoring from backup:', error);
            return false;
        }
    }

    /**
     * إنشاء نسخة احتياطية
     */
    createBackup() {
        try {
            const exportData = this.exportSessions();
            
            if (exportData) {
                // حفظ النسخة الاحتياطية في localStorage منفصل
                const backupKey = `${this.storageKey}_backup_${Date.now()}`;
                localStorage.setItem(backupKey, JSON.stringify(exportData));
                
                console.log('Backup created:', backupKey);
                return backupKey;
            }

            return null;

        } catch (error) {
            console.error('Error creating backup:', error);
            return null;
        }
    }

    /**
     * تدمير المدير وتنظيف الموارد
     */
    destroy() {
        this.stopAutoSave();
        
        // حفظ أخير قبل التدمير
        this.saveAllSessions();
        
        console.log('Customer Session Manager destroyed');
    }
}

// تصدير الكلاس للاستخدام العام
window.CustomerSessionManager = CustomerSessionManager;