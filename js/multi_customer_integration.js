/**
 * Multi Customer Integration
 * ملف التكامل الرئيسي للوضع المقسم متعدد العملاء
 */

(function() {
    'use strict';

    // متغيرات عامة
    let customerTabsManager = null;
    let isInitialized = false;

    // تهيئة النظام عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // التحقق من تفعيل الوضع المقسم
        if (window.multiCustomerMode) {
            initializeMultiCustomerSystem();
        }
    });

    /**
     * تهيئة نظام العملاء المتعددين
     */
    function initializeMultiCustomerSystem() {
        if (isInitialized) return;

        console.log('Initializing Multi Customer System...');

        try {
            // إنشاء مدير شريط العملاء
            customerTabsManager = new CustomerTabsManager();
            
            // ربط المدير بالنافذة العامة
            window.customerTabsManager = customerTabsManager;

            // تحسين دالة الحفظ الموجودة
            enhanceSaveFunction();

            // تحسين دالة إضافة الأصناف
            enhanceAddItemFunction();

            // إضافة مستمعات الأحداث المخصصة
            addCustomEventListeners();

            // تحديث واجهة المستخدم
            updateUIForMultiCustomer();

            isInitialized = true;
            console.log('Multi Customer System initialized successfully');

            // إظهار إشعار النجاح
            if (typeof toastr !== 'undefined') {
                toastr.success('تم تفعيل الوضع المقسم متعدد العملاء', 'نظام العملاء المتعددين');
            }

        } catch (error) {
            console.error('Error initializing Multi Customer System:', error);
            
            if (typeof toastr !== 'undefined') {
                toastr.error('حدث خطأ في تهيئة نظام العملاء المتعددين', 'خطأ');
            }
        }
    }

    /**
     * تحسين دالة الحفظ الموجودة
     */
    function enhanceSaveFunction() {
        // ربط زر الحفظ الرئيسي مع النظام متعدد العملاء
        const viewItemsButton = document.getElementById('view-items');
        if (viewItemsButton) {
            // إزالة المستمعات القديمة
            const newButton = viewItemsButton.cloneNode(true);
            viewItemsButton.parentNode.replaceChild(newButton, viewItemsButton);

            // إضافة مستمع جديد للوضع المقسم
            newButton.addEventListener('click', async function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (customerTabsManager) {
                    // في الوضع المقسم، نحفظ العميل النشط فقط
                    const success = await customerTabsManager.saveCurrentCustomerSession();
                    
                    if (success) {
                        console.log('تم حفظ العميل النشط بنجاح في الوضع المقسم');
                    }
                } else {
                    // في الوضع العادي، استخدم الطريقة التقليدية
                    console.log('الوضع العادي - استخدام الطريقة التقليدية');
                }
            });

            console.log('زر الحفظ الرئيسي تم ربطه مع النظام متعدد العملاء');
        }

        // حفظ الدالة الأصلية وتحسينها
        const originalSaveFunction = window.saveItemsToFile;

        // إنشاء دالة محسنة
        window.saveItemsToFile = function() {
            if (customerTabsManager) {
                // حفظ العميل النشط أولاً
                customerTabsManager.saveCurrentCustomer();
                
                // استخدام الدالة الأصلية للحفظ
                if (originalSaveFunction && typeof originalSaveFunction === 'function') {
                    return originalSaveFunction.apply(this, arguments);
                }
            }

            // إذا لم يكن هناك مدير عملاء، استخدم الدالة الأصلية
            if (originalSaveFunction && typeof originalSaveFunction === 'function') {
                return originalSaveFunction.apply(this, arguments);
            }
        };

        console.log('Save function enhanced for multi-customer support');
    }

    /**
     * تحسين دالة إضافة الأصناف
     */
    function enhanceAddItemFunction() {
        // البحث عن أزرار الإضافة الموجودة وإعادة ربطها
        const addButtons = document.querySelectorAll('.add-btn');
        addButtons.forEach(button => {
            // إزالة المستمعات القديمة وإضافة جديدة
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const row = this.closest('tr');
                if (row && row.dataset.itemId) {
                    const item = {
                        id: row.dataset.itemId,
                        name: row.dataset.itemName,
                        price: row.dataset.itemPrice || '0',
                        type: row.dataset.itemType || 'piece'
                    };

                    if (customerTabsManager) {
                        customerTabsManager.addItemToCurrentCustomer(item);
                    }
                }
            });
        });

        console.log('Add item function enhanced for multi-customer support');
    }

    /**
     * إضافة مستمعات الأحداث المخصصة
     */
    function addCustomEventListeners() {
        // مستمع لتحديث الجدول عند تغيير الوضع المتجاوب
        document.addEventListener('responsiveModeChanged', function(e) {
            if (customerTabsManager && e.detail.isResponsive) {
                // تحديث جدول الأصناف المختارة في الوضع المتجاوب
                setTimeout(() => {
                    customerTabsManager.updateSelectedItemsTable();
                }, 100);
            }
        });

        // مستمع لحفظ البيانات عند إغلاق النافذة
        window.addEventListener('beforeunload', function() {
            if (customerTabsManager) {
                customerTabsManager.saveCurrentCustomer();
            }
        });

        // مستمع لاختصارات الكيبورد المحسنة
        document.addEventListener('keydown', function(e) {
            if (!customerTabsManager) return;

            // Ctrl + Shift + S: حفظ جميع العملاء
            if (e.ctrlKey && e.shiftKey && (e.key === 'S' || e.key === 's')) {
                e.preventDefault();
                saveAllCustomers();
            }

            // Ctrl + Shift + C: تنظيف العملاء الفارغين
            if (e.ctrlKey && e.shiftKey && (e.key === 'C' || e.key === 'c')) {
                e.preventDefault();
                customerTabsManager.cleanupEmptyCustomers();
                
                if (typeof toastr !== 'undefined') {
                    toastr.info('تم تنظيف العملاء الفارغين', 'تنظيف');
                }
            }
        });

        console.log('Custom event listeners added');
    }

    /**
     * تحديث واجهة المستخدم للوضع المقسم
     */
    function updateUIForMultiCustomer() {
        // إضافة ��ئة CSS للجسم
        document.body.classList.add('multi-customer-mode');

        // تحديث عنوان الصفحة
        const titleElement = document.querySelector('title');
        if (titleElement && !titleElement.textContent.includes('متعدد العملاء')) {
            titleElement.textContent += ' - متعدد العملاء';
        }

        // إضافة مؤشر بصري للوضع النشط
        

        console.log('UI updated for multi-customer mode');
    }

    /**
     * حفظ جميع العملاء
     */
    async function saveAllCustomers() {
        if (!customerTabsManager) return;

        try {
            const customersData = customerTabsManager.getAllCustomersData();
            const customerIds = Object.keys(customersData);

            if (customerIds.length === 0) {
                if (typeof toastr !== 'undefined') {
                    toastr.warning('لا توجد عملاء بأصناف للحفظ', 'حفظ جميع العملاء');
                }
                return;
            }

            let savedCount = 0;

            for (const id of customerIds) {
                const customer = customersData[id];

                // التحقق من اختيار العميل إذا لزم الأمر
                if ((customer.invoiceType === 'customer_sale' || customer.invoiceType === 'customer_return') && !customer.accountBuyerId) {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(`يرجى اختيار العميل للعميل ${id} قبل الحفظ.`, 'خطأ في الحفظ');
                    }
                    continue; // تخطي هذا العميل
                }

                // إنشاء بيانات الفاتورة
                const invoiceData = {
                    type: customer.invoiceType || 'purchase',
                    invoice_type: customer.invoiceType || 'purchase',
                    store_id: window.encryptedStoreId,
                    account_id: window.encryptedAccountId,
                    account_buyer_id: customer.accountBuyerId ? window.encryptedAccountsMap[customer.accountBuyerId] : null,
                    branch_id: customer.branchId,
                    items: customer.items,
                    images: [],
                    multiCustomerMode: true,
                    customerId: id,
                    timestamp: Date.now(),
                    sessionId: `multi_customer_${id}_${Date.now()}`
                };

                let result;
                if (typeof saveInvoiceWithOfflineSupport === 'function') {
                    result = await saveInvoiceWithOfflineSupport(invoiceData);
                } else {
                    const formData = new FormData();
                    formData.append('store_id', invoiceData.store_id);
                    formData.append('account_id', invoiceData.account_id);
                    formData.append('account_buyer_id', invoiceData.account_buyer_id || '');
                    formData.append('items', JSON.stringify(invoiceData.items));
                    formData.append('invoice_type', invoiceData.type);
                    formData.append('images', JSON.stringify(invoiceData.images));
                    formData.append('multi_customer_mode', 'true');
                    formData.append('customer_id', id);
                    if (invoiceData.branch_id) {
                        formData.append('branch_id', invoiceData.branch_id);
                    }

                    const response = await fetch('save_sale_invoice.php', {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (result.success) {
                    // تشغيل الصوت المناسب
                    if (window.playSound) {
                        if (customer.invoiceType === 'customer_return') {
                            window.playSound('return');
                        } else {
                            window.playSound('success');
                        }
                    }

                    // مسح بيانات العميل
                    const managerCustomer = customerTabsManager.customers.get(id);
                    if (managerCustomer) {
                        managerCustomer.items = [];
                        managerCustomer.hasItems = false;
                    }

                    savedCount++;

                    // إذا كان العميل النشط، تحديث الواجهة
                    if (id === customerTabsManager.activeCustomer) {
                        window.addedItems = [];
                        customerTabsManager.updateSelectedItemsTable();
                        customerTabsManager.updateOriginalTable();
                        customerTabsManager.updateItemCount();
                    }
                } else {
                    // تشغيل صوت الخطأ
                    if (window.playSound) {
                        window.playSound('error');
                    }
                    if (typeof toastr !== 'undefined') {
                        toastr.error(`حدث خطأ في حفظ العميل ${id}: ${result.message || 'خطأ غير معروف'}`, 'خطأ');
                    }
                }
            }

            // تحديث عرض التبويبات بعد الحفظ
            customerTabsManager.updateTabsDisplay();

            if (typeof toastr !== 'undefined') {
                toastr.success(`تم حفظ ${savedCount} عميل بنجاح`, 'حفظ جميع العملاء');
            }

        } catch (error) {
            console.error('Error saving all customers:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('حدث خطأ في حفظ العملاء', 'خطأ');
            }
        }
    }

    /**
     * تصدير الدوال للاستخدام العام
     */
    window.MultiCustomerIntegration = {
        initialize: initializeMultiCustomerSystem,
        saveAllCustomers: saveAllCustomers,
        getCustomerTabsManager: () => customerTabsManager,
        isInitialized: () => isInitialized
    };

    // تصدير دالة التهيئة للاستخدام المباشر
    window.initializeMultiCustomerSystem = initializeMultiCustomerSystem;

})();