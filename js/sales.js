let initialLoad = true;
let previousOrderCounts = {};
let isFiltering = false;
let newOrderPlayed = false;
let suppressSound = true; // Changed to true by default to suppress sound initially
let firstSSE = true;
let audioEnabled = false;
let modalOpen = false;
let lastUpdate = 0;
let sseReconnectTimer;
let currentTab = 'sales'; // Default tab is sales
let sortDirection = 'asc';
let currentSortColumn = '';
let searchFormCollapsed = false;
let profitColumnsVisible = true; // متغير لتتبع حالة إظهار أعمدة المكسب والتكلفة

// دالة لتنسيق الأرقام بشكل ذكي - إزالة الأصفار الزائدة
function formatNumber(number) {
    const num = parseFloat(number);
    if (isNaN(num)) return '0';

    // إذا كان الرقم صحيح، اعرضه بدون خانات عشرية
    if (num === Math.floor(num)) {
        return Math.floor(num).toString();
    }

    // إذا كان عشري، اعرضه مع إزالة الأصفار الزائدة
    const formatted = num.toFixed(4); // استخدام 4 خانات عشرية للدقة
    return parseFloat(formatted).toString(); // إزالة الأصفار الزائدة تلقائياً
}

// دالة لتنسيق الوقت مع التاريخ بشكل جميل
function formatDateTime(dateTimeString, timeString = null) {
    if (!dateTimeString && !timeString) return '-';
    
    try {
        let dateStr = '';
        let timeStr = '';
        
        // إذا كان لدينا تاريخ ووقت منفصلين
        if (dateTimeString && timeString) {
            dateStr = dateTimeString;
            timeStr = timeString;
        } else if (dateTimeString) {
            // إذا كان التاريخ والوقت في نص واحد
            if (dateTimeString.includes(' ')) {
                [dateStr, timeStr] = dateTimeString.split(' ');
            } else {
                // إذا كان فقط تاريخ أو وقت
                if (dateTimeString.includes(':')) {
                    timeStr = dateTimeString;
                } else {
                    dateStr = dateTimeString;
                }
            }
        }
        
        let formattedResult = '';
        
        // تنسيق التاريخ
        if (dateStr) {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                const today = new Date();
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                
                if (date.toDateString() === today.toDateString()) {
                    formattedResult += 'اليوم';
                } else if (date.toDateString() === yesterday.toDateString()) {
                    formattedResult += 'أمس';
                } else {
                    formattedResult += date.toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
                }
            }
        }
        
        // تنسيق الوقت
        if (timeStr) {
            const [hours, minutes] = timeStr.split(':');
            const hour24 = parseInt(hours);
            const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
            const ampm = hour24 >= 12 ? 'م' : 'ص';
            
            const formattedTime = `${hour12}:${minutes} ${ampm}`;
            
            if (formattedResult) {
                formattedResult += ` - ${formattedTime}`;
            } else {
                formattedResult = formattedTime;
            }
        }
        
        return formattedResult || '-';
    } catch (error) {
        return dateTimeString || timeString || '-';
    }
}

// دالة لتنسيق اوقت فقط (للتوافق مع الكود القديم)
function formatTime(timeString) {
    return formatDateTime(null, timeString);
}

// دالة لتبديل عرض نموذج البحث
function toggleSearchForm() {
    const searchForm = document.querySelector('.search-form');
    const toggleBtn = document.querySelector('.toggle-search-btn i');
    
    searchFormCollapsed = !searchFormCollapsed;
    
    if (searchFormCollapsed) {
        searchForm.style.display = 'none';
        toggleBtn.className = 'fas fa-chevron-up';
    } else {
        searchForm.style.display = 'block';
        toggleBtn.className = 'fas fa-chevron-down';
    }
}

// دالة لتحديث عداد العناصر المحددة
function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
    const count = selectedCheckboxes.length;
    const selectedCountElement = document.getElementById('selected-count');
    const actionButtons = document.querySelectorAll('.fixed-navbar .action-btn');
    
    selectedCountElement.textContent = count;
    
    // تفعيل/إلغاء تفعيل الأزرار بناءً على عدد العناصر المحددة
    actionButtons.forEach(btn => {
        btn.disabled = count === 0;
    });
}

// دالة لتحديد/إلغاء تحديد جميع العناصر
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');
    
    orderCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelectedCount();
}

// دالة لتحديد جميع الطلبات
function selectAllOrders() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    selectAllCheckbox.checked = true;
    toggleSelectAll();
}

// دالة لتحديث الإحصائيات
function updateStatistics(data) {
    let totalOrders = data.length;
    let totalAmount = 0;
    let pendingOrders = 0;
    let confirmedOrders = 0;
    
    data.forEach(order => {
        totalAmount += parseFloat(order.total_amount || 0);
        if (order.status === 'pending') pendingOrders++;
        else if (order.status === 'confirmed') confirmedOrders++;
    });
    
    document.getElementById('total-orders').textContent = totalOrders;
    document.getElementById('total-amount').textContent = formatNumber(totalAmount) + ' ج.م';
    document.getElementById('pending-orders').textContent = pendingOrders;
    document.getElementById('confirmed-orders').textContent = confirmedOrders;
    
    // تحديث عدادات التبويبات
    if (currentTab === 'sales') {
        document.getElementById('sales-count').textContent = totalOrders;
    } else if (currentTab === 'services') {
        document.getElementById('services-count').textContent = totalOrders;
    } else {
        document.getElementById('returns-count').textContent = totalOrders;
    }
}

// دالة لتحديث عنوان الجدول
function updateTableTitle() {
    const tableTitle = document.getElementById('table-title');
    const tableSubtitle = document.getElementById('table-subtitle');
    
    if (currentTab === 'sales') {
        tableTitle.textContent = 'قائمة المبيعات';
        tableSubtitle.textContent = 'عرض جميع عمليات البيع';
    } else if (currentTab === 'services') {
        tableTitle.textContent = 'قائمة مبيعات الخدمات';
        tableSubtitle.textContent = 'عرض جميع مبيعات الخدمات';
    } else {
        tableTitle.textContent = 'قائمة المرتجعات';
        tableSubtitle.textContent = 'عرض جميع عمليات الإرجاع';
    }
}

// دالة لتحديث البيانات
function refreshData() {
    const refreshBtn = document.querySelector('.refresh-btn i');
    if (refreshBtn) {
        refreshBtn.classList.add('fa-spin');
        setTimeout(() => {
            refreshBtn.classList.remove('fa-spin');
        }, 1000);
    }
    fetchOrders(false);
}

// دالة لتحديث عناوين الأعمدة بناءً على نوع التبويب
// دالة لتبديل إظهار/إخفاء أعمدة المكسب والتكلفة
function toggleProfitColumns() {
    profitColumnsVisible = !profitColumnsVisible;
    
    // تحديث أيقونة العين والزر
    const eyeIcon = document.getElementById('eye-icon');
    const toggleBtn = document.getElementById('toggle-profit-btn');
    
    if (profitColumnsVisible) {
        eyeIcon.className = 'fas fa-eye';
        toggleBtn.title = 'إخفاء خانات المكسب والتكلفة';
        toggleBtn.classList.remove('columns-hidden');
    } else {
        eyeIcon.className = 'fas fa-eye-slash';
        toggleBtn.title = 'إظهار خانات المكسب والتكلفة';
        toggleBtn.classList.add('columns-hidden');
    }
    
    updateTableHeaders();
}

function updateTableHeaders() {
    // إضافة/إزالة كلاس للجدول بناءً على النوع
    const tableWrapper = document.querySelector('.table-wrapper');
    if (currentTab === 'returns') {
        tableWrapper.classList.add('returns-mode');
    } else {
        tableWrapper.classList.remove('returns-mode');
    }
    
    // تحديث عرض زر تبديل أعمدة المكسب والتكلفة
    const toggleProfitBtn = document.getElementById('toggle-profit-btn');
    if (toggleProfitBtn) {
        if (currentTab === 'sales' || currentTab === 'services') {
            toggleProfitBtn.style.display = 'inline-block';
        } else {
            toggleProfitBtn.style.display = 'none';
        }
    }
    
    // تحديث عرض أعمدة المكسب
    const profitHeaders = document.querySelectorAll('.profit-column');
    profitHeaders.forEach(header => {
        if ((currentTab === 'sales' || currentTab === 'services') && profitColumnsVisible) {
            header.style.display = 'table-cell';
        } else {
            header.style.display = 'none';
        }
    });
    
    // تحديث عناوين الأعمدة في نافذة تفاصيل الفاتورة
    const modalCostHeader = document.querySelector('#orderDetailsModal thead th:nth-child(5)'); // عود التكلفة
    const modalProfitHeader = document.querySelector('#orderDetailsModal thead th:nth-child(8)'); // عمود المكسب
    
    if (modalCostHeader && modalProfitHeader) {
        if ((currentTab === 'sales' || currentTab === 'services') && profitColumnsVisible) {
            modalCostHeader.style.display = 'table-cell';
            modalProfitHeader.style.display = 'table-cell';
            modalCostHeader.textContent = 'تكلفة القطعة';
            modalProfitHeader.textContent = 'مكسب الصنف';
        } else {
            modalCostHeader.style.display = 'none';
            modalProfitHeader.style.display = 'none';
        }
    }
    
    // تحديث عرض أعمدة التكلفة والمكسب في صفوف البيانات
    const modalCostCells = document.querySelectorAll('#order-details-body .cost');
    const modalProfitCells = document.querySelectorAll('#order-details-body .profit');
    
    modalCostCells.forEach(cell => {
        cell.style.display = ((currentTab === 'sales' || currentTab === 'services') && profitColumnsVisible) ? 'table-cell' : 'none';
    });
    
    modalProfitCells.forEach(cell => {
        cell.style.display = ((currentTab === 'sales' || currentTab === 'services') && profitColumnsVisible) ? 'table-cell' : 'none';
    });
}

// تبديل التبويبات والبيانات المعروضة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث عناوين الأعمدة عند تحميل الصفحة
    updateTableHeaders();
    
    // تحديث عنوان الجدول
    updateTableTitle();
    
    // تحديث عداد العناصر المحددة
    updateSelectedCount();
    
    // تحميل البيانات عند تحميل اصفحة
    fetchOrders(false);
    
    const tabButtons = document.querySelectorAll('.tab-btn');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الكلاس active من جميع الأزرار
            tabButtons.forEach(btn => btn.classList.remove('active'));
            
            // إضافة الكلاس active للزر المضغوط
            this.classList.add('active');
            
            // تغيير التبويب الحالي
            currentTab = this.getAttribute('data-tab');
            
            // إعادة تعيين القيم وجلب البيانات الجديدة
            document.getElementById('search-name').value = '';
            document.getElementById('search-phone').value = '';
            document.getElementById('search-barcode').value = '';
            document.getElementById('status-filter').value = '';
            
            // تعيين تاريخ البداية للشهر الحالي
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            document.getElementById('start-date').value = firstDay.toISOString().split('T')[0];
            document.getElementById('end-date').value = '';
            
            // تحديث عرض عناوين الأعمدة بناءً على نوع التبويب
            updateTableHeaders();
            
            // تحديث عنوان الجدول
            updateTableTitle();
            
            // إعادة تعيين عداد العناصر المحددة
            updateSelectedCount();
            
            // جلب البيانات الجديدة بناء على التبويب الحالي
            suppressSound = true;
            fetchOrders(false);
        });
    });
});

document.getElementById('status-filter').addEventListener('change', () => {
    const statusValue = document.getElementById('status-filter').value;
    if (statusValue === "") {
        // When switching back to "All statuses", set the flag to ignore sound
        suppressSound = true;
        isFiltering = false;
        fetchOrders(false);
    } else {
        isFiltering = true;
        fetchOrders(false);
    }
});

document.getElementById('start-date').addEventListener('change', () => {
    suppressSound = true;
    fetchOrders(false);
});

document.getElementById('end-date').addEventListener('change', () => {
    suppressSound = true;
    fetchOrders(false);
});

document.getElementById('search-name').addEventListener('input', () => {
    suppressSound = true;
});

document.getElementById('search-phone').addEventListener('input', () => {
    suppressSound = true;
    fetchOrders(false);
});

function searchOrders() {
    suppressSound = true;
    fetchOrders(false);
}

function clearDates() {
    document.getElementById('start-date').value = '';
    document.getElementById('end-date').value = '';
    suppressSound = true;
    fetchOrders(false);
}

function fetchOrders(playSound = true) {
    // إظهار مؤشر التحميل
    const loadingSpinner = document.getElementById('loading-spinner');
    const ordersTableBody = document.getElementById('orders-table-body');
    
    loadingSpinner.classList.add('active');
    
    // حفظ حالة الاختيار الحالية قبل مسح الجدول
    let selectedOrders = {};
    document.querySelectorAll('.order-checkbox:checked').forEach(chk => {
        const key = chk.getAttribute('data-plain-account-id') + '_' + chk.getAttribute('data-order-date');
        selectedOrders[key] = true;
    });
    
    const now = Date.now();
    if (now - lastUpdate < 2000) {
        loadingSpinner.classList.remove('active');
        return; // Prevent updates faster than 2 sec
    }
    lastUpdate = now;
    
    const searchName = document.getElementById('search-name').value;
    const searchPhone = document.getElementById('search-phone').value;
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    const statusFilter = document.getElementById('status-filter').value;
    const displayMode = document.getElementById('display-mode').value;
    const searchBarcode = document.getElementById('search-barcode').value;
    const storeId = window.encryptedStoreId || '';

    const params = new URLSearchParams();
    if (searchName) params.append('search_name', searchName);
    if (searchPhone) params.append('search_phone', searchPhone);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (statusFilter) params.append('status', statusFilter);
    if (searchBarcode) params.append('search_barcode', searchBarcode);
    if (storeId) params.append('store_id', storeId);
    if (displayMode) params.append('display_mode', displayMode);
    // إضافة معلمة لتحديد نوع البيانات (مبيعات أو مرتجعات)
    params.append('data_type', currentTab);

    fetch(`get_orders.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            const ordersTableBody = document.getElementById('orders-table-body');
            const previousOrderCount = ordersTableBody.children.length;
            ordersTableBody.innerHTML = '';

            const isFilterActive = statusFilter !== "" || startDate !== "" || endDate !== "" || searchName !== "" || searchPhone !== "";

            let hasRealChanges = false;
            data.forEach(order => {
                const encryptedAccountId = order.encrypted_account_id;
                const plainAccountId = order.account_id;
                const key = plainAccountId + '_' + order.order_date;
                const isChecked = selectedOrders[key] ? 'checked' : '';
                const status = order.status === 'pending' 
                    ? '<span class="status-frame pending">انتظار</span>' 
                    : order.status === 'delayed' 
                        ? '<span class="status-frame delayed">مؤجل</span>' 
                        : '<span class="status-frame confirmed">مؤكد</span>';
                const customerName = order.customer_name || '-';
                const profit = (currentTab === 'sales' || currentTab === 'services') ? (profitColumnsVisible ? formatNumber(order.total_profit || 0) : '—') : '';
                const profitCell = (currentTab === 'sales' || currentTab === 'services') ? `<td class="profit-column" style="display: ${profitColumnsVisible ? 'table-cell' : 'none'}">${profit}</td>` : '<td class="profit-column" style="display: none;"></td>';
                
                // التحقق من طريقة العرض اليومي الشامل
                const displayMode = document.getElementById('display-mode').value;
                const isDailyView = displayMode === 'by_daily';
                
                // في العرض اليومي الشامل، نعرض تفاصيل اليوم بدلاً من المستخدم
                const usernameDisplay = isDailyView ? `<span style="color: #007bff; font-weight: bold;">${order.username}</span>` : order.username;
                const usernameClickAction = isDailyView ? `showDailyDetails("${order.order_date}")` : `showUserOrders("${plainAccountId}")`;
                
                // في العرض اليومي، نخفي أيقونة الطباعة لأنه لا يوجد فاتورة محددة
                const printIcon = isDailyView ? 
                    '<i class="fas fa-calendar-day" style="font-size: 18px; color: #28a745;" title="عرض يومي شامل"></i>' :
                    `<i class="fas fa-print printer-icon" style="cursor:pointer; font-size: 18px; color: #444;" onclick='showInvoiceOptions("${encryptedAccountId}", "${order.order_date}")'></i>`;
                
                // في العرض اليومي، نعرض تفاصيل اليوم بدلاً من تفاصيل فاتورة محددة
                const detailsAction = isDailyView ? 
                    `showDailyDetails("${order.order_date}")` :
                    `showOrderDetails("${encryptedAccountId}", "${order.order_date}", "${order.min_time || ''}", "${order.max_time || ''}", false)`;
                
                // في العرض اليومي، نخفي أيقونة الحذف لأنه لا يمكن حذف يوم كامل
                const deleteIcon = isDailyView ? 
                    '<i class="fas fa-info-circle" style="font-size: 18px; color: #17a2b8;" title="لا يمكن حذف يوم كامل"></i>' :
                    `<i class="fas fa-trash-alt delete-icon icon-spacing" style="cursor:pointer; font-size: 18px; color: #dc3545; margin-right: 8px;" onclick='deleteOrder("${encryptedAccountId}", "${order.order_date}", this, false, "${order.min_time || ''}", "${order.max_time || ''}")'></i>`;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="checkbox" 
                            class="order-checkbox" 
                            data-plain-account-id="${plainAccountId}" 
                            data-encrypted-account-id="${encryptedAccountId}" 
                            data-order-date="${order.order_date}" ${isChecked}
                            onchange="updateSelectedCount()"
                            ${isDailyView ? 'disabled title="لا يمكن تحديد العرض اليومي"' : ''}>
                    </td>
                    <td>
                        ${printIcon}
                    </td>
                    <td class="username" data-account-id="${plainAccountId}" onclick='${usernameClickAction}' style="cursor: pointer;">
                        ${usernameDisplay}
                    </td>
                    <td>${customerName}</td>
                    <td>${order.phone}</td>
                    <td>${status}</td>
                    <td>${formatNumber(order.total_quantity)}</td>
                    <td>${formatNumber(order.total_amount)}</td>
                    ${profitCell}
                    <td>${order.order_date_display || order.order_date || '-'}</td>
                    <td>
                        <i class="fas fa-eye eye-icon" style="cursor:pointer; font-size: 18px; color: #007bff;" onclick='${detailsAction}'></i>
                        ${deleteIcon}
                    </td>
                `;
                ordersTableBody.appendChild(row);

                const previousCount = previousOrderCounts[order.order_date] || 0;
                if (order.total_quantity > previousCount) {
                    hasRealChanges = true;
                }
                previousOrderCounts[order.order_date] = order.total_quantity;
            });

            if (hasRealChanges && !isFilterActive && !suppressSound && !modalOpen && playSound) {
                window.playNewOrderSound();
            }

            initialLoad = false;
            isFiltering = false;
            suppressSound = false;
            
            // تحديث عرض الأعمدة بعد تحديث البيانات
            updateTableHeaders();
            
            // تحديث الإحصائيات
            updateStatistics(data);
            
            // تحديث عنوان الجدول
            updateTableTitle();
            
            // إخفاء مؤشر التحميل
            loadingSpinner.classList.remove('active');
            
            // إظهار/إخفاء حالة فارغة
            const emptyState = document.getElementById('empty-state');
            if (data.length === 0) {
                emptyState.style.display = 'block';
                ordersTableBody.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                ordersTableBody.style.display = 'table-row-group';
            }
        })
        .catch(error => {
            console.error('Error fetching orders:', error);
            // إخفاء مؤشر التحميل في حالة الخطأ أيضاً
            loadingSpinner.classList.remove('active');
        });
}

function showOrderDetails(encryptedAccountId, orderDate = null, minTime = null, maxTime = null, playSound = true) {
    modalOpen = true;

    let url = `get_order_details.php?account_id=${encodeURIComponent(encryptedAccountId)}&data_type=${currentTab}`;
    
    if (orderDate) {
        url += `&order_date=${orderDate}`;
    }
    if (minTime) {
        url += `&min_time=${encodeURIComponent(minTime)}`;
    }
    if (maxTime) {
        url += `&max_time=${encodeURIComponent(maxTime)}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (!data || data.length === 0) {
                console.error('Error fetching order details: No data received');
                return;
            }

            const orderDetailsBody = document.getElementById('order-details-body');
            orderDetailsBody.innerHTML = '';
            orderDetailsBody.dataset.accountId = encryptedAccountId;
            orderDetailsBody.dataset.orderDate = orderDate || '';
            orderDetailsBody.dataset.minTime = minTime || '';
            orderDetailsBody.dataset.maxTime = maxTime || '';
            orderDetailsBody.dataset.dataType = currentTab; // تخزين نوع البيانات للاستخدام لاحقًا

            data.forEach(item => {
                const status = item.status === 'pending' 
                    ? '<span class="status-frame pending">انتظار</span>' 
                    : item.status === 'delayed' 
                        ? '<span class="status-frame delayed">مؤجل</span>' 
                        : '<span class="status-frame confirmed">مؤكد</span>';
                
                // إظهار التكلفة والمكسب في حالة المبيعات والخدمات
                const cost = (currentTab === 'sales' || currentTab === 'services') ? (profitColumnsVisible ? formatNumber(item.cost || 0) : '—') : '';
                const profit = (currentTab === 'sales' || currentTab === 'services') ? (profitColumnsVisible ? formatNumber(item.profit || 0) : '—') : '';
                const costCell = (currentTab === 'sales' || currentTab === 'services') ? `<td class="cost" style="display: ${profitColumnsVisible ? 'table-cell' : 'none'}">${cost}</td>` : '<td class="cost" style="display: none;"></td>';
                const profitCell = (currentTab === 'sales' || currentTab === 'services') ? `<td class="profit" style="display: ${profitColumnsVisible ? 'table-cell' : 'none'}">${profit}</td>` : '<td class="profit" style="display: none;"></td>';
                
                // تنسيق التاريخ والوقت بشكل جميل
                const saleTime = item.sale_time ? formatDateTime(item.order_date, item.sale_time) : '-';
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="order-detail-checkbox" data-sale-id="${item.sale_id}"></td>
                    <td class="sale_id">${item.sale_id}</td>
                    <td class="name">${item.name}</td>
                    <td class="quantity" contenteditable="true" onkeypress="handleQuantityKeyPress(event, this, ${item.sale_id}, '${item.status}', ${item.item_id})">${formatNumber(item.quantity)}</td>
                    ${costCell}
                    <td class="price">${formatNumber(item.price)}</td>
                    <td class="total_amount">${formatNumber(item.total_amount)}</td>
                    ${profitCell}
                    <td class="sale-time">${saleTime}</td>
                    <td>${status}</td>
                    <td>
                        <i class="fas fa-trash-alt" style="cursor:pointer; font-size: 18px; color: #dc3545;" onclick='deleteOrderItem(${item.sale_id}, this)'></i>
                    </td>
                `;
                orderDetailsBody.appendChild(row);
            });
            
            // حساب إجمالي المكسب وإضافته للنافذة
            if ((currentTab === 'sales' || currentTab === 'services') && profitColumnsVisible) {
                let totalProfit = 0;
                data.forEach(item => {
                    totalProfit += parseFloat(item.profit || 0);
                });
                
                // إضافة أو تحديث عنصر إجمالي المكسب
                let profitSummary = document.getElementById('profit-summary');
                if (!profitSummary) {
                    profitSummary = document.createElement('div');
                    profitSummary.id = 'profit-summary';
                    profitSummary.style.cssText = 'margin: 15px 0; padding: 10px; background-color: #e8f5e8; border-radius: 5px; text-align: center; font-weight: bold; color: #2e7d32;';
                    
                    const modalContent = document.querySelector('#orderDetailsModal .modal-content');
                    const deleteButton = modalContent.querySelector('.action-btn');
                    modalContent.insertBefore(profitSummary, deleteButton);
                }
                profitSummary.innerHTML = `إجمالي المكسب: ${formatNumber(totalProfit)}`;
                profitSummary.style.display = 'block';
            } else {
                // إخفاء عنصر إجمالي المكسب في حالة المرتجعات أو عند إخفاء أعمدة المكسب
                const profitSummary = document.getElementById('profit-summary');
                if (profitSummary) {
                    profitSummary.style.display = 'none';
                }
            }

            const modal = document.getElementById('orderDetailsModal');
            modal.classList.add('active');
        })
        .catch(error => console.error('Error fetching order details:', error));
}

function markAsPaid(button, saleId, encryptedAccountId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، ادفعها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            const row = button.closest('tr');
            const quantity = row.querySelector('.quantity').textContent.trim();
            const price = row.querySelector('.price').textContent.trim();
            const totalAmount = row.querySelector('.total_amount').textContent.trim();
            const collected = row.querySelector('.collected').textContent.trim();
            const remaining = totalAmount - collected;
            const orderDate = document.querySelector('#order-details-body').dataset.orderDate; // Correctly retrieve order_date

            if (remaining <= 0) {
                console.error('This item has already been paid for.');
                return;
            }

            fetch('mark_as_paid.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ quantity: quantity, price: price, total_amount: totalAmount, sale_id: saleId })
            }).then(response => response.json())
              .then(data => {
                  if (data.status === 'success') {
                      showOrderDetails(encryptedAccountId, orderDate, false); // Pass the correct order_date
                      fetchOrders(); // Refresh orders table
                  }
              })
              .catch(error => console.error('Error marking item as paid:', error));
        }
    });
}

function markAsDelayed(element, saleId, encryptedAccountId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، أجله!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            const orderDate = document.querySelector('#order-details-body').dataset.orderDate; // Correctly retrieve order_date

            fetch(`mark_as_delayed.php?sale_id=${saleId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showOrderDetails(encryptedAccountId, orderDate, false); // Pass the correct order_date
                        fetchOrders(); // Refresh orders table
                    }
                })
                .catch(error => console.error('Error marking item as delayed:', error));
        }
    });
}

function closeModal() {
    modalOpen = false;
    document.getElementById('orderDetailsModal').classList.remove('active');
}

window.onclick = function(event) {
    const modal = document.getElementById('orderDetailsModal');
    const userOrdersModal = document.getElementById('userOrdersModal');
    if (event.target === modal) {
        closeModal();
    } else if (event.target === userOrdersModal) {
        closeUserOrdersModal();
    }
}

// Fetch orders on page load
document.addEventListener('DOMContentLoaded', () => {
    fetchOrders(false);
    initialLoad = false; // Set initialLoad to false after the first fetch
});

// Use Server-Sent Events (SSE) to update orders continuously
if (typeof(EventSource) !== "undefined") {
    const storeId = window.encryptedStoreId || '';
    const setupSSE = () => {
        const source = new EventSource(`sse_orders.php?store_id=${storeId}`);
        source.onmessage = (e) => {
            if (firstSSE) {
                firstSSE = false;
                return;
            }
            // Disable sound by passing false
            fetchOrders(false);
            
            // تحديث نافذة التفاصيل إذا كانت مفتوحة
            refreshModalIfOpen();
        };
        source.onerror = () => {
            source.close();
            clearTimeout(sseReconnectTimer);
            sseReconnectTimer = setTimeout(setupSSE, 5000);
        };
    };
    setupSSE();
} else {
    console.error("Your browser does not support Server-Sent Events.");
}

// دالة لتحديث نافذة التفاصيل إذا كانت مفتوحة
function refreshModalIfOpen() {
    const modal = document.getElementById('orderDetailsModal');
    const orderDetailsBody = document.getElementById('order-details-body');
    
    // التحقق من أن النافذة مفتوحة وتحتوي على بيانات
    if (modal && modal.classList.contains('active') && orderDetailsBody && orderDetailsBody.dataset.accountId) {
        const accountId = orderDetailsBody.dataset.accountId;
        const orderDate = orderDetailsBody.dataset.orderDate;
        const minTime = orderDetailsBody.dataset.minTime;
        const maxTime = orderDetailsBody.dataset.maxTime;
        
        // تحديث تفاصيل الفاتورة بصمت (بدون صوت)
        showOrderDetails(accountId, orderDate, minTime, maxTime, false);
    }
}

// دالة للتعامل مع تحديث الكمية عند الضغط على Enter
function handleQuantityKeyPress(event, element, saleId, status, itemId) {
    if (event.key === 'Enter') {
        event.preventDefault();
        updateQuantity(element, saleId, status, itemId);
    }
}

// دالة لتحديث الكمية (مبسطة بدون ملف منفصل)
function updateQuantity(element, saleId, status, itemId) {
    const newQuantity = parseFloat(element.textContent.trim());
    const oldQuantity = parseFloat(element.dataset.oldQuantity || element.textContent.trim());
    
    if (isNaN(newQuantity) || newQuantity <= 0) {
        alert('الكمية غير صالحة');
        element.textContent = oldQuantity;
        return;
    }

    // تحديث المجموع والمكسب في الصف مباشرة
    const row = element.closest('tr');
    const priceCell = row.querySelector('.price');
    const totalCell = row.querySelector('.total_amount');
    const profitCell = row.querySelector('.profit');
    const costCell = row.querySelector('.cost');
    
    if (priceCell && totalCell) {
        const price = parseFloat(priceCell.textContent);
        const newTotal = price * newQuantity;
        totalCell.textContent = formatNumber(newTotal);
        
        // تحديث المكسب إذا كان موجوداً
        if (profitCell && costCell && currentTab === 'sales') {
            const cost = parseFloat(costCell.textContent) || 0;
            const newProfit = (price - cost) * newQuantity;
            profitCell.textContent = formatNumber(newProfit);
        }
    }
    
    // تحديث إجمالي المكسب
    updateProfitSummary();
    
    // حفظ القيمة الجديدة
    element.dataset.oldQuantity = newQuantity;
    
    // تحديث الجدول الرئيسي بعد تأخير قصير
    setTimeout(() => {
        fetchOrders(false);
    }, 500);
}

// دالة لتحديث إجمالي المكسب في نافذة التفاصيل
function updateProfitSummary() {
    if (currentTab !== 'sales' && currentTab !== 'services') return;
    
    const profitCells = document.querySelectorAll('#order-details-body .profit');
    let totalProfit = 0;
    
    profitCells.forEach(cell => {
        const profit = parseFloat(cell.textContent) || 0;
        totalProfit += profit;
    });
    
    const profitSummary = document.getElementById('profit-summary');
    if (profitSummary) {
        profitSummary.innerHTML = `إجمالي المكسب: ${formatNumber(totalProfit)}`;
    }
}

function deleteOrderFromModal() {
    const saleId = document.querySelector('#order-details-body .sale_id').textContent.trim();
    const encryptedAccountId = document.querySelector('#order-details-body').dataset.accountId;
    const orderDate = document.querySelector('#order-details-body').dataset.orderDate;
    deleteOrder(encryptedAccountId, orderDate, document.querySelector(`#orders-table-body tr[data-sale-id="${saleId}"]`), true);
    setTimeout(() => showOrderDetails(encryptedAccountId, orderDate, false), 500); // Refresh order details after a short delay
}

function deleteOrder(accountId, orderDate, element, fromModal = false, minTime = '', maxTime = '') {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، احذفها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // استخدام نقطة النهاية المناسبة بناءً على التبويب الحالي
            const endpoint = currentTab === 'returns' ? 'delete_full_return.php' : 'delete_full_order.php';
            
            // جلب store_id من الحقل المخفي
            const storeId = document.getElementById('store-id').value;
            
            // جلب min_time و max_time إذا كان الحذف من داخل المودال
            if (fromModal) {
            const orderDetailsBody = document.getElementById('order-details-body');
            minTime = orderDetailsBody.dataset.minTime || '';
            maxTime = orderDetailsBody.dataset.maxTime || '';
            }
            
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    account_id: accountId, 
                    order_date: orderDate,
                    store_id: storeId,
                    min_time: minTime,
                    max_time: maxTime
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إزالة الصف الخاص بالطلب المحذوف من واجهة المستخدم
                    if (element) {
                        element.closest('tr').remove();
                    }
                    // إذا كان الحذف من داخل المودال، تحقق من بقاء أصناف أخرى
                    if (fromModal) {
                        const orderDetailsBody = document.getElementById('order-details-body');
                        if (orderDetailsBody.children.length === 0) {
                            closeModal(); // إغلاق المودال إذا لم يتبقَ أي صنف
                        }
                    }
                    // تحديث قائمة الطلبات الرئيسية إن دعت الحاجة
                    fetchOrders();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => console.error('Error:', error));
        }
    });
}

function payAllItems() {
    const orderDetailsBody = document.getElementById('order-details-body');
    const rows = orderDetailsBody.querySelectorAll('tr');
    const saleIds = [];
    const orderDate = orderDetailsBody.dataset.orderDate; // Get the order date
    const encryptedAccountId = orderDetailsBody.dataset.accountId; // Get the account ID of the order

    rows.forEach(row => {
        const saleId = row.querySelector('.sale_id').textContent.trim();
        const collected = row.querySelector('.collected').textContent.trim();

        if (collected == 0) {
            saleIds.push(saleId);
        }
    });

    if (saleIds.length > 0) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، ادفع الكل!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('pay_all_items.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ sale_ids: saleIds, account_id: encryptedAccountId, order_date: orderDate }) // Send account ID and order date
                }).then(response => response.json())
                  .then(data => {
                      if (data.status === 'success') {
                          // Update item statuses in the modal
                          rows.forEach(row => {
                              const collectedCell = row.querySelector('.collected');
                              const statusCell = row.querySelector('.status-frame');
                              if (collectedCell.textContent.trim() == 0) {
                                  collectedCell.textContent = row.querySelector('.total_amount').textContent.trim();
                                  statusCell.className = 'status-frame confirmed';
                                  statusCell.textContent = 'مؤكد';
                              }
                          });
                          fetchOrders(); // Refresh orders table
                          showOrderDetails(encryptedAccountId, orderDate, false); // Fetch order details with account ID and order date
                      } else {
                          alert(data.message);
                      }
                  })
                  .catch(error => console.error('Error marking all items as paid:', error));
            }
        });
    }
}

function deleteOrderItem(saleId, element) {
    const dataType = document.getElementById('order-details-body').dataset.dataType || 'sales';
    const endpoint = dataType === 'returns' ? 'delete_return.php' : 'delete_order.php';

    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، احذفها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ order_id: saleId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إزالة الصف الخاص بالصنف المحذوف من واجهة المستخدم
                    if (element) {
                        element.closest('tr').remove();
                    }
                    // تحديث قائمة الطلبات الرئيسية إن دعت الحاجة
                    fetchOrders();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => console.error('Error:', error));
        }
    });
}

function deleteSelectedItems() {
    const checkboxes = document.querySelectorAll('#order-details-body .order-detail-checkbox:checked');
    if (checkboxes.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'يرجى اختيار عنصر واحد على الأقل للحذف'
        });
        return;
    }

    const dataType = document.getElementById('order-details-body').dataset.dataType || 'sales';
    const saleIds = Array.from(checkboxes).map(checkbox => checkbox.getAttribute('data-sale-id'));

    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، احذفها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            const endpoint = dataType === 'returns' ? 'delete_multiple_returns.php' : 'delete_multiple_orders.php';
            
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ ids: saleIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // حذف الصفوف من الجدول
                    saleIds.forEach(id => {
                        const checkbox = document.querySelector(`.order-detail-checkbox[data-sale-id="${id}"]`);
                        if (checkbox) {
                            const row = checkbox.closest('tr');
                            if (row) row.remove();
                        }
                    });
                    
                    // تحديث القوائم الرئيسية
                    fetchOrders();
                    
                    Swal.fire(
                        'تم الحذف!',
                        'تم حذف العناصر المحددة بنجاح.',
                        'success'
                    );
                } else {
                    Swal.fire(
                        'خطأ!',
                        data.message || 'حدث خطأ أثناء الحذف.',
                        'error'
                    );
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire(
                    'خطأ!',
                    'حدث خطأ أثناء الاتصال بالخادم.',
                    'error'
                );
            });
        }
    });
}

// New function to pay only selected items
function paySelectedItems() {
    const checkboxes = document.querySelectorAll('#order-details-body .order-detail-checkbox:checked');
    if (checkboxes.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'اختر صنف واحد على الأقل للدفع'
        });
        return;
    }
    let saleIds = [];
    // Changed index from 8 to 9 for status cell
    for (const chk of checkboxes) {
        const row = chk.closest('tr');
        const statusText = row.cells[9].innerText.trim();
        if (statusText !== 'انتظار' && statusText !== 'مؤجل') {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يمكن دفع الأصناف التي حالتها انتظار أو مؤجل فقط'
            });
            return;
        }
        saleIds.push(chk.getAttribute('data-sale-id'));
    }
    const orderDetailsBody = document.getElementById('order-details-body');
    const orderDate = orderDetailsBody.dataset.orderDate;
    const encryptedAccountId = orderDetailsBody.dataset.accountId;
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا الإجراء!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، ادفعها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('pay_selected_items.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    sale_ids: saleIds,
                    account_id: encryptedAccountId,
                    order_date: orderDate
                })
            }).then(response => response.json())
              .then(data => {
                  if (data.status === 'success') {
                      Swal.fire({
                          icon: 'success',
                          title: 'نجاح',
                          text: 'تم الدفع للأصناف المحددة'
                      });
                      showOrderDetails(encryptedAccountId, orderDate, false);
                      fetchOrders();
                  } else {
                      Swal.fire({
                          icon: 'error',
                          title: 'خطأ',
                          text: data.message
                      });
                  }
              })
              .catch(error => console.error('Error:', error));
        }
    });
}

function handleReturnedKeyPress(event, element, saleId) {
    if (event.key === 'Enter') {
        event.preventDefault();
        updateReturned(element, saleId);
    }
}

function updateReturned(element, saleId) {
    const newVal = parseFloat(element.textContent.trim());
    const oldVal = parseFloat(element.dataset.oldReturned);
    if (isNaN(newVal)) {
        alert('القيمة غير صالحة');
        element.textContent = oldVal;
        return;
    }
    fetch('update_returned.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            sale_id: saleId,
            new_returned: newVal
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Update the oldReturned data attribute to newVal on success
            element.dataset.oldReturned = newVal;
            // Optionally, refresh order details
            const orderDate = document.querySelector('#order-details-body').dataset.orderDate;
            const encryptedAccountId = document.querySelector('#order-details-body').dataset.accountId;
            showOrderDetails(encryptedAccountId, orderDate, false);
            fetchOrders();
        } else {
            alert(data.message);
            element.textContent = oldVal;
        }
    })
    .catch(error => {
        console.error('Error updating returned:', error);
        element.textContent = oldVal;
    });
}

// Function to show invoice printing options
function showInvoiceOptions(encryptedAccountId, orderDate) {
    Swal.fire({
        title: 'خيارات الطباعة',
        html: `
            <div style="display: flex; flex-direction: column; gap: 10px;">
                <button class="swal2-confirm swal2-styled" onclick="printInvoice('${encryptedAccountId}', '${orderDate}', 'regular')">
                    طباعة فاتورة عادية
                </button>
                <button class="swal2-confirm swal2-styled" onclick="printInvoice('${encryptedAccountId}', '${orderDate}', 'thermal')">
                    طباعة فاتورة حرارية
                </button>
            </div>
        `,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'إلغاء'
    });
}

// Function to handle invoice printing
function printInvoice(encryptedAccountId, orderDate, type) {
    let url = '';
    if (type === 'regular') {
        url = `print_invoice.php?account_id=${encryptedAccountId}&order_date=${orderDate}`;
    } else if (type === 'thermal') {
        url = `print_thermal_invoice.php?account_id=${encryptedAccountId}&order_date=${orderDate}`;
    }
    
    if (url) {
        window.open(url, '_blank');
        Swal.close();
    }
}

// Function to view selected orders in a combined modal
function viewSelectedOrders() {
    const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'يرجى اختيار فاتورة واحدة على الأقل لعرض التفاصيل'
        });
        return;
    }

    // جمع معرفات الحسابات والتواريخ المحددة
    const selectedOrders = [];
    selectedCheckboxes.forEach(checkbox => {
        const accountId = checkbox.getAttribute('data-encrypted-account-id');
        const orderDate = checkbox.getAttribute('data-order-date');
        selectedOrders.push({ accountId, orderDate });
    });

    // إنشاء نافذة منبثقة لعرض الفواتير المجمعة
    showCombinedOrdersModal(selectedOrders);
}

// Function to show combined orders modal
function showCombinedOrdersModal(selectedOrders) {
    // إنشاء HTML للنافذة المنبثقة
    const modalHtml = `
        <div id="combinedOrdersModal" class="modal active">
            <div class="modal-content" style="max-width: 95%; width: 1200px;">
                <span class="close" onclick="closeCombinedOrdersModal()">&times;</span>
                <h2>تفاصيل الفواتير المجمعة (${selectedOrders.length} فاتورة)</h2>
                <div id="combinedOrdersContent">
                    <div class="loading-spinner active">
                        <div class="spinner-container">
                            <div class="spinner"></div>
                            <div class="loading-text">
                                <p>جاري تحميل تفاصيل الفواتير...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة إلى الصفحة
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // تحميل تفاصيل الفواتير
    loadCombinedOrdersData(selectedOrders);
}

// Function to load combined orders data
async function loadCombinedOrdersData(selectedOrders) {
    try {
        const allOrdersData = [];
        
        // تحميل تفاصيل كل فاتورة
        for (const order of selectedOrders) {
            const url = `get_order_details.php?account_id=${encodeURIComponent(order.accountId)}&order_date=${order.orderDate}&data_type=${currentTab}`;
            const response = await fetch(url);
            const data = await response.json();
            
            if (data && data.length > 0) {
                // إضافة معلومات الفاتورة لكل صنف
                data.forEach(item => {
                    item.invoice_account_id = order.accountId;
                    item.invoice_order_date = order.orderDate;
                });
                allOrdersData.push(...data);
            }
        }

        // عرض البيانات في الجدول
        displayCombinedOrdersData(allOrdersData);
        
    } catch (error) {
        console.error('Error loading combined orders:', error);
        document.getElementById('combinedOrdersContent').innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>حدث خطأ</h3>
                <p>تعذر تحميل تفاصيل الفواتير</p>
            </div>
        `;
    }
}

// Function to display combined orders data
function displayCombinedOrdersData(allOrdersData) {
    if (allOrdersData.length === 0) {
        document.getElementById('combinedOrdersContent').innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-inbox"></i>
                </div>
                <h3>لا توجد بيانات</h3>
                <p>لم يتم العثور على أي أصناف في الفواتير المحددة</p>
            </div>
        `;
        return;
    }

    // حساب الإجماليات
    let totalQuantity = 0;
    let totalAmount = 0;
    let totalProfit = 0;

    allOrdersData.forEach(item => {
        totalQuantity += parseFloat(item.quantity || 0);
        totalAmount += parseFloat(item.total_amount || 0);
        if (currentTab === 'sales') {
            totalProfit += parseFloat(item.profit || 0);
        }
    });

    // إنشاء HTML للجدول
    let tableHtml = `
        <div class="summary-bar" style="margin-bottom: 20px; padding: 15px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 10px; display: flex; justify-content: space-around; flex-wrap: wrap;">
            <div class="summary-item" style="padding: 10px 15px; margin: 5px; border-radius: 8px; background: var(--color-primary); color: white;">
                <strong>إجمالي الكميات: ${formatNumber(totalQuantity)}</strong>
            </div>
            <div class="summary-item" style="padding: 10px 15px; margin: 5px; border-radius: 8px; background: var(--color-primary); color: white;">
                <strong>إجمالي المبلغ: ${formatNumber(totalAmount)}</strong>
            </div>
            ${currentTab === 'sales' ? `
            <div class="summary-item" style="padding: 10px 15px; margin: 5px; border-radius: 8px; background: var(--color-primary); color: white;">
                <strong>إجمالي المكسب: ${formatNumber(totalProfit)}</strong>
            </div>
            ` : ''}
        </div>
        <div class="table-wrapper">
            <table class="enhanced-table">
                <thead>
                    <tr>
                        <th>معرف البيع</th>
                        <th>اسم الصنف</th>
                        <th>الكمية</th>
                        ${currentTab === 'sales' ? '<th>تكلفة القطعة</th>' : ''}
                        <th>سعر القطعة</th>
                        <th>مجموع السعر</th>
                        ${currentTab === 'sales' ? '<th>مكسب الصنف</th>' : ''}
                        <th>تاريخ ووقت البيع</th>
                        <th>الحالة</th>
                        <th>تاريخ الفاتورة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة صفوف البيانات
    allOrdersData.forEach(item => {
        const status = item.status === 'pending' 
            ? '<span class="status-frame pending">انتظار</span>' 
            : item.status === 'delayed' 
                ? '<span class="status-frame delayed">مؤجل</span>' 
                : '<span class="status-frame confirmed">مؤكد</span>';
        
        const saleTime = item.sale_time ? formatDateTime(item.order_date, item.sale_time) : '-';
        const cost = currentTab === 'sales' ? formatNumber(item.cost || 0) : '';
        const profit = currentTab === 'sales' ? formatNumber(item.profit || 0) : '';
        
        tableHtml += `
            <tr>
                <td>${item.sale_id}</td>
                <td>${item.name}</td>
                <td>${formatNumber(item.quantity)}</td>
                ${currentTab === 'sales' ? `<td>${cost}</td>` : ''}
                <td>${formatNumber(item.price)}</td>
                <td>${formatNumber(item.total_amount)}</td>
                ${currentTab === 'sales' ? `<td>${profit}</td>` : ''}
                <td class="sale-time">${saleTime}</td>
                <td>${status}</td>
                <td>${item.order_date || '-'}</td>
            </tr>
        `;
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('combinedOrdersContent').innerHTML = tableHtml;
}

// Function to close combined orders modal
function closeCombinedOrdersModal() {
    const modal = document.getElementById('combinedOrdersModal');
    if (modal) {
        modal.remove();
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('combinedOrdersModal');
    if (modal && event.target === modal) {
        closeCombinedOrdersModal();
    }
});

// دالة لعرض تقرير المبيعات الشامل
function showSalesReport() {
    showComprehensiveReport('sales');
}

// دالة لعرض تقرير الخدمات الشامل
function showServicesReport() {
    showComprehensiveReport('services');
}

// دالة موحدة لعرض التقارير الشاملة
function showComprehensiveReport(reportType = 'sales') {
    // تحديد الوضع الحالي (فاتح أم مظلم)
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    
    // ألوان متكيفة مع الوضع
    const colors = {
        background: isDarkMode ? '#21262d' : '#f8f9fa',
        text: isDarkMode ? '#e6edf3' : '#333333',
        title: isDarkMode ? '#58a6ff' : '#3498db',
        totalBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#e8f5e8',
        totalBorder: isDarkMode ? '#28a745' : '#4CAF50',
        totalText: isDarkMode ? '#4caf50' : '#2e7d32',
        cardBorder: isDarkMode ? '#30363d' : 'transparent',
        shadow: isDarkMode ? '0 4px 15px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
        profitBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#d4edda',
        profitBorder: isDarkMode ? '#28a745' : '#155724',
        profitText: isDarkMode ? '#4caf50' : '#155724',
        salesBg: isDarkMode ? 'linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%)' : '#e3f2fd',
        salesBorder: isDarkMode ? '#3498db' : '#1565c0',
        salesText: isDarkMode ? '#58a6ff' : '#1565c0',
        pendingBg: isDarkMode ? 'linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%)' : '#fff3cd',
        pendingBorder: isDarkMode ? '#ffc107' : '#856404',
        pendingText: isDarkMode ? '#f7cc47' : '#856404',
        confirmedBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#d1ecf1',
        confirmedBorder: isDarkMode ? '#28a745' : '#0c5460',
        confirmedText: isDarkMode ? '#4caf50' : '#0c5460'
    };

    // تحديد العنوان والأيقونة بناءً على نوع التقرير
    const reportConfig = {
        sales: {
            title: 'تقرير المبيعات الشامل',
            icon: 'fas fa-chart-pie',
            subtitle: 'تحليل المبيعات',
            loadingText: 'جاري تحميل بيانات المبيعات...'
        },
        services: {
            title: 'تقرير الخدمات الشامل',
            icon: 'fas fa-concierge-bell',
            subtitle: 'تحليل مبيعات الخدمات',
            loadingText: 'جاري تحميل بيانات الخدمات...'
        }
    };

    const config = reportConfig[reportType];

    // عرض النافذة مع محتوى فارغ أولاً
    Swal.fire({
        title: `<i class="${config.icon}" style="color: #28a745;"></i> ${config.title}`,
        html: `
            <div style="text-align: right; padding: 25px; font-family: 'Cairo', sans-serif;">
                <div style="text-align: center; margin-bottom: 25px;">
                    <h3 style="color: ${colors.title}; margin-bottom: 8px; font-weight: 700;">
                        <i class="fas fa-chart-line" style="margin-left: 10px;"></i>
                        ${config.subtitle}
                    </h3>
                    <p style="color: ${colors.text}; opacity: 0.8; margin: 0; font-size: 16px;">
                        ${window.storeName || 'متجر الوليد'}
                    </p>
                    <div style="width: 60px; height: 3px; background: ${colors.title}; margin: 15px auto; border-radius: 2px;"></div>
                </div>
                
                <!-- تبويبات التقرير -->
                <div style="display: flex; justify-content: center; margin-bottom: 20px; border-bottom: 2px solid ${colors.title};">
                    <button onclick="switchReportTab('sales')" class="report-tab-btn ${reportType === 'sales' ? 'active' : ''}" 
                            style="padding: 10px 20px; margin: 0 5px; border: none; background: ${reportType === 'sales' ? colors.title : 'transparent'}; 
                                   color: ${reportType === 'sales' ? 'white' : colors.title}; border-radius: 8px 8px 0 0; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-shopping-cart" style="margin-left: 5px;"></i>
                        تحليل المبيعات
                    </button>
                    <button onclick="switchReportTab('services')" class="report-tab-btn ${reportType === 'services' ? 'active' : ''}"
                            style="padding: 10px 20px; margin: 0 5px; border: none; background: ${reportType === 'services' ? colors.title : 'transparent'}; 
                                   color: ${reportType === 'services' ? 'white' : colors.title}; border-radius: 8px 8px 0 0; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-concierge-bell" style="margin-left: 5px;"></i>
                        تحليل الخدمات
                    </button>
                </div>
                
                <div id="salesReportContent" style="min-height: 300px;">
                    <div style="text-align: center; padding: 50px; color: ${colors.text};">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 15px;"></i>
                        <p>${config.loadingText}</p>
                    </div>
                </div>
                
                <div class="info-section" style="margin-top: 20px; padding: 15px; background: ${isDarkMode ? 'rgba(88, 166, 255, 0.1)' : 'rgba(52, 152, 219, 0.1)'}; border-radius: 10px; border: 1px dashed ${colors.title};">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <i class="fas fa-info-circle" style="color: ${colors.title}; font-size: 16px;"></i>
                        <span style="color: ${colors.title}; font-weight: 600; font-size: 14px;">معلومات التقرير</span>
                    </div>
                    <div style="color: ${colors.text}; font-size: 13px; line-height: 1.5; opacity: 0.9;">
                        • التقرير يشمل جميع ${reportType === 'sales' ? 'المبيعات' : 'الخدمات'} والمرتجعات في الفترة المحددة<br>
                        • يتم حساب الأرباح بناءً على الفرق بين سعر البيع وسعر التكلفة<br>
                        • الإحصائيات تشمل الطلبات المؤكدة والمعلقة والمؤجلة<br>
                        • يمكن استخدام الفلاتر لتخصيص التقرير حسب الفترة الزمنية
                    </div>
                </div>
            </div>
        `,
        icon: null,
        confirmButtonText: 'إغلاق',
        confirmButtonColor: '#28a745',
        width: '900px',
        customClass: {
            popup: isDarkMode ? 'swal2-dark' : '',
            title: isDarkMode ? 'swal2-title-dark' : '',
            content: isDarkMode ? 'swal2-content-dark' : ''
        },
        background: isDarkMode ? '#0d1117' : '#ffffff',
        color: isDarkMode ? '#e6edf3' : '#333333',
        showClass: {
            popup: 'animate__animated animate__fadeInUp animate__faster'
        },
        hideClass: {
            popup: 'animate__animated animate__fadeOutDown animate__faster'
        },
        didOpen: () => {
            // تحميل البيانات بعد فتح النافذة
            loadSalesReportData(colors, reportType);
        }
    });
}

// دالة لتبديل تبويبات التقرير
function switchReportTab(reportType) {
    // تحديث الأزرار
    document.querySelectorAll('.report-tab-btn').forEach(btn => {
        btn.classList.remove('active');
        btn.style.background = 'transparent';
        btn.style.color = '#3498db';
    });
    
    const activeBtn = document.querySelector(`[onclick="switchReportTab('${reportType}')"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
        activeBtn.style.background = '#3498db';
        activeBtn.style.color = 'white';
    }
    
    // تحديث المحتوى
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    const colors = {
        background: isDarkMode ? '#21262d' : '#f8f9fa',
        text: isDarkMode ? '#e6edf3' : '#333333',
        title: isDarkMode ? '#58a6ff' : '#3498db',
        totalBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#e8f5e8',
        totalBorder: isDarkMode ? '#28a745' : '#4CAF50',
        totalText: isDarkMode ? '#4caf50' : '#2e7d32',
        cardBorder: isDarkMode ? '#30363d' : 'transparent',
        shadow: isDarkMode ? '0 4px 15px rgba(0, 0, 0, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
        profitBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#d4edda',
        profitBorder: isDarkMode ? '#28a745' : '#155724',
        profitText: isDarkMode ? '#4caf50' : '#155724',
        salesBg: isDarkMode ? 'linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%)' : '#e3f2fd',
        salesBorder: isDarkMode ? '#3498db' : '#1565c0',
        salesText: isDarkMode ? '#58a6ff' : '#1565c0',
        pendingBg: isDarkMode ? 'linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%)' : '#fff3cd',
        pendingBorder: isDarkMode ? '#ffc107' : '#856404',
        pendingText: isDarkMode ? '#f7cc47' : '#856404',
        confirmedBg: isDarkMode ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%)' : '#d1ecf1',
        confirmedBorder: isDarkMode ? '#28a745' : '#0c5460',
        confirmedText: isDarkMode ? '#4caf50' : '#0c5460'
    };
    
    loadSalesReportData(colors, reportType);
}

// دالة لعرض تفاصيل اليوم في العرض اليومي الشامل
function showDailyDetails(orderDate) {
    modalOpen = true;
    
    const storeId = window.encryptedStoreId || '';
    let url = `get_daily_details.php?order_date=${orderDate}&data_type=${currentTab}`;
    if (storeId) {
        url += `&store_id=${storeId}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (!data || data.length === 0) {
                Swal.fire({
                    icon: 'info',
                    title: 'لا توجد بيانات',
                    text: 'لم يتم العثور على أي معاملات في هذا التاريخ'
                });
                modalOpen = false;
                return;
            }

            // إنشاء نافذة منبثقة لعرض تفاصيل اليوم
            const modalHtml = `
                <div id="dailyDetailsModal" class="modal active">
                    <div class="modal-content" style="max-width: 95%; width: 1200px;">
                        <span class="close" onclick="closeDailyDetailsModal()">&times;</span>
                        <h2>تفاصيل يوم ${orderDate}</h2>
                        <div id="dailyDetailsContent">
                            <div class="loading-spinner active">
                                <div class="spinner-container">
                                    <div class="spinner"></div>
                                    <div class="loading-text">
                                        <p>جاري تحميل تفاصيل اليوم...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة إلى الصفحة
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // عرض البيانات
            displayDailyDetailsData(data, orderDate);
        })
        .catch(error => {
            console.error('Error fetching daily details:', error);
            modalOpen = false;
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء تحميل تفاصيل اليوم'
            });
        });
}

// دالة لعرض بيانات تفاصيل اليوم
function displayDailyDetailsData(data, orderDate) {
    // حساب الإجماليات
    let totalQuantity = 0;
    let totalAmount = 0;
    let totalProfit = 0;
    let uniqueUsers = new Set();

    data.forEach(item => {
        totalQuantity += parseFloat(item.quantity || 0);
        totalAmount += parseFloat(item.total_amount || 0);
        if (currentTab === 'sales' || currentTab === 'services') {
            totalProfit += parseFloat(item.profit || 0);
        }
        uniqueUsers.add(item.username);
    });

    // إنشاء HTML للجدول
    let tableHtml = `
        <div class="summary-bar" style="margin-bottom: 20px; padding: 15px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 10px; display: flex; justify-content: space-around; flex-wrap: wrap;">
            <div class="summary-item" style="padding: 10px 15px; margin: 5px; border-radius: 8px; background: var(--color-primary); color: white;">
                <strong>إجمالي الكميات: ${formatNumber(totalQuantity)}</strong>
            </div>
            <div class="summary-item" style="padding: 10px 15px; margin: 5px; border-radius: 8px; background: var(--color-primary); color: white;">
                <strong>إجمالي المبلغ: ${formatNumber(totalAmount)}</strong>
            </div>
            ${(currentTab === 'sales' || currentTab === 'services') ? `
            <div class="summary-item" style="padding: 10px 15px; margin: 5px; border-radius: 8px; background: var(--color-primary); color: white;">
                <strong>إجمالي المكسب: ${formatNumber(totalProfit)}</strong>
            </div>
            ` : ''}
            <div class="summary-item" style="padding: 10px 15px; margin: 5px; border-radius: 8px; background: var(--color-primary); color: white;">
                <strong>عدد المستخدمين: ${uniqueUsers.size}</strong>
            </div>
        </div>
        <div class="table-wrapper">
            <table class="enhanced-table">
                <thead>
                    <tr>
                        <th>معرف البيع</th>
                        <th>اسم المستخدم</th>
                        <th>اسم الصنف</th>
                        <th>الكمية</th>
                        ${(currentTab === 'sales' || currentTab === 'services') ? '<th>تكلفة القطعة</th>' : ''}
                        <th>سعر القطعة</th>
                        <th>مجموع السعر</th>
                        ${(currentTab === 'sales' || currentTab === 'services') ? '<th>مكسب الصنف</th>' : ''}
                        <th>وقت البيع</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة صفوف البيانات
    data.forEach(item => {
        const status = item.status === 'pending' 
            ? '<span class="status-frame pending">انتظار</span>' 
            : item.status === 'delayed' 
                ? '<span class="status-frame delayed">مؤجل</span>' 
                : '<span class="status-frame confirmed">مؤكد</span>';
        
        const saleTime = item.sale_time ? formatDateTime(null, item.sale_time) : '-';
        const cost = (currentTab === 'sales' || currentTab === 'services') ? formatNumber(item.cost || 0) : '';
        const profit = (currentTab === 'sales' || currentTab === 'services') ? formatNumber(item.profit || 0) : '';
        
        tableHtml += `
            <tr>
                <td>${item.sale_id}</td>
                <td>${item.username}</td>
                <td>${item.name}</td>
                <td>${formatNumber(item.quantity)}</td>
                ${(currentTab === 'sales' || currentTab === 'services') ? `<td>${cost}</td>` : ''}
                <td>${formatNumber(item.price)}</td>
                <td>${formatNumber(item.total_amount)}</td>
                ${(currentTab === 'sales' || currentTab === 'services') ? `<td>${profit}</td>` : ''}
                <td class="sale-time">${saleTime}</td>
                <td>${status}</td>
            </tr>
        `;
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('dailyDetailsContent').innerHTML = tableHtml;
    modalOpen = false;
}

// دالة لإغلاق نافذة تفاصيل اليوم
function closeDailyDetailsModal() {
    const modal = document.getElementById('dailyDetailsModal');
    if (modal) {
        modal.remove();
    }
    modalOpen = false;
}

// دالة لتحميل بيانات تقرير المبيعات
function loadSalesReportData(colors, reportType = 'sales') {
    const storeId = window.encryptedStoreId || '';
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    const statusFilter = document.getElementById('status-filter').value;
    const salesReportContent = document.getElementById('salesReportContent');
    
    // إعداد المعاملات
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (statusFilter) params.append('status', statusFilter);
    if (storeId) params.append('store_id', storeId);
    params.append('report_type', 'comprehensive');
    
    // تحديد نوع المرتجعات بناءً على نوع التقرير
    const returnsType = reportType === 'services' ? 'service_returns' : 'returns';
    
    // تحميل بيانات المبيعات/الخدمات والمرتجعات
    Promise.all([
        fetch(`get_sales_report.php?${params.toString()}&data_type=${reportType}`).then(r => r.json()),
        fetch(`get_sales_report.php?${params.toString()}&data_type=${returnsType}`).then(r => r.json())
    ]).then(([salesData, returnsData]) => {
        let html = '';
        
        if (salesData.success && returnsData.success) {
            const sales = salesData.data;
            const returns = returnsData.data;
            
            // حساب الإجماليات
            const totalSalesAmount = sales.total_amount || 0;
            const totalSalesProfit = sales.total_profit || 0;
            const totalSalesOrders = sales.total_orders || 0;
            const totalSalesQuantity = sales.total_quantity || 0;
            const totalSalesItems = sales.total_items || 0;
            const totalCollected = sales.total_collected || 0;
            const totalRemaining = sales.total_remaining || 0;
            const avgOrderValue = sales.avg_order_value || 0;
            const totalDiscounts = sales.total_discounts || 0;
            const uniqueCustomers = sales.unique_customers || 0;
            const uniqueItems = sales.unique_items || 0;
            const activeDays = sales.active_days || 0;
            const dailyAvgSales = sales.daily_avg_sales || 0;
            const dailyAvgOrders = sales.daily_avg_orders || 0;
            
            const totalReturnsAmount = returns.total_amount || 0;
            const totalReturnsOrders = returns.total_orders || 0;
            const totalReturnsQuantity = returns.total_quantity || 0;
            
            const netSalesAmount = totalSalesAmount - totalReturnsAmount;
            const netProfit = totalSalesProfit; // المرتجعات لا تؤثر على الربح المحسوب مسبقاً
            
            // إحصائيات المبيعات الأساسية
            html += `
                <div style="display: grid; gap: 15px; margin-bottom: 25px;">
                    <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-shopping-cart"></i>
                        إحصائيات المبيعات الأساسية
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div style="background: ${colors.salesBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.salesBorder}; box-shadow: ${colors.shadow};">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <div style="color: ${colors.salesText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي المبيعات</div>
                                    <div style="color: ${colors.salesText}; font-size: 20px; font-weight: 700;">${formatNumber(totalSalesAmount)} جنيه</div>
                                    <div style="color: ${colors.salesText}; font-size: 12px; margin-top: 5px;">${totalSalesOrders} فاتورة - ${totalSalesItems} صنف</div>
                                </div>
                                <div style="background: linear-gradient(135deg, #3498db, #2980b9); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-chart-line" style="color: white; font-size: 20px;"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div style="background: ${colors.profitBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.profitBorder}; box-shadow: ${colors.shadow};">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <div style="color: ${colors.profitText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي الأرباح</div>
                                    <div style="color: ${colors.profitText}; font-size: 20px; font-weight: 700;">${formatNumber(totalSalesProfit)} جنيه</div>
                                    <div style="color: ${colors.profitText}; font-size: 12px; margin-top: 5px;">هامش ربح: ${totalSalesAmount > 0 ? formatNumber((totalSalesProfit / totalSalesAmount) * 100) : 0}%</div>
                                </div>
                                <div style="background: linear-gradient(135deg, #28a745, #20c997); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-dollar-sign" style="color: white; font-size: 20px;"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div style="background: ${colors.pendingBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.pendingBorder}; box-shadow: ${colors.shadow};">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <div style="color: ${colors.pendingText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي الكميات</div>
                                    <div style="color: ${colors.pendingText}; font-size: 20px; font-weight: 700;">${formatNumber(totalSalesQuantity)} قطعة</div>
                                    <div style="color: ${colors.pendingText}; font-size: 12px; margin-top: 5px;">متوسط الكمية: ${totalSalesOrders > 0 ? formatNumber(totalSalesQuantity / totalSalesOrders) : 0}</div>
                                </div>
                                <div style="background: linear-gradient(135deg, #ffc107, #e0a800); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-boxes" style="color: white; font-size: 20px;"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div style="background: ${colors.confirmedBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.confirmedBorder}; box-shadow: ${colors.shadow};">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <div style="color: ${colors.confirmedText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">متوسط قيمة الفاتورة</div>
                                    <div style="color: ${colors.confirmedText}; font-size: 20px; font-weight: 700;">${formatNumber(avgOrderValue)} جنيه</div>
                                    <div style="color: ${colors.confirmedText}; font-size: 12px; margin-top: 5px;">إجمالي الخصومات: ${formatNumber(totalDiscounts)}</div>
                                </div>
                                <div style="background: linear-gradient(135deg, #17a2b8, #138496); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-calculator" style="color: white; font-size: 20px;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
                            
            // تقرير مبيعات المستخمين
            if (sales.user_sales_report && sales.user_sales_report.length > 0) {
                html += `
                    <div style="margin-top: 25px;">
                        <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-users"></i>
                            تقرير مبيعات المستخدمين
                        </h4>
                        <div style="display: grid; gap: 10px;">
                `;
                sales.user_sales_report.forEach((user, index) => {
                    const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                    html += `
                        <div style="background: ${colors.itemBg}; padding: 15px; border-radius: 8px; display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; gap: 10px; border: 1px solid ${colors.itemBorder}; box-shadow: ${colors.shadow};">
                            <div style="display: flex; align-items: center; gap: 15px; flex-grow: 1; min-width: 150px;">
                                <span style="font-size: 18px; font-weight: bold; color: ${colors.text};">${rankIcon}</span>
                                <span style="font-weight: 600; color: ${colors.text};">${user.username}</span>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px;">
                                <span style="color: ${colors.salesText}; font-weight: bold; font-size: 15px;">${formatNumber(user.total_amount)} جنيه</span>
                                <span style="color: ${colors.profitText}; font-weight: bold; font-size: 14px; opacity: 0.9;">ربح: ${formatNumber(user.total_profit)} جنيه</span>
                            </div>
                        </div>
                    `;
                });
                html += '</div></div>';
            }
            
            // إحصائيات المرتجعات
            if (totalReturnsOrders > 0) {
                html += `
                    <div style="display: grid; gap: 15px; margin-bottom: 25px;">
                        <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-undo"></i>
                            إحصائيات المرتجعات
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="background: ${colors.pendingBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.pendingBorder}; box-shadow: ${colors.shadow};">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div>
                                        <div style="color: ${colors.pendingText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">إجمالي المرتجعات</div>
                                        <div style="color: ${colors.pendingText}; font-size: 20px; font-weight: 700;">${formatNumber(totalReturnsAmount)} جنيه</div>
                                        <div style="color: ${colors.pendingText}; font-size: 12px; margin-top: 5px;">${totalReturnsOrders} فاتورة إرجاع</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #dc3545, #c82333); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-undo" style="color: white; font-size: 20px;"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: ${colors.pendingBg}; padding: 20px; border-radius: 12px; border: 1px solid ${colors.pendingBorder}; box-shadow: ${colors.shadow};">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div>
                                        <div style="color: ${colors.pendingText}; opacity: 0.8; font-size: 14px; margin-bottom: 5px;">كمية المرتجعات</div>
                                        <div style="color: ${colors.pendingText}; font-size: 20px; font-weight: 700;">${formatNumber(totalReturnsQuantity)} قطعة</div>
                                        <div style="color: ${colors.pendingText}; font-size: 12px; margin-top: 5px;">مرتجعة</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #6c757d, #5a6268); width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-box-open" style="color: white; font-size: 20px;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // الإجماليات النهائية
            html += `
                <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                    <div style="background: ${colors.totalBg}; padding: 25px; border-radius: 15px; border: 2px solid ${colors.totalBorder}; text-align: center; position: relative; overflow: hidden;">
                        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, transparent 70%); pointer-events: none;"></div>
                        <div style="position: relative; z-index: 1;">
                            <div style="color: ${colors.totalText}; font-size: 16px; margin-bottom: 10px; font-weight: 600;">
                                <i class="fas fa-calculator" style="margin-left: 8px;"></i>
                                صافي المبيعات (بعد خصم المرتجعات)
                            </div>
                            <div style="color: ${colors.totalText}; font-size: 32px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                ${formatNumber(netSalesAmount)} جنيه
                            </div>
                            <div style="width: 80px; height: 2px; background: ${colors.totalBorder}; margin: 15px auto; border-radius: 1px;"></div>
                            <div style="color: ${colors.totalText}; font-size: 12px; opacity: 0.8;">
                                صافي الربح: ${formatNumber(netProfit)} جنيه
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // تقارير تحليل الأصناف
            if (sales.item_analysis_reports) {
                const reports = sales.item_analysis_reports;
                const reportTitles = {
                    most_sold: { title: 'الأصناف الأكثر مبيعاً (حسب الكمية)', icon: 'fas fa-sort-amount-up', unit: 'قطعة' },
                    highest_revenue: { title: 'الأصناف الأعلى إيراداً', icon: 'fas fa-dollar-sign', unit: 'جنيه' },
                    most_profitable: { title: 'الأصناف الأكثر ربحية', icon: 'fas fa-chart-line', unit: 'جنيه' }
                };

                html += '<div style="margin-top: 25px;">';

                for (const [key, report] of Object.entries(reports)) {
                    if (report.length > 0) {
                        html += `
                            <div style="margin-bottom: 25px;">
                                <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                                    <i class="${reportTitles[key].icon}"></i>
                                    ${reportTitles[key].title}
                                </h4>
                                <div style="display: grid; gap: 10px;">
                        `;
                        report.forEach((item, index) => {
                            const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                            html += `
                                <div style="background: ${colors.itemBg}; padding: 15px; border-radius: 8px; display: grid; grid-template-columns: auto 1fr auto; align-items: center; gap: 15px; border: 1px solid ${colors.itemBorder}; box-shadow: ${colors.shadow};">
                                    <span style="font-size: 18px; font-weight: bold;">${rankIcon}</span>
                                    <span style="font-weight: 600; color: ${colors.text};">${item.name}</span>
                                    <span style="color: ${colors.profitText}; font-weight: bold; font-size: 16px; text-align: left;">${formatNumber(item.value)} ${reportTitles[key].unit}</span>
                                </div>
                            `;
                        });
                        html += '</div></div>';
                    }
                }
                html += '</div>';
            }
            
            // إحصائيات حالة الطلبات
            if (sales.status_breakdown) {
                html += `
                    <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                        <h4 style="color: ${colors.title}; margin-bottom: 15px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-flag"></i>
                            توزيع الطلبات حسب الحالة
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                `;
                
                if (sales.status_breakdown.confirmed > 0) {
                    html += `
                        <div style="background: ${colors.confirmedBg}; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid ${colors.confirmedBorder}; box-shadow: ${colors.shadow};">
                            <div style="font-size: 24px; font-weight: bold; color: ${colors.confirmedText}; margin-bottom: 5px;">${sales.status_breakdown.confirmed}</div>
                            <div style="color: ${colors.confirmedText}; font-size: 14px; opacity: 0.8;">طلبات مؤكدة</div>
                        </div>
                    `;
                }
                
                if (sales.status_breakdown.pending > 0) {
                    html += `
                        <div style="background: ${colors.pendingBg}; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid ${colors.pendingBorder}; box-shadow: ${colors.shadow};">
                            <div style="font-size: 24px; font-weight: bold; color: ${colors.pendingText}; margin-bottom: 5px;">${sales.status_breakdown.pending}</div>
                            <div style="color: ${colors.pendingText}; font-size: 14px; opacity: 0.8;">طلبات معلقة</div>
                        </div>
                    `;
                }
                
                if (sales.status_breakdown.delayed > 0) {
                    html += `
                        <div style="background: ${colors.pendingBg}; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid ${colors.pendingBorder}; box-shadow: ${colors.shadow};">
                            <div style="font-size: 24px; font-weight: bold; color: ${colors.pendingText}; margin-bottom: 5px;">${sales.status_breakdown.delayed}</div>
                            <div style="color: ${colors.pendingText}; font-size: 14px; opacity: 0.8;">طلبات مؤجلة</div>
                        </div>
                    `;
                }
                
                html += '</div></div>';
            }
            
        } else {
            html = `
                <div style="text-align: center; padding: 50px; color: ${colors.text};">
                    <i class="fas fa-exclamation-circle" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p>حدث خطأ في تحميل بيانات التقرير</p>
                </div>
            `;
        }
        
        if (html === '') {
            html = `
                <div style="text-align: center; padding: 50px; color: ${colors.text};">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p>لا توجد بيانات متاحة للفترة المحددة</p>
                </div>
            `;
        }
        
        salesReportContent.innerHTML = html;
    }).catch(error => {
        console.error('Error loading sales report data:', error);
        salesReportContent.innerHTML = `
            <div style="text-align: center; padding: 50px; color: ${colors.pendingText};">
                <i class="fas fa-exclamation-circle" style="font-size: 48px; margin-bottom: 15px;"></i>
                <p>حدث خطأ في تحميل البيانات</p>
            </div>
        `;
    });
}