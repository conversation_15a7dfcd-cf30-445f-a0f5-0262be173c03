// Firebase initialization and FCM token handling
(function() {
  'use strict';

  // Prevent multiple initializations
  if (window.firebaseInitialized) {
    console.log('Firebase already initialized, skipping...');
    return;
  }

  // Firebase configuration
  const firebaseConfig = {
    apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
    authDomain: "macm-84114.firebaseapp.com",
    projectId: "macm-84114",
    storageBucket: "macm-84114.firebasestorage.app",
    messagingSenderId: "860043675105",
    appId: "1:860043675105:web:72586005d5bd035ff8bea0"
  };

  function initializeFirebase() {
    // Check if Firebase is available
    if (typeof firebase === 'undefined') {
      console.log('Firebase not loaded yet, waiting...');
      setTimeout(initializeFirebase, 100);
      return;
    }

    // Initialize Firebase only if not already initialized
    try {
      if (!firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        console.log('Firebase initialized successfully');
      } else {
        console.log('Using existing Firebase app');
      }

      // Mark as initialized
      window.firebaseInitialized = true;

      // Continue with messaging setup
      setupFirebaseMessaging();

    } catch (error) {
      console.error('Firebase initialization error:', error);
      return;
    }
  }

  function setupFirebaseMessaging() {
    // Check if messaging is available
    if (typeof firebase.messaging === 'undefined') {
      console.log('Firebase Messaging not loaded yet, waiting...');
      setTimeout(setupFirebaseMessaging, 100);
      return;
    }

    // Get messaging instance
    const messaging = firebase.messaging();

    // Register the service worker for Firebase Cloud Messaging
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/firebase-messaging-sw.js')
        .then(registration => {
          console.log('Firebase Messaging SW registered:', registration);
          // Set the registration for messaging
          messaging.useServiceWorker(registration);
        })
        .catch(err => {
          console.error('Firebase Messaging SW registration failed:', err);
        });
    }

    // Request permission and get token
    messaging.getToken({ vapidKey: 'BMVJO7rt5hONuPb0UzJm2B9T52CuXtcjsWDmHKXf8ass2zyctrBrjXWncazpezhWSdBbcrr8pPcegRixWaTiSBI' })
      .then((currentToken) => {
        if (currentToken) {
          console.log('FCM token:', currentToken);
          // Save token globally for testing
          window.fcmToken = currentToken;
          // Save the token to the server
          saveTokenToServer(currentToken);
        } else {
          console.log('No registration token available. Request permission to generate one.');
        }
      })
      .catch((err) => {
        console.log('An error occurred while retrieving token. ', err);
      });

    // Handle incoming messages when the app is in the foreground
    messaging.onMessage((payload) => {
      console.log('Message received in foreground:', payload);

      // Display the notification using SweetAlert2 with high z-index
      if (typeof Swal !== 'undefined') {
        Swal.fire({
          title: payload.notification.title,
          text: payload.notification.body,
          icon: 'info',
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          customClass: {
            container: 'firebase-notification-container'
          },
          didOpen: () => {
            // Ensure the notification appears above everything
            const swalContainer = document.querySelector('.swal2-container');
            if (swalContainer) {
              swalContainer.style.zIndex = '99999';
            }
          }
        });
      }

      // Also show native notification with custom icon if permission is granted
      if ('Notification' in window && Notification.permission === 'granted') {
        const notification = new Notification(payload.notification.title, {
          body: payload.notification.body,
          icon: '/uploads/img/logo2.png',
          badge: '/uploads/img/logo2.png',
          tag: 'elwaled-foreground-notification',
          requireInteraction: false,
          silent: false
        });

        // Auto close after 5 seconds
        setTimeout(() => {
          notification.close();
        }, 5000);

        // Handle click event
        notification.onclick = function() {
          window.focus();
          notification.close();
        };
      }

      // Play notification sound if available
      const notificationSound = document.getElementById('notificationSound');
      if (notificationSound) {
        notificationSound.play().catch(e => console.log('Could not play notification sound:', e));
      }

      // Update notifications count and list
      if (typeof updateUnreadCount === 'function') {
        updateUnreadCount();
      }

      // Refresh notifications if popup is open
      if (typeof refreshNotificationsOnNewMessage === 'function') {
        refreshNotificationsOnNewMessage();
      }
    });
  }

  // Function to save the token to the server
  function saveTokenToServer(token) {
    fetch('save_fcm_token.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token: token }),
    })
    .then(response => response.json())
    .then(data => {
      console.log('Token saved to server:', data);
    })
    .catch((error) => {
      console.error('Error saving token to server:', error);
    });
  }

  // Start initialization when DOM is ready or immediately if already ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeFirebase);
  } else {
    initializeFirebase();
  }

})();
