document.addEventListener('DOMContentLoaded', () => {
    const cartKey = 'cart_user';

    // Remove "cart" key from localStorage
    localStorage.removeItem('cart');

    const debounce = (func, delay) => {
        let debounceTimer;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => func.apply(context, args), delay);
        };
    };

    const cart = {
        get: function() {
            return JSON.parse(localStorage.getItem(cartKey)) || [];
        },

        update: function(items) {
            localStorage.setItem(cartKey, JSON.stringify(items));
            this.render();
            this.syncWithServer(items);
        },

        addItemToCart: function(item) {
            if (item.price === null) {
                console.error('Item price is null, cannot add to cart');
                return;
            }
            let items = this.get();
            const existingItem = items.find(cartItem => cartItem.id === item.id);
            if (existingItem) {
                existingItem.quantity += 1; // Increase quantity by 1 if the item already exists
            } else {
                item.quantity = 1; // Ensure the quantity is set to 1
                items.push(item);
            }
            this.update(items);
            updateCartCount(); // Update cart count after adding item
            this.showVisualEffect('added', item.name);

            // Remove "cart" key from localStorage after adding item
            localStorage.removeItem('cart');
        },

        remove: function(itemId) {
            let items = this.get();
            items = items.filter(item => item.id !== itemId);
            this.update(items);
            updateCartCount(); // Update cart count after removing item
            this.showVisualEffect('removed');
        },

        syncWithServer: debounce(async function(items) {
            try {
                const res = await fetch('update_cart.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ items })
                });

                if (!res.ok) throw new Error('Failed to sync cart');
            } catch (error) {
                console.error('Cart sync error:', error);
                showAlert('حدث خطأ في حفظ البيانات، يرجى المحاولة لاحقاً');
            }
        }, 300),

        render: function() {
            const container = document.getElementById('cart-items-list');
            const items = this.get();

            if (!items.length) {
                container.innerHTML = '<li class="list-group-item">لا توجد أصناف في السلة.</li>';
                return;
            }

            const fragment = document.createDocumentFragment();

            items.forEach(item => {
                const li = document.createElement('li');
                li.className = 'list-group-item d-flex justify-content-between align-items-center';
                li.innerHTML = `
                    <div class="flex-grow-1">
                        <h5 class="mb-1">${item.name}</h5>
                        <small>${item.quantity} × ${Number(item.price).toFixed(2)} دينار</small>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <span class="text-primary">${(item.quantity * Number(item.price)).toFixed(2)} دينار</span>
                        <button class="btn btn-danger btn-sm remove-btn" data-id="${item.id}">
                            حذف
                        </button>
                    </div>
                `;

                li.querySelector('.remove-btn').addEventListener('click', () => {
                    cart.remove(item.id);
                });

                fragment.appendChild(li);
            });

            container.innerHTML = '';
            container.appendChild(fragment);
        },

        showVisualEffect: function(action, itemName = '') {
            const effectBox = document.createElement('div');
            effectBox.className = `alert alert-${action === 'added' ? 'success' : 'danger'} fixed-top m-3`;
            effectBox.textContent = action === 'added' ? `تم إضافة ${itemName} إلى العربة` : 'تم إزالة الصنف من العربة';
            document.body.appendChild(effectBox);
            setTimeout(() => effectBox.remove(), 3000);
        }
    };

    const updateCartCount = () => {
        const cartCount = document.getElementById('cart-count');
        const cartTotalItems = document.getElementById('cart-total-items');
        const totalQuantity = cart.get().reduce((sum, item) => sum + item.quantity, 0);
        if (cartCount && cartTotalItems) {
            cartCount.textContent = `(${totalQuantity})`;
            cartTotalItems.textContent = totalQuantity;
        }
    };

    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', (event) => {
            const itemCard = event.target.closest('.item-card');
            const item = {
                id: itemCard.getAttribute('data-id'),
                name: itemCard.getAttribute('data-name'),
                price: parseFloat(itemCard.getAttribute('data-price')),
                img: itemCard.getAttribute('data-img'),
                quantity: 0 // Default quantity
            };
            cart.addItemToCart(item);
        });
    });

    const clearCartButton = document.getElementById('clear-cart');
    if (clearCartButton) {
        clearCartButton.addEventListener('click', () => {
            cart.update([]);
        });
    }

    // Render cart items on page load
    cart.render();
    updateCartCount();
});