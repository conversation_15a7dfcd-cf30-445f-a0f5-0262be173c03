// نظام الحفظ المحلي المحسن مع دعم العمل بدون اتصال
let isOnline = navigator.onLine;
let pendingSaves = [];
let syncInProgress = false;
let lastSyncAttempt = 0;
const SYNC_RETRY_DELAY = 5000; // 5 ثوان
const MAX_RETRY_ATTEMPTS = 3;

// مراقبة حالة الاتصال
function initializeConnectionMonitoring() {
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // فحص دوري لحالة الاتصال
    setInterval(checkConnectionStatus, 10000); // كل 10 ثوان
    
    updateConnectionStatus();
}

function handleOnline() {
    console.log('اتصال الإنترنت متاح');
    isOnline = true;
    updateConnectionStatus();
    syncPendingData();
}

function handleOffline() {
    console.log('انقطع اتصال الإنترنت - التبديل للوضع المحلي');
    isOnline = false;
    updateConnectionStatus();
}

function checkConnectionStatus() {
    const wasOnline = isOnline;
    isOnline = navigator.onLine;
    
    if (!wasOnline && isOnline) {
        handleOnline();
    } else if (wasOnline && !isOnline) {
        handleOffline();
    }
}

function updateConnectionStatus() {
    const statusIndicator = document.getElementById('connectionStatus');
    const statusText = document.getElementById('connectionStatusText');
    
    if (statusIndicator && statusText) {
        if (isOnline) {
            statusIndicator.className = 'connection-status online';
            statusText.innerHTML = '<i class="fas fa-wifi"></i> متصل';
            
            // إخفاء تنبيه عدم الاتصال إذا كان ظاهراً
            const offlineAlert = document.getElementById('offlineAlert');
            if (offlineAlert) {
                offlineAlert.style.display = 'none';
            }
        } else {
            statusIndicator.className = 'connection-status offline';
            statusText.innerHTML = '<i class="fas fa-wifi-slash"></i> غير متصل';
            
            // إظهار تنبيه عدم الاتصال
            showOfflineAlert();
        }
    }
}

function showOfflineAlert() {
    let offlineAlert = document.getElementById('offlineAlert');
    if (!offlineAlert) {
        offlineAlert = document.createElement('div');
        offlineAlert.id = 'offlineAlert';
        offlineAlert.className = 'alert alert-warning offline-alert';
        offlineAlert.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <strong>وضع عدم الاتصال:</strong>
                    يتم حفظ البيانات محلياً وسيتم مزامنتها عند عودة الاتصال
                </div>
            </div>
        `;
        
        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(offlineAlert, container.firstChild);
        }
    }
    offlineAlert.style.display = 'block';
}

// حفظ البيانات محلياً
function saveToLocalStorage(quantities) {
    try {
        const saveData = {
            quantities: quantities,
            timestamp: Date.now(),
            inventory_id: window.inventoryId || ''
        };
        
        localStorage.setItem('inventory_offline_data', JSON.stringify(saveData));
        localStorage.setItem('quantities', JSON.stringify(quantities)); // للتوافق مع النظام القديم
        
        console.log('تم حفظ البيانات محلياً:', saveData);
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات محلياً:', error);
        return false;
    }
}

// تحميل البيانات من التخزين المحلي
function loadFromLocalStorage() {
    try {
        const savedData = localStorage.getItem('inventory_offline_data');
        if (savedData) {
            const data = JSON.parse(savedData);
            if (data.inventory_id === (window.inventoryId || '')) {
                return data.quantities || {};
            }
        }
        
        // التوافق مع النظام القديم
        const oldData = localStorage.getItem('quantities');
        return oldData ? JSON.parse(oldData) : {};
    } catch (error) {
        console.error('خطأ في تحميل البيانات المحلية:', error);
        return {};
    }
}

// إضافة عملية حفظ للقائمة المعلقة
function addToPendingSaves(quantities) {
    const saveOperation = {
        id: Date.now(),
        quantities: quantities,
        timestamp: Date.now(),
        attempts: 0,
        inventory_id: window.inventoryId || ''
    };
    
    pendingSaves.push(saveOperation);
    console.log('تمت إضافة عملية حفظ للقائمة المعلقة:', saveOperation);
    
    // حفظ القائمة المعلقة في التخزين المحلي
    localStorage.setItem('pending_saves', JSON.stringify(pendingSaves));
    
    // تحديث عداد العمليات المعلقة
    updatePendingCounter();
}

// تم نقل دوال إكمال الجرد إلى ملف inventory_completion.js

// مزامنة البيانات المعلقة
async function syncPendingData() {
    if (syncInProgress || !isOnline || pendingSaves.length === 0) {
        return;
    }
    
    syncInProgress = true;
    console.log('بدء مزامنة البيانات المعلقة:', pendingSaves.length, 'عملية');
    
    const successfulSaves = [];
    
    for (const saveOperation of pendingSaves) {
        try {
            const success = await syncSingleOperation(saveOperation);
            if (success) {
                successfulSaves.push(saveOperation.id);
                console.log('تمت مزامنة العملية بنجاح:', saveOperation.id);
            } else {
                saveOperation.attempts++;
                if (saveOperation.attempts >= MAX_RETRY_ATTEMPTS) {
                    console.error('فشل في مزامنة العملية بعد عدة محاولات:', saveOperation.id);
                    successfulSaves.push(saveOperation.id); // إزالة من القائمة لتجنب المحاولة مرة أخرى
                }
            }
        } catch (error) {
            console.error('خطأ في مزامنة العملية:', saveOperation.id, error);
            saveOperation.attempts++;
        }
    }
    
    // إزالة العمليات المكتملة
    pendingSaves = pendingSaves.filter(op => !successfulSaves.includes(op.id));
    localStorage.setItem('pending_saves', JSON.stringify(pendingSaves));
    
    syncInProgress = false;
    
    // تحديث عداد العمليات المعلقة
    updatePendingCounter();

// تم نقل دوال إكمال الجرد إلى ملف inventory_completion.js    
    if (successfulSaves.length > 0) {
        showSyncSuccessMessage(successfulSaves.length);
    }
    
    console.log('انتهت مزامنة البيانات. العمليات المتبقية:', pendingSaves.length);
}

// مزامنة عملية واحدة
async function syncSingleOperation(saveOperation) {
    try {
        const response = await fetch('update_inventory_json.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                inventory_id: saveOperation.inventory_id,
                quantities: saveOperation.quantities
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            return data.success;
        }
        return false;
    } catch (error) {
        console.error('خطأ في مزامنة العملية:', error);
        return false;
    }
}

// إظهار رسالة نجاح المزامنة
function showSyncSuccessMessage(count) {
    Swal.fire({
        icon: 'success',
        title: 'تمت المزامنة بنجاح',
        text: `تم مزامنة ${count} عملية حفظ مع الخادم`,
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
    });
}

// تحميل القائمة المعلقة من التخزين المحلي
function loadPendingSaves() {
    try {
        const saved = localStorage.getItem('pending_saves');
        if (saved) {
            pendingSaves = JSON.parse(saved);
            console.log('تم تحميل العمليات المعلقة:', pendingSaves.length);
        }
    } catch (error) {
        console.error('خطأ في تحميل العمليات المعلقة:', error);
        pendingSaves = [];
    }
    
    // تحديث العداد بعد التحميل
    updatePendingCounter();
}

// تم نقل دوال إكمال الجرد إلى ملف inventory_completion.js

// تحديث عداد العمليات المعلقة
function updatePendingCounter() {
    const pendingBadge = document.getElementById('pendingCount');
    const syncButton = document.getElementById('syncButton');
    
    if (pendingBadge) {
        if (pendingSaves.length > 0) {
            pendingBadge.textContent = pendingSaves.length;
            pendingBadge.style.display = 'inline-block';
            
            // تغيير لون الزر إذا كان هناك عمليات معلقة
            if (syncButton) {
                syncButton.classList.add('has-pending');
            }
        } else {
            pendingBadge.style.display = 'none';
            
            // إزالة تمييز الزر
            if (syncButton) {
                syncButton.classList.remove('has-pending');
            }
        }
    }
}



function saveQuantities() {
    const quantities = {};
    document.querySelectorAll('tr[data-item-id]').forEach(row => {
        const itemId = row.getAttribute('data-item-id');
        const quantityInput = row.querySelector('input[name="closing_quantity[]"]');
        
        if (!itemId || !quantityInput) {
            return;
        }
        
        let quantity = quantityInput.value || '';
        quantity = convertToEnglishNumbers(quantity); // Convert to English before saving
        quantities[itemId] = quantity;
    });
    
    // حفظ في التخزين المحلي دائماً
    saveToLocalStorage(quantities);
}

function loadQuantities() {
    // تحميل من التخزين المحلي أولاً
    const localQuantities = loadFromLocalStorage();
    
    // دمج مع البيانات من الملف إذا وجدت
    const quantities = {...localQuantities};
    
    document.querySelectorAll('tr[data-item-id]').forEach(row => {
        const itemId = row.getAttribute('data-item-id');
        const quantityInput = row.querySelector('input[name="closing_quantity[]"]');
        
        if (!itemId || !quantityInput) {
            return;
        }
        
        if (quantities[itemId] !== undefined) {
            quantityInput.value = convertToEnglishNumbers(quantities[itemId]);
        }
    });
}

function filterItems() {
    const filter = document.getElementById('searchField').value.toLowerCase();
    document.querySelectorAll('tr[data-item-id]').forEach(row => {
        const itemName = row.cells[0].textContent.toLowerCase();
        const barcode = row.getAttribute('data-barcode') ? row.getAttribute('data-barcode').toLowerCase() : '';
        if (itemName.includes(filter) || barcode.includes(filter)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function updateSoldQuantities() {
    document.querySelectorAll('tr[data-item-id]').forEach(row => {
        const totalRecorded = parseFloat(row.querySelector('input[name="total_recorded_quantity[]"]').value) || 0;
        const closingInput = row.querySelector('input[name="closing_quantity[]"]');
        const closingVal = parseFloat(closingInput.value) || 0;
        const sold = totalRecorded - closingVal;
        const soldElement = row.querySelector('.sold_value');
        
        soldElement.innerText = sold.toFixed(2);
        
        // تحديث ألوان الشارات
        if(sold < 0) {
            soldElement.className = 'sold_value badge badge-danger';
            row.style.backgroundColor = "#ffe6e6";
            row.style.borderLeft = "4px solid #dc3545";
        } else if(sold > 0) {
            soldElement.className = 'sold_value badge badge-success';
            row.style.backgroundColor = "";
            row.style.borderLeft = "";
        } else {
            soldElement.className = 'sold_value badge badge-secondary';
            row.style.backgroundColor = "";
            row.style.borderLeft = "";
        }
    });
}

function convertToEnglishNumbers(input) {
    const arabicToEnglishMap = {
        '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
    };
    return input.replace(/[٠-٩]/g, char => arabicToEnglishMap[char] || char);
}

function clearSearch() {
    const searchField = document.getElementById('searchField');
    searchField.value = '';
    filterItems(); // Reapply filter to show all items
}

// تم إزالة saveQuantitiesToJson واستبدالها بـ saveAllQuantities المحسنة

// This DOMContentLoaded will be merged with the main one below

// Enhanced page unload handling with offline support
window.addEventListener('beforeunload', function(e) {
    // Save to local storage as backup
    saveQuantities();
    
    // Force save to server before leaving if online
    const quantities = {};
    document.querySelectorAll('tr[data-item-id]').forEach(row => {
        // استخدام getAttribute بدلاً من dataset
        const itemId = row.getAttribute('data-item-id');
        const quantityInput = row.querySelector('input[name="closing_quantity[]"]');
        
        if (!itemId || !quantityInput) {
            return;
        }
        
        const quantity = convertToEnglishNumbers(quantityInput.value || '');
        
        // Always save the quantity to maintain proper mapping
        quantities[itemId] = quantity;
    });
    
    // Filter out empty quantities for saving
    const quantitiesToSave = {};
    Object.keys(quantities).forEach(itemId => {
        const quantity = quantities[itemId];
        if (quantity !== '' && quantity !== null && quantity !== undefined) {
            quantitiesToSave[itemId] = quantity;
        }
    });
    
    console.log('Saving on page unload:', quantitiesToSave);
    
    // Save locally first
    saveToLocalStorage(quantities);
    
    // Try to save to server if online and there's data to save
    if (isOnline && Object.keys(quantitiesToSave).length > 0) {
        const data = JSON.stringify({
            inventory_id: window.inventoryId || '',
            quantities: quantitiesToSave
        });
        
        if (navigator.sendBeacon) {
            navigator.sendBeacon('update_inventory_json.php', data);
        } else {
            // Fallback for older browsers
            fetch('update_inventory_json.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: data,
                keepalive: true
            }).catch(error => {
                console.error('Error saving on unload:', error);
                // Add to pending saves if failed
                addToPendingSaves(quantitiesToSave);
            });
        }
    } else if (!isOnline && Object.keys(quantitiesToSave).length > 0) {
        // Add to pending saves if offline
        addToPendingSaves(quantitiesToSave);
    }
});

window.addEventListener('load', loadQuantities);

// Auto-save every 30 seconds as additional backup and attempt sync
setInterval(function() {
    saveAllQuantities();
    
    // محاولة مزامنة البيانات المعلقة إذا كان الاتصال متاحاً
    if (isOnline && pendingSaves.length > 0) {
        syncPendingData();
    }
}, 30000);

window.addEventListener('scroll', function() {
    // شريط البحث الثابت
    const searchContainer = document.querySelector('.search-container-enhanced');
    if (searchContainer) {
        if (window.pageYOffset > 200) {
            searchContainer.classList.add('sticky');
        } else {
            searchContainer.classList.remove('sticky');
        }
    }
    
    // زر العودة للأعلى
    const backToTop = document.querySelector('.back-to-top');
    if (backToTop) {
        if (window.pageYOffset > 300) {
            backToTop.classList.add('show');
        } else {
            backToTop.classList.remove('show');
        }
    }
});

// Prevent zoom on multi-touch events
document.addEventListener('touchstart', function(event) {
    if (event.touches.length > 1) {
        event.preventDefault();
    }
}, { passive: false });

// منع التكبير باللمس (gesture zoom)
document.addEventListener('gesturestart', function (e) {
    e.preventDefault();
});

function liveCompute() {
    calcCurrent = document.getElementById('calcDisplay').value;
    try {
        const result = eval(calcCurrent);
        document.getElementById('calcResult').innerText = result !== undefined ? result : '0'; // Ensure result is always visible
    } catch (e) {
        const lastValidResult = eval(calcCurrent.slice(0, -1)) || '0'; // Evaluate up to the last valid character
        document.getElementById('calcResult').innerText = lastValidResult; // Show the last valid result
    }
}

function downloadExcel() {
    const rows = [];
    rows.push(["اسم الصنف", "جملة الصنف", "الكمية"]);

    document.querySelectorAll('tr[data-item-id]').forEach(row => {
        const itemName = row.cells[0].textContent.trim();
        const itemCost = row.cells[1].textContent.trim();
        const userQuantity = row.querySelector('input[name="closing_quantity[]"]').value.trim();
        rows.push([itemName, itemCost, userQuantity]);
    });

    // Create CSV content with UTF-8 BOM for proper Arabic encoding
    let csvContent = "\uFEFF" + rows.map(e => e.join(",")).join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", "inventory_details.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Enhanced debounce function to avoid frequent AJAX calls
// هذه هي الدالة الوحيدة للحفظ المحسنة مع دعم العمل بدون اتصال
let saveTimer;
let lastSavedData = {};

function saveAllQuantities() {
    clearTimeout(saveTimer);
    saveTimer = setTimeout(() => {
        const quantities = {};
        let hasChanges = false;
        
        document.querySelectorAll('tr[data-item-id]').forEach(row => {
            // استخدام getAttribute بدلاً من dataset لضمان الحصول على القيمة الصحيحة
            const itemId = row.getAttribute('data-item-id');
            const quantityInput = row.querySelector('input[name="closing_quantity[]"]');
            
            if (!itemId || !quantityInput) {
                console.warn('Missing item ID or quantity input for row:', row);
                return;
            }
            
            const quantity = convertToEnglishNumbers(quantityInput.value || '');
            
            console.log(`Processing item ${itemId} with quantity: ${quantity}`);
            
            // Always save the quantity, even if it's empty or zero, to maintain proper mapping
            quantities[itemId] = quantity;
            
            // Check if data has changed
            if (lastSavedData[itemId] !== quantity) {
                hasChanges = true;
            }
        });
        
        console.log('Quantities to save:', quantities);
        
        // حفظ محلي دائماً (حتى لو كان الاتصال متاحاً)
        saveToLocalStorage(quantities);
        
        // Filter out empty quantities for saving (but keep them in the object for proper mapping)
        const quantitiesToSave = {};
        Object.keys(quantities).forEach(itemId => {
            const quantity = quantities[itemId];
            if (quantity !== '' && quantity !== null && quantity !== undefined) {
                quantitiesToSave[itemId] = quantity;
            }
        });
        
        console.log('Filtered quantities to save:', quantitiesToSave);
        
        // Only save if there are actual changes and valid quantities
        if (hasChanges && Object.keys(quantitiesToSave).length > 0) {
            if (isOnline) {
                // محاولة الحفظ على الخادم
                fetch('update_inventory_json.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        inventory_id: window.inventoryId || '',
                        quantities: quantitiesToSave
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Update lastSavedData with all quantities (including empty ones) for proper change tracking
                        lastSavedData = {...quantities};
                        console.log('Save successful, updated lastSavedData:', lastSavedData);
                        // Show subtle success indicator
                        showSaveIndicator('success');
                    } else {
                        console.error('Save failed:', data.message);
                        // إضافة للقائمة المعلقة في حالة فشل الحفظ
                        addToPendingSaves(quantitiesToSave);
                        showSaveIndicator('offline');
                    }
                })
                .catch(error => {
                    console.error('Error saving data:', error);
                    // إضافة للقائمة المعلقة في حالة انقطاع الاتصال
                    addToPendingSaves(quantitiesToSave);
                    showSaveIndicator('offline');
                    
                    // تحديث حالة الاتصال
                    isOnline = false;
                    updateConnectionStatus();
                });
            } else {
                // إضافة للقائمة المعلقة مباشرة إذا كان الاتصال منقطعاً
                addToPendingSaves(quantitiesToSave);
                showSaveIndicator('offline');
                console.log('البيانات محفوظة محلياً - سيتم المزامنة عند عودة الاتصال');
            }
        } else {
            console.log('No changes detected or no valid quantities to save');
        }
    }, 500); // Increased debounce time for better performance
}

function showSaveIndicator(type) {
    // Create or update save indicator
    let indicator = document.getElementById('saveIndicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'saveIndicator';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(-20px);
        `;
        document.body.appendChild(indicator);
    }
    
    if (type === 'success') {
        indicator.style.backgroundColor = '#28a745';
        indicator.innerHTML = '<i class="fas fa-check"></i> تم الحفظ على الخادم';
    } else if (type === 'offline') {
        indicator.style.backgroundColor = '#ffc107';
        indicator.style.color = '#212529';
        indicator.innerHTML = '<i class="fas fa-save"></i> محفوظ محلياً - سيتم المزامنة لاحقاً';
    } else {
        indicator.style.backgroundColor = '#dc3545';
        indicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> خطأ في الحفظ';
    }
    
    // Show indicator
    indicator.style.opacity = '1';
    indicator.style.transform = 'translateY(0)';
    
    // Hide after 3 seconds for offline status, 2 seconds for others
    const hideDelay = type === 'offline' ? 3000 : 2000;
    setTimeout(() => {
        indicator.style.opacity = '0';
        indicator.style.transform = 'translateY(-20px)';
    }, hideDelay);
}

// This will be merged with the main DOMContentLoaded below

// ميزة قارئ الباركود
let globalBarcode = "";
let barcodeTimer = null;
let highlightedRow = null;

document.addEventListener('keypress', function(e) {
    // تجاهل إذا كان التركيز على حقل إدخال أو إذا كان المفتاح Enter
    if (document.activeElement.tagName.toLowerCase() === "input" || e.key === "Enter") return;

    globalBarcode += e.key;

    if (barcodeTimer) clearTimeout(barcodeTimer);

    barcodeTimer = setTimeout(() => {
        processBarcode(globalBarcode);
        globalBarcode = "";
    }, 150);
});

function processBarcode(barcode) {
    // البحث عن الصنف بناءً على الباركود
    const itemRows = document.querySelectorAll('tr[data-item-id]');
    let found = null;

    itemRows.forEach(row => {
        const rowBarcode = row.getAttribute('data-barcode');
        if (rowBarcode && rowBarcode.trim() === barcode.trim()) {
            found = row;
        }
    });

    if (found) {
        // إزالة التمييز من الصف السابق إن وجد
        if (highlightedRow) {
            highlightedRow.classList.remove('barcode-highlighted');
        }

        // تمييز الصف الجديد باللون الأزرق
        found.classList.add('barcode-highlighted');
        highlightedRow = found;

        // نقل الصف لأول الجدول
        const tbody = found.parentNode;
        tbody.insertBefore(found, tbody.firstChild);

        // التركيز على حقل الكمية
        const quantityInput = found.querySelector('input[name="closing_quantity[]"]');
        if (quantityInput) {
            quantityInput.focus();
            quantityInput.select();
        }

        // التمرير للصف
        found.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // إظهار رسالة نجاح
        Swal.fire({
            icon: 'success',
            title: 'تم العثور على الصنف',
            text: `تم العثور على: ${found.cells[0].textContent}`,
            timer: 2000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    } else {
        // إظهار رسالة خطأ
        Swal.fire({
            icon: 'error',
            title: 'صنف غير موجود',
            text: "لم يتم العثور على صنف بهذا الباركود: " + barcode,
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    }
}

// إزالة التمييز عند كتابة كمية - مدمج مع مستمع الأحداث الرئيسي
document.addEventListener('input', function(e) {
    if (e.target.name === 'closing_quantity[]' && e.target.value.trim() !== '') {
        const row = e.target.closest('tr');
        if (row && row.classList.contains('barcode-highlighted')) {
            row.classList.remove('barcode-highlighted');
            if (highlightedRow === row) {
                highlightedRow = null;
            }
        }
        // لا نحتاج لتحديث الإحصائيات هنا لأنها تتم في مستمع الأحداث الرئيسي
    }
});

// وظائف التحسينات الجديدة
function updateProgress() {
    const totalItems = document.querySelectorAll('tr[data-item-id]').length;
    let filledItems = 0;
    
    // عد الحقول التي تحتوي على قيم فعلية
    document.querySelectorAll('input[name="closing_quantity[]"]').forEach(input => {
        if (input.value.trim() !== '' && input.value.trim() !== '0') {
            filledItems++;
        }
    });
    
    const percentage = totalItems > 0 ? (filledItems / totalItems) * 100 : 0;
    
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-text');
    
    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }
    if (progressText) {
        progressText.textContent = `${filledItems} صنف مسجل من ${totalItems} (${Math.round(percentage)}%)`;
    }
}

function updateStats() {
    const totalItems = document.querySelectorAll('tr[data-item-id]').length;
    let filledItems = 0;
    
    // عد الحقول التي تحتوي على قيم فعلية
    document.querySelectorAll('input[name="closing_quantity[]"]').forEach(input => {
        if (input.value.trim() !== '' && input.value.trim() !== '0') {
            filledItems++;
        }
    });
    
    const emptyItems = totalItems - filledItems;
    
    document.getElementById('total-items').textContent = totalItems;
    document.getElementById('completed-items').textContent = filledItems;
    document.getElementById('remaining-items').textContent = emptyItems;
}

function updateFloatingSummary() {
    const floatingSummary = document.querySelector('.floating-summary');
    const totalItems = document.querySelectorAll('tr[data-item-id]').length;
    let filledItems = 0;
    
    // عد الحقول التي تحتوي على قيم فعلية
    document.querySelectorAll('input[name="closing_quantity[]"]').forEach(input => {
        if (input.value.trim() !== '' && input.value.trim() !== '0') {
            filledItems++;
        }
    });
    
    const percentage = totalItems > 0 ? Math.round((filledItems/totalItems)*100) : 0;
    
    if (floatingSummary) {
        const summaryContent = floatingSummary.querySelector('.summary-content');
        if (summaryContent) {
            summaryContent.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">تقدم الجرد</div>
                <div>${filledItems} مسجل/${totalItems}</div>
                <div style="font-size: 0.8em; color: #666;">${percentage}% مكتمل</div>
            `;
        }
        
        if (filledItems > 0) {
            floatingSummary.classList.add('show');
        } else {
            floatingSummary.classList.remove('show');
        }
    }
}

function filterByStatus(status) {
    const rows = document.querySelectorAll('tr[data-item-id]');
    
    // إزالة active من جميع الأزرار
    document.querySelectorAll('.quick-btn').forEach(btn => btn.classList.remove('active'));
    
    rows.forEach(row => {
        const input = row.querySelector('input[name="closing_quantity[]"]');
        const hasValue = input.value.trim() !== '' && input.value.trim() !== '0';
        
        switch(status) {
            case 'all':
                row.style.display = '';
                document.querySelector('[data-filter="all"]').classList.add('active');
                break;
            case 'completed':
                row.style.display = hasValue ? '' : 'none';
                document.querySelector('[data-filter="completed"]').classList.add('active');
                break;
            case 'pending':
                row.style.display = !hasValue ? '' : 'none';
                document.querySelector('[data-filter="pending"]').classList.add('active');
                break;
        }
    });
}

function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// مزامنة يدوية للبيانات
function manualSync() {
    const syncButton = document.getElementById('syncButton');
    
    if (!isOnline) {
        Swal.fire({
            icon: 'warning',
            title: 'لا يوجد اتصال بالإنترنت',
            text: 'يرجى التأكد من اتصال الإنترنت قبل المزامنة',
            timer: 3000,
            showConfirmButton: false
        });
        return;
    }
    
    if (pendingSaves.length === 0) {
        Swal.fire({
            icon: 'info',
            title: 'لا توجد بيانات للمزامنة',
            text: 'جميع البيانات محدثة بالفعل',
            timer: 2000,
            showConfirmButton: false
        });
        return;
    }
    
    // تعطيل الزر أثناء المزامنة
    syncButton.disabled = true;
    syncButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المزامنة...';
    
    syncPendingData().then(() => {
        // إعادة تفعيل الزر
        syncButton.disabled = false;
        syncButton.innerHTML = '<i class="fas fa-sync-alt"></i> مزامنة البيانات';
        
        if (pendingSaves.length === 0) {
            Swal.fire({
                icon: 'success',
                title: 'تمت المزامنة بنجاح',
                text: 'تم مزامنة جميع البيانات مع الخادم',
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'مزامنة جزئية',
                text: `تم مزامنة بعض البيانات. يتبقى ${pendingSaves.length} عملية`,
                timer: 3000,
                showConfirmButton: false
            });
        }
    }).catch(error => {
        console.error('خطأ في المزامنة اليدوية:', error);
        syncButton.disabled = false;
        syncButton.innerHTML = '<i class="fas fa-sync-alt"></i> مزامنة البيانات';
        
        Swal.fire({
            icon: 'error',
            title: 'فشل في المزامنة',
            text: 'حدث خطأ أثناء مزامنة البيانات',
            timer: 3000,
            showConfirmButton: false
        });
    });
}

// تم دمج مستمعي الأحداث للتمرير في مكان واحد أعلاه

// عرض معلومات التخزين المحلي
function showLocalStorageInfo() {
    const localData = loadFromLocalStorage();
    // حساب عدد الأصناف التي لها قيم فعلية فقط
    const actualLocalDataCount = Object.keys(localData).filter(itemId => {
        const quantity = localData[itemId];
        return quantity !== '' && quantity !== null && quantity !== undefined && 
               quantity !== '0' && parseFloat(quantity) > 0;
    }).length;
    const totalLocalDataCount = Object.keys(localData).length;
    const pendingCount = pendingSaves.length;
    
    // حساب حجم البيانات المحلية
    const localStorageSize = JSON.stringify(localData).length;
    const pendingSavesSize = JSON.stringify(pendingSaves).length;
    
    // تحديد آخر وقت حفظ
    const offlineData = localStorage.getItem('inventory_offline_data');
    let lastSaveTime = 'غير محدد';
    if (offlineData) {
        try {
            const data = JSON.parse(offlineData);
            if (data.timestamp) {
                lastSaveTime = new Date(data.timestamp).toLocaleString('ar-EG');
            }
        } catch (error) {
            console.error('خطأ في قراءة وقت الحفظ:', error);
        }
    }
    
    const infoHtml = `
        <div style="text-align: right; line-height: 1.8;">
            <h4 style="color: #007bff; margin-bottom: 15px;">
                <i class="fas fa-info-circle"></i> معلومات التخزين المحلي
            </h4>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <strong>البيانات المحفوظة محلياً:</strong>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>عدد الأصناف المسجلة: <span style="color: #28a745; font-weight: bold;">${actualLocalDataCount}</span></li>
                    <li>إجمالي الأصناف المحفوظة: <span style="color: #6c757d;">${totalLocalDataCount}</span></li>
                    <li>حجم البيانات: <span style="color: #17a2b8;">${(localStorageSize / 1024).toFixed(2)} KB</span></li>
                    <li>آخر حفظ: <span style="color: #6c757d;">${lastSaveTime}</span></li>
                </ul>
            </div>
            
            <div style="background: ${pendingCount > 0 ? '#fff3cd' : '#d4edda'}; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <strong>العمليات المعلقة:</strong>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>عدد العمليات المعلقة: <span style="color: ${pendingCount > 0 ? '#856404' : '#155724'}; font-weight: bold;">${pendingCount}</span></li>
                    <li>حجم العمليات المعلقة: <span style="color: #6c757d;">${(pendingSavesSize / 1024).toFixed(2)} KB</span></li>
                    <li>حالة الاتصال: <span style="color: ${isOnline ? '#155724' : '#721c24'}; font-weight: bold;">${isOnline ? 'متصل' : 'غير متصل'}</span></li>
                </ul>
            </div>
            
            <div style="background: #e2e3e5; padding: 15px; border-radius: 8px;">
                <strong>ملاحظات:</strong>
                <ul style="margin: 10px 0; padding-right: 20px; font-size: 14px;">
                    <li>يتم حفظ البيانات تلقائياً في التخزين المحلي</li>
                    <li>العمليات المعلقة سيتم مزامنتها عند عودة الاتصال</li>
                    <li>البيانات المحلية آمنة ولن تفقد عند إغلاق المتصفح</li>
                </ul>
            </div>
        </div>
    `;
    
    Swal.fire({
        html: infoHtml,
        width: '600px',
        showCloseButton: true,
        showConfirmButton: true,
        confirmButtonText: 'حسناً',
        confirmButtonColor: '#007bff',
        customClass: {
            popup: 'text-right'
        }
    });
}

// دالة محسنة لتحديث جميع الواجهات بناءً على البيانات المحملة فقط
function updateAllUIAfterDataLoad() {
    console.log('Starting UI update after data load...');
    
    // تحديث الكلاسات للحقول المملوءة بحذر شديد
    document.querySelectorAll('input[name="closing_quantity[]"]').forEach(input => {
        const value = input.value.trim();
        const hasActualValue = value !== '' && value !== '0' && !isNaN(parseFloat(value)) && parseFloat(value) > 0;
        
        if (hasActualValue) {
            input.classList.add('filled');
            console.log(`Added filled class to input with value: ${value}`);
        } else {
            input.classList.remove('filled');
            // لا نسجل إزالة الكلاس لتجنب الضوضاء في الـ console
        }
    });
    
    // تحديث جميع الإحصائيات والواجهات
    updateStats();
    updateProgress();
    updateFloatingSummary();
    updateSoldQuantities();
    
    // إظهار رسالة إذا تم استرجاع بيانات
    const hasExistingData = window.hasExistingData || false;
    if (hasExistingData) {
        console.log('تم استرجاع البيانات المحفوظة مسبقاً وتحديث لوحة البيانات');
    }
    
    console.log('UI update completed.');
}

// تحديث الإحصائيات عند تحميل الصفحة - مدمج مع جميع الوظائف
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة نظام مراقبة الاتصال
    initializeConnectionMonitoring();
    
    // تحميل العمليات المعلقة
    loadPendingSaves();
    
    // Load quantities from the JSON file
    const quantitiesFromFile = window.quantitiesFromFile || {};
    
    // تحميل البيانات المحلية
    const localQuantities = loadFromLocalStorage();
    
    // دمج البيانات من الملف والتخزين المحلي
    const mergedQuantities = {...quantitiesFromFile, ...localQuantities};

    // Load existing quantities into inputs with validation
    document.querySelectorAll('tr[data-item-id]').forEach(row => {
        const itemId = row.getAttribute('data-item-id');
        const input = row.querySelector('input[name="closing_quantity[]"]');
        
        if (!itemId || !input) {
            console.warn('Missing item ID or input for row:', row);
            return;
        }
        
        if (mergedQuantities.hasOwnProperty(itemId)) {
            const savedValue = mergedQuantities[itemId];
            
            // Load the saved value regardless of whether it's empty or not
            if (savedValue !== null && savedValue !== undefined) {
                const stringValue = savedValue.toString();
                input.value = stringValue;
                
                // Add filled class only if the value is not empty and not zero
                if (stringValue.trim() !== '' && stringValue.trim() !== '0') {
                    const numericValue = parseFloat(stringValue);
                    if (!isNaN(numericValue) && numericValue > 0) {
                        input.classList.add('filled');
                    }
                }
                
                console.log(`Loaded saved quantity for item ${itemId}: ${savedValue}`);
            }
        }
    });

    // إضافة مستمعي الأحداث لحقول الكمية - مدمج واحد فقط
    document.querySelectorAll('input[name="closing_quantity[]"]').forEach((input, index) => {
        // Add unique identifier for debugging
        input.setAttribute('data-input-index', index);
        
        input.addEventListener('input', function() {
            const row = this.closest('tr[data-item-id]');
            const itemId = row ? row.getAttribute('data-item-id') : 'unknown';
            
            console.log(`Input changed for item ${itemId} (input index: ${index}), new value: ${this.value}`);
            
            // Convert Arabic numbers immediately
            const convertedValue = convertToEnglishNumbers(this.value);
            if (this.value !== convertedValue) {
                this.value = convertedValue;
                console.log(`Converted Arabic numbers for item ${itemId}: ${convertedValue}`);
            }
            
            // Save data using the enhanced function
            saveAllQuantities();
            
            // Update UI
            updateSoldQuantities();
            updateProgress();
            updateFloatingSummary();
            updateStats();
            
            // Add visual feedback
            if (convertedValue.trim() !== '' && convertedValue.trim() !== '0') {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });
        
        // Save on blur as well (when user leaves the field)
        input.addEventListener('blur', function() {
            const row = this.closest('tr[data-item-id]');
            const itemId = row ? row.getAttribute('data-item-id') : 'unknown';
            console.log(`Input blur for item ${itemId}, saving data...`);
            saveAllQuantities();
        });
    });

    // تحديث جميع الواجهات بعد تحميل البيانات
    updateAllUIAfterDataLoad();
    
    // إظهار تنبيه حالة الحفظ
    showSaveStatusAlert();
    
    // إظهار إشعار إذا تم استرجاع بيانات من التخزين المحلي
    // حساب عدد الأصناف التي لها قيم فعلية فقط (ليس فارغة أو صفر)
    const actualItemsCount = Object.keys(localQuantities).filter(itemId => {
        const quantity = localQuantities[itemId];
        return quantity !== '' && quantity !== null && quantity !== undefined && 
               quantity !== '0' && parseFloat(quantity) > 0;
    }).length;
    
    const hasLocalData = actualItemsCount > 0;
    
    if (hasLocalData) {
        setTimeout(() => {
            Swal.fire({
                icon: 'info',
                title: 'تم استرجاع البيانات المحلية',
                text: `تم استرجاع ${actualItemsCount} صنف مسجل من التخزين المحلي`,
                timer: 4000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }, 2000);
    }
    
    // بدء الفحص الدوري للبيانات فقط إذا كان هناك بيانات محفوظة
    const hasExistingData = window.hasExistingData || false;
    if (hasExistingData || hasLocalData) {
        console.log('Starting periodic data check due to existing saved data...');
        checkAndUpdateDataPeriodically();
    }
});

function showSaveStatusAlert() {
    const alert = document.getElementById('saveStatusAlert');
    const statusText = document.getElementById('saveStatusText');
    
    // فحص إذا كان هناك بيانات محفوظة مسبقاً
    const hasExistingData = window.hasExistingData || false;
    const hasPendingSaves = pendingSaves.length > 0;
    
    if (hasExistingData || hasPendingSaves) {
        let message = '';
        if (hasExistingData) {
            // حساب عدد الأصناف المسجلة فعلياً من الملف
            const fileQuantities = window.quantitiesFromFile || {};
            const actualFileItemsCount = Object.keys(fileQuantities).filter(itemId => {
                const quantity = fileQuantities[itemId];
                return quantity !== '' && quantity !== null && quantity !== undefined && 
                       quantity !== '0' && parseFloat(quantity) > 0;
            }).length;
            
            if (actualFileItemsCount > 0) {
                message += `تم العثور على ${actualFileItemsCount} صنف مسجل محفوظ مسبقاً - تم استعادتها تلقائياً`;
            } else {
                message += 'تم العثور على بيانات محفوظة مسبقاً - تم استعادتها تلقائياً';
            }
        }
        if (hasPendingSaves) {
            if (message) message += '<br>';
            message += `يوجد ${pendingSaves.length} عملية حفظ معلقة - سيتم مزامنتها عند توفر الاتصال`;
        }
        
        statusText.innerHTML = message;
        alert.className = hasPendingSaves ? 'alert alert-warning' : 'alert alert-success';
        alert.style.borderRightColor = hasPendingSaves ? '#ffc107' : '#28a745';
        
        // تحديث إضافي للوحة البيانات بعد إظهار التنبيه
        setTimeout(() => {
            updateAllUIAfterDataLoad();
        }, 100);
    } else {
        statusText.innerHTML = 'يتم حفظ البيانات تلقائياً أثناء الكتابة - لا تقلق من فقدان عملك';
        alert.className = 'alert alert-info';
        alert.style.borderRightColor = '#17a2b8';
    }
    
    alert.style.display = 'block';
    
    // إخفاء التنبيه بعد 12 ثانية إذا كان هناك عمليات معلقة، 10 ثوان للباقي
    const hideDelay = hasPendingSaves ? 12000 : 10000;
    setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transition = 'opacity 0.5s ease';
        setTimeout(() => {
            alert.style.display = 'none';
        }, 500);
    }, hideDelay);
}

// دالة محسنة لفحص وتحديث البيانات دورياً (بدون تدخل غير مرغوب)
function checkAndUpdateDataPeriodically() {
    // فحص كل 30 ثانية فقط للتأكد من تطابق الواجهة مع البيانات
    setInterval(() => {
        let needsUpdate = false;
        let debugInfo = [];
        
        document.querySelectorAll('input[name="closing_quantity[]"]').forEach(input => {
            const value = input.value.trim();
            const hasValue = value !== '' && value !== '0' && !isNaN(parseFloat(value));
            const hasFilledClass = input.classList.contains('filled');
            
            // فقط إصلاح التناقضات الواضحة
            if (hasValue && !hasFilledClass) {
                input.classList.add('filled');
                needsUpdate = true;
                debugInfo.push(`Added filled class to item with value: ${value}`);
            } else if (!hasValue && hasFilledClass) {
                input.classList.remove('filled');
                needsUpdate = true;
                debugInfo.push(`Removed filled class from empty item`);
            }
        });
        
        // تحديث الواجهة فقط إذا كان هناك تناقضات فعلية
        if (needsUpdate) {
            console.log('Periodic check found inconsistencies:', debugInfo);
            updateStats();
            updateProgress();
            updateFloatingSummary();
            updateSoldQuantities();
        }
    }, 30000); // زيادة الفترة إلى 30 ثانية لتقليل التدخل
}
// وظائف الآلة الحاسبة
let calcCurrent = '';

function calcInput(val) {
  calcCurrent += val;
  document.getElementById('calcDisplay').value = calcCurrent;
  liveCompute();
}

function calcOperation(op) {
  if(calcCurrent === '') return;
  calcCurrent += op;
  document.getElementById('calcDisplay').value = calcCurrent;
  liveCompute();
}

function calcCompute() {
  try {
    const result = eval(calcCurrent);
    document.getElementById('calcDisplay').value = result;
    calcCurrent = result.toString();
    liveCompute();
  } catch(e) {
    document.getElementById('calcDisplay').value = "Error";
    calcCurrent = '';
    document.getElementById('calcResult').innerText = '';
  }
}

function calcClear() {
  calcCurrent = '';
  document.getElementById('calcDisplay').value = '';
  document.getElementById('calcResult').innerText = '0';
}

function liveCompute() {
  calcCurrent = document.getElementById('calcDisplay').value;
  try {
      const result = eval(calcCurrent);
      document.getElementById('calcResult').innerText = result !== undefined ? result : '0'; // Ensure result is always visible
  } catch (e) {
      const lastValidResult = eval(calcCurrent.slice(0, -1)) || '0'; // Evaluate up to the last valid character
      document.getElementById('calcResult').innerText = lastValidResult; // Show the last valid result
  }
}

function calcUndo() {
  // حذف آخر حرف/عملية من مدخلات الآلة الحاسبة
  calcCurrent = calcCurrent.slice(0, -1);
  document.getElementById('calcDisplay').value = calcCurrent;
  liveCompute();
}

// تفعيل خاصية السحب للعناصر
dragElement(document.getElementById("calculator"));

function dragElement(elm) {
  let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
  const header = document.getElementById("calcHeader");
  if (header) {
    header.onmousedown = dragMouseDown;
    header.addEventListener("touchstart", dragTouchStart, {passive: false});
  } else {
    elm.onmousedown = dragMouseDown;
    elm.addEventListener("touchstart", dragTouchStart, {passive: false});
  }

  function dragMouseDown(e) {
    e = e || window.event;
    e.preventDefault();
    pos3 = e.clientX;
    pos4 = e.clientY;
    document.onmouseup = closeDragElement;
    document.onmousemove = elementDrag;
  }

  function elementDrag(e) {
    e = e || window.event;
    e.preventDefault();
    pos1 = pos3 - e.clientX;
    pos2 = pos4 - e.clientY;
    pos3 = e.clientX;
    pos4 = e.clientY;
    elm.style.top = (elm.offsetTop - pos2) + "px";
    elm.style.left = (elm.offsetLeft - pos1) + "px";
    elm.style.right = "auto"; // إزالة الخاصية "right" لتجنب التعارض
  }

  function closeDragElement() {
    document.onmouseup = null;
    document.onmousemove = null;
  }
  
  function dragTouchStart(e) {
    e.preventDefault();
    pos3 = e.touches[0].clientX;
    pos4 = e.touches[0].clientY;
    document.addEventListener("touchmove", elementTouchDrag, {passive: false});
    document.addEventListener("touchend", closeTouchDrag, {passive: false});
  }
  
  function elementTouchDrag(e) {
    e.preventDefault();
    pos1 = pos3 - e.touches[0].clientX;
    pos2 = pos4 - e.touches[0].clientY;
    pos3 = e.touches[0].clientX;
    pos4 = e.touches[0].clientY;
    elm.style.top = (elm.offsetTop - pos2) + "px";
    elm.style.left = (elm.offsetLeft - pos1) + "px";
    elm.style.right = "auto";
  }
  
  function closeTouchDrag() {
    document.removeEventListener("touchmove", elementTouchDrag);
    document.removeEventListener("touchend", closeTouchDrag);
  }
}

function toggleCalculator() {
  var calc = document.getElementById('calculator');
  if (calc.style.display === "none" || calc.style.display === "") {
    calc.style.display = "block";
  } else {
    calc.style.display = "none";
  }
}

function closeCalculator() {
  document.getElementById('calculator').style.display = "none";
}

// إضافة مستمع حدث لتشغيل دالة closeCalculator عند حدث touchend على زر الإغلاق
document.addEventListener('DOMContentLoaded', function() {
    var closeBtn = document.getElementById('calcCloseBtn');
    if (closeBtn) {
        closeBtn.addEventListener('touchend', function(e) {
            e.preventDefault();
            closeCalculator();
        });
    }
});