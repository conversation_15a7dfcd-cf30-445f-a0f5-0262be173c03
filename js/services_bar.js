/**
 * Services Bar Manager
 * إدارة شريط أيقونات الخدمات المفضلة
 */

class ServicesBarManager {
    constructor() {
        this.services = [];
        this.favoriteServices = [];
        this.serviceProviders = {
            'vodafone': { name: 'فودافون', color: '#e60000', icon: 'fas fa-mobile-alt' },
            'orange': { name: 'أورانج', color: '#ff6600', icon: 'fas fa-mobile-alt' },
            'etisalat': { name: 'اتصالات', color: '#00b04f', icon: 'fas fa-mobile-alt' },
            'we': { name: 'وي', color: '#662d91', icon: 'fas fa-mobile-alt' },
            'other': { name: 'أخرى', color: '#6c757d', icon: 'fas fa-cogs' }
        };
        this.serviceTypes = {
            'topup_card': { name: 'كارت شحن', icon: 'fas fa-credit-card' },
            'balance': { name: 'رصيد', icon: 'fas fa-coins' },
            'bundle': { name: 'باقة', icon: 'fas fa-box' },
            'bill': { name: 'فاتورة', icon: 'fas fa-file-invoice' },
            'cash_withdraw': { name: 'سحب نقدي', icon: 'fas fa-money-bill-wave' },
            'cash_deposit': { name: 'إيداع نقدي', icon: 'fas fa-piggy-bank' },
            'other': { name: 'أخرى', icon: 'fas fa-ellipsis-h' }
        };
        this.init();
    }

    async init() {
        // تحميل بيانات الخدمات من النافذة العامة
        this.loadServices();
        
        // تحميل الخدمات المفضلة من قاعدة البيانات
        await this.loadFavoriteServices();
        
        // إنشاء شريط الخدمات
        this.createServicesBar();
        
        console.log('Services Bar Manager initialized');
    }

    loadServices() {
        if (window.invoiceServices && Array.isArray(window.invoiceServices)) {
            this.services = window.invoiceServices;
            console.log('تم تحميل', this.services.length, 'خدمة');
            console.log('عينة من بيانات الخدمات:', this.services[0]);
        } else {
            console.warn('لم يتم العثور على بيانات الخدمات');
            this.services = [];
        }
    }

    async loadFavoriteServices() {
        // استخراج المفضلة من بيانات الخدمات المحملة
        this.favoriteServices = [];
        
        if (this.services && this.services.length > 0) {
            this.services.forEach(service => {
                if (service.is_favorite && service.is_favorite > 0) {
                    this.favoriteServices.push(parseInt(service.item_id));
                }
            });
            
            console.log('تم تحميل المفضلة من قاعدة البيانات:', this.favoriteServices);
        }
    }

    saveFavoriteServices() {
        // لا نحتاج لحفظ في localStorage بعد الآن
        // سيتم الحفظ مباشرة في قاعدة البيانات عند التغيير
    }

    createServicesBar() {
        // التحقق من وجود شريط الخدمات مسبقاً
        let servicesBarContainer = document.getElementById('services-bar-container');
        if (servicesBarContainer) {
            servicesBarContainer.remove();
        }

        // إنشاء الحاوية الرئيسية
        servicesBarContainer = document.createElement('div');
        servicesBarContainer.id = 'services-bar-container';
        servicesBarContainer.className = 'services-bar-container';
        
        // تحديد الألوان حسب الوضع
        const isDarkMode = document.body.classList.contains('dark-mode');
        const barBgColor = isDarkMode ? 'var(--dark-surface-light)' : '#ffffff';
        const borderColor = isDarkMode ? 'var(--dark-border-light)' : '#e0e0e0';
        const textColor = isDarkMode ? 'var(--dark-text)' : '#333333';
        const arrowBgColor = isDarkMode ? 'var(--dark-surface)' : '#f8f9fa';
        const arrowColor = isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)';

        servicesBarContainer.style.cssText = `
            position: fixed;
            bottom: 80px;
            left: 20px;
            right: 20px;
            background: ${barBgColor};
            border: 1px solid ${borderColor};
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, ${isDarkMode ? '0.3' : '0.15'});
            z-index: 1000;
            display: flex;
            align-items: center;
            backdrop-filter: blur(10px);
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        `;

        // إنشاء السهم الأيسر (للتمرير لليسار)
        const leftArrow = document.createElement('button');
        leftArrow.className = 'services-scroll-arrow left-arrow';
        leftArrow.innerHTML = '<i class="fas fa-chevron-left"></i>';
        leftArrow.style.cssText = `
            background: ${arrowBgColor};
            border: 1px solid ${borderColor};
            color: ${arrowColor};
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            margin: 0 5px;
            flex-shrink: 0;
            opacity: 0.7;
        `;

        // إنشاء السهم الأيمن (للتمرير لليمين)
        const rightArrow = document.createElement('button');
        rightArrow.className = 'services-scroll-arrow right-arrow';
        rightArrow.innerHTML = '<i class="fas fa-chevron-right"></i>';
        rightArrow.style.cssText = `
            background: ${arrowBgColor};
            border: 1px solid ${borderColor};
            color: ${arrowColor};
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            margin: 0 5px;
            flex-shrink: 0;
            opacity: 0.7;
        `;

        // إنشاء شريط الخدمات القابل للتمرير
        const servicesBar = document.createElement('div');
        servicesBar.id = 'services-bar';
        servicesBar.className = 'services-bar';
        servicesBar.style.cssText = `
            display: flex;
            align-items: center;
            gap: 10px;
            overflow-x: auto;
            flex: 1;
            padding: 15px 10px;
            scroll-behavior: smooth;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;

        // إخفاء شريط التمرير
        servicesBar.style.setProperty('scrollbar-width', 'none');
        const style = document.createElement('style');
        style.textContent = `
            #services-bar::-webkit-scrollbar {
                display: none;
            }
        `;
        document.head.appendChild(style);

        // إنشاء محتوى الشريط
        this.populateServicesBar(servicesBar);

        // تجميع العناصر (في RTL: السهم الأيمن أولاً، ثم الشريط، ثم السهم الأيسر)
        servicesBarContainer.appendChild(rightArrow);
        servicesBarContainer.appendChild(servicesBar);
        servicesBarContainer.appendChild(leftArrow);

        // إضافة أحداث التمرير
        this.setupScrollEvents(servicesBar, leftArrow, rightArrow);

        // إدراج الحاوية في الصفحة
        document.body.appendChild(servicesBarContainer);

        // تحديث حالة الأسهم بعد تحميل المحتوى
        setTimeout(() => {
            this.updateArrowsVisibility(servicesBar, leftArrow, rightArrow);
        }, 100);
    }

    populateServicesBar(container) {
        // مسح المحتوى الحالي
        container.innerHTML = '';

        // تحديد الألوان حسب الوضع
        const isDarkMode = document.body.classList.contains('dark-mode');
        const textColor = isDarkMode ? 'var(--dark-text)' : '#333333';

        // إضافة عنوان الشريط
        const title = document.createElement('div');
        title.style.cssText = `
            font-weight: 600;
            color: ${textColor};
            margin-left: 15px;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        `;
        title.innerHTML = `
            <i class="fas fa-concierge-bell" style="color: ${isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)'};"></i>
            الخدمات:
        `;
        container.appendChild(title);

        // زر "الخدمات العادية" (أول عنصر)
        const allServicesBtn = this.createServiceButton({
            name: 'جميع الخدمات',
            icon: 'fas fa-th-large',
            color: isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)',
            isMainButton: true
        });
        allServicesBtn.addEventListener('click', () => {
            this.openServicesModal();
            
            // إزالة التأثيرات النشطة بعد الضغط
            setTimeout(() => {
                allServicesBtn.blur();
                allServicesBtn.style.transform = 'translateY(0)';
                allServicesBtn.style.boxShadow = 'none';
            }, 200);
        });
        container.appendChild(allServicesBtn);

        // إضافة فاصل
        const separator = document.createElement('div');
        separator.style.cssText = `
            width: 1px;
            height: 40px;
            background: ${isDarkMode ? 'var(--dark-border-light)' : '#e0e0e0'};
            margin: 0 10px;
        `;
        container.appendChild(separator);

        // إضافة الخدمات المفضلة
        this.favoriteServices.forEach(serviceId => {
            const service = this.services.find(s => s.item_id == serviceId);
            if (service) {
                const serviceBtn = this.createFavoriteServiceButton(service);
                container.appendChild(serviceBtn);
            }
        });

        // إذا لم توجد خدمات مفضلة، أظهر رسالة
        if (this.favoriteServices.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.style.cssText = `
                color: ${isDarkMode ? 'var(--dark-text-muted)' : '#6c757d'};
                font-style: italic;
                font-size: 14px;
                margin-right: 10px;
            `;
            emptyMessage.textContent = 'لا توجد خدمات مفضلة بعد';
            container.appendChild(emptyMessage);
        }
    }

    createServiceButton(config) {
        const button = document.createElement('button');
        button.className = 'service-button';
        
        const isDarkMode = document.body.classList.contains('dark-mode');
        const bgColor = config.isMainButton 
            ? (isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)')
            : (isDarkMode ? 'var(--dark-surface)' : '#ffffff');
        const textColor = config.isMainButton 
            ? '#ffffff' 
            : (isDarkMode ? 'var(--dark-text)' : '#333333');
        const borderColor = isDarkMode ? 'var(--dark-border-light)' : '#e0e0e0';

        button.style.cssText = `
            background: ${bgColor};
            border: 1px solid ${borderColor};
            border-radius: 12px;
            padding: 12px 16px;
            color: ${textColor};
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            min-width: 80px;
            font-family: 'Cairo', sans-serif;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        `;

        button.innerHTML = `
            <i class="${config.icon}" style="font-size: 18px; color: ${config.color || textColor};"></i>
            <span>${config.name}</span>
        `;

        // تأثيرات hover
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = `0 4px 15px rgba(0, 0, 0, ${isDarkMode ? '0.3' : '0.15'})`;
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = 'none';
        });

        return button;
    }

    createFavoriteServiceButton(service) {
        const provider = this.serviceProviders[service.service_provider] || this.serviceProviders['other'];
        const serviceType = this.serviceTypes[service.service_type] || this.serviceTypes['other'];
        
        const button = this.createServiceButton({
            name: service.name,
            icon: serviceType.icon,
            color: provider.color
        });

        // إضافة حدث النقر لإضافة ا��خدمة للفاتورة
        button.addEventListener('click', () => {
            this.addServiceToInvoice(service);
            
            // إزالة التأثيرات النشطة بعد الضغط
            setTimeout(() => {
                button.blur(); // إزالة التركيز
                button.style.transform = 'translateY(0)';
                button.style.boxShadow = 'none';
            }, 200);
        });

        return button;
    }

    openServicesModal() {
        // إنشاء نافذة الخدمات الكبيرة
        const isDarkMode = document.body.classList.contains('dark-mode');
        
        // تجميع الخدمات حسب مقدم الخدمة
        const servicesByProvider = {};
        this.services.forEach(service => {
            const provider = service.service_provider || 'other';
            if (!servicesByProvider[provider]) {
                servicesByProvider[provider] = {};
            }
            
            const serviceType = service.service_type || 'other';
            if (!servicesByProvider[provider][serviceType]) {
                servicesByProvider[provider][serviceType] = [];
            }
            
            servicesByProvider[provider][serviceType].push(service);
        });

        // إنشاء محتوى النافذة
        let modalContent = `
            <div style="max-height: 70vh; overflow-y: auto; direction: rtl;">
                <div class="services-tabs-container">
        `;

        // إنشاء التبويبات الرئيسية (مقدمي الخدمة)
        const providers = Object.keys(servicesByProvider);
        if (providers.length > 0) {
            modalContent += '<div class="provider-tabs" style="display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap;">';
            
            providers.forEach((provider, index) => {
                const providerInfo = this.serviceProviders[provider] || this.serviceProviders['other'];
                const isActive = index === 0 ? 'active' : '';
                
                modalContent += `
                    <button class="provider-tab ${isActive}" data-provider="${provider}" 
                            style="padding: 10px 20px; border: 2px solid ${providerInfo.color}; 
                                   background: ${index === 0 ? providerInfo.color : 'transparent'}; 
                                   color: ${index === 0 ? 'white' : providerInfo.color}; 
                                   border-radius: 8px; cursor: pointer; font-family: 'Cairo', sans-serif; 
                                   font-weight: 600; transition: all 0.3s ease;">
                        <i class="${providerInfo.icon}" style="margin-left: 8px;"></i>
                        ${providerInfo.name}
                    </button>
                `;
            });
            
            modalContent += '</div>';

            // إنشاء محتوى كل مقدم خدمة
            providers.forEach((provider, providerIndex) => {
                const isActiveProvider = providerIndex === 0 ? 'block' : 'none';
                modalContent += `<div class="provider-content" data-provider="${provider}" style="display: ${isActiveProvider};">`;
                
                const serviceTypes = Object.keys(servicesByProvider[provider]);
                
                if (serviceTypes.length > 0) {
                    // التبويبات الفرعية (أنواع الخدمة)
                    modalContent += '<div class="service-type-tabs" style="display: flex; gap: 8px; margin-bottom: 15px; flex-wrap: wrap;">';
                    
                    serviceTypes.forEach((serviceType, typeIndex) => {
                        const typeInfo = this.serviceTypes[serviceType] || this.serviceTypes['other'];
                        const isActiveType = typeIndex === 0 ? 'active' : '';
                        
                        modalContent += `
                            <button class="service-type-tab ${isActiveType}" data-provider="${provider}" data-type="${serviceType}"
                                    style="padding: 8px 15px; border: 1px solid ${isDarkMode ? 'var(--dark-border-light)' : '#ddd'}; 
                                           background: ${typeIndex === 0 ? (isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)') : (isDarkMode ? 'var(--dark-surface)' : 'white')}; 
                                           color: ${typeIndex === 0 ? 'white' : (isDarkMode ? 'var(--dark-text)' : '#333')}; 
                                           border-radius: 6px; cursor: pointer; font-family: 'Cairo', sans-serif; 
                                           font-size: 14px; transition: all 0.3s ease;">
                                <i class="${typeInfo.icon}" style="margin-left: 6px;"></i>
                                ${typeInfo.name}
                            </button>
                        `;
                    });
                    
                    modalContent += '</div>';

                    // محتوى كل نوع خدمة
                    serviceTypes.forEach((serviceType, typeIndex) => {
                        const isActiveType = typeIndex === 0 ? 'block' : 'none';
                        modalContent += `<div class="service-type-content" data-provider="${provider}" data-type="${serviceType}" style="display: ${isActiveType};">`;
                        
                        const services = servicesByProvider[provider][serviceType];
                        modalContent += '<div class="services-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px;">';
                        
                        services.forEach(service => {
                            const isFavorite = this.favoriteServices.includes(parseInt(service.item_id));
                            const starColor = isFavorite ? '#ffc107' : (isDarkMode ? '#6c757d' : '#ddd');
                            
                            // تحديد السعر والتسمية حسب نوع الخدمة
                            let displayPrice = '';
                            let priceLabel = '';
                            
                            if (service.service_type === 'cash_withdraw' || service.service_type === 'cash_deposit') {
                                displayPrice = service.commission_fixed || '0';
                                priceLabel = 'العمولة الثابتة';
                            } else {
                                displayPrice = service.price || '0';
                                priceLabel = 'السعر';
                            }
                            
                            modalContent += `
                                <div class="service-card" data-service-id="${service.item_id}" 
                                     style="border: 1px solid ${isDarkMode ? 'var(--dark-border-light)' : '#ddd'}; 
                                            border-radius: 10px; padding: 15px; 
                                            background: ${isDarkMode ? 'var(--dark-surface)' : 'white'}; 
                                            cursor: pointer; transition: all 0.3s ease; position: relative;">
                                    <button class="favorite-star" data-service-id="${service.item_id}" 
                                            style="position: absolute; top: 10px; right: 10px; background: none; 
                                                   border: none; font-size: 18px; color: ${starColor}; cursor: pointer;">
                                        <i class="fas fa-star"></i>
                                    </button>
                                    <div style="margin-top: 25px; text-align: center;">
                                        <h6 style="margin: 0 0 10px 0; color: ${isDarkMode ? 'var(--dark-text)' : '#333'}; 
                                                   font-family: 'Cairo', sans-serif; font-weight: 600;">
                                            ${service.name}
                                        </h6>
                                        <div style="font-size: 12px; color: ${isDarkMode ? 'var(--dark-text-muted)' : '#6c757d'}; margin-bottom: 10px;">
                                            ${priceLabel}: ${displayPrice} ج.م
                                        </div>
                                        <button class="add-service-btn" data-service-id="${service.item_id}"
                                                style="background: ${isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)'}; 
                                                       color: white; border: none; padding: 8px 16px; 
                                                       border-radius: 6px; font-family: 'Cairo', sans-serif; 
                                                       cursor: pointer; transition: all 0.3s ease;">
                                            <i class="fas fa-plus" style="margin-left: 5px;"></i>
                                            إضافة
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                        
                        modalContent += '</div></div>';
                    });
                }
                
                modalContent += '</div>';
            });
        } else {
            modalContent += '<div style="text-align: center; padding: 40px; color: #6c757d;">لا توجد خدمات متاحة</div>';
        }

        modalContent += '</div></div>';

        // عرض النافذة باستخدام SweetAlert2
        Swal.fire({
            title: '<i class="fas fa-concierge-bell"></i> الخدمات المتاحة',
            html: modalContent,
            width: '90%',
            maxWidth: '1000px',
            showCloseButton: true,
            showConfirmButton: false,
            customClass: {
                popup: 'services-modal-popup',
                title: 'services-modal-title'
            },
            didOpen: () => {
                this.bindModalEvents();
            }
        });
    }

    bindModalEvents() {
        // ربط أحداث التبويبات الرئيسية
        document.querySelectorAll('.provider-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const provider = e.target.dataset.provider;
                
                // إزالة التفعيل من جميع التبويبات
                document.querySelectorAll('.provider-tab').forEach(t => {
                    t.classList.remove('active');
                    const providerInfo = this.serviceProviders[t.dataset.provider] || this.serviceProviders['other'];
                    t.style.background = 'transparent';
                    t.style.color = providerInfo.color;
                });
                
                // تفعيل التبويب المحدد
                e.target.classList.add('active');
                const providerInfo = this.serviceProviders[provider] || this.serviceProviders['other'];
                e.target.style.background = providerInfo.color;
                e.target.style.color = 'white';
                
                // إخفاء جميع المحتويات
                document.querySelectorAll('.provider-content').forEach(content => {
                    content.style.display = 'none';
                });
                
                // إظهار المحتوى المحدد
                const targetContent = document.querySelector(`.provider-content[data-provider="${provider}"]`);
                if (targetContent) {
                    targetContent.style.display = 'block';
                }
            });
        });

        // ربط أحداث التبويبات الفرعية
        document.querySelectorAll('.service-type-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const provider = e.target.dataset.provider;
                const serviceType = e.target.dataset.type;
                
                // إزالة التفعيل من التبويبات الفرعية لنفس المقدم
                document.querySelectorAll(`.service-type-tab[data-provider="${provider}"]`).forEach(t => {
                    t.classList.remove('active');
                    const isDarkMode = document.body.classList.contains('dark-mode');
                    t.style.background = isDarkMode ? 'var(--dark-surface)' : 'white';
                    t.style.color = isDarkMode ? 'var(--dark-text)' : '#333';
                });
                
                // تفعيل التبويب المحدد
                e.target.classList.add('active');
                const isDarkMode = document.body.classList.contains('dark-mode');
                e.target.style.background = isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)';
                e.target.style.color = 'white';
                
                // إخفاء جميع محتويات الأنواع لنفس المقدم
                document.querySelectorAll(`.service-type-content[data-provider="${provider}"]`).forEach(content => {
                    content.style.display = 'none';
                });
                
                // إظهار المحتوى المحدد
                const targetContent = document.querySelector(`.service-type-content[data-provider="${provider}"][data-type="${serviceType}"]`);
                if (targetContent) {
                    targetContent.style.display = 'block';
                }
            });
        });

        // ربط أحداث النجوم (المفضلة)
        document.querySelectorAll('.favorite-star').forEach(star => {
            star.addEventListener('click', async (e) => {
                e.stopPropagation();
                const serviceId = e.target.closest('.favorite-star').dataset.serviceId;
                await this.toggleFavorite(serviceId);
                
                // تحديث لون النجمة
                const isFavorite = this.favoriteServices.includes(parseInt(serviceId));
                const isDarkMode = document.body.classList.contains('dark-mode');
                e.target.style.color = isFavorite ? '#ffc107' : (isDarkMode ? '#6c757d' : '#ddd');
            });
        });

        // ربط أحداث إضافة الخدمة
        document.querySelectorAll('.add-service-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const serviceId = e.target.dataset.serviceId;
                const service = this.services.find(s => s.item_id == serviceId);
                if (service) {
                    this.addServiceToInvoice(service);
                }
                // إزالة التأثيرات النشطة
                setTimeout(() => {
                    btn.blur();
                    btn.style.transform = 'translateY(0)';
                    btn.style.boxShadow = 'none';
                }, 200);
            });
        });

        // ربط أحداث النقر على كارت الخدمة
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.favorite-star') && !e.target.closest('.add-service-btn')) {
                    const serviceId = card.dataset.serviceId;
                    const service = this.services.find(s => s.item_id == serviceId);
                    if (service) {
                        this.addServiceToInvoice(service);
                    }
                }
                // إزالة التأثيرات النشطة
                setTimeout(() => {
                    card.style.transform = 'translateY(0)';
                    card.style.boxShadow = 'none';
                }, 200);
            });

            // تأثيرات hover
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
                card.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = 'none';
            });
        });
    }

    async toggleFavorite(serviceId) {
        try {
            const formData = new FormData();
            formData.append('service_id', serviceId);
            formData.append('account_id', window.encryptedAccountId);
            
            const response = await fetch('toggle_service_favorite.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                const index = this.favoriteServices.indexOf(parseInt(serviceId));
                
                if (result.action === 'added') {
                    if (index === -1) {
                        this.favoriteServices.push(parseInt(serviceId));
                    }
                    if (typeof toastr !== 'undefined') {
                        toastr.success('تم إضافة الخدمة للمفضلة');
                    }
                } else if (result.action === 'removed') {
                    if (index > -1) {
                        this.favoriteServices.splice(index, 1);
                    }
                    if (typeof toastr !== 'undefined') {
                        toastr.info('تم إزالة الخدمة من المفضلة');
                    }
                }
                
                this.updateServicesBar();
                console.log('تم تحديث المفضلة:', result.action);
            } else {
                console.error('خطأ في تحديث المفضلة:', result.message);
                if (typeof toastr !== 'undefined') {
                    toastr.error('خطأ في تحديث المفضلة');
                }
            }
        } catch (error) {
            console.error('خطأ في الاتصال بقاعدة البيانات:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('خطأ في الاتصال بالخادم');
            }
        }
    }

    removeFromFavorites(serviceId) {
        const index = this.favoriteServices.indexOf(serviceId);
        if (index > -1) {
            this.favoriteServices.splice(index, 1);
            this.saveFavoriteServices();
            this.updateServicesBar();
        }
    }

    updateServicesBar() {
        const servicesBar = document.getElementById('services-bar');
        if (servicesBar) {
            this.populateServicesBar(servicesBar);
            
            // تحديث حالة الأسهم بعد تحديث المحتوى
            setTimeout(() => {
                const leftArrow = document.querySelector('.left-arrow');
                const rightArrow = document.querySelector('.right-arrow');
                if (leftArrow && rightArrow) {
                    this.updateArrowsVisibility(servicesBar, leftArrow, rightArrow);
                }
            }, 100);
        }
    }

    addServiceToInvoice(service) {
        console.log('بيانات الخدمة الأصلية:', service);
        const isCashService = service.service_type === 'cash_withdraw' || service.service_type === 'cash_deposit';

        if (isCashService) {
            // For cash services, we show another modal. SweetAlert handles the replacement.
            // The amount input modal will open, and after confirming, it will close automatically.
            this.showCashAmountInput(service, (calculation) => {
                this.addCashServiceToInvoice(service, calculation);
            });
        } else if (service.is_custom_priced) {
            // For custom priced services
            this.showCustomPriceInput(service, (customData) => {
                this.addCustomServiceToInvoice(service, customData);
            });
        } else {
            // For regular services, add the item and then close the services modal.
            this.addRegularServiceToInvoice(service);
            Swal.close();
        }
    }

    showCustomPriceInput(service, callback) {
        Swal.fire({
            title: 'أدخل التكلفة وسعر البيع',
            html:
                '<input id="swal-cost" class="swal2-input" placeholder="التكلفة (ج.م)" type="number">' +
                '<input id="swal-price" class="swal2-input" placeholder="سعر البيع (ج.م)" type="number">',
            focusConfirm: false,
            showCancelButton: true,
            confirmButtonText: 'تأكيد',
            cancelButtonText: 'إلغاء',
            preConfirm: () => {
                const cost = parseFloat(document.getElementById('swal-cost').value);
                const price = parseFloat(document.getElementById('swal-price').value);
                if (isNaN(cost) || cost <= 0 || isNaN(price) || price <= 0) {
                    Swal.showValidationMessage('يرجى إدخال قيم صالحة للتكلفة وسعر البيع');
                    return;
                }
                return { cost, price };
            },
            didOpen: () => {
                const costInput = document.getElementById('swal-cost');
                const priceInput = document.getElementById('swal-price');
                
                [costInput, priceInput].forEach(input => {
                    input.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            Swal.clickConfirm();
                        }
                    });
                });
            }
        }).then((result) => {
            if (result.isConfirmed) {
                callback(result.value);
            }
        });
    }

    addCustomServiceToInvoice(service, customData) {
        console.log('إضافة خدمة مخصصة:', service.name);
        console.log('تفاصيل السعر:', customData);
        
        const serviceItem = {
            id: service.item_id,
            name: service.name,
            price: customData.price,
            cost: customData.cost,
            quantity: 1,
            type: 'service',
            service_provider: service.service_provider,
            service_type: service.service_type,
            is_custom_priced: true
        };

        console.log('كائن الخدمة المخصصة المُنشأ:', serviceItem);

        this.addServiceItemToInvoice(serviceItem);

        if (typeof toastr !== 'undefined') {
            toastr.success(
                `تم إضافة ${service.name}<br>` +
                `التكلفة: ${customData.cost} ج.م<br>` +
                `سعر البيع: ${customData.price} ج.م`,
                'خدمة مخصصة',
                { timeOut: 5000, enableHtml: true }
            );
        }

        Swal.close();

        console.log('تم إضافة خدمة مخصصة:', service.name, 'بتكلفة:', customData.cost, 'وسعر بيع:', customData.price);
    }

    showCashAmountInput(service, callback) {
        Swal.fire({
            title: 'أدخل المبلغ',
            input: 'number',
            inputLabel: 'المبلغ (ج.م)',
            inputPlaceholder: 'أدخل المبلغ هنا',
            showCancelButton: true,
            confirmButtonText: 'حساب العمولة',
            cancelButtonText: 'إ��غاء',
            inputValidator: (value) => {
                if (!value || parseFloat(value) <= 0) {
                    return 'يرجى إدخال مبلغ صالح أكبر من 0';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const amount = parseFloat(result.value);
                const calculation = this.calculateCashCommission(amount, service.commission_threshold, service.commission_fixed, service.commission_per_thousand);
                callback(calculation);
            }
        });
    }

    calculateCashCommission(amount, threshold, fixed, perThousand) {
        threshold = parseFloat(threshold) || 0;
        fixed = parseFloat(fixed) || 0;
        perThousand = parseFloat(perThousand) || 0;

        let commission = 0;

        if (amount < threshold) {
            commission = fixed;
        } else {
            const thousands = Math.floor(amount / 1000);
            commission = thousands * perThousand;

            const remainder = amount % 1000;
            if (remainder > 0) {
                commission += fixed;
            }
        }

        return {
            amount: amount,
            commission: commission,
            totalCommission: commission, // للتوافق
            finalTotal: amount + commission
        };
    }

    addCashServiceToInvoice(service, calculation) {
        console.log('إضافة خدمة نقدية:', service.name);
        console.log('تفاصيل الحساب:', calculation);
        
        // إنشاء كائن صنف للخدمة النقدية
        const serviceItem = {
            id: service.item_id,
            name: service.name,
            price: calculation.finalTotal, //  finalTotal المبلغ الإجمالي
            cost: calculation.amount, // <-- This is the original amount, which we'll treat as the cost
            quantity: calculation.amount, //  <-- Set quantity to the user-entered amount
            type: 'service',
            service_provider: service.service_provider,
            service_type: service.service_type,
            isCashService: true,
            cashCalculation: calculation,
            // بيانات إضافية للخدمة النقدية
            commission_threshold: service.commission_threshold,
            commission_fixed: service.commission_fixed,
            commission_per_thousand: service.commission_per_thousand
        };

        console.log('كائن الخدمة النقدية المُنشأ:', serviceItem);

        // إضافة الخدمة للفاتورة
        this.addServiceItemToInvoice(serviceItem);

        // إظهار رسالة نجاح مفصلة
        if (typeof toastr !== 'undefined') {
            toastr.success(
                `تم إضافة ${service.name}<br>` +
                `المبلغ: ${calculation.amount} ج.م<br>` +
                `العمولة: ${calculation.totalCommission} ج.م<br>` +
                `الإجمالي: ${calculation.finalTotal} ج.م`,
                'خدمة نقدية',
                { timeOut: 5000, enableHtml: true }
            );
        }

        console.log('تم إضافة خدمة نقدية:', service.name, 'بمبلغ:', calculation.amount, 'وعمولة:', calculation.totalCommission);
    }

    addRegularServiceToInvoice(service) {
        console.log('إضافة خدمة عادية:', service.name);
        
        // تحديد السعر للخدمات العادية
        const servicePrice = parseFloat(service.price) || 0;
        
        // إنشاء كائن صنف للخدمة العادية
        const serviceItem = {
            id: service.item_id,
            name: service.name,
            price: servicePrice,
            quantity: 1,
            type: 'service',
            service_provider: service.service_provider,
            service_type: service.service_type,
            isCashService: false
        };

        console.log('كائن ال��دمة العادية المُنشأ:', serviceItem);

        // إضافة الخدمة للفاتورة
        this.addServiceItemToInvoice(serviceItem);

        // إظهار رسالة نجاح
        if (typeof toastr !== 'undefined') {
            toastr.success(`تم إضافة خدمة: ${service.name} بسعر ${serviceItem.price} ج.م`);
        }

        console.log('تم إضافة خدمة عادية:', service.name, 'بسعر:', serviceItem.price);
    }

    addServiceItemToInvoice(serviceItem) {
        // التحقق من وجود مدير العملاء المتعددين
        if (window.customerTabsManager && typeof window.customerTabsManager.addItemToCurrentCustomer === 'function') {
            console.log('إضافة للوضع المتعدد العملاء');
            window.customerTabsManager.addItemToCurrentCustomer(serviceItem);
        } else {
            console.log('إضافة للوضع العادي');
            // إضافة للطريقة العادية
            if (typeof addItemToSelectedList === 'function') {
                console.log('استخدام addItemToSelectedList');
                addItemToSelectedList(serviceItem);
            } else if (window.addedItems) {
                console.log('إضافة مباشرة للمصفوفة');
                // إضافة م��اشرة للمصفوفة
                const existingIndex = window.addedItems.findIndex(item => item.id === serviceItem.id);
                if (existingIndex > -1) {
                    // للخدمات النقدية، لا نزيد الكمية بل نضيف خدمة جديدة
                    if (serviceItem.isCashService) {
                        window.addedItems.push(serviceItem);
                    } else {
                        window.addedItems[existingIndex].quantity += 1;
                    }
                } else {
                    window.addedItems.push(serviceItem);
                }
                
                console.log('المصفوفة بعد الإضافة:', window.addedItems);
                
                // تحديث واجهة المستخدم
                if (typeof updateItemCount === 'function') {
                    updateItemCount();
                }
                if (typeof updateInvoiceTotal === 'function') {
                    updateInvoiceTotal();
                }
            }
        }
    }

    // دالة لإخفاء/إظهار الشريط
    toggleVisibility() {
        const servicesBar = document.getElementById('services-bar');
        if (servicesBar) {
            const isVisible = servicesBar.style.display !== 'none';
            servicesBar.style.display = isVisible ? 'none' : 'flex';
        }
    }

    // إعداد أحداث التمرير للأسهم
    setupScrollEvents(servicesBar, leftArrow, rightArrow) {
        const scrollAmount = 200; // مقدار التمرير بالبكسل

        // في النظام RTL:
        // السهم الأيسر = التمرير لليسار (قيم سالبة)
        // السهم الأيمن = التمرير لليمين (قيم موجبة)

        // حدث النقر على السهم الأيسر (التمرير لليسار)
        leftArrow.addEventListener('click', () => {
            console.log('Left arrow clicked - scrolling left');
            servicesBar.scrollBy({
                left: -scrollAmount,
                behavior: 'smooth'
            });
        });

        // حدث النقر على السهم الأيمن (التمرير لليمين)
        rightArrow.addEventListener('click', () => {
            console.log('Right arrow clicked - scrolling right');
            servicesBar.scrollBy({
                left: scrollAmount,
                behavior: 'smooth'
            });
        });

        // تحديث حالة الأسهم عند التمرير
        servicesBar.addEventListener('scroll', () => {
            this.updateArrowsVisibility(servicesBar, leftArrow, rightArrow);
        });

        // تأثيرات hover للأسهم
        [leftArrow, rightArrow].forEach(arrow => {
            arrow.addEventListener('mouseenter', () => {
                arrow.style.opacity = '1';
                arrow.style.transform = 'scale(1.1)';
                arrow.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
            });

            arrow.addEventListener('mouseleave', () => {
                arrow.style.opacity = '0.7';
                arrow.style.transform = 'scale(1)';
                arrow.style.boxShadow = 'none';
            });

            arrow.addEventListener('mousedown', () => {
                arrow.style.transform = 'scale(0.95)';
            });

            arrow.addEventListener('mouseup', () => {
                arrow.style.transform = 'scale(1.1)';
            });
        });

        // دعم التمرير بالماوس
        servicesBar.addEventListener('wheel', (e) => {
            // منع السلوك الافتراضي للتمرير العمودي للصفحة
            e.preventDefault();
            
            // تحديد اتجاه التمرير (أفقي أو رأسي) واستخدامه للتمرير الأفقي
            const scrollAmount = e.deltaY !== 0 ? e.deltaY : e.deltaX;
            
            servicesBar.scrollBy({
                left: scrollAmount,
                behavior: 'smooth'
            });
        });

        // دعم ا��تمرير باللمس للأجهزة المحمولة
        let startX = 0;
        let scrollLeft = 0;

        servicesBar.addEventListener('touchstart', (e) => {
            startX = e.touches[0].pageX - servicesBar.offsetLeft;
            scrollLeft = servicesBar.scrollLeft;
        });

        servicesBar.addEventListener('touchmove', (e) => {
            if (!startX) return;
            e.preventDefault();
            const x = e.touches[0].pageX - servicesBar.offsetLeft;
            const walk = (x - startX) * 2;
            servicesBar.scrollLeft = scrollLeft - walk;
        });

        servicesBar.addEventListener('touchend', () => {
            startX = 0;
        });
    }

    // تحديث حالة ظهور الأسهم
    updateArrowsVisibility(servicesBar, leftArrow, rightArrow) {
        const scrollLeft = Math.abs(servicesBar.scrollLeft);
        const scrollWidth = servicesBar.scrollWidth;
        const clientWidth = servicesBar.clientWidth;
        const maxScrollLeft = scrollWidth - clientWidth;

        console.log('Scroll Debug:', {
            scrollLeft: scrollLeft,
            scrollWidth: scrollWidth,
            clientWidth: clientWidth,
            maxScrollLeft: maxScrollLeft,
            canScrollLeft: scrollLeft < maxScrollLeft,
            canScrollRight: scrollLeft > 0
        });

        // إخفاء الأسهم إذا لم يكن هناك حاجة للتمرير
        if (scrollWidth <= clientWidth + 10) { // 10 بكسل تسامح
            leftArrow.style.display = 'none';
            rightArrow.style.display = 'none';
            console.log('Both arrows hidden - no scrolling needed');
            return;
        } else {
            leftArrow.style.display = 'flex';
            rightArrow.style.display = 'flex';
            console.log('Both arrows visible - scrolling available');
        }

        // السهم الأيسر (للتمرير لليسار)
        if (scrollLeft >= maxScrollLeft - 5) { // 5 بكسل تسامح
            leftArrow.style.opacity = '0.3';
            leftArrow.style.pointerEvents = 'none';
            leftArrow.style.cursor = 'not-allowed';
            console.log('Left arrow disabled');
        } else {
            leftArrow.style.opacity = '0.7';
            leftArrow.style.pointerEvents = 'auto';
            leftArrow.style.cursor = 'pointer';
            console.log('Left arrow enabled');
        }

        // السهم الأيمن (للتمرير لليمين)
        if (scrollLeft <= 5) { // 5 بكسل تسامح
            rightArrow.style.opacity = '0.3';
            rightArrow.style.pointerEvents = 'none';
            rightArrow.style.cursor = 'not-allowed';
            console.log('Right arrow disabled');
        } else {
            rightArrow.style.opacity = '0.7';
            rightArrow.style.pointerEvents = 'auto';
            rightArrow.style.cursor = 'pointer';
            console.log('Right arrow enabled');
        }
    }

    // دالة لتحديث الشريط عند تغيير الوضع (فاتح/مظلم)
    updateTheme() {
        this.createServicesBar();
    }
}

// تصدير الكلاس للاستخدام العام
window.ServicesBarManager = ServicesBarManager;

// تهيئة مدير شريط الخدمات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من وجود بيانات الخدمات قبل التهيئة
    if (window.invoiceServices) {
        window.servicesBarManager = new ServicesBarManager();
    } else {
        console.warn('بيانات الخدمات غير متاحة - لن يتم إنشاء شريط الخدمات');
    }
});