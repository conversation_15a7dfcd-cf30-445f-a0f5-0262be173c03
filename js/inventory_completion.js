// نظام إكمال الجرد المحسن مع تتبع التقدم
let inventoryProcessing = false;
let processSteps = [];
let currentStepIndex = 0;
let progressModalInstance = null;
let keepModalOpen = false;



// دالة لإرسال نموذج الجرد
function submitInventoryForm() {
    const form = document.getElementById('inventoryForm');
    if (form) {
        form.submit();
    }
}

// نظام إكمال الجرد المحسن مع تتبع التقدم
function preventMultipleSubmissions() {
    console.log('تم استدعاء دالة preventMultipleSubmissions');
    
    if (inventoryProcessing) {
        Swal.fire({
            icon: 'warning',
            title: 'عملية جارية',
            text: 'يتم تنفيذ عملية إكمال الجرد حالياً، يرجى الانتظار...',
            timer: 3000,
            showConfirmButton: false
        });
        return false;
    }

    // التحقق من وجود أصناف بدون كميات
    const emptyItems = [];
    const allRows = document.querySelectorAll('tr[data-item-id]');
    console.log('عدد الصفوف الموجودة:', allRows.length);
    
    allRows.forEach(row => {
        const itemName = row.cells[0].textContent.trim();
        const quantityInput = row.querySelector('input[name="closing_quantity[]"]');
        const quantity = quantityInput ? quantityInput.value.trim() : '';
        
        console.log(`الصنف: ${itemName}, الكمية: "${quantity}"`);
        
        if (quantity === '' || quantity === '0') {
            emptyItems.push(itemName);
        }
    });
    
    console.log('عدد الأصناف الفارغة:', emptyItems.length);
    
    if (emptyItems.length > 0) {
        const itemsList = emptyItems.slice(0, 10).join('، ') + (emptyItems.length > 10 ? `... و ${emptyItems.length - 10} أصناف أخرى` : '');
        
        Swal.fire({
            title: 'يوجد أصناف لم يتم إدخال كمياتها',
            html: `
                <div style="text-align: right; direction: rtl;">
                    <p style="margin-bottom: 15px; font-weight: bold; color: #dc3545;">
                        <i class="fas fa-exclamation-triangle"></i> 
                        يجب إكمال الجرد لجميع الأصناف قبل المتابعة
                    </p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>عدد الأصناف المتبقية: ${emptyItems.length}</strong>
                    </div>
                    <div style="background: #fff3cd; padding: 10px; border-radius: 5px; font-size: 14px;">
                        <strong>بعض الأصناف المتبقية:</strong><br>
                        ${itemsList}
                    </div>
                </div>
            `,
            icon: 'warning',
            confirmButtonText: 'حسناً، سأكمل الجرد',
            confirmButtonColor: '#ffc107',
            showCancelButton: true,
            cancelButtonText: 'متابعة بدون إكمال',
            cancelButtonColor: '#dc3545',
            reverseButtons: true,
            customClass: {
                popup: 'text-right'
            }
        }).then((result) => {
            if (result.isDismissed || result.dismiss === Swal.DismissReason.cancel) {
                // المستخدم اختار المتابعة بدون إكمال
                proceedWithInventoryCompletion();
            }
            // إذا اختار "حسناً، سأكمل الجرد" لا نفعل شيء (يبقى في الصفحة)
        });
        
        return false; // منع الإرسال
    }
    
    // إذا كانت جميع الأصناف مكتملة، متابعة الإرسال
    proceedWithInventoryCompletion();
}

function proceedWithInventoryCompletion() {
    if (inventoryProcessing) return;
    
    inventoryProcessing = true;
    
    // تحديد خطوات العملية
    processSteps = [
        { name: 'التحقق من البيانات', duration: 2000, action: validateInventoryData },
        { name: 'حفظ أصناف الجرد', duration: 3000, action: saveInventoryItems },
        { name: 'نقل المصروفات', duration: 2500, action: transferExpenses },
        { name: 'نقل إقفالات الورديات', duration: 2000, action: transferShiftClosures },
        { name: 'نقل تحويلات الأرصدة', duration: 1500, action: transferBalanceTransfers },
        { name: 'نقل فواتير الشراء', duration: 3000, action: transferPurchaseInvoices },
        { name: 'إنهاء العملية', duration: 1000, action: finalizeProcess }
    ];
    
    currentStepIndex = 0;
    
    // إظهار نافذة التقدم
    showProgressModal();
    
    // بدء تنفيذ الخطوات
    executeNextStep();
}

function showProgressModal() {
    const progressHtml = `
        <div style="text-align: right; direction: rtl;">
            <div style="margin-bottom: 20px;">
                <h4 style="color: #007bff; margin-bottom: 15px;">
                    <i class="fas fa-cogs"></i> جاري إكمال الجرد
                </h4>
                <p style="color: #6c757d; margin-bottom: 20px;">
                    يتم تنفيذ العملية على مراحل متعددة لضمان سلامة البيانات
                </p>
            </div>
            
            <!-- شريط التقدم العام -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span style="font-weight: bold;">التقدم العام</span>
                    <span id="overallProgress">0%</span>
                </div>
                <div style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden;">
                    <div id="overallProgressBar" style="background: linear-gradient(90deg, #007bff, #0056b3); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
            </div>
            
            <!-- الخطوة الحالية -->
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                    <div id="currentStepIcon" style="width: 30px; height: 30px; border-radius: 50%; background: #007bff; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <div>
                        <div id="currentStepName" style="font-weight: bold; color: #007bff;">جاري التحضير...</div>
                        <div id="currentStepStatus" style="font-size: 0.9em; color: #6c757d;">يرجى الانتظار</div>
                    </div>
                </div>
                <div style="background: #e9ecef; border-radius: 5px; height: 8px; overflow: hidden;">
                    <div id="currentStepProgress" style="background: #28a745; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
            </div>
            
            <!-- قائمة الخطوات -->
            <div style="max-height: 200px; overflow-y: auto;">
                <div id="stepsList">
                    ${processSteps.map((step, index) => `
                        <div id="step-${index}" class="step-item" style="display: flex; align-items: center; gap: 10px; padding: 8px; margin-bottom: 5px; border-radius: 5px; background: ${index === 0 ? '#e3f2fd' : '#f8f9fa'};">
                            <div class="step-icon" style="width: 20px; height: 20px; border-radius: 50%; background: ${index === 0 ? '#2196f3' : '#dee2e6'}; display: flex; align-items: center; justify-content: center; font-size: 12px; color: white;">
                                ${index === 0 ? '<i class="fas fa-spinner fa-spin"></i>' : (index + 1)}
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: ${index === 0 ? 'bold' : 'normal'}; color: ${index === 0 ? '#1976d2' : '#6c757d'};">${step.name}</div>
                            </div>
                            <div class="step-status" style="font-size: 0.8em; color: #6c757d;">
                                ${index === 0 ? 'جاري التنفيذ...' : 'في الانتظار'}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div style="margin-top: 20px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 0.9em;">
                <i class="fas fa-info-circle" style="color: #856404;"></i>
                <strong>ملاحظة:</strong> لا تغلق هذه النافذة أو تحديث الصفحة أثناء تنفيذ العملية
            </div>
            
            <!-- تحذير إضافي -->
            <div style="margin-top: 10px; padding: 8px; background: #f8d7da; border-radius: 5px; font-size: 0.85em; color: #721c24;">
                <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                <strong>تحذير:</strong> إذا ظهرت رسائل أخرى، ستعود هذه النافذة تلقائياً للمتابعة
            </div>
        </div>
    `;
    
    keepModalOpen = true;
    progressModalInstance = Swal.fire({
        html: progressHtml,
        width: '600px',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        showCloseButton: false,
        customClass: {
            popup: 'text-right'
        },
        didOpen: () => {
            // تعطيل جميع طرق إغلاق النافذة أثناء المعالجة
            const swalContainer = Swal.getContainer();
            if (swalContainer) {
                swalContainer.style.pointerEvents = 'none';
                swalContainer.querySelector('.swal2-popup').style.pointerEvents = 'auto';
            }
        }
    });
    
    // مراقبة إغلاق النافذة وإعادة فتحها إذا لزم الأمر
    monitorModalState();
}

// دالة مراقبة حالة النافذة وإعادة فتحها إذا أُغلقت أثناء المعالجة
function monitorModalState() {
    const checkModalInterval = setInterval(() => {
        if (keepModalOpen && inventoryProcessing && (!Swal.isVisible() || !progressModalInstance)) {
            // النافذة مُغلقة والمعالجة ما زالت جارية - إعادة فتحها
            console.log('إعادة فتح نافذة التقدم...');
            showProgressModal();
            updateCurrentProgress(); // تحديث التقدم الحالي
        }
        
        if (!keepModalOpen || !inventoryProcessing) {
            clearInterval(checkModalInterval);
        }
    }, 1000);
}

// دالة لتحديث التقدم الحالي عند إعادة فتح النافذة
function updateCurrentProgress() {
    if (currentStepIndex < processSteps.length) {
        const currentStep = processSteps[currentStepIndex];
        updateCurrentStepUI(currentStep, currentStepIndex);
        updateOverallProgress();
    }
}

function executeNextStep() {
    if (currentStepIndex >= processSteps.length) {
        // انتهت جميع الخطوات
        completeInventoryProcess();
        return;
    }
    
    const currentStep = processSteps[currentStepIndex];
    
    // تحديث واجهة الخطوة الحالية
    updateCurrentStepUI(currentStep, currentStepIndex);
    
    // تحديث التقدم العام
    updateOverallProgress();
    
    // تنفيذ الخطوة
    executeStep(currentStep, currentStepIndex);
}

function updateCurrentStepUI(step, index) {
    const currentStepName = document.getElementById('currentStepName');
    const currentStepStatus = document.getElementById('currentStepStatus');
    const currentStepIcon = document.getElementById('currentStepIcon');
    const currentStepProgress = document.getElementById('currentStepProgress');
    
    if (currentStepName) currentStepName.textContent = step.name;
    if (currentStepStatus) currentStepStatus.textContent = 'جاري التنفيذ...';
    if (currentStepIcon) currentStepIcon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    if (currentStepProgress) currentStepProgress.style.width = '0%';
    
    // تحديث قائمة الخطوات
    document.querySelectorAll('.step-item').forEach((item, i) => {
        const stepIcon = item.querySelector('.step-icon');
        const stepStatus = item.querySelector('.step-status');
        
        if (i < index) {
            // خطوة مكتملة
            item.style.background = '#d4edda';
            stepIcon.style.background = '#28a745';
            stepIcon.innerHTML = '<i class="fas fa-check"></i>';
            stepStatus.textContent = 'مكتمل';
            stepStatus.style.color = '#155724';
        } else if (i === index) {
            // خطوة حالية
            item.style.background = '#e3f2fd';
            stepIcon.style.background = '#2196f3';
            stepIcon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            stepStatus.textContent = 'جاري التنفيذ...';
            stepStatus.style.color = '#1976d2';
        } else {
            // خطوة في الانتظار
            item.style.background = '#f8f9fa';
            stepIcon.style.background = '#dee2e6';
            stepIcon.innerHTML = i + 1;
            stepStatus.textContent = 'في الانتظار';
            stepStatus.style.color = '#6c757d';
        }
    });
}

function updateOverallProgress() {
    const progress = Math.round((currentStepIndex / processSteps.length) * 100);
    const overallProgress = document.getElementById('overallProgress');
    const overallProgressBar = document.getElementById('overallProgressBar');
    
    if (overallProgress) overallProgress.textContent = progress + '%';
    if (overallProgressBar) overallProgressBar.style.width = progress + '%';
}

function executeStep(step, index) {
    // محاكاة تقدم الخطوة
    let stepProgress = 0;
    const stepInterval = setInterval(() => {
        stepProgress += Math.random() * 20;
        if (stepProgress > 100) stepProgress = 100;
        
        const currentStepProgress = document.getElementById('currentStepProgress');
        if (currentStepProgress) {
            currentStepProgress.style.width = stepProgress + '%';
        }
        
        if (stepProgress >= 100) {
            clearInterval(stepInterval);
            
            // تنفيذ الإجراء الفعلي للخطوة
            if (step.action) {
                step.action().then(() => {
                    completeCurrentStep();
                }).catch((error) => {
                    handleStepError(error, step, index);
                });
            } else {
                completeCurrentStep();
            }
        }
    }, step.duration / 10);
}

function completeCurrentStep() {
    // تحديث واجهة الخطوة المكتملة
    const currentStepStatus = document.getElementById('currentStepStatus');
    const currentStepIcon = document.getElementById('currentStepIcon');
    
    if (currentStepStatus) currentStepStatus.textContent = 'مكتمل بنجاح';
    if (currentStepIcon) {
        currentStepIcon.style.background = '#28a745';
        currentStepIcon.innerHTML = '<i class="fas fa-check"></i>';
    }
    
    // الانتقال للخطوة التالية
    currentStepIndex++;
    setTimeout(() => {
        executeNextStep();
    }, 500);
}

function handleStepError(error, step, index) {
    console.error(`خطأ في الخطوة ${step.name}:`, error);
    
    const currentStepStatus = document.getElementById('currentStepStatus');
    const currentStepIcon = document.getElementById('currentStepIcon');
    
    if (currentStepStatus) currentStepStatus.textContent = 'فشل في التنفيذ';
    if (currentStepIcon) {
        currentStepIcon.style.background = '#dc3545';
        currentStepIcon.innerHTML = '<i class="fas fa-times"></i>';
    }
    
    // تحديث قائمة الخطوات
    const stepItem = document.getElementById(`step-${index}`);
    if (stepItem) {
        stepItem.style.background = '#f8d7da';
        const stepIcon = stepItem.querySelector('.step-icon');
        const stepStatus = stepItem.querySelector('.step-status');
        
        if (stepIcon) {
            stepIcon.style.background = '#dc3545';
            stepIcon.innerHTML = '<i class="fas fa-times"></i>';
        }
        if (stepStatus) {
            stepStatus.textContent = 'فشل';
            stepStatus.style.color = '#721c24';
        }
    }
    
    // إيقاف مراقبة النافذة مؤقتاً لإظهار رسالة الخطأ
    keepModalOpen = false;
    
    // إظهار خيارات للمستخدم
    setTimeout(() => {
        Swal.fire({
            icon: 'error',
            title: 'فشل في تنفيذ الخطوة',
            html: `
                <div style="text-align: right; direction: rtl;">
                    <p>فشل في تنفيذ خطوة: <strong>${step.name}</strong></p>
                    <p style="color: #dc3545; font-size: 0.9em;">الخطأ: ${error.message || 'خطأ غير محدد'}</p>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'إعادة المحاولة',
            cancelButtonText: 'تخطي هذه الخطوة',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // استئناف مراقبة النافذة وإعادة تنفيذ الخطوة
                keepModalOpen = true;
                showProgressModal();
                updateCurrentProgress();
                executeStep(step, index);
            } else {
                // تخطي الخطوة والمتابعة
                keepModalOpen = true;
                showProgressModal();
                updateCurrentProgress();
                currentStepIndex++;
                executeNextStep();
            }
        });
    }, 1000);
}

function completeInventoryProcess() {
    inventoryProcessing = false;
    keepModalOpen = false; // السماح بإغلاق النافذة الآن
    
    // تحديث التقدم النهائي
    const overallProgress = document.getElementById('overallProgress');
    const overallProgressBar = document.getElementById('overallProgressBar');
    
    if (overallProgress) overallProgress.textContent = '100%';
    if (overallProgressBar) overallProgressBar.style.width = '100%';
    
    setTimeout(() => {
        Swal.fire({
            icon: 'success',
            title: 'تم إكمال الجرد بنجاح',
            html: `
                <div style="text-align: right; direction: rtl;">
                    <p style="margin-bottom: 15px;">تم إكمال جميع خطوات الجرد بنجاح</p>
                    <div style="background: #d4edda; padding: 15px; border-radius: 8px;">
                        <i class="fas fa-check-circle" style="color: #155724;"></i>
                        <strong>العمليات المكتملة:</strong>
                        <ul style="margin: 10px 0; padding-right: 20px; text-align: right;">
                            <li>حفظ أصناف الجرد</li>
                            <li>نقل المصروفات</li>
                            <li>نقل إقفالات الورديات</li>
                            <li>نقل تحويلات الأرصدة</li>
                            <li>نقل فواتير الشراء</li>
                        </ul>
                    </div>
                </div>
            `,
            confirmButtonText: 'حسناً',
            allowOutsideClick: false
        }).then(() => {
            // إرسال النموذج الفعلي
            submitInventoryForm();
        });
    }, 1000);
}

// دوال تنفيذ الخطوات الفعلية
async function validateInventoryData() {
    return new Promise((resolve, reject) => {
        try {
            // التحقق من صحة البيانات
            const form = document.querySelector('form[method="POST"]');
            if (!form) {
                throw new Error('لم يتم العثور على نموذج الجرد');
            }
            
            const inventoryItems = document.querySelectorAll('tr[data-item-id]');
            if (inventoryItems.length === 0) {
                throw new Error('لا توجد أصناف في الجرد');
            }
            
            resolve();
        } catch (error) {
            reject(error);
        }
    });
}

async function saveInventoryItems() {
    return new Promise((resolve) => {
        // حفظ البيانات محلياً كنسخة احتياطية
        if (typeof saveAllQuantities === 'function') {
            saveAllQuantities();
        }
        resolve();
    });
}

async function transferExpenses() {
    return new Promise((resolve) => {
        // محاكاة نقل المصروفات
        setTimeout(resolve, 1000);
    });
}

async function transferShiftClosures() {
    return new Promise((resolve) => {
        // محاكاة نقل إقفالات الورديات
        setTimeout(resolve, 800);
    });
}

async function transferBalanceTransfers() {
    return new Promise((resolve) => {
        // محاكاة نقل تحويلات الأرصدة
        setTimeout(resolve, 600);
    });
}

async function transferPurchaseInvoices() {
    return new Promise((resolve) => {
        // محاكاة نقل فواتير الشراء
        setTimeout(resolve, 1200);
    });
}

async function finalizeProcess() {
    return new Promise((resolve) => {
        // إنهاء العملية
        setTimeout(resolve, 500);
    });
}

function submitInventoryForm() {
    // إرسال النموذج الفعلي
    const form = document.querySelector('form[method="POST"]');
    if (form) {
        form.submit();
    } else {
        console.error('لم يتم العثور على نموذج الجرد');
        Swal.fire({
            icon: 'error',
            title: 'خطأ في الإرسال',
            text: 'لم يتم العثور على نموذج الجرد',
            timer: 3000,
            showConfirmButton: false
        });
    }
}

// تصدير الدوال للاستخدام العام
window.preventMultipleSubmissions = preventMultipleSubmissions;
window.proceedWithInventoryCompletion = proceedWithInventoryCompletion;