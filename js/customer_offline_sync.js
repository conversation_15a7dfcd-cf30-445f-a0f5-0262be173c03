/**
 * Customer Offline Sync
 * نظام الحفظ الأوفلاين للعملاء المتعددين
 */

class CustomerOfflineSync {
    constructor(accountId) {
        this.accountId = accountId;
        this.dbName = `customer_invoices_${accountId}`;
        this.dbVersion = 1;
        this.db = null;
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.fileSystemManager = null;
        this.init();
    }

    async init() {
        try {
            // تهيئة قاعدة البيانات
            await this.initDatabase();
            
            // Initialize File System Manager if available
            if (typeof FileSystemManager !== 'undefined') {
                this.fileSystemManager = new FileSystemManager();
                console.log('File System Manager initialized for customer offline sync');
            } else {
                console.log('File System Manager not available for customer offline sync');
            }
            
            // مراقبة حالة الاتصال
            this.setupConnectionMonitoring();
            
            // تفعيل المزامنة التلقائية
            this.startAutoSync();
            
            console.log('Customer Offline Sync initialized');

        } catch (error) {
            console.error('Error initializing Customer Offline Sync:', error);
        }
    }

    /**
     * تهيئة قاعدة البيانات المحلية
     */
    async initDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('Failed to open database'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // إنشاء جدول الفواتير
                if (!db.objectStoreNames.contains('invoices')) {
                    const invoiceStore = db.createObjectStore('invoices', {
                        keyPath: 'id',
                        autoIncrement: true
                    });
                    
                    invoiceStore.createIndex('customerId', 'customerId', { unique: false });
                    invoiceStore.createIndex('timestamp', 'timestamp', { unique: false });
                    invoiceStore.createIndex('synced', 'synced', { unique: false });
                    invoiceStore.createIndex('invoiceType', 'invoiceType', { unique: false });
                }

                // إنشاء جدول الجلسات
                if (!db.objectStoreNames.contains('sessions')) {
                    const sessionStore = db.createObjectStore('sessions', {
                        keyPath: 'customerId'
                    });
                    
                    sessionStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
                }

                // إنشاء جدول السجلات
                if (!db.objectStoreNames.contains('logs')) {
                    const logStore = db.createObjectStore('logs', {
                        keyPath: 'id',
                        autoIncrement: true
                    });
                    
                    logStore.createIndex('timestamp', 'timestamp', { unique: false });
                    logStore.createIndex('type', 'type', { unique: false });
                }
            };
        });
    }

    /**
     * حفظ فاتورة عميل في قاعدة البيانات المحلية
     */
    async saveCustomerInvoice(customerId, invoiceData) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['invoices'], 'readwrite');
            const store = transaction.objectStore('invoices');

            // تنظيف البيانات من عناصر DOM قبل الحفظ
            const cleanInvoiceData = this.cleanDataForStorage(invoiceData);

            const invoice = {
                customerId: customerId,
                accountId: this.accountId,
                items: cleanInvoiceData.items || [],
                invoiceType: cleanInvoiceData.invoiceType || 'purchase',
                branchId: cleanInvoiceData.branchId || null,
                accountBuyerId: cleanInvoiceData.accountBuyerId || null,
                timestamp: Date.now(),
                synced: this.isOnline,
                totalAmount: this.calculateTotal(cleanInvoiceData.items || []),
                metadata: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    version: '1.0.0'
                }
            };

            const request = store.add(invoice);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    console.log(`Invoice saved for customer ${customerId}:`, invoice);
                    
                    // إضافة للطابور إذا كان غير متصل
                    if (!this.isOnline) {
                        this.addToSyncQueue(invoice);
                    } else {
                        // محاولة المزامنة فوراً
                        this.syncInvoice(invoice);
                    }
                    
                    resolve(request.result);
                };

                request.onerror = () => {
                    reject(new Error('Failed to save invoice'));
                };
            });

        } catch (error) {
            console.error('Error saving customer invoice:', error);
            throw error;
        }
    }

    /**
     * حفظ جلسة عميل
     */
    async saveCustomerSession(customerId, sessionData) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['sessions'], 'readwrite');
            const store = transaction.objectStore('sessions');

            const session = {
                customerId: customerId,
                ...sessionData,
                lastUpdated: Date.now()
            };

            const request = store.put(session);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    console.log(`Session saved for customer ${customerId}`);
                    resolve(request.result);
                };

                request.onerror = () => {
                    reject(new Error('Failed to save session'));
                };
            });

        } catch (error) {
            console.error('Error saving customer session:', error);
            throw error;
        }
    }

    /**
     * تحميل جلسة عميل
     */
    async loadCustomerSession(customerId) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['sessions'], 'readonly');
            const store = transaction.objectStore('sessions');
            const request = store.get(customerId);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    resolve(request.result || null);
                };

                request.onerror = () => {
                    reject(new Error('Failed to load session'));
                };
            });

        } catch (error) {
            console.error('Error loading customer session:', error);
            return null;
        }
    }

    /**
     * تحميل جميع الجلسات
     */
    async loadAllSessions() {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['sessions'], 'readonly');
            const store = transaction.objectStore('sessions');
            const request = store.getAll();

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    const sessions = {};
                    request.result.forEach(session => {
                        sessions[session.customerId] = session;
                    });
                    resolve(sessions);
                };

                request.onerror = () => {
                    reject(new Error('Failed to load sessions'));
                };
            });

        } catch (error) {
            console.error('Error loading all sessions:', error);
            return {};
        }
    }

    /**
     * الحصول على الفواتير غير المتزامنة
     */
    async getUnsyncedInvoices() {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['invoices'], 'readonly');
            const store = transaction.objectStore('invoices');
            const index = store.index('synced');
            const request = index.getAll(false);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    resolve(request.result);
                };

                request.onerror = () => {
                    reject(new Error('Failed to get unsynced invoices'));
                };
            });

        } catch (error) {
            console.error('Error getting unsynced invoices:', error);
            return [];
        }
    }

    /**
     * مزامنة فاتورة واحدة
     */
    async syncInvoice(invoice) {
        try {
            if (!this.isOnline) {
                console.log('Offline - adding invoice to sync queue');
                this.addToSyncQueue(invoice);
                return false;
            }

            // إعداد البيانات للإرسال
            const formData = new FormData();
            formData.append('account_id', window.encryptedAccountId);
            formData.append('items', JSON.stringify(invoice.items));
            formData.append('invoice_type', invoice.invoiceType);
            
            if (invoice.branchId) {
                formData.append('branch_id', invoice.branchId);
            }
            
            if (invoice.accountBuyerId) {
                formData.append('account_buyer_id', invoice.accountBuyerId);
            }

            // إرسال الفاتورة
            const response = await fetch('save_sale_invoice.php', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                // تحدي�� حالة المزامنة
                await this.markInvoiceAsSynced(invoice.id);
                console.log('Invoice synced successfully:', invoice.id);
                return true;
            } else {
                throw new Error('Failed to sync invoice');
            }

        } catch (error) {
            console.error('Error syncing invoice:', error);
            this.addToSyncQueue(invoice);
            return false;
        }
    }

    /**
     * مزامنة جميع الفواتير غير المتزامنة
     */
    async syncAllInvoices() {
        try {
            const unsyncedInvoices = await this.getUnsyncedInvoices();
            
            if (unsyncedInvoices.length === 0) {
                console.log('No invoices to sync');
                return { success: 0, failed: 0 };
            }

            let successCount = 0;
            let failedCount = 0;

            for (const invoice of unsyncedInvoices) {
                const synced = await this.syncInvoice(invoice);
                if (synced) {
                    successCount++;
                } else {
                    failedCount++;
                }
            }

            console.log(`Sync completed: ${successCount} success, ${failedCount} failed`);
            
            // إظهار إشعار للمستخ��م
            if (typeof toastr !== 'undefined') {
                if (failedCount === 0) {
                    toastr.success(`تم مزامنة ${successCount} فاتورة بنجاح`);
                } else {
                    toastr.warning(`تم مزامنة ${successCount} فاتورة، فشل في ${failedCount}`);
                }
            }

            return { success: successCount, failed: failedCount };

        } catch (error) {
            console.error('Error syncing all invoices:', error);
            return { success: 0, failed: 0 };
        }
    }

    /**
     * تحديد فاتورة كمتزامنة
     */
    async markInvoiceAsSynced(invoiceId) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['invoices'], 'readwrite');
            const store = transaction.objectStore('invoices');
            const getRequest = store.get(invoiceId);

            return new Promise((resolve, reject) => {
                getRequest.onsuccess = () => {
                    const invoice = getRequest.result;
                    if (invoice) {
                        invoice.synced = true;
                        invoice.syncedAt = Date.now();
                        
                        const putRequest = store.put(invoice);
                        putRequest.onsuccess = () => resolve();
                        putRequest.onerror = () => reject(new Error('Failed to update invoice'));
                    } else {
                        reject(new Error('Invoice not found'));
                    }
                };

                getRequest.onerror = () => {
                    reject(new Error('Failed to get invoice'));
                };
            });

        } catch (error) {
            console.error('Error marking invoice as synced:', error);
            throw error;
        }
    }

    /**
     * إضافة فاتورة لطابور المزامنة
     */
    addToSyncQueue(invoice) {
        const existingIndex = this.syncQueue.findIndex(item => item.id === invoice.id);
        
        if (existingIndex === -1) {
            this.syncQueue.push(invoice);
            console.log('Invoice added to sync queue:', invoice.id);
        }
    }

    /**
     * معالجة طابور المزامنة
     */
    async processSyncQueue() {
        if (!this.isOnline || this.syncQueue.length === 0) {
            return;
        }

        console.log(`Processing sync queue: ${this.syncQueue.length} items`);

        const queueCopy = [...this.syncQueue];
        this.syncQueue = [];

        for (const invoice of queueCopy) {
            const synced = await this.syncInvoice(invoice);
            if (!synced) {
                // إعادة إضافة للطابور إذا فشلت المزامنة
                this.addToSyncQueue(invoice);
            }
        }
    }

    /**
     * إعداد مراقبة حالة الاتصال
     */
    setupConnectionMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('Connection restored - processing sync queue');
            this.processSyncQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('Connection lost - switching to offline mode');
        });
    }

    /**
     * تفعيل المزامنة التلقائية
     */
    startAutoSync() {
        // مزامنة كل 30 ثانية
        setInterval(() => {
            if (this.isOnline) {
                this.processSyncQueue();
            }
        }, 30000);

        // مزامنة عند استعادة التركيز على النافذة
        window.addEventListener('focus', () => {
            if (this.isOnline) {
                this.processSyncQueue();
            }
        });
    }

    /**
     * تنظيف البيانات من عناصر DOM قبل الحفظ
     */
    cleanDataForStorage(data) {
        if (data === null || data === undefined) {
            return data;
        }
        
        // If it's a DOM element, return null
        if (data instanceof Element || data instanceof HTMLElement) {
            console.warn('DOM element detected and removed from customer storage data');
            return null;
        }
        
        // If it's an array, clean each element
        if (Array.isArray(data)) {
            return data.map(item => this.cleanDataForStorage(item)).filter(item => item !== null);
        }
        
        // If it's an object, clean each property
        if (typeof data === 'object') {
            const cleanedData = {};
            for (const [key, value] of Object.entries(data)) {
                const cleanedValue = this.cleanDataForStorage(value);
                if (cleanedValue !== null) {
                    cleanedData[key] = cleanedValue;
                }
            }
            return cleanedData;
        }
        
        // For primitive types, return as is
        return data;
    }

    /**
     * حساب إجمالي الفاتورة
     */
    calculateTotal(items) {
        return items.reduce((total, item) => {
            return total + ((item.quantity || 1) * parseFloat(item.price || 0));
        }, 0);
    }

    /**
     * تسجيل حدث في السجل
     */
    async logEvent(type, message, data = null) {
        try {
            if (!this.db) return;

            const transaction = this.db.transaction(['logs'], 'readwrite');
            const store = transaction.objectStore('logs');

            const logEntry = {
                type: type,
                message: message,
                data: data,
                timestamp: Date.now(),
                userAgent: navigator.userAgent
            };

            store.add(logEntry);

        } catch (error) {
            console.error('Error logging event:', error);
        }
    }

    /**
     * الحصول على إحصائيات قاعدة البيانات
     */
    async getStats() {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const stats = {
                totalInvoices: 0,
                unsyncedInvoices: 0,
                totalSessions: 0,
                dbSize: 0
            };

            // عدد الفواتير الإجمالي
            const invoiceTransaction = this.db.transaction(['invoices'], 'readonly');
            const invoiceStore = invoiceTransaction.objectStore('invoices');
            const invoiceCountRequest = invoiceStore.count();

            // عدد الفواتير غير المتزامنة
            const unsyncedIndex = invoiceStore.index('synced');
            const unsyncedCountRequest = unsyncedIndex.count(false);

            // عدد الجلسات
            const sessionTransaction = this.db.transaction(['sessions'], 'readonly');
            const sessionStore = sessionTransaction.objectStore('sessions');
            const sessionCountRequest = sessionStore.count();

            return new Promise((resolve) => {
                let completed = 0;
                const checkComplete = () => {
                    completed++;
                    if (completed === 3) {
                        resolve(stats);
                    }
                };

                invoiceCountRequest.onsuccess = () => {
                    stats.totalInvoices = invoiceCountRequest.result;
                    checkComplete();
                };

                unsyncedCountRequest.onsuccess = () => {
                    stats.unsyncedInvoices = unsyncedCountRequest.result;
                    checkComplete();
                };

                sessionCountRequest.onsuccess = () => {
                    stats.totalSessions = sessionCountRequest.result;
                    checkComplete();
                };
            });

        } catch (error) {
            console.error('Error getting stats:', error);
            return null;
        }
    }

    /**
     * تنظيف البيانات الق��يمة
     */
    async cleanup(maxAge = 30 * 24 * 60 * 60 * 1000) { // 30 days
        try {
            if (!this.db) return 0;

            const cutoffTime = Date.now() - maxAge;
            let deletedCount = 0;

            // تنظيف الفواتير القديمة المتزامنة
            const transaction = this.db.transaction(['invoices'], 'readwrite');
            const store = transaction.objectStore('invoices');
            const index = store.index('timestamp');
            const range = IDBKeyRange.upperBound(cutoffTime);
            const request = index.openCursor(range);

            return new Promise((resolve) => {
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        const invoice = cursor.value;
                        if (invoice.synced) {
                            cursor.delete();
                            deletedCount++;
                        }
                        cursor.continue();
                    } else {
                        console.log(`Cleaned up ${deletedCount} old invoices`);
                        resolve(deletedCount);
                    }
                };
            });

        } catch (error) {
            console.error('Error during cleanup:', error);
            return 0;
        }
    }

    /**
     * تدمير المدير وتنظيف الموارد
     */
    async destroy() {
        try {
            // مزامنة أخيرة
            await this.processSyncQueue();
            
            // إغلاق قاعدة البيانات
            if (this.db) {
                this.db.close();
                this.db = null;
            }

            console.log('Customer Offline Sync destroyed');

        } catch (error) {
            console.error('Error destroying Customer Offline Sync:', error);
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.CustomerOfflineSync = CustomerOfflineSync;