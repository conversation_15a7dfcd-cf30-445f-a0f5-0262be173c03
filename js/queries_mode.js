// متغيرات عامة لوضع الاستعلامات
let queriesItems = [];
let barcodeItemsMap = {};
let isQueriesModeActive = false;

// دالة تشغيل/إيقاف وضع الاستعلامات
function toggleQueriesMode() {
    const normalView = document.querySelector('.table-container');
    const queriesView = document.getElementById('queriesMode');
    
    if (isQueriesModeActive) {
        exitQueriesMode();
    } else {
        enterQueriesMode();
    }
}

// دالة الدخول لوضع الاستعلامات
function enterQueriesMode() {
    isQueriesModeActive = true;
    
    // إخفاء العرض العادي
    document.querySelector('.table-container').classList.add('queries-mode-inactive');
    document.querySelector('.reports-actions-container').classList.add('queries-mode-inactive');
    document.getElementById('searchField').style.display = 'none';
    
    // إظهار وضع الاستعلامات
    document.getElementById('queriesMode').classList.remove('queries-mode-inactive');
    document.getElementById('queriesMode').classList.add('queries-mode-active');
    
    // تحميل بيانات الأصناف والباركود
    loadItemsData();
    
    // تحديث حالة الباركود
    updateBarcodeStatus('جاهز لاستقبال الباركود من قارئ الباركود');
    
    // إضافة تأثير نشط لحالة الباركود
    const barcodeStatus = document.querySelector('.barcode-status');
    if (barcodeStatus) {
        barcodeStatus.classList.add('active');
    }
    
    // تحديث نص الزر
    const btn = document.querySelector('.queries-mode-btn');
    btn.innerHTML = '<i class="fas fa-times"></i> الخروج من وضع ا��استعلامات';
    btn.onclick = exitQueriesMode;
    
    // رسالة ترحيب
    showInfoMessage('تم تفعيل وضع الاستعلامات - استخدم قارئ الباركود لإضافة الأصناف');
}

// دالة الخروج من وضع الاستعلامات
function exitQueriesMode() {
    isQueriesModeActive = false;
    
    // إظهار العرض العادي
    document.querySelector('.table-container').classList.remove('queries-mode-inactive');
    document.querySelector('.reports-actions-container').classList.remove('queries-mode-inactive');
    document.getElementById('searchField').style.display = '';
    
    // إخفاء وضع الاستعلامات
    document.getElementById('queriesMode').classList.add('queries-mode-inactive');
    document.getElementById('queriesMode').classList.remove('queries-mode-active');
    
    // إزالة تأثير نشط لحالة الباركود
    const barcodeStatus = document.querySelector('.barcode-status');
    if (barcodeStatus) {
        barcodeStatus.classList.remove('active');
    }
    
    // تحديث نص الزر
    const btn = document.querySelector('.queries-mode-btn');
    btn.innerHTML = '<i class="fas fa-search-plus"></i> وضع الاستعلامات';
    btn.onclick = toggleQueriesMode;
    
    // رسالة وداع
    showInfoMessage('تم الخروج من وضع الاستعلامات');
}

// دالة معالجة الباركود (مستوحاة من invoice_responsive.js)
function processBarcode(barcode) {
    // معالجة الباركود المخصص
    if (barcode.startsWith('2') && barcode.length === 13) {
        return processCustomBarcode(barcode);
    }
    
    // معالجة الباركود العادي
    const itemId = barcodeItemsMap[barcode];
    if (itemId) {
        return findItemById(itemId);
    }
    
    return null;
}

// دالة معالجة الباركود المخصص
function processCustomBarcode(barcode) {
    const itemBarcode = barcode.substring(1, 7);
    const weightInGrams = parseInt(barcode.substring(7, 12));
    const quantityInKg = weightInGrams / 1000;
    
    const itemId = barcodeItemsMap[itemBarcode];
    if (itemId) {
        const item = findItemById(itemId);
        if (item) {
            item.quantity = quantityInKg;
            item.isCustomBarcode = true;
            return item;
        }
    }
    
    return null;
}

// دالة إضافة صنف للجدول
function addItemToQueriesTable(item) {
    // التحقق من وجود الصنف مسبقاً
    const existingIndex = queriesItems.findIndex(qi => qi.id === item.id);
    
    if (existingIndex !== -1) {
        // زيادة الكمية إذا كان موجوداً
        queriesItems[existingIndex].quantity += (item.quantity || 1);
        updateQueriesTableRow(existingIndex);
    } else {
        // إضافة صنف جديد في أعلى المصفوفة
        queriesItems.unshift({
            id: item.id,
            name: item.name,
            type: item.type,
            quantity: item.quantity || 1,
            cost: item.cost,
            price: item.price,
            isCustomBarcode: item.isCustomBarcode || false
        });
        
        addQueriesTableRow(0);
    }
    
    updateQueriesSummary();
}

// دالة إضافة صف جديد للجدول
function addQueriesTableRow(index) {
    const item = queriesItems[index];
    const tbody = document.getElementById('queriesTableBody');
    
    const profitPerPiece = item.price - item.cost;
    const quarterProfit = profitPerPiece / 4;
    const totalWholesale = item.cost * item.quantity;
    
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${item.name}</td>
        <td>${item.type}</td>
        <td>${item.quantity}</td>
        <td>${item.cost}</td>
        <td>${item.price}</td>
        <td>${profitPerPiece.toFixed(2)}</td>
        <td>${quarterProfit.toFixed(2)}</td>
        <td>${totalWholesale.toFixed(2)}</td>
        <td>
            <button onclick="removeQueriesItem(${index})" class="btn btn-danger btn-sm">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    tbody.insertBefore(row, tbody.firstChild);
}

// دالة تحديث صف موجود
function updateQueriesTableRow(index) {
    const item = queriesItems[index];
    const tbody = document.getElementById('queriesTableBody');
    const row = tbody.rows[index];
    
    const profitPerPiece = item.price - item.cost;
    const quarterProfit = profitPerPiece / 4;
    const totalWholesale = item.cost * item.quantity;
    
    row.cells[2].textContent = item.quantity;
    row.cells[5].textContent = profitPerPiece.toFixed(2);
    row.cells[6].textContent = quarterProfit.toFixed(2);
    row.cells[7].textContent = totalWholesale.toFixed(2);
}

// دالة تحديث الملخص
function updateQueriesSummary() {
    const itemCount = queriesItems.length;
    const totalWholesale = queriesItems.reduce((sum, item) => sum + (item.cost * item.quantity), 0);
    const totalProfit = queriesItems.reduce((sum, item) => sum + ((item.price - item.cost) * item.quantity), 0);
    
    document.getElementById('queriesItemCount').textContent = itemCount;
    document.getElementById('queriesTotalWholesale').textContent = totalWholesale.toFixed(2);
    document.getElementById('queriesTotalProfit').textContent = totalProfit.toFixed(2);
}

// دالة تحميل بيانات الأصناف والباركود
function loadItemsData() {
    // إعادة تعيين خريطة الباركود
    barcodeItemsMap = {};
    
    // جلب البيانات من الجدول الموجود في الصفحة
    const tableRows = document.querySelectorAll('#itemsTable tr, .table tbody tr');
    
    tableRows.forEach(row => {
        const itemId = row.dataset.itemId;
        const itemBarcode = row.dataset.itemBarcode;
        
        if (itemId && itemBarcode && itemBarcode.trim() !== '') {
            barcodeItemsMap[itemBarcode] = itemId;
            console.log('تم تحميل الباركود:', itemBarcode, 'للصنف:', row.dataset.itemName);
        }
    });
    
    console.log(`تم تحميل ${Object.keys(barcodeItemsMap).length} باركود للأصناف.`);
}

// دالة البحث عن صنف بواسطة معرفه
function findItemById(itemId) {
    const tableRows = document.querySelectorAll('#itemsTable tr, .table tbody tr');
    
    for (let row of tableRows) {
        if (row.dataset.itemId === itemId) {
            return {
                id: itemId,
                name: row.dataset.itemName,
                type: row.dataset.itemType,
                cost: parseFloat(row.dataset.itemCost) || 0,
                price: parseFloat(row.dataset.itemPrice) || 0,
                barcode: row.dataset.itemBarcode
            };
        }
    }
    
    return null;
}

// دالة مسح جدول الاستعلامات
function clearQueriesTable() {
    if (queriesItems.length === 0) {
        showInfoMessage('الجدول فارغ بالفعل');
        return;
    }
    
    Swal.fire({
        title: 'تأكيد المسح',
        text: 'هل تريد مسح جميع الأصناف من الجدول؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، امسح الكل',
        cancelButtonText: 'إلغاء',
        customClass: {
            popup: document.documentElement.getAttribute('data-theme') === 'dark' ? 'swal2-dark' : ''
        }
    }).then((result) => {
        if (result.isConfirmed) {
            queriesItems = [];
            document.getElementById('queriesTableBody').innerHTML = '';
            updateQueriesSummary();
            showSuccessMessage('تم مسح جميع الأصناف من الجدول');
        }
    });
}

// تحديث كمية صنف
function updateItemQuantity(index, newQuantity) {
    const quantity = parseFloat(newQuantity);
    
    if (isNaN(quantity) || quantity <= 0) {
        showWarningMessage('يجب إدخال كمية صحيحة أكبر من صفر');
        // إعادة القيمة السابقة
        const input = document.querySelector(`#queriesTableBody tr:nth-child(${index + 1}) .quantity-input`);
        if (input) {
            input.value = queriesItems[index].quantity;
        }
        return;
    }
    
    const oldQuantity = queriesItems[index].quantity;
    queriesItems[index].quantity = quantity;
    updateQueriesTableRow(index);
    updateQueriesSummary();
    
    if (quantity !== oldQuantity) {
        showSuccessMessage(`تم تحديث كمية "${queriesItems[index].name}" إلى ${quantity}`);
    }
}

// حذف صنف من الجدول
function removeQueriesItem(index) {
    const item = queriesItems[index];
    
    Swal.fire({
        title: 'تأكيد الحذف',
        text: `هل تريد حذف "${item.name}" من الجدول؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        customClass: {
            popup: document.documentElement.getAttribute('data-theme') === 'dark' ? 'swal2-dark' : ''
        }
    }).then((result) => {
        if (result.isConfirmed) {
            queriesItems.splice(index, 1);
            rebuildQueriesTable();
            updateQueriesSummary();
            showSuccessMessage(`تم حذف "${item.name}" من الجدول`);
        }
    });
}

// إعادة بناء الجدول بالكامل
function rebuildQueriesTable() {
    const tbody = document.getElementById('queriesTableBody');
    tbody.innerHTML = '';
    
    queriesItems.forEach((item, index) => {
        addQueriesTableRow(index);
    });
}

// عرض رسائل النجاح والخطأ
function showSuccessMessage(message) {
    if (typeof toastr !== 'undefined') {
        // تكوين toastr للوضع المظلم والفاتح
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-left",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "3000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut",
            "toastClass": isDarkMode ? "toast toast-dark" : "toast",
            "iconClasses": {
                error: 'toast-error',
                info: 'toast-info',
                success: 'toast-success',
                warning: 'toast-warning'
            }
        };
        
        toastr.success(message, 'نجح العملية', {
            iconClass: 'fas fa-check-circle'
        });
    } else {
        alert(message);
    }
}

function showErrorMessage(message) {
    if (typeof toastr !== 'undefined') {
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-left",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "4000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut",
            "toastClass": isDarkMode ? "toast toast-dark" : "toast",
            "iconClasses": {
                error: 'toast-error',
                info: 'toast-info',
                success: 'toast-success',
                warning: 'toast-warning'
            }
        };
        
        toastr.error(message, 'خطأ', {
            iconClass: 'fas fa-exclamation-circle'
        });
    } else {
        alert(message);
    }
}

function showWarningMessage(message) {
    if (typeof toastr !== 'undefined') {
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-left",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "3500",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut",
            "toastClass": isDarkMode ? "toast toast-dark" : "toast",
            "iconClasses": {
                error: 'toast-error',
                info: 'toast-info',
                success: 'toast-success',
                warning: 'toast-warning'
            }
        };
        
        toastr.warning(message, 'تحذير', {
            iconClass: 'fas fa-exclamation-triangle'
        });
    } else {
        alert(message);
    }
}

function showInfoMessage(message) {
    if (typeof toastr !== 'undefined') {
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-left",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "3000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut",
            "toastClass": isDarkMode ? "toast toast-dark" : "toast",
            "iconClasses": {
                error: 'toast-error',
                info: 'toast-info',
                success: 'toast-success',
                warning: 'toast-warning'
            }
        };
        
        toastr.info(message, 'معلومات', {
            iconClass: 'fas fa-info-circle'
        });
    } else {
        alert(message);
    }
}

// دالة تحديث حالة الباركود
function updateBarcodeStatus(message) {
    const statusElement = document.querySelector('.barcode-status span');
    if (statusElement) {
        statusElement.textContent = message;
    }
}

// دالة فتح نافذة البحث عن الأصناف
function openItemSearchModal() {
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    
    // ألوان متكيفة مع الوضع
    const colors = {
        background: isDarkMode ? '#0d1117' : '#ffffff',
        text: isDarkMode ? '#e6edf3' : '#333333',
        title: isDarkMode ? '#58a6ff' : '#3498db',
        inputBg: isDarkMode ? '#21262d' : '#ffffff',
        inputBorder: isDarkMode ? '#30363d' : '#ced4da',
        tableBg: isDarkMode ? '#161b22' : '#ffffff',
        tableHeaderBg: isDarkMode ? '#21262d' : '#f8f9fa',
        tableBorder: isDarkMode ? '#30363d' : '#dee2e6',
        hoverBg: isDarkMode ? '#21262d' : '#f5f5f5',
        buttonPrimary: isDarkMode ? '#238636' : '#28a745',
        buttonSecondary: isDarkMode ? '#6c757d' : '#6c757d'
    };

    Swal.fire({
        title: '<i class="fas fa-search" style="color: #28a745;"></i> البحث عن الأصناف',
        html: `
            <div style="text-align: right; padding: 20px; font-family: 'Cairo', sans-serif; color: ${colors.text};">
                <div style="margin-bottom: 20px;">
                    <div style="display: grid; gap: 15px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: ${colors.text};">البحث بالاسم:</label>
                            <input type="text" id="searchByName" placeholder="ادخل اسم الصنف..." 
                                   style="width: 100%; padding: 10px; border: 1px solid ${colors.inputBorder}; border-radius: 5px; 
                                          background: ${colors.inputBg}; color: ${colors.text}; font-family: 'Cairo', sans-serif;"
                                   oninput="searchItems()">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: ${colors.text};">البحث بالباركود:</label>
                            <input type="text" id="searchByBarcode" placeholder="ادخل الباركود..." 
                                   style="width: 100%; padding: 10px; border: 1px solid ${colors.inputBorder}; border-radius: 5px; 
                                          background: ${colors.inputBg}; color: ${colors.text}; font-family: 'Cairo', sans-serif;"
                                   oninput="searchItems()">
                        </div>
                    </div>
                </div>
                
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid ${colors.tableBorder}; border-radius: 8px;">
                <table id="searchResultsTable" style="width: 100%; border-collapse: collapse; background: ${colors.tableBg};">
                <thead>
                <tr style="background: ${colors.tableHeaderBg}; position: sticky; top: 0; z-index: 1;">
                <th style="padding: 12px; border-bottom: 1px solid ${colors.tableBorder}; text-align: right; font-weight: 600; color: ${colors.text};">اسم الصنف</th>
                <th style="padding: 12px; border-bottom: 1px solid ${colors.tableBorder}; text-align: center; font-weight: 600; color: ${colors.text};">سعر الجملة</th>
                <th style="padding: 12px; border-bottom: 1px solid ${colors.tableBorder}; text-align: center; font-weight: 600; color: ${colors.text};">سعر البيع</th>
                <th style="padding: 12px; border-bottom: 1px solid ${colors.tableBorder}; text-align: center; font-weight: 600; color: ${colors.text};">إضافة</th>
                </tr>
                </thead>
                <tbody id="searchResultsBody">
                <!-- سيتم ملء النتائج هنا -->
                </tbody>
                </table>
                </div>
                
                <div id="noResultsMessage" style="text-align: center; padding: 40px; color: ${colors.text}; opacity: 0.7; display: none;">
                <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px;"></i>
                <p>لا توجد نتائج للبحث</p>
                </div>
                </div>
                `,
                icon: null,
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق',
                cancelButtonColor: colors.buttonSecondary,
                width: '1200px',
                customClass: {
                popup: isDarkMode ? 'swal2-dark' : '',
                title: isDarkMode ? 'swal2-title-dark' : '',
                content: isDarkMode ? 'swal2-content-dark' : ''
                },
                background: colors.background,
                color: colors.text,
                didOpen: () => {
                // تحميل جميع الأصناف عند فتح النافذة
                loadAllItemsForSearch();
                
                // إضافة مستمع للضغط على Enter في حقول البحث
                document.getElementById('searchByName').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                searchItems();
                }
                });
                
                document.getElementById('searchByBarcode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                searchItems();
                }
                });
                }
                });
}

// دالة تحميل جميع الأصناف للبحث
function loadAllItemsForSearch() {
    const tableRows = document.querySelectorAll('#itemsTable tr, .table tbody tr');
    const searchResultsBody = document.getElementById('searchResultsBody');
    const noResultsMessage = document.getElementById('noResultsMessage');
    
    if (!searchResultsBody) return;
    
    searchResultsBody.innerHTML = '';
    let hasResults = false;
    
    tableRows.forEach(row => {
        const itemId = row.dataset.itemId;
        const itemName = row.dataset.itemName;
        const itemCost = row.dataset.itemCost;
        const itemPrice = row.dataset.itemPrice;
        const itemType = row.dataset.itemType;
        const itemBarcode = row.dataset.itemBarcode;
        
        if (itemId && itemName) {
            hasResults = true;
            const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
            const hoverBg = isDarkMode ? '#21262d' : '#f5f5f5';
            const tableBorder = isDarkMode ? '#30363d' : '#dee2e6';
            const textColor = isDarkMode ? '#e6edf3' : '#333333';
            
            const resultRow = document.createElement('tr');
            resultRow.style.cursor = 'pointer';
            resultRow.style.transition = 'background-color 0.2s ease';
            resultRow.dataset.itemId = itemId;
            resultRow.dataset.itemName = itemName;
            resultRow.dataset.itemCost = itemCost;
            resultRow.dataset.itemPrice = itemPrice;
            resultRow.dataset.itemType = itemType;
            resultRow.dataset.itemBarcode = itemBarcode;
            
            resultRow.innerHTML = `
                <td style="padding: 12px; border-bottom: 1px solid ${tableBorder}; text-align: right; color: ${textColor};">${itemName}</td>
                <td style="padding: 12px; border-bottom: 1px solid ${tableBorder}; text-align: center; color: ${textColor};">${itemCost || '0'}</td>
                <td style="padding: 12px; border-bottom: 1px solid ${tableBorder}; text-align: center; color: ${textColor};">${itemPrice || '0'}</td>
                <td style="padding: 12px; border-bottom: 1px solid ${tableBorder}; text-align: center;">
                    <button onclick="addItemFromSearch('${itemId}')" 
                            style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-family: 'Cairo', sans-serif;"
                            onmouseover="this.style.background='#0069d9'" 
                            onmouseout="this.style.background='#007bff'">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </td>
            `;
            
            // إضافة تأثير hover
            resultRow.addEventListener('mouseenter', function() {
                this.style.backgroundColor = hoverBg;
            });
            
            resultRow.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'transparent';
            });
            
            searchResultsBody.appendChild(resultRow);
        }
    });
    
    if (!hasResults) {
        noResultsMessage.style.display = 'block';
        document.getElementById('searchResultsTable').style.display = 'none';
    } else {
        noResultsMessage.style.display = 'none';
        document.getElementById('searchResultsTable').style.display = 'table';
    }
}

// دالة البحث في الأصناف
function searchItems() {
    const nameSearch = document.getElementById('searchByName').value.toLowerCase().trim();
    const barcodeSearch = document.getElementById('searchByBarcode').value.toLowerCase().trim();
    const searchResultsBody = document.getElementById('searchResultsBody');
    const noResultsMessage = document.getElementById('noResultsMessage');
    
    if (!searchResultsBody) return;
    
    const allRows = searchResultsBody.querySelectorAll('tr');
    let visibleCount = 0;
    
    allRows.forEach(row => {
        const itemName = row.dataset.itemName.toLowerCase();
        const itemBarcode = (row.dataset.itemBarcode || '').toLowerCase();
        
        const nameMatch = !nameSearch || itemName.includes(nameSearch);
        const barcodeMatch = !barcodeSearch || itemBarcode.includes(barcodeSearch);
        
        if (nameMatch && barcodeMatch) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    if (visibleCount === 0) {
        noResultsMessage.style.display = 'block';
        document.getElementById('searchResultsTable').style.display = 'none';
    } else {
        noResultsMessage.style.display = 'none';
        document.getElementById('searchResultsTable').style.display = 'table';
    }
}

// دالة إضافة صنف من نتائج البحث
function addItemFromSearch(itemId) {
    const item = findItemById(itemId);
    if (item) {
        addItemToQueriesTable(item);
        showSuccessMessage(`تم إضافة "${item.name}" إلى جدول الاستعلامات`);
        
        // إغلاق نافذة البحث
        Swal.close();
    } else {
        showErrorMessage('لم يتم العثور على الصنف');
    }
}
