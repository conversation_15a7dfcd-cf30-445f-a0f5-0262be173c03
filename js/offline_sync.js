// offline_sync.js - Manages offline invoice functionality with File System Access API
// تحديث محسن مع دعم الوضع المظل�� والتصميم المتجاوب وحفظ الملفات المشفرة
// الميزات الجديدة:
// - دعم كامل للوضع المظلم مع ألوان متناسقة
// - مراقبة تغيير الثيم ديناميكياً
// - تحسين موقع رمز الاتصال (تم نقله إلى الأسفل)
// - تأثيرات بصرية محسنة وانتقالات أكثر سلاسة
// - تصميم متجاوب للشاشات المختلفة
// - حفظ الفواتير في ملفات JSON مشفرة باستخدام File System Access API
// - مزامنة وحذف الملفات بعد المزامنة الناجحة

// Initialize IndexedDB database for offline storage
let db;
let DB_NAME = 'invoiceOfflineDB'; // Will be updated with user ID
const DB_VERSION = 1;
const PENDING_STORE = 'pendingInvoices';
const STATUS_STORE = 'syncStatus';

// File System Manager instance
let fileSystemManager = null;

// Internet connectivity detection variables
let isActuallyOnline = true;
let connectivityCheckInterval = null;
const CONNECTIVITY_CHECK_INTERVAL = 10000; // 10 seconds

// Get current user ID for database naming
function getCurrentUserId() {
    // Try to get from window variables first
    if (window.encryptedAccountId) {
        return window.encryptedAccountId;
    }

    // Fallback to session storage or cookie
    const sessionUserId = sessionStorage.getItem('current_user_id');
    if (sessionUserId) {
        return sessionUserId;
    }

    // Last resort - generate a temporary ID
    console.warn('No user ID found, using temporary ID');
    return 'temp_user_' + Date.now();
}

// Update database name with user ID
function updateDBNameForUser() {
    const userId = getCurrentUserId();
    DB_NAME = `invoiceOfflineDB_${userId}`;
    console.log('Using database:', DB_NAME);
}

// Initialize database
function initDB() {
    return new Promise((resolve, reject) => {
        // Update database name for current user
        updateDBNameForUser();

        const request = indexedDB.open(DB_NAME, DB_VERSION);

        request.onerror = event => {
            console.error("IndexedDB error:", event.target.error);
            reject("Error opening IndexedDB");
        };

        request.onsuccess = event => {
            db = event.target.result;
            console.log("IndexedDB initialized successfully");
            resolve(db);
        };

        request.onupgradeneeded = event => {
            const db = event.target.result;

            // Create object store for pending invoices
            if (!db.objectStoreNames.contains(PENDING_STORE)) {
                const store = db.createObjectStore(PENDING_STORE, { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp', { unique: false });
            }

            // Create object store for sync status
            if (!db.objectStoreNames.contains(STATUS_STORE)) {
                db.createObjectStore(STATUS_STORE, { keyPath: 'key' });
            }
        };
    });
}

// Save offline status
function saveOfflineStatus(isOffline) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STATUS_STORE], 'readwrite');
        const store = transaction.objectStore(STATUS_STORE);

        const status = { key: 'offlineStatus', isOffline, lastUpdated: new Date().toISOString() };
        const request = store.put(status);

        request.onsuccess = () => resolve(true);
        request.onerror = event => reject(event.target.error);
    });
}

// Check actual internet connectivity with ping
async function checkInternetConnectivity() {
    try {
        // Create a controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // Increased timeout to 8 seconds
        
        // Send a GET request instead of HEAD to get more reliable response
        const response = await fetch('ping.php?_=' + Date.now(), {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // Check if the response is successful
        if (response.ok) {
            // Try to parse the JSON response to ensure it's valid
            try {
                const responseText = await response.text();
                console.log('Ping response text:', responseText); // Debug log
                
                const data = JSON.parse(responseText);
                if (data.status === 'ok') {
                    // If the request succeeds and returns valid data, we have internet connectivity
                    if (!isActuallyOnline) {
                        console.log('الإنترنت عاد بعد انقطاع فعلي');
                        handleOnlineStatus();
                    }
                    isActuallyOnline = true;
                } else {
                    console.warn('Ping response status not ok:', data);
                    throw new Error('Invalid response status: ' + data.status);
                }
            } catch (jsonError) {
                console.error('Failed to parse ping response as JSON:', jsonError);
                console.error('Response text was:', responseText);
                throw new Error('Invalid JSON response: ' + jsonError.message);
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
    } catch (err) {
        // If the request fails, we don't have internet connectivity
        if (isActuallyOnline) {
            console.error('فشل الوصول إلى الإنترنت (Ping failed):', err.message);
            console.error('Error details:', err);
            
            // Only handle as offline if it's a network error, not a parsing error
            if (err.name === 'AbortError' || 
                err.message.includes('HTTP') || 
                err.message.includes('fetch') ||
                err.message.includes('NetworkError') ||
                err.message.includes('Failed to fetch')) {
                handleOfflineStatus();
            }
        }
        isActuallyOnline = false;
    }
}

// Start periodic connectivity checking
function startConnectivityMonitoring() {
    // Clear any existing interval
    if (connectivityCheckInterval) {
        clearInterval(connectivityCheckInterval);
    }
    
    // Start periodic checking
    connectivityCheckInterval = setInterval(checkInternetConnectivity, CONNECTIVITY_CHECK_INTERVAL);
    
    // Do an initial check
    checkInternetConnectivity();
}

// Stop connectivity monitoring
function stopConnectivityMonitoring() {
    if (connectivityCheckInterval) {
        clearInterval(connectivityCheckInterval);
        connectivityCheckInterval = null;
    }
}

// Check if device is offline (improved version)
function isOffline() {
    // Check both navigator.onLine and actual internet connectivity
    return !navigator.onLine || !isActuallyOnline;
}

// Clear offline data for user logout
function clearOfflineDataForLogout() {
    return new Promise((resolve, reject) => {
        try {
            // Close current database connection
            if (db) {
                db.close();
            }

            // Delete the current user's database
            const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

            deleteRequest.onsuccess = () => {
                console.log('Offline data cleared for user logout');
                db = null;
                resolve(true);
            };

            deleteRequest.onerror = (event) => {
                console.error('Error clearing offline data:', event.target.error);
                reject(event.target.error);
            };

            deleteRequest.onblocked = () => {
                console.warn('Database deletion blocked, forcing close');
                // Force close and try again
                setTimeout(() => {
                    const retryRequest = indexedDB.deleteDatabase(DB_NAME);
                    retryRequest.onsuccess = () => resolve(true);
                    retryRequest.onerror = (e) => reject(e.target.error);
                }, 100);
            };
        } catch (error) {
            console.error('Error in clearOfflineDataForLogout:', error);
            reject(error);
        }
    });
}

// Add invoice to offline queue with duplicate prevention
function addInvoiceToQueue(invoiceData) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readwrite');
        const store = transaction.objectStore(PENDING_STORE);

        // تنظيف البيانات من عناصر DOM قبل الحفظ
        const cleanInvoiceData = cleanDataForStorage(invoiceData);
        
        // Create a unique hash for this invoice to prevent duplicates
        const invoiceHash = generateInvoiceHash(cleanInvoiceData);
        
        // Check if this invoice already exists
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
            const existingInvoices = getAllRequest.result;
            const currentUserId = getCurrentUserId();
            
            // Check for duplicate invoices from the same user
            const isDuplicate = existingInvoices.some(existing => {
                if (existing.userId !== currentUserId) return false;
                
                // للوضع المقسم، تحقق من معرف العميل أيضاً
                if (cleanInvoiceData.multiCustomerMode && existing.multiCustomerMode) {
                    if (existing.customerId !== cleanInvoiceData.customerId) return false;
                }
                
                const existingHash = generateInvoiceHash(existing);
                return existingHash === invoiceHash;
            });
            
            if (isDuplicate) {
                console.log('Duplicate invoice detected, not adding to queue');
                resolve(false); // Return false to indicate duplicate
                return;
            }
            
            // Add timestamp and user info to track when it was stored
            cleanInvoiceData.timestamp = new Date().toISOString();
            cleanInvoiceData.userId = getCurrentUserId();
            cleanInvoiceData.invoiceHash = invoiceHash;
            
            // إضافة معلومات إضافية للوضع المقسم
            if (cleanInvoiceData.multiCustomerMode) {
                cleanInvoiceData.isMultiCustomer = true;
                cleanInvoiceData.offlineSessionId = `offline_${cleanInvoiceData.customerId}_${Date.now()}`;
            }
            
            const addRequest = store.add(cleanInvoiceData);
            
            addRequest.onsuccess = () => {
                console.log('Invoice added to offline queue with hash:', invoiceHash);
                if (cleanInvoiceData.multiCustomerMode) {
                    console.log('Multi-customer invoice saved offline for customer:', cleanInvoiceData.customerId);
                }
                updatePendingCount();
                resolve(true);
            };
            
            addRequest.onerror = event => reject(event.target.error);
        };
        
        getAllRequest.onerror = event => reject(event.target.error);
    });
}

// Clean data from DOM elements before storage
function cleanDataForStorage(data) {
    if (data === null || data === undefined) {
        return data;
    }
    
    // If it's a DOM element, return null
    if (data instanceof Element || data instanceof HTMLElement) {
        console.warn('DOM element detected and removed from storage data');
        return null;
    }
    
    // If it's an array, clean each element
    if (Array.isArray(data)) {
        return data.map(item => cleanDataForStorage(item)).filter(item => item !== null);
    }
    
    // If it's an object, clean each property
    if (typeof data === 'object') {
        const cleanedData = {};
        for (const [key, value] of Object.entries(data)) {
            const cleanedValue = cleanDataForStorage(value);
            if (cleanedValue !== null) {
                cleanedData[key] = cleanedValue;
            }
        }
        return cleanedData;
    }
    
    // For primitive types, return as is
    return data;
}

// Generate a unique hash for an invoice to detect duplicates
function generateInvoiceHash(invoiceData) {
    const hashData = {
        store_id: invoiceData.store_id,
        account_id: invoiceData.account_id,
        account_buyer_id: invoiceData.account_buyer_id,
        type: invoiceData.type || invoiceData.invoice_type,
        items: invoiceData.items ? invoiceData.items.map(item => ({
            id: item.id,
            quantity: item.quantity,
            price: item.price
        })) : []
    };
    
    // Simple hash function (you could use a more sophisticated one)
    return btoa(JSON.stringify(hashData)).replace(/[^a-zA-Z0-9]/g, '');
}

// Count pending invoices
function countPendingInvoices() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readonly');
        const store = transaction.objectStore(PENDING_STORE);

        const countRequest = store.count();
        countRequest.onsuccess = () => resolve(countRequest.result);
        countRequest.onerror = event => reject(event.target.error);
    });
}

// Get all pending invoices (filtered by current user)
function getPendingInvoices() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readonly');
        const store = transaction.objectStore(PENDING_STORE);

        const request = store.getAll();

        request.onsuccess = () => {
            const allInvoices = request.result;
            const currentUserId = getCurrentUserId();

            // Filter invoices to only include current user's invoices
            const userInvoices = allInvoices.filter(invoice => {
                // Check if invoice has userId field (new format)
                if (invoice.userId) {
                    return invoice.userId === currentUserId;
                }

                // For legacy invoices without userId, check account_id
                if (invoice.account_id && window.encryptedAccountId) {
                    return invoice.account_id === window.encryptedAccountId;
                }

                // If no user identification, exclude for safety
                console.warn('Found invoice without user identification, excluding:', invoice);
                return false;
            });

            console.log(`Found ${userInvoices.length} pending invoices for current user out of ${allInvoices.length} total`);
            resolve(userInvoices);
        };

        request.onerror = event => reject(event.target.error);
    });
}

// Remove invoice from queue after successful sync
function removeInvoiceFromQueue(id) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readwrite');
        const store = transaction.objectStore(PENDING_STORE);

        const request = store.delete(id);

        request.onsuccess = () => {
            updatePendingCount();
            resolve(true);
        };

        request.onerror = event => reject(event.target.error);
    });
}

// Update UI to show pending invoice count
function updatePendingCount() {
    countPendingInvoices().then(count => {
        const offlineStatus = document.getElementById('offline-status');
        if (offlineStatus) {
            const pendingBadge = offlineStatus.querySelector('.pending-badge');
            if (pendingBadge) {
                pendingBadge.textContent = count;
                pendingBadge.style.display = count > 0 ? 'inline-block' : 'none';
            }
        }
    });
}

// Synchronize one invoice with retry mechanism
async function syncOneInvoice(invoiceData, retryCount = 0) {
    const maxRetries = 2; // أقصى عدد محاولات إعادة الإرسال

    // Fix for invoices saved without a customer ID
    if ((invoiceData.type === 'customer_sale' || invoiceData.type === 'customer_return') && !invoiceData.account_buyer_id) {
        console.warn('Invoice is missing customer ID. Assigning default encrypted customer ID for customer 53.');
        if (window.encryptedAccountsMap && window.encryptedAccountsMap['53']) {
            invoiceData.account_buyer_id = window.encryptedAccountsMap['53']; // Assign default encrypted customer ID
        } else {
            console.error('Encrypted ID for customer 53 not found. Cannot fix invoice.');
        }
    }

    try {
        // Choose the appropriate endpoint based on invoice type
        let endpoint;
        if (invoiceData.type === 'customer_sale' || invoiceData.type === 'customer_return') {
            endpoint = 'save_sale_invoice.php';
        } else if (invoiceData.type === 'sale') {
            endpoint = 'confirm_sale_invoice.php';
        } else {
            endpoint = 'confirm_invoice.php';
        }

        // Create form data
        const formData = new FormData();
        formData.append('store_id', invoiceData.store_id);
        formData.append('account_id', invoiceData.account_id);

        if (invoiceData.branch_id) {
            formData.append('branch_id', invoiceData.branch_id);
        }

        if (invoiceData.account_buyer_id) {
            formData.append('account_buyer_id', invoiceData.account_buyer_id);
        }

        formData.append('items', JSON.stringify(invoiceData.items));
        formData.append('invoice_type', invoiceData.invoice_type || invoiceData.type);

        // Handle images
        if (invoiceData.images && invoiceData.images.length) {
            formData.append('images', JSON.stringify(invoiceData.images));
        } else {
            formData.append('images', JSON.stringify([]));
        }

        // Send the invoice to the server
        const response = await fetch(endpoint, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            return true;
        } else {
            // إذا كان الخطأ متعلق بالتكرار، اعتبر العملية ناجحة (الفاتورة موجودة بالفعل)
            if (result.message && (
                result.message.includes('تم إرسال نفس الطلب') || 
                result.message.includes('فاتورة مطابقة') ||
                result.message.includes('مكررة')
            )) {
                console.log('Invoice already exists on server, marking as synced');
                return true; // اعتبر العملية ناجحة
            }
            throw new Error(result.message || 'Unknown error during sync');
        }
    } catch (error) {
        console.error("Sync error:", error);

        // إعادة المحاولة في حالة أخطاء الشبكة
        if (retryCount < maxRetries && (error.name === 'TypeError' || error.message.includes('HTTP error'))) {
            console.log(`Retrying invoice sync (attempt ${retryCount + 1}/${maxRetries}) after network error`);
            await new Promise(resolve => setTimeout(resolve, 3000)); // انتظار 3 ثوان
            return await syncOneInvoice(invoiceData, retryCount + 1);
        }

        return false;
    }
}

// Global sync state management
let isSyncInProgress = false;
let lastSyncTime = 0;

// Try to synchronize all pending invoices (unified sync for IndexedDB and encrypted files)
async function syncPendingInvoices() {
    if (isOffline()) {
        console.log('Still offline, cannot sync');
        return false;
    }

    // Prevent multiple sync operations
    if (isSyncInProgress) {
        console.log('Sync already in progress, skipping');
        return false;
    }

    // Prevent too frequent sync attempts (minimum 5 seconds between syncs)
    const currentTime = Date.now();
    if (currentTime - lastSyncTime < 5000) {
        console.log('Sync attempted too soon, waiting...');
        return false;
    }

    isSyncInProgress = true;
    lastSyncTime = currentTime;

    try {
        // Get all pending sources (IndexedDB + encrypted files)
        const pendingInvoices = await getPendingInvoices();
        let encryptedFiles = [];
        
        // Get encrypted files if File System Manager is available
        if (fileSystemManager && fileSystemManager.isFileSystemSupported()) {
            try {
                const fileStats = await fileSystemManager.getFileStats();
                if (fileStats.totalFiles > 0) {
                    encryptedFiles = await fileSystemManager.getAllInvoiceFiles();
                    console.log(`Found ${encryptedFiles.length} encrypted files to sync`);
                }
            } catch (fileError) {
                console.warn('Could not get encrypted files:', fileError.message);
            }
        }

        // Create unified list avoiding duplicates
        const unifiedInvoices = await createUnifiedInvoiceList(pendingInvoices, encryptedFiles);
        
        if (unifiedInvoices.length === 0) {
            console.log('No pending invoices to sync');
            return true;
        }

        let successCount = 0;
        let syncedFromIndexedDB = 0;
        let syncedFromFiles = 0;

        // Show sync in progress toast notification
        showSyncToast('جاري مزامنة ' + unifiedInvoices.length + ' فواتير...', 'info');

        for (let i = 0; i < unifiedInvoices.length; i++) {
            const invoiceItem = unifiedInvoices[i];
            showSyncToast(`جاري مزامن�� الفاتورة ${i + 1} من ${unifiedInvoices.length}...`, 'info');

            const success = await syncOneInvoice(invoiceItem.data);

            if (success) {
                // Remove from IndexedDB if it exists there
                if (invoiceItem.indexedDBId) {
                    await removeInvoiceFromQueue(invoiceItem.indexedDBId);
                    syncedFromIndexedDB++;
                }
                
                // Remove encrypted file if it exists
                if (invoiceItem.fileName && fileSystemManager) {
                    try {
                        await fileSystemManager.deleteInvoiceFile(invoiceItem.fileName);
                        syncedFromFiles++;
                        console.log('Deleted encrypted file after sync:', invoiceItem.fileName);
                    } catch (deleteError) {
                        console.warn('Could not delete encrypted file:', deleteError.message);
                    }
                }
                
                successCount++;
                showSyncToast('تم مزامنة ' + successCount + ' من ' + unifiedInvoices.length + ' فواتير بنجاح', 'success');
            } else {
                showSyncToast(`فشل في مزامنة الفاتورة ${i + 1}. سيتم المحاولة مرة أخرى لاحقاً`, 'warning');
            }

            // إضافة تأخير 1.5 ثانية بين كل فاتورة لتجنب مشكلة منع التكرار في السيرفر
            if (i < unifiedInvoices.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
        }

        // Show final status with details
        if (successCount === unifiedInvoices.length) {
            let message = 'تمت مزامنة جميع الفواتير بنجاح';
            if (syncedFromFiles > 0) {
                message += ` (${syncedFromIndexedDB} من قاعدة البيانات، ${syncedFromFiles} من الملفات المشفرة)`;
            }
            showSyncToast(message, 'success');
            return true;
        } else {
            showSyncToast('تمت مزامنة ' + successCount + ' من ' + unifiedInvoices.length + ' فواتير', 'warning');
            return false;
        }
    } catch (error) {
        console.error('Error syncing invoices:', error);
        showSyncToast('حدث خطأ أثناء المزامنة', 'error');
        return false;
    } finally {
        // Always reset sync state
        isSyncInProgress = false;
    }
}

// Create unified list of invoices from IndexedDB and encrypted files, avoiding duplicates
async function createUnifiedInvoiceList(indexedDBInvoices, encryptedFiles) {
    const unifiedList = [];
    const processedHashes = new Set();

    // Process IndexedDB invoices first
    for (const invoice of indexedDBInvoices) {
        const hash = generateInvoiceHash(invoice);
        if (!processedHashes.has(hash)) {
            unifiedList.push({
                data: invoice,
                indexedDBId: invoice.id,
                fileName: null,
                source: 'indexedDB',
                hash: hash
            });
            processedHashes.add(hash);
        }
    }

    // Process encrypted files, skip duplicates
    for (const fileData of encryptedFiles) {
        try {
            const hash = generateInvoiceHash(fileData.data);
            if (!processedHashes.has(hash)) {
                unifiedList.push({
                    data: fileData.data,
                    indexedDBId: null,
                    fileName: fileData.fileName,
                    source: 'encryptedFile',
                    hash: hash
                });
                processedHashes.add(hash);
            } else {
                console.log('Skipping duplicate invoice from encrypted file:', fileData.fileName);
                // Delete the duplicate encrypted file
                if (fileSystemManager) {
                    try {
                        await fileSystemManager.deleteInvoiceFile(fileData.fileName);
                        console.log('Deleted duplicate encrypted file:', fileData.fileName);
                    } catch (deleteError) {
                        console.warn('Could not delete duplicate encrypted file:', deleteError.message);
                    }
                }
            }
        } catch (error) {
            console.error('Error processing encrypted file:', fileData.fileName, error);
        }
    }

    console.log(`Created unified list: ${unifiedList.length} unique invoices (${indexedDBInvoices.length} from IndexedDB, ${encryptedFiles.length} from files)`);
    return unifiedList;
}

// Show toast notification for sync status
function showSyncToast(message, type = 'info') {
    if (typeof toastr !== 'undefined') {
        // إعدادات خاصة لرسائل المزامنة لتجنب التراكم
        const syncToastrOptions = {
            "timeOut": type === 'info' ? "2000" : "4000", // رسائل المعلومات تختفي بسرعة
            "extendedTimeOut": "1000",
            "preventDuplicates": true,
            "newestOnTop": true
        };

        switch (type) {
            case 'success':
                toastr.success(message, '', syncToastrOptions);
                break;
            case 'error':
                toastr.error(message, '', syncToastrOptions);
                break;
            case 'warning':
                toastr.warning(message, '', syncToastrOptions);
                break;
            default:
                toastr.info(message, '', syncToastrOptions);
        }
    } else {
        console.log(message);
    }
}

// Save an invoice (works online or offline)
async function saveInvoiceWithOfflineSupport(invoiceData) {
    if (isOffline()) {
        // Offline: save to IndexedDB and encrypted file (if supported)
        try {
            const added = await addInvoiceToQueue(invoiceData);
            let fileResult = null;
            
            // Try to save to encrypted file if File System Manager is available
            if (fileSystemManager && fileSystemManager.isFileSystemSupported()) {
                try {
                    fileResult = await fileSystemManager.saveInvoiceToFile(invoiceData);
                    console.log('Invoice also saved to encrypted file:', fileResult.fileName);
                } catch (fileError) {
                    console.warn('Could not save to encrypted file:', fileError.message);
                    // Continue with IndexedDB save even if file save fails
                }
            }
            
            if (added) {
                let message = 'تم حفظ الفاتورة محلياً. ستتم المزامنة عند عودة الاتصال';
                if (fileResult) {
                    message += ' (محفوظة أيضاً في ملف مشفر)';
                }
                showSyncToast(message, 'info');
                return {
                    success: true,
                    offline: true,
                    message: message,
                    fileResult: fileResult
                };
            } else {
                // Duplicate detected
                return {
                    success: true,
                    offline: true,
                    message: 'الفاتورة محفوظة بالفعل محلياً'
                };
            }
        } catch (error) {
            console.error('Error saving offline:', error);
            return {
                success: false,
                offline: true,
                message: 'حدث خطأ أثناء حفظ الفاتورة محلياً'
            };
        }
    } else {
        // Online: use regular sync
        try {
            const success = await syncOneInvoice(invoiceData);
            if (success) {
                return {
                    success: true,
                    offline: false,
                    message: 'تم حفظ الفاتورة بنجاح'
                };
            } else {
                throw new Error('فشل في حفظ الفاتورة على السيرفر');
            }
        } catch (error) {
            console.error('Error saving online:', error);

            // If online save fails, try saving offline as fallback
            try {
                const added = await addInvoiceToQueue(invoiceData);
                let fileResult = null;
                
                // Try to save to encrypted file if File System Manager is available
                if (fileSystemManager && fileSystemManager.isFileSystemSupported()) {
                    try {
                        fileResult = await fileSystemManager.saveInvoiceToFile(invoiceData);
                        console.log('Invoice saved to encrypted file as fallback:', fileResult.fileName);
                    } catch (fileError) {
                        console.warn('Could not save to encrypted file as fallback:', fileError.message);
                    }
                }
                
                if (added) {
                    let message = 'تعذر الاتصال بالسيرفر. تم حفظ الفاتورة محلياً وستتم المزامنة لاحقاً';
                    if (fileResult) {
                        message += ' (محفوظة أيضاً في ملف مشفر)';
                    }
                    return {
                        success: true,
                        offline: true,
                        message: message,
                        fileResult: fileResult
                    };
                } else {
                    return {
                        success: true,
                        offline: true,
                        message: 'الفاتورة محفوظة بالفعل محلياً'
                    };
                }
            } catch (offlineError) {
                return {
                    success: false,
                    offline: true,
                    message: 'فشل حفظ الفاتورة عبر الإنترنت وفشل الحفظ المحلي'
                };
            }
        }
    }
}

// Initialize the offline functionality
async function initOfflineSystem() {
    try {
        await initDB();

        // Initialize File System Manager if supported
        if (typeof FileSystemManager !== 'undefined') {
            fileSystemManager = new FileSystemManager();
            console.log('File System Manager initialized');
        } else {
            console.log('File System Manager not available, using IndexedDB only');
        }

        // Set up online/offline event listeners
        window.addEventListener('online', handleOnlineStatus);
        window.addEventListener('offline', handleOfflineStatus);

        // Monitor theme changes for dynamic styling updates
        setupThemeObserver();

        // Start connectivity monitoring
        startConnectivityMonitoring();

        // Initial status check
        if (isOffline()) {
            handleOfflineStatus();
        } else {
            handleOnlineStatus();
        }

        // Update UI with pending count
        updatePendingCount();

        return true;
    } catch (error) {
        console.error('Error initializing offline system:', error);
        return false;
    }
}

// Setup theme observer to handle dynamic theme changes
function setupThemeObserver() {
    // Watch for changes in body class to detect theme changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                // Theme changed, update offline status styling if visible
                updateOfflineStatusStyling();
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
    });
}

// Update offline status styling based on current theme
function updateOfflineStatusStyling() {
    const offlineStatus = document.getElementById('offline-status');
    if (!offlineStatus) return;

    const isDarkMode = document.body.classList.contains('dark-mode');
    const isCurrentlyOffline = offlineStatus.classList.contains('offline');

    // Apply appropriate styling based on current state and theme
    if (isCurrentlyOffline) {
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(229, 62, 62, 0.2)';
            offlineStatus.style.borderColor = 'rgba(229, 62, 62, 0.5)';
            offlineStatus.style.color = '#fc8181';
        } else {
            offlineStatus.style.backgroundColor = '#fff5f5';
            offlineStatus.style.borderColor = '#ffcccc';
            offlineStatus.style.color = '#e53e3e';
        }
    } else if (offlineStatus.querySelector('.status-text').textContent === 'متصل') {
        // Only style if showing "connected" text
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(56, 161, 105, 0.2)';
            offlineStatus.style.borderColor = 'rgba(56, 161, 105, 0.5)';
            offlineStatus.style.color = '#68d391';
        } else {
            offlineStatus.style.backgroundColor = '#f0fff4';
            offlineStatus.style.borderColor = '#c6f6d5';
            offlineStatus.style.color = '#38a169';
        }
    }
}

// Handle online event
function handleOnlineStatus() {
    console.log('Connection is back online');
    updateOfflineStatusUI(false);
    saveOfflineStatus(false);

    // Try to sync pending invoices automatically with delay and check
    setTimeout(async () => {
        if (!isSyncInProgress) {
            // Unified sync handles both IndexedDB and encrypted files
            await syncPendingInvoices();
        } else {
            console.log('Sync already in progress when coming online, skipping auto-sync');
        }

        // Check for pending cashier switch
        const pendingAccountId = sessionStorage.getItem('pendingSwitchAccountId');
        if (pendingAccountId) {
            showPendingSwitchMessage(pendingAccountId);
        }
    }, 2000);
}

// Function to show pending switch message
function showPendingSwitchMessage(accountId) {
    const messageDiv = document.createElement('div');
    messageDiv.id = 'pending-switch-message';
    messageDiv.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 100%);
        padding: 25px;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: 2px solid rgba(0, 123, 255, 0.1);
        z-index: 10000;
        font-family: 'Cairo', Arial, sans-serif;
        direction: rtl;
        text-align: right;
        max-width: 350px;
        animation: slideInUp 0.4s ease-out;
        transition: all 0.3s ease;
    `;

    // Check if dark mode is active
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    if (isDarkMode) {
        messageDiv.style.background = 'linear-gradient(135deg, var(--dark-surface-light) 0%, var(--dark-surface) 100%)';
        messageDiv.style.border = '2px solid var(--dark-border-light)';
        messageDiv.style.boxShadow = '0 20px 60px rgba(26, 35, 50, 0.4)';
        messageDiv.style.color = 'var(--dark-text)';
    }

    messageDiv.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <i class="fas fa-wifi" style="color: ${isDarkMode ? 'var(--blue-soft)' : 'var(--success-color)'}; font-size: 1.5rem; margin-left: 10px;"></i>
            <h4 style="margin: 0; color: ${isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)'}; font-weight: 600;">تم استعادة الاتصال</h4>
        </div>
        <p style="margin: 0 0 20px 0; color: ${isDarkMode ? 'var(--dark-text-secondary)' : '#666'}; line-height: 1.5;">
            تم عودة الاتصال بالإنترنت. يجب الضغط على الزر أدناه لتحديث الصفحة والمتابعة:
        </p>
        <div style="display: flex; justify-content: center;">
            <button id="refresh-btn" style="
                background: linear-gradient(135deg, ${isDarkMode ? 'var(--blue-muted)' : 'var(--primary-color)'} 0%, ${isDarkMode ? 'var(--blue-accent)' : 'var(--primary-dark)'} 100%);
                border: none;
                border-radius: 10px;
                padding: 15px 30px;
                color: white;
                font-weight: 600;
                font-family: 'Cairo', Arial, sans-serif;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(${isDarkMode ? '91, 155, 213' : '0, 123, 255'}, 0.3);
                font-size: 16px;
                width: 100%;
            ">
                <i class="fas fa-sync-alt" style="margin-left: 8px;"></i>
                تحديث الصفحة الآن
            </button>
        </div>
    `;

    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        #pending-switch-message:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
        }
        
        .dark-mode #pending-switch-message:hover {
            box-shadow: 0 25px 70px rgba(26, 35, 50, 0.5);
        }
        
        #refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(${isDarkMode ? '91, 155, 213' : '0, 123, 255'}, 0.4);
        }
        
        #dismiss-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
    `;
    document.head.appendChild(style);

    // Add event listeners
    const refreshBtn = messageDiv.querySelector('#refresh-btn');

    refreshBtn.addEventListener('click', function() {
        window.location.href = window.location.pathname + '?account_id=' + encodeURIComponent(accountId) + '&multi_customer=true';
        sessionStorage.removeItem('pendingSwitchAccountId');
    });

    document.body.appendChild(messageDiv);
}

// Handle offline event
function handleOfflineStatus() {
    console.log('Connection is offline');
    updateOfflineStatusUI(true);
    saveOfflineStatus(true);
}

// Update UI to show offline status
function updateOfflineStatusUI(isOfflineNow) {
    const offlineStatus = document.getElementById('offline-status');
    if (!offlineStatus) return;

    // Check if dark mode is active
    const isDarkMode = document.body.classList.contains('dark-mode');

    if (isOfflineNow) {
        offlineStatus.classList.add('offline');
        offlineStatus.querySelector('.status-text').textContent = 'أنت غير متصل';

        // Apply dark mode friendly colors
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(229, 62, 62, 0.2)';
            offlineStatus.style.borderColor = 'rgba(229, 62, 62, 0.5)';
            offlineStatus.style.color = '#fc8181';
        } else {
            offlineStatus.style.backgroundColor = '#fff5f5';
            offlineStatus.style.borderColor = '#ffcccc';
            offlineStatus.style.color = '#e53e3e';
        }
    } else {
        offlineStatus.classList.remove('offline');
        offlineStatus.querySelector('.status-text').textContent = 'متصل';

        // Apply dark mode friendly colors for online status
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(56, 161, 105, 0.2)';
            offlineStatus.style.borderColor = 'rgba(56, 161, 105, 0.5)';
            offlineStatus.style.color = '#68d391';
        } else {
            offlineStatus.style.backgroundColor = '#f0fff4';
            offlineStatus.style.borderColor = '#c6f6d5';
            offlineStatus.style.color = '#38a169';
        }

        // Only show for a short time when coming back online
        setTimeout(() => {
            // Reset to default styles based on theme
            if (isDarkMode) {
                offlineStatus.style.backgroundColor = '';
                offlineStatus.style.borderColor = '';
                offlineStatus.style.color = '';
            } else {
                offlineStatus.style.backgroundColor = '';
                offlineStatus.style.borderColor = '';
                offlineStatus.style.color = '';
            }
            offlineStatus.querySelector('.status-text').textContent = '';
        }, 5000);
    }

    // Update position to be lower (to fix height issue)
    offlineStatus.style.top = '80px'; // Moved down from 15px
}

// Clean up old invoices from other users (safety measure)
function cleanupOldInvoices() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readwrite');
        const store = transaction.objectStore(PENDING_STORE);
        const request = store.getAll();

        request.onsuccess = () => {
            const allInvoices = request.result;
            const currentUserId = getCurrentUserId();
            let cleanupCount = 0;

            // Remove invoices that don't belong to current user
            allInvoices.forEach(invoice => {
                if (invoice.userId && invoice.userId !== currentUserId) {
                    const deleteRequest = store.delete(invoice.id);
                    deleteRequest.onsuccess = () => cleanupCount++;
                }
            });

            if (cleanupCount > 0) {
                console.log(`Cleaned up ${cleanupCount} old invoices from other users`);
            }

            resolve(cleanupCount);
        };

        request.onerror = event => reject(event.target.error);
    });
}

// Setup network listeners for connection status
function setupNetworkListeners() {
    // Listen for online/offline events
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOfflineStatus);
    
    // Initial status check
    if (navigator.onLine) {
        handleOnlineStatus();
    } else {
        handleOfflineStatus();
    }
}

// Setup sync UI elements
function setupSyncUI() {
    // Setup sync button if it exists
    const syncButton = document.getElementById('sync-button');
    if (syncButton) {
        syncButton.addEventListener('click', () => {
            if (!isSyncInProgress) {
                syncPendingInvoices();
            }
        });
    }
    
    // Update pending count display
    updatePendingCount();
}

// Expose cleanup function globally for logout
window.clearOfflineDataForLogout = clearOfflineDataForLogout;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initOfflineSystem().then(() => {
        console.log('Offline system with improved connectivity detection initialized successfully');
    }).catch(error => {
        console.error('Failed to initialize offline sync:', error);
    });
});

// Clean up when page unloads
window.addEventListener('beforeunload', function() {
    stopConnectivityMonitoring();
});

// Also clean up on page visibility change (when tab becomes hidden)
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Page is hidden, reduce connectivity checking frequency
        if (connectivityCheckInterval) {
            clearInterval(connectivityCheckInterval);
            connectivityCheckInterval = setInterval(checkInternetConnectivity, CONNECTIVITY_CHECK_INTERVAL * 2); // Double the interval
        }
    } else {
        // Page is visible again, restore normal frequency
        startConnectivityMonitoring();
    }
});