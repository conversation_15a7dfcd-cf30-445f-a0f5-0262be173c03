/**
 * Firebase Integration for Login Page
 * This file handles Firebase initialization and FCM token management for the login page
 */

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
    authDomain: "macm-84114.firebaseapp.com",
    projectId: "macm-84114",
    storageBucket: "macm-84114.firebasestorage.app",
    messagingSenderId: "860043675105",
    appId: "1:860043675105:web:72586005d5bd035ff8bea0"
};

// VAPID key for web push notifications
const vapidKey = 'BMVJO7rt5hONuPb0UzJm2B9T52CuXtcjsWDmHKXf8ass2zyctrBrjXWncazpezhWSdBbcrr8pPcegRixWaTiSBI';

// Global variables
let messaging = null;
let isFirebaseInitialized = false;

/**
 * Initialize Firebase
 */
function initializeFirebase() {
    try {
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        messaging = firebase.messaging();
        isFirebaseInitialized = true;
        console.log('Firebase initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing Firebase:', error);
        return false;
    }
}

/**
 * Register Service Workers
 */
function registerServiceWorkers() {
    if ('serviceWorker' in navigator) {
        // Register Firebase Messaging Service Worker
        navigator.serviceWorker.register('/firebase-messaging-sw.js')
            .then(registration => {
                console.log('Firebase Messaging SW registered:', registration);
                if (messaging) {
                    messaging.useServiceWorker(registration);
                }
            })
            .catch(err => {
                console.error('Firebase Messaging SW registration failed:', err);
            });

        // Register main Service Worker
        navigator.serviceWorker.register('/sw.js')
            .then(reg => console.log('Main SW registered:', reg))
            .catch(err => console.error('Main SW registration failed:', err));
    }
}

/**
 * Request notification permission
 */
function requestNotificationPermission() {
    return new Promise((resolve, reject) => {
        if (!('Notification' in window)) {
            reject('Notifications not supported');
            return;
        }

        if (Notification.permission === 'granted') {
            resolve('granted');
            return;
        }

        if (Notification.permission === 'denied') {
            reject('Permission denied');
            return;
        }

        Notification.requestPermission().then((permission) => {
            if (permission === 'granted') {
                resolve('granted');
            } else {
                reject('Permission not granted');
            }
        });
    });
}

/**
 * Get FCM token
 */
function getFCMToken() {
    return new Promise((resolve, reject) => {
        if (!messaging) {
            reject('Messaging not initialized');
            return;
        }

        messaging.getToken({ vapidKey: vapidKey })
            .then((currentToken) => {
                if (currentToken) {
                    console.log('FCM token obtained:', currentToken);
                    resolve(currentToken);
                } else {
                    reject('No registration token available');
                }
            })
            .catch((err) => {
                console.error('An error occurred while retrieving token:', err);
                reject(err);
            });
    });
}

/**
 * Save FCM token to server
 */
function saveTokenToServer(token) {
    // Use the specialized endpoint for after-login registration
    const endpoint = window.isUserLoggedIn ? 'register_fcm_token_after_login.php' : 'save_fcm_token.php';

    return fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: token }),
    })
    .then(response => response.json())
    .then(data => {
        console.log('Token saved to server:', data);
        return data;
    })
    .catch((error) => {
        console.error('Error saving token to server:', error);
        throw error;
    });
}

/**
 * Initialize FCM after successful login
 */
function initializeFCMAfterLogin() {
    console.log('Initializing FCM after login...');

    requestNotificationPermission()
        .then(() => {
            return getFCMToken();
        })
        .then((token) => {
            return saveTokenToServer(token);
        })
        .then((response) => {
            if (response.success) {
                console.log('FCM token successfully registered');
                showNotificationStatus('تم تفعيل الإشعارات بنجاح', 'success');
            } else {
                console.error('Failed to save token:', response.message);
            }
        })
        .catch((error) => {
            console.error('FCM initialization failed:', error);
            // Don't show error to user as notifications are optional
        });
}

/**
 * Handle incoming messages when app is in foreground
 */
function setupMessageHandler() {
    if (!messaging) return;

    messaging.onMessage((payload) => {
        console.log('Message received in foreground:', payload);

        // Display notification using SweetAlert2
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: payload.notification.title,
                text: payload.notification.body,
                icon: 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true
            });
        }

        // Play notification sound
        playNotificationSound();
    });
}

/**
 * Play notification sound
 */
function playNotificationSound() {
    const notificationSound = document.getElementById('notificationSound');
    if (notificationSound) {
        notificationSound.play().catch(e => {
            console.log('Could not play notification sound:', e);
        });
    }
}

/**
 * Show notification status message
 */
function showNotificationStatus(message, type = 'info') {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: type,
            title: message,
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });
    }
}

/**
 * Request permission early when user interacts with page
 */
function requestPermissionEarly() {
    if ('Notification' in window && Notification.permission === 'default') {
        requestNotificationPermission()
            .then(() => {
                console.log('Notification permission granted early');
            })
            .catch(() => {
                console.log('Notification permission not granted');
            });
    }
}

/**
 * Check if user just logged in and register token
 */
function checkForRecentLogin() {
    // Check if we're on the login page and user is now logged in
    if (window.isUserLoggedIn && window.location.pathname.includes('index.php')) {
        console.log('User recently logged in, registering FCM token...');
        setTimeout(() => {
            initializeFCMAfterLogin();
        }, 500);
    }
}

/**
 * Initialize everything when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Firebase
    if (initializeFirebase()) {
        // Register service workers
        registerServiceWorkers();

        // Setup message handler
        setupMessageHandler();

        // Check for recent login
        checkForRecentLogin();

        // If user is already logged in, initialize FCM
        if (window.isUserLoggedIn) {
            setTimeout(() => {
                initializeFCMAfterLogin();
            }, 1000);
        }
    }

    // Setup login form handler
    const loginForm = document.querySelector('form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            console.log('Login form submitted, preparing FCM registration...');

            // Show FCM registration message
            const fcmDiv = document.getElementById('fcmTokenRegistration');
            if (fcmDiv) {
                fcmDiv.style.display = 'block';
            }

            // Set a flag to register FCM token after successful login
            localStorage.setItem('pendingFCMRegistration', 'true');
        });
    }

    // Check if there's a pending FCM registration
    if (localStorage.getItem('pendingFCMRegistration') === 'true' && window.isUserLoggedIn) {
        localStorage.removeItem('pendingFCMRegistration');
        console.log('Processing pending FCM registration...');
        setTimeout(() => {
            initializeFCMAfterLogin();
        }, 2000);
    }

    // Request permission on first user interaction
    document.addEventListener('click', requestPermissionEarly, { once: true });
    document.addEventListener('keydown', requestPermissionEarly, { once: true });
});

// Export functions for global use
window.initializeFCMAfterLogin = initializeFCMAfterLogin;
window.requestNotificationPermission = requestNotificationPermission;
window.getFCMToken = getFCMToken;
window.saveTokenToServer = saveTokenToServer;
