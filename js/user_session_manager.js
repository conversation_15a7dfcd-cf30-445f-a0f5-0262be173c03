// user_session_manager.js - Manages user session changes and data cleanup
// يدير تغييرات جلسات المستخدمين وتنظيف البيانات

// Track current user to detect changes
let currentUserId = null;
let sessionCheckInterval = null;

// Initialize user session tracking
function initUserSessionTracking() {
    // Get current user ID
    currentUserId = getCurrentUserId();
    
    // Store in session storage for persistence
    if (currentUserId) {
        sessionStorage.setItem('current_user_id', currentUserId);
    }
    
    // Start periodic session checking
    startSessionMonitoring();
    
    console.log('User session tracking initialized for user:', currentUserId);
}

// Get current user ID from various sources
function getCurrentUserId() {
    // Try window variables first (most reliable)
    if (window.encryptedAccountId) {
        return window.encryptedAccountId;
    }
    
    // Try session storage
    const sessionUserId = sessionStorage.getItem('current_user_id');
    if (sessionUserId) {
        return sessionUserId;
    }
    
    // Try to extract from cookies or other sources
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'account_id' && value) {
            return value;
        }
    }
    
    return null;
}

// Monitor session changes
function startSessionMonitoring() {
    // Check every 30 seconds for session changes
    sessionCheckInterval = setInterval(checkForUserChange, 30000);
    
    // Also check when page becomes visible (user switches tabs)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            checkForUserChange();
        }
    });
    
    // Check when window gains focus
    window.addEventListener('focus', checkForUserChange);
}

// Check if user has changed
function checkForUserChange() {
    const newUserId = getCurrentUserId();
    
    // If no user ID found, user might have logged out
    if (!newUserId) {
        console.log('No user ID found, user might have logged out');
        handleUserLogout();
        return;
    }
    
    // If user ID changed, handle user switch
    if (currentUserId && newUserId !== currentUserId) {
        console.log('User change detected:', currentUserId, '->', newUserId);
        handleUserSwitch(currentUserId, newUserId);
    }
    
    currentUserId = newUserId;
}

// Handle user logout
function handleUserLogout() {
    console.log('Handling user logout, clearing offline data');
    
    // Clear offline data
    if (typeof clearOfflineDataForLogout === 'function') {
        clearOfflineDataForLogout().catch(error => {
            console.error('Error clearing offline data on logout:', error);
        });
    }
    
    // Clear session storage
    sessionStorage.removeItem('current_user_id');
    
    // Stop monitoring
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
        sessionCheckInterval = null;
    }
    
    currentUserId = null;
}

// Handle user switch
function handleUserSwitch(oldUserId, newUserId) {
    console.log(`User switched from ${oldUserId} to ${newUserId}`);
    
    // Clear offline data for old user
    if (typeof clearOfflineDataForLogout === 'function') {
        clearOfflineDataForLogout().then(() => {
            console.log('Cleared offline data for previous user');
            
            // Update session storage with new user
            sessionStorage.setItem('current_user_id', newUserId);
            
            // Reinitialize offline sync for new user
            if (typeof initDB === 'function') {
                initDB().then(() => {
                    console.log('Reinitialized offline sync for new user');
                }).catch(error => {
                    console.error('Error reinitializing offline sync:', error);
                });
            }
        }).catch(error => {
            console.error('Error clearing offline data for previous user:', error);
            // Continue with new user setup even if cleanup failed
            sessionStorage.setItem('current_user_id', newUserId);
        });
    } else {
        // Fallback: just update session storage
        sessionStorage.setItem('current_user_id', newUserId);
    }
}

// Clean up old databases (utility function)
function cleanupOldUserDatabases() {
    return new Promise((resolve) => {
        // Get list of all IndexedDB databases (if supported)
        if ('databases' in indexedDB) {
            indexedDB.databases().then(databases => {
                const cleanupPromises = databases
                    .filter(db => db.name.startsWith('invoiceOfflineDB_'))
                    .map(db => {
                        return new Promise((resolveCleanup) => {
                            const deleteRequest = indexedDB.deleteDatabase(db.name);
                            deleteRequest.onsuccess = () => {
                                console.log('Cleaned up old database:', db.name);
                                resolveCleanup();
                            };
                            deleteRequest.onerror = () => resolveCleanup(); // Continue even if fails
                            deleteRequest.onblocked = () => resolveCleanup(); // Continue even if blocked
                        });
                    });
                
                Promise.all(cleanupPromises).then(() => resolve());
            }).catch(() => resolve()); // Continue even if databases() fails
        } else {
            // Fallback for browsers that don't support databases()
            resolve();
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure other scripts are loaded
    setTimeout(initUserSessionTracking, 1000);
});

// Expose functions globally
window.initUserSessionTracking = initUserSessionTracking;
window.cleanupOldUserDatabases = cleanupOldUserDatabases;
window.handleUserLogout = handleUserLogout;
