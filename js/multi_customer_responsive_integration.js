/**
 * Multi Customer Responsive Integration
 * تكامل العملاء المتعددين مع الوضع المتجاوب
 */

(function() {
    'use strict';

    // متغيرات التكامل
    let originalRestoreSavedItems = null;
    let originalUpdateInvoiceTotal = null;
    let isResponsiveIntegrationActive = false;

    // تهيئة التكامل عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        if (window.multiCustomerMode) {
            initializeResponsiveIntegration();
        }
    });

    /**
     * تهيئة التكامل مع الوضع المتجاوب
     */
    function initializeResponsiveIntegration() {
        if (isResponsiveIntegrationActive) return;

        console.log('Initializing Multi Customer Responsive Integration...');

        // انتظار تحميل مدير العملاء
        const checkManagerInterval = setInterval(() => {
            if (window.customerTabsManager) {
                clearInterval(checkManagerInterval);
                setupResponsiveIntegration();
            }
        }, 100);

        // تنظيف بعد 5 ثوان إذا لم يتم العثور على المدير
        setTimeout(() => {
            clearInterval(checkManagerInterval);
        }, 5000);
    }

    /**
     * إعداد التكامل مع الوضع المتجاوب
     */
    function setupResponsiveIntegration() {
        try {
            // تحسين دالة استرجاع الأصناف المحفوظة
            enhanceRestoreSavedItems();

            // تحسين دالة تحديث إجمالي الفاتورة
            enhanceUpdateInvoiceTotal();

            // إضافة مستمعات أحداث الوضع المتجاوب
            addResponsiveEventListeners();

            // تحسين دالة إضافة الأصناف في الوضع المتجاوب
            enhanceResponsiveAddItem();

            // تحسين دالة حذف الأصناف في الوضع المتجاوب
            enhanceResponsiveRemoveItem();

            isResponsiveIntegrationActive = true;
            console.log('Multi Customer Responsive Integration initialized successfully');

        } catch (error) {
            console.error('Error setting up responsive integration:', error);
        }
    }

    /**
     * تحسين دالة استرجاع الأصناف المحفوظة
     */
    function enhanceRestoreSavedItems() {
        // البحث عن الدالة الأصلية في النطاق العام
        if (typeof window.restoreSavedItemsResponsive === 'function') {
            originalRestoreSavedItems = window.restoreSavedItemsResponsive;

            window.restoreSavedItemsResponsive = function() {
                if (window.customerTabsManager) {
                    // استخدام دالة التحديث المحسنة من مدير العملاء
                    window.customerTabsManager.updateSelectedItemsTable();
                } else {
                    // استخدام الدالة الأصلية
                    originalRestoreSavedItems.apply(this, arguments);
                }
            };

            console.log('restoreSavedItemsResponsive enhanced for multi-customer');
        }
    }

    /**
     * تحسين دالة تحديث إجمالي الفاتورة
     */
    function enhanceUpdateInvoiceTotal() {
        // البحث عن الدالة الأصلية
        if (typeof window.updateInvoiceTotal === 'function') {
            originalUpdateInvoiceTotal = window.updateInvoiceTotal;

            window.updateInvoiceTotal = function() {
                if (window.customerTabsManager) {
                    // استخدام دالة التحديث من مدير العملاء
                    window.customerTabsManager.updateInvoiceTotal();
                } else {
                    // استخدام الدالة الأصلية
                    originalUpdateInvoiceTotal.apply(this, arguments);
                }
            };

            console.log('updateInvoiceTotal enhanced for multi-customer');
        }
    }

    /**
     * إضافة مستمعات أحداث الوضع المتجاوب
     */
    function addResponsiveEventListeners() {
        // مستمع لتغيير الوضع المتجاوب
        document.addEventListener('click', function(e) {
            // التحقق من النقر على زر التبديل للوضع المتجاوب
            if (e.target.closest('#responsive-toggle, .responsive-toggle')) {
                setTimeout(() => {
                    handleResponsiveModeChange();
                }, 100);
            }
        });

        // مستمع لتحديث الجدول عند تغيير حجم النافذة
        window.addEventListener('resize', debounce(() => {
            if (window.customerTabsManager && window.responsiveMode) {
                window.customerTabsManager.updateSelectedItemsTable();
            }
        }, 250));

        console.log('Responsive event listeners added');
    }

    /**
     * معالجة تغيير الوضع المتجاوب
     */
    function handleResponsiveModeChange() {
        if (!window.customerTabsManager) return;

        const isResponsive = window.responsiveMode || window.isResponsiveModeActive;
        
        if (isResponsive) {
            // تم التبديل للوضع المتجاوب
            setTimeout(() => {
                window.customerTabsManager.updateSelectedItemsTable();
                window.customerTabsManager.updateInvoiceTotal();
            }, 200);
        } else {
            // تم التبديل للوضع العادي
            setTimeout(() => {
                window.customerTabsManager.updateOriginalTable();
            }, 200);
        }

        console.log('Responsive mode changed, updated tables accordingly');
    }

    /**
     * تحسين دالة إضافة الأصناف في الوضع المتجاوب
     */
    function enhanceResponsiveAddItem() {
        // مراقبة إضافة أصناف جديدة في الوضع المتجاوب
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // البحث عن أزرار الإضافة الجديدة
                            const addButtons = node.querySelectorAll ? node.querySelectorAll('.add-btn') : [];
                            addButtons.forEach(button => {
                                enhanceAddButton(button);
                            });

                            // إذا كان العنصر نفسه زر إضافة
                            if (node.classList && node.classList.contains('add-btn')) {
                                enhanceAddButton(node);
                            }
                        }
                    });
                }
            });
        });

        // مراقبة التغييرات في الجدول
        const tableContainer = document.querySelector('.items, #itemsList, .table-container');
        if (tableContainer) {
            observer.observe(tableContainer, {
                childList: true,
                subtree: true
            });
        }

        console.log('Responsive add item function enhanced');
    }

    /**
     * تحسين زر الإضافة
     */
    function enhanceAddButton(button) {
        if (button.dataset.enhanced) return; // تجنب التحسين المتكرر

        button.dataset.enhanced = 'true';

        // إزالة المستمعات القديمة
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // إضافة مستمع جديد
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const row = this.closest('tr');
            if (row && row.dataset.itemId && window.customerTabsManager) {
                const item = {
                    id: row.dataset.itemId,
                    name: row.dataset.itemName,
                    price: row.dataset.itemPrice || '0',
                    type: row.dataset.itemType || 'piece'
                };

                window.customerTabsManager.addItemToCurrentCustomer(item);
            }
        });
    }

    /**
     * تحسين دالة حذف الأصناف في الوضع المتجاوب
     */
    function enhanceResponsiveRemoveItem() {
        // مراقبة أزرار الحذف في جدول الأصناف المختارة
        document.addEventListener('click', function(e) {
            if (e.target.closest('.remove-btn') && window.customerTabsManager) {
                const button = e.target.closest('.remove-btn');
                const row = button.closest('tr');
                
                if (row && row.dataset.itemId) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    window.customerTabsManager.removeItemFromCustomer(row.dataset.itemId);
                }
            }
        });

        console.log('Responsive remove item function enhanced');
    }

    /**
     * دالة تأخير التنفيذ
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * تصدير الدوال للاستخدام العام
     */
    window.MultiCustomerResponsiveIntegration = {
        initialize: initializeResponsiveIntegration,
        handleResponsiveModeChange: handleResponsiveModeChange,
        isActive: () => isResponsiveIntegrationActive
    };

})();