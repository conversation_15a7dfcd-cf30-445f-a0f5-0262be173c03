/**
 * File System Manager
 * مدير نظام الملفات باستخدام File System Access API
 * يدير حفظ وقراءة وحذف ملفات JSON المشفرة للفواتير
 */

class FileSystemManager {
    constructor() {
        this.directoryHandle = null;
        this.subDirectoryHandle = null;
        this.isSupported = 'showDirectoryPicker' in window;
        this.encryptionKey = null;
        this.storageKey = 'fs_directory_handle';
        this.subFolderName = 'elwaled_market';
        this.init();
    }

    async init() {
        try {
            // إنشاء مفتاح التش��ير
            this.encryptionKey = await this.generateEncryptionKey();
            
            // محاولة استعادة مقبض المجلد دون عرض المحدد
            await this.tryRestoreWithPermission();
            
            console.log('File System Manager initialized');
        } catch (error) {
            console.error('Error initializing File System Manager:', error);
        }
    }

    /**
     * التأكد من وجود المجلد الفرعي المخصص
     */
    async ensureSubDirectory() {
        if (!this.directoryHandle) {
            throw new Error('No main directory handle available');
        }

        try {
            this.subDirectoryHandle = await this.directoryHandle.getDirectoryHandle(this.subFolderName, { create: true });
            console.log('Subdirectory ensured:', this.subFolderName);
        } catch (error) {
            console.error('Error ensuring subdirectory:', error);
            throw error;
        }
    }

    /**
     * التحقق من دعم File System Access API
     */
    isFileSystemSupported() {
        return this.isSupported;
    }

    /**
     * طلب اختيار مجلد من المستخدم
     */
    async requestDirectoryAccess() {
        if (!this.isSupported) {
            throw new Error('File System Access API غير مدعوم في هذا المتصفح');
        }

        try {
            this.directoryHandle = await window.showDirectoryPicker({
                mode: 'readwrite',
                startIn: 'downloads'  // ابدأ في مجلد التنزيلات
            });

            // إنشاء المجلد الفرعي المخصص
            await this.ensureSubDirectory();

            // حفظ مقبض المجلد الرئيسي
            await this.saveDirectoryHandle();

            console.log('Directory access granted:', this.directoryHandle.name);
            console.log('Subdirectory ready:', this.subFolderName);
            return true;
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('User cancelled directory selection');
                return false;
            }
            throw error;
        }
    }

    /**
     * حفظ مقبض المجلد في IndexedDB
     */
    async saveDirectoryHandle() {
        if (!this.directoryHandle) return;

        try {
            // فتح قاعدة بيانات IndexedDB
            const db = await this.openIndexedDB();
            const transaction = db.transaction(['handles'], 'readwrite');
            const store = transaction.objectStore('handles');

            await store.put({
                id: 'directory',
                handle: this.directoryHandle,
                timestamp: Date.now()
            });

            console.log('Directory handle saved');
        } catch (error) {
            console.error('Error saving directory handle:', error);
        }
    }

    /**
     * استعادة مقبض المجلد من IndexedDB
     */
    async restoreDirectoryHandle(showPrompt = true) {
        try {
            const db = await this.openIndexedDB();
            const transaction = db.transaction(['handles'], 'readonly');
            const store = transaction.objectStore('handles');
            const result = await store.get('directory');

            if (result && result.handle) {
                // التحقق من صحة المقبض
                let permission = await result.handle.queryPermission({ mode: 'readwrite' });
                
                if (permission === 'granted') {
                    this.directoryHandle = result.handle;
                    console.log('Directory handle restored');
                    await this.ensureSubDirectory();
                    return true;
                } else if (permission === 'prompt' && showPrompt) {
                    // طلب الإذن مرة أخرى
                    const newPermission = await result.handle.requestPermission({ mode: 'readwrite' });
                    if (newPermission === 'granted') {
                        this.directoryHandle = result.handle;
                        console.log('Directory permission re-granted');
                        await this.ensureSubDirectory();
                        return true;
                    }
                }
            }
        } catch (error) {
            console.error('Error restoring directory handle:', error);
        }

        return false;
    }

    async tryRestoreWithPermission() {
        return await this.restoreDirectoryHandle(true);
    }

    async tryRestoreSilent() {
        return await this.restoreDirectoryHandle(false);
    }

    async ensureHandle() {
        if (this.directoryHandle) {
            return true;
        }

        // محاولة الاستعادة مع طلب الإذن
        let restored = await this.tryRestoreWithPermission();
        if (restored) return true;

        // إذا فشلت الاستعادة، طلب مجلد جديد
        return await this.requestDirectoryAccess();
    }

    /**
     * فتح قاعدة بيانات IndexedDB لحفظ مقابض الملفات
     */
    async openIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('FileSystemHandles', 1);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('handles')) {
                    db.createObjectStore('handles', { keyPath: 'id' });
                }
            };
        });
    }

    /**
     * إنشاء مفتاح تشفير
     */
    async generateEncryptionKey() {
        try {
            // استخدام معرف المستخدم كأساس للمفتاح
            const userId = window.encryptedAccountId || 'default_user';
            const encoder = new TextEncoder();
            const data = encoder.encode(userId + '_invoice_encryption_key');
            
            // إنشاء hash للمفتاح
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = new Uint8Array(hashBuffer);
            
            // تحويل إلى string للاستخدام في التشفير البسيط
            return Array.from(hashArray).map(b => b.toString(16).padStart(2, '0')).join('');
        } catch (error) {
            console.error('Error generating encryption key:', error);
            // fallback key
            return 'default_encryption_key_' + (window.encryptedAccountId || 'user');
        }
    }

    /**
     * تشفير البيانات
     */
    encryptData(data) {
        try {
            const jsonString = JSON.stringify(data);
            return jsonString;
        } catch (error) {
            console.error('Error encrypting data:', error);
            throw error;
        }
    }

    /**
     * فك تشفير البي��نات
     */
    decryptData(encryptedData) {
        try {
            return JSON.parse(encryptedData);
        } catch (error) {
            console.error('Error decrypting data:', error);
            throw error;
        }
    }

    /**
     * إنشاء اسم ملف مشفر
     */
    generateEncryptedFileName(invoiceData) {
        try {
            // إنشاء معرف فريد للفاتورة
            const timestamp = Date.now();
            const userId = window.encryptedAccountId || 'user';
            const itemCount = invoiceData.items ? invoiceData.items.length : 0;
            const customerId = invoiceData.customerId || 'default';
            
            // إنشاء hash للاسم
            const nameData = `${userId}_${customerId}_${timestamp}_${itemCount}`;
            const hash = this.simpleHash(nameData);
            
            return `invoice_${hash}.json`;
        } catch (error) {
            console.error('Error generating encrypted filename:', error);
            return `invoice_${Date.now()}.json`;
        }
    }

    /**
     * إنشاء hash بسيط
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(16);
    }

    /**
     * حفظ فاتورة في ملف JSON مشفر
     */
    async saveInvoiceToFile(invoiceData) {
        // التأكد من وجود المقبض قبل الحفظ
        const hasAccess = await this.ensureHandle();
        if (!hasAccess) {
            console.warn('تم إلغاء الوصول إلى المجلد، سيتم الحفظ في IndexedDB فقط');
            return { success: false, message: 'تم إلغاء الوصول' };
        }

        try {
            // التأكد من وجود المجلد الفرعي
            await this.ensureSubDirectory();
            
            // إنشاء اسم ملف مشفر
            const fileName = this.generateEncryptedFileName(invoiceData);
            
            // تشفير البيانات
            const data = this.encryptData(invoiceData);
            
            // إنشاء الملف داخل المجلد الفرعي
            const fileHandle = await this.subDirectoryHandle.getFileHandle(fileName, { create: true });
            const writable = await fileHandle.createWritable();
            
            // كتابة البيانات المشفرة
            await writable.write(data);
            await writable.close();
            
            console.log('Invoice saved to encrypted file:', fileName);
            
            return {
                success: true,
                fileName: fileName,
                filePath: this.directoryHandle.name + '/' + this.subFolderName + '/' + fileName
            };
        } catch (error) {
            console.error('Error saving invoice to file:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * قراءة جميع ملفات الفواتير المحفوظة
     */
    async readAllInvoiceFiles() {
        // محاولة الاستعادة الصامتة
        if (!this.subDirectoryHandle) {
            const restored = await this.tryRestoreSilent();
            if (!restored) {
                console.log('No directory access for reading files');
                return [];
            }
            await this.ensureSubDirectory();
        }

        try {
            const invoices = [];
            
            // قراءة جميع ��لملفات في المجلد الفرعي
            for await (const [name, handle] of this.subDirectoryHandle.entries()) {
                if (handle.kind === 'file' && name.startsWith('invoice_') && name.endsWith('.json')) {
                    try {
                        const file = await handle.getFile();
                        const encryptedContent = await file.text();
                        
                        // فك تشفير البيانات
                        const invoiceData = this.decryptData(encryptedContent);
                        
                        invoices.push({
                            fileName: name,
                            data: invoiceData,
                            fileHandle: handle,
                            lastModified: file.lastModified
                        });
                    } catch (error) {
                        console.error(`Error reading invoice file ${name}:`, error);
                    }
                }
            }
            
            console.log(`Found ${invoices.length} invoice files`);
            return invoices;
        } catch (error) {
            console.error('Error reading invoice files:', error);
            return [];
        }
    }

    /**
     * حذف ملف فاتورة
     */
    async deleteInvoiceFile(fileName) {
        if (!this.subDirectoryHandle) {
            const restored = await this.tryRestoreSilent();
            if (!restored) {
                console.warn('No access to delete file');
                return false;
            }
        }

        try {
            await this.subDirectoryHandle.removeEntry(fileName);
            console.log('Invoice file deleted:', fileName);
            return true;
        } catch (error) {
            console.error('Error deleting invoice file:', error);
            return false;
        }
    }

    /**
     * حذف جميع ملفات الفواتير المتزامنة
     */
    async deleteAllSyncedInvoices() {
        if (!this.subDirectoryHandle) {
            throw new Error('لا يوجد وصول للمجلد');
        }

        try {
            let deletedCount = 0;
            const filesToDelete = [];
            
            // جمع أسماء الملفات للحذف
            for await (const [name, handle] of this.subDirectoryHandle.entries()) {
                if (handle.kind === 'file' && name.startsWith('invoice_') && name.endsWith('.json')) {
                    filesToDelete.push(name);
                }
            }
            
            // حذف الملفات
            for (const fileName of filesToDelete) {
                try {
                    await this.subDirectoryHandle.removeEntry(fileName);
                    deletedCount++;
                } catch (error) {
                    console.error(`Error deleting file ${fileName}:`, error);
                }
            }
            
            console.log(`Deleted ${deletedCount} invoice files`);
            return deletedCount;
        } catch (error) {
            console.error('Error deleting synced invoices:', error);
            throw error;
        }
    }

    /**
     * مزامنة ملفات ال��واتير مع الخادم
     */
    async syncInvoiceFiles() {
        try {
            const invoiceFiles = await this.readAllInvoiceFiles();
            
            if (invoiceFiles.length === 0) {
                console.log('No invoice files to sync');
                return { success: 0, failed: 0, deleted: 0 };
            }

            let successCount = 0;
            let failedCount = 0;
            let deletedCount = 0;

            for (const invoiceFile of invoiceFiles) {
                try {
                    // محاولة مزامنة الفاتورة
                    const synced = await this.syncSingleInvoice(invoiceFile.data);
                    
                    if (synced) {
                        // حذف الملف بعد المزامنة الناجحة
                        await this.deleteInvoiceFile(invoiceFile.fileName);
                        successCount++;
                        deletedCount++;
                    } else {
                        failedCount++;
                    }
                } catch (error) {
                    console.error(`Error syncing invoice file ${invoiceFile.fileName}:`, error);
                    failedCount++;
                }
                
                // تأخير قصير بين المزامنات
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log(`File sync completed: ${successCount} success, ${failedCount} failed, ${deletedCount} deleted`);
            
            return { success: successCount, failed: failedCount, deleted: deletedCount };
        } catch (error) {
            console.error('Error syncing invoice files:', error);
            throw error;
        }
    }

    /**
     * مزامنة فاتورة واحدة مع الخادم
     */
    async syncSingleInvoice(invoiceData) {
        try {
            // تحديد نقطة النهاية المناسبة
            let endpoint;
            if (invoiceData.type === 'customer_sale' || invoiceData.type === 'customer_return') {
                endpoint = 'save_sale_invoice.php';
            } else if (invoiceData.type === 'sale') {
                endpoint = 'confirm_sale_invoice.php';
            } else {
                endpoint = 'confirm_invoice.php';
            }

            // إعداد البيانات للإرسال
            const formData = new FormData();
            formData.append('store_id', invoiceData.store_id);
            formData.append('account_id', invoiceData.account_id);
            formData.append('items', JSON.stringify(invoiceData.items));
            formData.append('invoice_type', invoiceData.invoice_type || invoiceData.type);
            formData.append('images', JSON.stringify(invoiceData.images || []));

            if (invoiceData.branch_id) {
                formData.append('branch_id', invoiceData.branch_id);
            }

            if (invoiceData.account_buyer_id) {
                formData.append('account_buyer_id', invoiceData.account_buyer_id);
            }

            // إضافة معلومات الوضع المقسم إذا كانت متوفرة
            if (invoiceData.multiCustomerMode) {
                formData.append('multi_customer_mode', 'true');
                formData.append('customer_id', invoiceData.customerId);
                formData.append('session_id', invoiceData.sessionId);
            }

            // إرسال الطلب
            const response = await fetch(endpoint, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                return true;
            } else {
                // التحقق من رسائل التكرار
                if (result.message && (
                    result.message.includes('تم إرسال نفس الطلب') || 
                    result.message.includes('فاتورة مطابقة') ||
                    result.message.includes('مكررة')
                )) {
                    console.log('Invoice already exists on server, marking as synced');
                    return true;
                }
                throw new Error(result.message || 'Unknown error during sync');
            }
        } catch (error) {
            console.error('Error syncing single invoice:', error);
            return false;
        }
    }

    /**
     * الحصول على إحصائيات الملفات
     */
    async getFileStats() {
        try {
            if (!this.subDirectoryHandle) {
                const restored = await this.tryRestoreSilent();
                if (!restored) {
                    return { totalFiles: 0, totalSize: 0 };
                }
                await this.ensureSubDirectory();
            }

            let totalFiles = 0;
            let totalSize = 0;

            for await (const [name, handle] of this.subDirectoryHandle.entries()) {
                if (handle.kind === 'file' && name.startsWith('invoice_') && name.endsWith('.json')) {
                    totalFiles++;
                    try {
                        const file = await handle.getFile();
                        totalSize += file.size;
                    } catch (error) {
                        console.error(`Error getting file size for ${name}:`, error);
                    }
                }
            }

            return { totalFiles, totalSize };
        } catch (error) {
            console.error('Error getting file stats:', error);
            return { totalFiles: 0, totalSize: 0 };
        }
    }

    /**
     * الحصول على جميع ملفات الفواتير مع بياناتها
     */
    async getAllInvoiceFiles() {
        try {
            if (!this.subDirectoryHandle) {
                const restored = await this.tryRestoreSilent();
                if (!restored) {
                    return [];
                }
                await this.ensureSubDirectory();
            }

            const invoiceFiles = [];

            for await (const [name, handle] of this.subDirectoryHandle.entries()) {
                if (handle.kind === 'file' && name.startsWith('invoice_') && name.endsWith('.json')) {
                    try {
                        const file = await handle.getFile();
                        const content = await file.text();
                        const data = this.decryptData(content);
                        
                        invoiceFiles.push({
                            fileName: name,
                            data: data,
                            size: file.size,
                            lastModified: file.lastModified
                        });
                    } catch (error) {
                        console.warn('Could not read invoice file:', name, error);
                    }
                }
            }

            return invoiceFiles;
        } catch (error) {
            console.error('Error getting all invoice files:', error);
            return [];
        }
    }

    /**
     * تنظيف الملفات القديمة
     */
    async cleanupOldFiles(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
        try {
            if (!this.subDirectoryHandle) {
                const restored = await this.tryRestoreSilent();
                if (!restored) {
                    return 0;
                }
            }

            const cutoffTime = Date.now() - maxAge;
            let deletedCount = 0;

            for await (const [name, handle] of this.subDirectoryHandle.entries()) {
                if (handle.kind === 'file' && name.startsWith('invoice_') && name.endsWith('.json')) {
                    try {
                        const file = await handle.getFile();
                        if (file.lastModified < cutoffTime) {
                            await this.subDirectoryHandle.removeEntry(name);
                            deletedCount++;
                        }
                    } catch (error) {
                        console.error(`Error processing file ${name}:`, error);
                    }
                }
            }

            console.log(`Cleaned up ${deletedCount} old files`);
            return deletedCount;
        } catch (error) {
            console.error('Error cleaning up old files:', error);
            return 0;
        }
    }

    /**
     * تدمير المدير وتنظيف الموارد
     */
    async destroy() {
        try {
            // مزامنة أخيرة
            if (this.subDirectoryHandle) {
                await this.syncInvoiceFiles();
            }
            
            this.directoryHandle = null;
            this.subDirectoryHandle = null;
            this.encryptionKey = null;
            
            console.log('File System Manager destroyed');
        } catch (error) {
            console.error('Error destroying File System Manager:', error);
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.FileSystemManager = FileSystemManager;