// تطبيق الثيم فوراً قبل تحميل أي شيء آخر لتجنب الوميض
(function() {
    'use strict';
    
    // تطبيق الثيم فوراً من localStorage
    const savedTheme = localStorage.getItem("theme");
    
    if (savedTheme) {
        // إذا كان هناك ثيم محفوظ، طبقه فوراً
        document.documentElement.setAttribute("data-theme", savedTheme);
    } else {
        // إذا لم يكن هناك ثيم محفوظ، تحقق من تفضيلات النظام
        if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
            document.documentElement.setAttribute("data-theme", "dark");
            localStorage.setItem("theme", "dark");
        } else {
            document.documentElement.setAttribute("data-theme", "light");
            localStorage.setItem("theme", "light");
        }
    }
    
    // إضافة مستمع لتغييرات تفضيلات النظام
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
        
        // دالة للتعامل مع تغيير تفضيلات النظام
        function handleSystemThemeChange(e) {
            // فقط إذا لم يكن المستخدم قد اختار ثيم محدد
            const userTheme = localStorage.getItem("theme");
            if (!userTheme) {
                const newTheme = e.matches ? "dark" : "light";
                document.documentElement.setAttribute("data-theme", newTheme);
                localStorage.setItem("theme", newTheme);
            }
        }
        
        // إضافة المستمع
        if (mediaQuery.addListener) {
            mediaQuery.addListener(handleSystemThemeChange);
        } else if (mediaQuery.addEventListener) {
            mediaQuery.addEventListener('change', handleSystemThemeChange);
        }
    }
    
    // تحسين الأداء: إضافة كلاس للجسم لتجنب الانتقالات أثناء التحميل الأولي
    document.documentElement.classList.add('theme-loading');
    
    // إزالة كلاس التحميل بعد تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.documentElement.classList.remove('theme-loading');
            }, 100);
        });
    } else {
        setTimeout(function() {
            document.documentElement.classList.remove('theme-loading');
        }, 100);
    }
})();