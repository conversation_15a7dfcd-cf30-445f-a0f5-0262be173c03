<?php
include 'db_connection.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get POST data
    $input_data = json_decode(file_get_contents('php://input'), true);
    
    if (isset($input_data['order_id'])) {
        $return_id = $input_data['order_id'];
        
        try {
            // Start transaction
            $conn->begin_transaction();
            
            // First, fetch the return data to have information for inventory updates if needed
            $stmt = $conn->prepare("SELECT item_id, quantity FROM returns WHERE return_id = ?");
            $stmt->bind_param("i", $return_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $returnData = $result->fetch_assoc();
            $stmt->close();
            
            // Now delete the return record
            $stmt = $conn->prepare("DELETE FROM returns WHERE return_id = ?");
            $stmt->bind_param("i", $return_id);
            $stmt->execute();
            
            if ($stmt->affected_rows > 0) {
                // Commit transaction
                $conn->commit();
                
                // Return success response
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'تم حذف البيانات بنجاح']);
                exit;
            } else {
                // Rollback if no rows were affected
                $conn->rollback();
                
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'لم يتم العثور على البيانات للحذف']);
                exit;
            }
        } catch (Exception $e) {
            // Rollback on error
            $conn->rollback();
            
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'خطأ: ' . $e->getMessage()]);
            exit;
        }
    } else {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'بيانات غير كافية']);
        exit;
    }
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صالحة']);
    exit;
}

// Close connection
$conn->close();
?> 